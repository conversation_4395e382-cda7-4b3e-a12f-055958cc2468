import { createSupabaseServerClient } from "@/lib/supabase/server"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { Users, UserPlus, Music, Headphones } from "lucide-react"

export default async function CommunityPage() {
  const supabase = createSupabaseServerClient()

  // Récupérer les profils
  const { data: profiles } = await supabase.from("profiles").select("*").limit(6)

  // Récupérer les groupes de l'utilisateur
  const {
    data: { user },
  } = await supabase.auth.getUser()

  const { data: userGroups } = await supabase
    .from("groups")
    .select("*, group_members!inner(*)")
    .eq("group_members.user_id", user?.id || "")
    .limit(3)

  // Récupérer les groupes suggérés
  const { data: suggestedGroups } = await supabase.from("groups").select("*").limit(3)

  // Récupérer les messages récents
  const { data: recentMessages } = await supabase
    .from("messages")
    .select("*, profiles(*)")
    .eq("recipient_id", user?.id || "")
    .order("created_at", { ascending: false })
    .limit(3)

  // Récupérer les activités récentes
  const { data: activities } = await supabase
    .from("activities")
    .select("*, profiles(*)")
    .order("created_at", { ascending: false })
    .limit(5)

  // Récupérer les morceaux tendance
  const { data: trendingSongs } = await supabase
    .from("songs")
    .select("*, profiles(*)")
    .order("plays", { ascending: false })
    .limit(3)

  // Récupérer les opportunités de collaboration
  const { data: collaborations } = await supabase.from("collaboration_opportunities").select("*, profiles(*)").limit(2)

  return (
    <div className="container mx-auto p-6">
      <Tabs defaultValue="activity" className="w-full">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <div className="w-full md:w-64 space-y-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">Communauté</h2>
              <Button size="sm" variant="outline">
                <UserPlus className="h-4 w-4 mr-2" />
                Inviter
              </Button>
            </div>

            <TabsList className="flex flex-col h-auto bg-transparent space-y-1">
              <TabsTrigger value="groups" className="justify-start w-full data-[state=active]:bg-accent">
                Groupes
              </TabsTrigger>
              <TabsTrigger value="messages" className="justify-start w-full data-[state=active]:bg-accent">
                Messages
              </TabsTrigger>
              <TabsTrigger value="notifications" className="justify-start w-full data-[state=active]:bg-accent">
                Notifications
              </TabsTrigger>
              <TabsTrigger value="activity" className="justify-start w-full data-[state=active]:bg-accent">
                Fil d'activité
              </TabsTrigger>
            </TabsList>

            <div className="space-y-4">
              <h3 className="font-medium">Mes Groupes</h3>
              <div className="space-y-2">
                {(
                  userGroups || [
                    {
                      id: "1",
                      name: "Electronic Producers",
                      members_count: 28,
                      online_count: 3,
                      last_activity: "New synth patch uploaded by @synthking",
                    },
                    {
                      id: "2",
                      name: "Acoustic Songwriters",
                      members_count: 16,
                      online_count: 2,
                      last_activity: "Weekly challenge: Write a song about...",
                    },
                    {
                      id: "3",
                      name: "Vocal Collaborations",
                      members_count: 18,
                      online_count: 2,
                      last_activity: "Looking for male vocals for EDM track",
                    },
                  ]
                ).map((group, index) => (
                  <Link href={`/community/groups/${'id' in group && typeof group.id === 'string' ? group.id : index}`} key={'id' in group && typeof group.id === 'string' ? group.id : index}>
                    <Card className="hover:bg-accent/50 transition-colors">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10 bg-primary/10">
                            <AvatarFallback className="bg-primary/10 text-primary">
                              {typeof (group as any).icon === 'string' ? (group as any).icon :
                                index === 0 ? (
                                  <Music className="h-5 w-5" />
                                ) : index === 1 ? (
                                  <Headphones className="h-5 w-5" />
                                ) : (
                                  <Users className="h-5 w-5" />
                                )}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{group.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {group.members_count} membres • {group.online_count} en ligne
                            </p>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2 truncate">
                          <span className="font-medium">Récent:</span> {group.last_activity}
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>

              <h3 className="font-medium">Groupes suggérés</h3>
              <div className="space-y-2">
                {(
                  suggestedGroups || [
                    { id: "4", name: "Mixing & Mastering Pros", members_count: 73 },
                    { id: "5", name: "Lo-Fi Producers", members_count: 128 },
                  ]
                ).map((group, index) => (
                  <Card key={'id' in group && typeof group.id === 'string' ? group.id : index} className="hover:bg-accent/50 transition-colors">
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10 bg-primary/10">
                          <AvatarFallback className="bg-primary/10 text-primary">
                            <Users className="h-5 w-5" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">{group.name}</p>
                          <p className="text-xs text-muted-foreground">{group.members_count} membres</p>
                        </div>
                        <Button size="sm" variant="secondary">
                          Rejoindre
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="pt-4 border-t">
                <h3 className="font-medium mb-2">Messages récents</h3>
                <div className="space-y-2">
                  {(
                    recentMessages || [
                      {
                        id: "1",
                        sender: { name: "Alex Kim", avatar_url: null },
                        content: "Hey, listened to your new track! Love the...",
                        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                      },
                      {
                        id: "2",
                        sender: { name: "Sofia Lopez", avatar_url: null },
                        content: "About that collab we discussed, I've got some ideas...",
                        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                      },
                      {
                        id: "3",
                        sender: { name: "Marcus Johnson", avatar_url: null },
                        content: "Thanks for the feedback on my mix!",
                        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                      },
                    ]
                  ).map((message, index) => (
                    <Link href={`/messages/${message.sender && 'id' in message.sender && typeof message.sender.id === 'string' ? message.sender.id : index}`} key={message.id || index}>
                      <Card className="hover:bg-accent/50 transition-colors">
                        <CardContent className="p-3">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage
                                src={message.sender?.avatar_url || "/placeholder.svg?height=32&width=32&query=person"}
                              />
                              <AvatarFallback>{message.sender?.name?.charAt(0) || "U"}</AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <p className="font-medium text-sm">{message.sender?.name}</p>
                                <p className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(message.created_at), { addSuffix: true, locale: fr })}
                                </p>
                              </div>
                              <p className="text-sm text-muted-foreground truncate">{message.content}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <TabsContent value="groups" className="space-y-4">
              <div className="flex items-center justify-between">
                <CardTitle>Groupes</CardTitle>
                <Button size="sm">Créer un groupe</Button>
              </div>
              <Input type="search" placeholder="Rechercher un groupe..." className="max-w-sm" />
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  {
                    id: "1",
                    name: "Electronic Producers",
                    members_count: 28,
                    online_count: 3,
                    last_activity: "New synth patch uploaded by @synthking",
                  },
                  {
                    id: "2",
                    name: "Acoustic Songwriters",
                    members_count: 16,
                    online_count: 2,
                    last_activity: "Weekly challenge: Write a song about...",
                  },
                  {
                    id: "3",
                    name: "Vocal Collaborations",
                    members_count: 18,
                    online_count: 2,
                    last_activity: "Looking for male vocals for EDM track",
                  },
                  { id: "4", name: "Mixing & Mastering Pros", members_count: 73 },
                  { id: "5", name: "Lo-Fi Producers", members_count: 128 },
                  { id: "6", name: "Indie Rock Collective", members_count: 45 },
                ].map((group, index) => (
                  <Link href={`/community/groups/${'id' in group && typeof group.id === 'string' ? group.id : index}`} key={'id' in group && typeof group.id === 'string' ? group.id : index}>
                    <Card className="hover:bg-accent/50 transition-colors">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-10 w-10 bg-primary/10">
                            <AvatarFallback className="bg-primary/10 text-primary">
                              {typeof (group as any).icon === 'string' ? (group as any).icon :
                                index === 0 ? (
                                  <Music className="h-5 w-5" />
                                ) : index === 1 ? (
                                  <Headphones className="h-5 w-5" />
                                ) : (
                                  <Users className="h-5 w-5" />
                                )}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{group.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {group.members_count} membres • {group.online_count} en ligne
                            </p>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2 truncate">
                          <span className="font-medium">Récent:</span> {group.last_activity}
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="messages" className="space-y-4">
              <CardTitle>Messages</CardTitle>
              <div className="space-y-2">
                {[
                  {
                    id: "1",
                    sender: { name: "Alex Kim", avatar_url: null },
                    content: "Hey, listened to your new track! Love the...",
                    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                  },
                  {
                    id: "2",
                    sender: { name: "Sofia Lopez", avatar_url: null },
                    content: "About that collab we discussed, I've got some ideas...",
                    created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                  },
                  {
                    id: "3",
                    sender: { name: "Marcus Johnson", avatar_url: null },
                    content: "Thanks for the feedback on my mix!",
                    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                  },
                ].map((message, index) => (
                  <Link href={`/messages/${message.sender && 'id' in message.sender && typeof message.sender.id === 'string' ? message.sender.id : index}`} key={message.id || index}>
                    <Card className="hover:bg-accent/50 transition-colors">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage
                              src={message.sender?.avatar_url || "/placeholder.svg?height=32&width=32&query=person"}
                            />
                            <AvatarFallback>{message.sender?.name?.charAt(0) || "U"}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="font-medium text-sm">{message.sender?.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {formatDistanceToNow(new Date(message.created_at), { addSuffix: true, locale: fr })}
                              </p>
                            </div>
                            <p className="text-sm text-muted-foreground truncate">{message.content}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="notifications">
              <CardTitle>Notifications</CardTitle>
              <p>No notifications yet.</p>
            </TabsContent>

            <TabsContent value="activity">
              <CardTitle>Fil d'activité</CardTitle>
              <p>No activity yet.</p>
            </TabsContent>
          </div>
        </div>
      </Tabs>
    </div>
  )
}
