CREATE OR R<PERSON>LACE FUNCTION toggle_profile_follow(
    p_profile_id UUID,
    p_user_id UUID
)
RETURNS TABLE (is_following BOOLEAN, new_follower_count INTEGER)
LANGUAGE plpgsql
SECURITY DEFINER -- Important pour pouvoir mettre à jour le compteur sur la table profiles
AS $$
DECLARE
    v_is_following BOOLEAN;
    v_new_follower_count INTEGER;
BEGIN
    -- Check if the user is already following
    SELECT EXISTS (
        SELECT 1
        FROM public.profile_followers
        WHERE follower_id = p_user_id AND followed_profile_id = p_profile_id
    ) INTO v_is_following;

    IF v_is_following THEN
        -- Unfollow: Delete the record and decrement count
        DELETE FROM public.profile_followers
        WHERE follower_id = p_user_id AND followed_profile_id = p_profile_id;

        UPDATE public.profiles
        SET follower_count = GREATEST(0, follower_count - 1) -- Ensure count doesn't go below 0
        WHERE id = p_profile_id
        RETURNING follower_count INTO v_new_follower_count;
        
        v_is_following := FALSE;
    ELSE
        -- Follow: Insert the record and increment count
        -- Prevent following oneself
        IF p_user_id = p_profile_id THEN
            RAISE EXCEPTION 'User cannot follow themselves.';
        END IF;

        INSERT INTO public.profile_followers (follower_id, followed_profile_id)
        VALUES (p_user_id, p_profile_id);

        UPDATE public.profiles
        SET follower_count = follower_count + 1
        WHERE id = p_profile_id
        RETURNING follower_count INTO v_new_follower_count;
        
        v_is_following := TRUE;
    END IF;

    RETURN QUERY SELECT v_is_following, v_new_follower_count;
END;
$$;
