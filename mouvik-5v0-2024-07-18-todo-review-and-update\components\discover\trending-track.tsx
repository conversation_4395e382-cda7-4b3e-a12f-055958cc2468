"use client"

import { Play } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { usePlaySong } from "@/hooks/use-play-song"
import type { Song } from "@/types"

interface TrendingTrackProps {
  track: Song & { profiles?: { name?: string } }
  position: number
}

export function TrendingTrack({ track, position }: TrendingTrackProps) {
  const { play } = usePlaySong()

  const handlePlay = () => {
    play(track)
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  return (
    <div className="flex items-center justify-between p-2 rounded-md hover:bg-accent group">
      <div className="flex items-center gap-3">
        <div className="w-6 text-center text-muted-foreground">{position}</div>
        <img
          src={track.cover_url || "/placeholder.svg?height=48&width=48&query=song cover"}
          alt={track.title}
          className="w-12 h-12 rounded"
        />
        <div>
          <p className="font-medium">{track.title}</p>
          <p className="text-sm text-muted-foreground">
            {track.profiles?.name || "Artiste"} • {formatDuration(track.duration || 0)}
          </p>
        </div>
      </div>
      <div className="flex items-center gap-4">
        <span className="text-sm text-muted-foreground">{track.plays.toLocaleString()} écoutes</span>
        <Button variant="ghost" size="icon" className="h-8 w-8 opacity-0 group-hover:opacity-100" onClick={handlePlay}>
          <Play className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
