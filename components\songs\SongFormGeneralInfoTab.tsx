import React from 'react';
import { FieldErrors } from 'react-hook-form';
import { Card, CardContent } from '@/components/ui/card';
import { RHFTextField, RHFSelect, RHFMultiSelectChip, RHFTextarea, RHFDatePicker } from '@/components/hook-form';
import { SelectItem } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { SongFormValues } from './song-schema'; 
import { LANGUAGE_OPTIONS, VISIBILITY_OPTIONS, STATUS_OPTIONS } from './song-options';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Control, UseFormWatch } from 'react-hook-form';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Music, Mic, Headphones, Clock, TrendingUp } from 'lucide-react';

interface SongFormGeneralInfoTabProps {
  control: Control<SongFormValues>;
  errors?: FieldErrors<SongFormValues>;
  albumsData?: Array<{ id: string; title: string }>;
  isLoadingAlbums?: boolean;
  watch?: UseFormWatch<SongFormValues>;
}

export const SongFormGeneralInfoTab: React.FC<SongFormGeneralInfoTabProps> = ({ 
  control,
  errors,
  albumsData, 
  isLoadingAlbums,
  watch,
}) => {
  // Calculer le pourcentage global de progression
  const calculateOverallProgress = (values: any) => {
    const progressFields = [
      'progression_intro',
      'progression_verse', 
      'progression_chorus',
      'progression_bridge',
      'progression_outro'
    ];
    
    const total = progressFields.reduce((sum, field) => {
      return sum + (values[field] || 0);
    }, 0);
    
    return Math.round(total / progressFields.length);
  };
  return (
    <div className="w-full space-y-8">
      {/* Basic Information Section */}
      <Card className="bg-gradient-to-br from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 shadow-xl border-0 ring-1 ring-slate-200 dark:ring-slate-700">
        <CardContent className="p-8">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-2">Informations de base</h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">Définissez les informations principales de votre morceau</p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FormField
              control={control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-medium">Titre du morceau</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Entrez le titre..." 
                      {...field} 
                      className="h-12 text-base border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="artist"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-medium">Nom de l'artiste principal</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Nom de l'artiste..." 
                      {...field} 
                      className="h-12 text-base border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <RHFTextField name="artist_name" label="Nom d'artiste (pour affichage)" description="Si différent du nom principal." />
            <RHFMultiSelectChip name="featured_artists" label="Featuring (Artistes invités)" options={[]} placeholder="Ajouter un artiste..." />
            <RHFSelect name="album_id" label="Album (Optionnel)">
              <SelectItem value="__NONE__">Aucun album</SelectItem>
              {albumsData?.map((album) => (
                <SelectItem key={album.id} value={album.id}>{album.title}</SelectItem>
              ))}
            </RHFSelect>
            <RHFTextField name="slug" label="Slug URL" placeholder="Ex: mon-super-morceau" description="Partie de l'URL pour la page publique." />
            <RHFDatePicker name="release_date" label="Date de sortie" />
            <RHFTextField name="duration_ms" label="Durée (ms)" type="number" placeholder="Ex: 240000 pour 4 minutes" />
            <RHFSelect name="lyrics_language" label="Langue des paroles">
              {LANGUAGE_OPTIONS.map(lang => <SelectItem key={lang.value} value={lang.value}>{lang.label}</SelectItem>)}
            </RHFSelect>
            <RHFSelect name="language" label="Langue principale du morceau (si différent)">
              {LANGUAGE_OPTIONS.map(lang => <SelectItem key={lang.value} value={lang.value}>{lang.label}</SelectItem>)}
            </RHFSelect>
          </div>
          <div className="mt-8">
            <RHFTextarea name="description" label="Description / Notes publiques" rows={3} placeholder="Courte description pour la page publique..." />
          </div>
        </CardContent>
      </Card>

      {/* Status & Visibility Section */}
      <Card className="bg-gradient-to-br from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 shadow-xl border-0 ring-1 ring-slate-200 dark:ring-slate-700">
        <CardContent className="p-8">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-2">Statut et visibilité</h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">Configurez les options de visibilité et les caractéristiques de votre morceau</p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <RHFSelect name="status" label="Statut du morceau">
              {STATUS_OPTIONS.map((opt: { value: string; label: string }) => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
            </RHFSelect>
            <RHFSelect name="visibility" label="Visibilité">
              {VISIBILITY_OPTIONS.map((opt: { value: string; label: string }) => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
            </RHFSelect>
          </div>
          <div className="space-y-6">
            <FormField
              control={control}
              name="is_public"
              render={({ field }) => (
                <FormItem className={cn(
                  "flex flex-row items-center justify-between rounded-xl border-2 p-6 shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl",
                  field.value 
                    ? "border-green-400 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 shadow-green-200 dark:shadow-green-800" 
                    : "border-slate-300 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 shadow-slate-200 dark:shadow-slate-700"
                )}>
                  <div className="space-y-1">
                    <FormLabel className="text-base font-semibold text-slate-900 dark:text-slate-100">Rendre ce morceau public</FormLabel>
                    <FormDescription className="text-sm text-slate-600 dark:text-slate-400">
                      Permettre aux visiteurs de voir ce morceau.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-green-500"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="is_favorite"
              render={({ field }) => (
                <FormItem className={cn(
                  "flex flex-row items-center justify-between rounded-xl border-2 p-6 shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl",
                  field.value 
                    ? "border-yellow-400 bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 shadow-yellow-200 dark:shadow-yellow-800" 
                    : "border-slate-300 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 shadow-slate-200 dark:shadow-slate-700"
                )}>
                  <div className="space-y-1">
                    <FormLabel className="text-base font-semibold text-slate-900 dark:text-slate-100">Marquer comme favori</FormLabel>
                    <FormDescription className="text-sm text-slate-600 dark:text-slate-400">
                      Ajouter ce morceau à vos favoris.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-yellow-500"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="is_archived"
              render={({ field }) => (
                <FormItem className={cn(
                  "flex flex-row items-center justify-between rounded-xl border-2 p-6 shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl",
                  field.value 
                    ? "border-orange-400 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 shadow-orange-200 dark:shadow-orange-800" 
                    : "border-slate-300 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 shadow-slate-200 dark:shadow-slate-700"
                )}>
                  <div className="space-y-1">
                    <FormLabel className="text-base font-semibold text-slate-900 dark:text-slate-100">Archiver ce morceau</FormLabel>
                    <FormDescription className="text-sm text-slate-600 dark:text-slate-400">
                      Masquer ce morceau des listes principales.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-orange-500"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="is_incomplete"
              render={({ field }) => (
                <FormItem className={cn(
                  "flex flex-row items-center justify-between rounded-xl border-2 p-6 shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl",
                  field.value 
                    ? "border-blue-400 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 shadow-blue-200 dark:shadow-blue-800" 
                    : "border-slate-300 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 shadow-slate-200 dark:shadow-slate-700"
                )}>
                  <div className="space-y-1">
                    <FormLabel className="text-base font-semibold text-slate-900 dark:text-slate-100">Marquer comme incomplet / Brouillon</FormLabel>
                    <FormDescription className="text-sm text-slate-600 dark:text-slate-400">
                      Indiquer que ce morceau est encore à l'état d'ébauche.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-blue-500"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="is_cover"
              render={({ field }) => (
                <FormItem className={cn(
                  "flex flex-row items-center justify-between rounded-xl border-2 p-6 shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl",
                  field.value 
                    ? "border-purple-400 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 shadow-purple-200 dark:shadow-purple-800" 
                    : "border-slate-300 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 shadow-slate-200 dark:shadow-slate-700"
                )}>
                  <div className="space-y-1">
                    <FormLabel className="text-base font-semibold text-slate-900 dark:text-slate-100">Ceci est une reprise (cover)</FormLabel>
                    <FormDescription className="text-sm text-slate-600 dark:text-slate-400">
                      Indiquer si ce morceau est une reprise d'un autre artiste.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-purple-500"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="is_instrumental"
              render={({ field }) => (
                <FormItem className={cn(
                  "flex flex-row items-center justify-between rounded-xl border-2 p-6 shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl",
                  field.value 
                    ? "border-teal-400 bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 shadow-teal-200 dark:shadow-teal-800" 
                    : "border-slate-300 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 shadow-slate-200 dark:shadow-slate-700"
                )}>
                  <div className="space-y-1">
                    <FormLabel className="text-base font-semibold text-slate-900 dark:text-slate-100">Morceau instrumental</FormLabel>
                    <FormDescription className="text-sm text-slate-600 dark:text-slate-400">
                      Indiquer si ce morceau ne contient pas de paroles.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-teal-500"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="is_explicit"
              render={({ field }) => (
                <FormItem className={cn(
                  "flex flex-row items-center justify-between rounded-xl border-2 p-6 shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl",
                  field.value 
                    ? "border-red-400 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 shadow-red-200 dark:shadow-red-800" 
                    : "border-slate-300 bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-800 dark:to-gray-800 shadow-slate-200 dark:shadow-slate-700"
                )}>
                  <div className="space-y-1">
                    <FormLabel className="text-base font-semibold text-slate-900 dark:text-slate-100">Contenu explicite</FormLabel>
                    <FormDescription className="text-sm text-slate-600 dark:text-slate-400">
                      Indiquer si ce morceau contient du contenu explicite.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-red-500"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Section Progression du Morceau */}
      <Card className="bg-gradient-to-br from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 shadow-xl border-0 ring-1 ring-slate-200 dark:ring-slate-700">
        <CardContent className="p-8">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-2 flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-500" />
              Progression du Morceau
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">Suivez l'avancement de chaque section de votre composition</p>
          </div>
          
          {watch && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-base font-medium">Progression Globale</Label>
                <Badge variant="outline" className="text-lg px-3 py-1">
                  {calculateOverallProgress(watch())}%
                </Badge>
              </div>
              <Progress value={calculateOverallProgress(watch())} className="h-3" />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={control}
              name="progression_intro"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="progression_intro" className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-amber-500" />
                      Introduction
                    </Label>
                    <Badge variant={(field.value ?? 0) >= 100 ? "default" : "secondary"}>
                      {field.value || 0}%
                    </Badge>
                  </div>
                  <FormControl>
                    <Slider
                      id="progression_intro"
                      min={0}
                      max={100}
                      step={1}
                      value={[field.value || 0]}
                      onValueChange={(value) => field.onChange(value[0])}
                      className="w-full"
                    />
                  </FormControl>
                  <Progress value={field.value || 0} className="h-2" />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={control}
              name="progression_verse"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="progression_verse" className="flex items-center gap-2">
                      <Music className="h-4 w-4 text-blue-500" />
                      Couplet
                    </Label>
                    <Badge variant={(field.value ?? 0) >= 100 ? "default" : "secondary"}>
                      {field.value || 0}%
                    </Badge>
                  </div>
                  <FormControl>
                    <Slider
                      id="progression_verse"
                      min={0}
                      max={100}
                      step={1}
                      value={[field.value || 0]}
                      onValueChange={(value) => field.onChange(value[0])}
                      className="w-full"
                    />
                  </FormControl>
                  <Progress value={field.value || 0} className="h-2" />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={control}
              name="progression_chorus"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="progression_chorus" className="flex items-center gap-2">
                      <Mic className="h-4 w-4 text-green-500" />
                      Refrain
                    </Label>
                    <Badge variant={(field.value ?? 0) >= 100 ? "default" : "secondary"}>
                      {field.value || 0}%
                    </Badge>
                  </div>
                  <FormControl>
                    <Slider
                      id="progression_chorus"
                      min={0}
                      max={100}
                      step={1}
                      value={[field.value || 0]}
                      onValueChange={(value) => field.onChange(value[0])}
                      className="w-full"
                    />
                  </FormControl>
                  <Progress value={field.value || 0} className="h-2" />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={control}
              name="progression_bridge"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="progression_bridge" className="flex items-center gap-2">
                      <Headphones className="h-4 w-4 text-purple-500" />
                      Pont
                    </Label>
                    <Badge variant={(field.value ?? 0) >= 100 ? "default" : "secondary"}>
                      {field.value || 0}%
                    </Badge>
                  </div>
                  <FormControl>
                    <Slider
                      id="progression_bridge"
                      min={0}
                      max={100}
                      step={1}
                      value={[field.value || 0]}
                      onValueChange={(value) => field.onChange(value[0])}
                      className="w-full"
                    />
                  </FormControl>
                  <Progress value={field.value || 0} className="h-2" />
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={control}
              name="progression_outro"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="progression_outro" className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-orange-500" />
                      Outro
                    </Label>
                    <Badge variant={(field.value ?? 0) >= 100 ? "default" : "secondary"}>
                      {field.value || 0}%
                    </Badge>
                  </div>
                  <FormControl>
                    <Slider
                      id="progression_outro"
                      min={0}
                      max={100}
                      step={1}
                      value={[field.value || 0]}
                      onValueChange={(value) => field.onChange(value[0])}
                      className="w-full"
                    />
                  </FormControl>
                  <Progress value={field.value || 0} className="h-2" />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
