/**
 * Module MIDI pour la lecture d'accords et d'arpèges
 * Supporte la lecture en bloc ou en arpège avec différents patterns
 */

export interface MidiNote {
  note: number; // Note MIDI (0-127)
  velocity: number; // Vélocité (0-127)
  duration: number; // Durée en millisecondes
  delay: number; // D<PERSON><PERSON> avant la note en millisecondes
}

export interface ChordPosition {
  frets: number[];
  fingers: number[];
  barres: Array<{ fret: number; fromString: number; toString: number }>;
  midi: number[];
  difficulty: string;
  baseFret: number;
}

export interface ArpeggioPattern {
  name: string;
  pattern: number[]; // Indices des cordes dans l'ordre de jeu
  timing: number[]; // Timing relatif pour chaque note (en millisecondes)
}

export class MidiChordPlayer {
  private audioContext: AudioContext | null = null;
  private oscillators: OscillatorNode[] = [];
  private gainNodes: GainNode[] = [];

  constructor() {
    if (typeof window !== 'undefined' && 'AudioContext' in window) {
      this.audioContext = new AudioContext();
    }
  }

  /**
   * Patterns d'arpèges prédéfinis
   */
  static readonly ARPEGGIO_PATTERNS: { [key: string]: ArpeggioPattern } = {
    ascending: {
      name: 'Montant',
      pattern: [0, 1, 2, 3, 4, 5],
      timing: [0, 150, 300, 450, 600, 750]
    },
    descending: {
      name: 'Descendant',
      pattern: [5, 4, 3, 2, 1, 0],
      timing: [0, 150, 300, 450, 600, 750]
    },
    alternating: {
      name: 'Alterné',
      pattern: [0, 2, 1, 3, 2, 4, 3, 5],
      timing: [0, 200, 400, 600, 800, 1000, 1200, 1400]
    },
    fingerpicking: {
      name: 'Fingerpicking',
      pattern: [0, 2, 1, 3, 2, 1],
      timing: [0, 300, 600, 900, 1200, 1500]
    },
    travis: {
      name: 'Travis Picking',
      pattern: [0, 2, 1, 2, 0, 3, 1, 3],
      timing: [0, 250, 500, 750, 1000, 1250, 1500, 1750]
    }
  };

  /**
   * Convertit une note MIDI en fréquence
   */
  private midiToFrequency(midiNote: number): number {
    return 440 * Math.pow(2, (midiNote - 69) / 12);
  }

  /**
   * Joue un accord en bloc
   */
  async playChord(
    position: ChordPosition,
    duration: number = 2000,
    velocity: number = 80
  ): Promise<void> {
    if (!this.audioContext) {
      console.warn('AudioContext non disponible');
      return;
    }

    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }

    this.stopAll();

    const now = this.audioContext.currentTime;
    const notes = this.getMidiNotes(position);

    notes.forEach((midiNote, index) => {
      if (midiNote > 0) { // Ignorer les cordes non jouées (-1 ou 0)
        const oscillator = this.audioContext!.createOscillator();
        const gainNode = this.audioContext!.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext!.destination);
        
        oscillator.frequency.setValueAtTime(
          this.midiToFrequency(midiNote),
          now
        );
        
        oscillator.type = 'sawtooth';
        
        // Envelope ADSR simple
        const normalizedVelocity = velocity / 127;
        gainNode.gain.setValueAtTime(0, now);
        gainNode.gain.linearRampToValueAtTime(normalizedVelocity * 0.3, now + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(normalizedVelocity * 0.2, now + 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.001, now + duration / 1000);
        
        oscillator.start(now);
        oscillator.stop(now + duration / 1000);
        
        this.oscillators.push(oscillator);
        this.gainNodes.push(gainNode);
      }
    });
  }

  /**
   * Joue un arpège selon un pattern donné
   */
  async playArpeggio(
    position: ChordPosition,
    patternName: string = 'ascending',
    velocity: number = 80,
    noteDuration: number = 500
  ): Promise<void> {
    if (!this.audioContext) {
      console.warn('AudioContext non disponible');
      return;
    }

    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }

    this.stopAll();

    const pattern = MidiChordPlayer.ARPEGGIO_PATTERNS[patternName];
    if (!pattern) {
      console.warn(`Pattern d'arpège '${patternName}' non trouvé`);
      return;
    }

    const notes = this.getMidiNotes(position);
    const now = this.audioContext.currentTime;

    pattern.pattern.forEach((stringIndex, patternIndex) => {
      if (stringIndex < notes.length && notes[stringIndex] > 0) {
        const delay = pattern.timing[patternIndex] || 0;
        const startTime = now + delay / 1000;
        
        const oscillator = this.audioContext!.createOscillator();
        const gainNode = this.audioContext!.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext!.destination);
        
        oscillator.frequency.setValueAtTime(
          this.midiToFrequency(notes[stringIndex]),
          startTime
        );
        
        oscillator.type = 'sawtooth';
        
        // Envelope pour arpège
        const normalizedVelocity = velocity / 127;
        gainNode.gain.setValueAtTime(0, startTime);
        gainNode.gain.linearRampToValueAtTime(normalizedVelocity * 0.4, startTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + noteDuration / 1000);
        
        oscillator.start(startTime);
        oscillator.stop(startTime + noteDuration / 1000);
        
        this.oscillators.push(oscillator);
        this.gainNodes.push(gainNode);
      }
    });
  }

  /**
   * Extrait les notes MIDI d'une position d'accord
   */
  private getMidiNotes(position: ChordPosition): number[] {
    return position.midi || [];
  }

  /**
   * Arrête toutes les notes en cours
   */
  stopAll(): void {
    this.oscillators.forEach(osc => {
      try {
        osc.stop();
      } catch (e) {
        // L'oscillateur est peut-être déjà arrêté
      }
    });
    
    this.gainNodes.forEach(gain => {
      try {
        gain.disconnect();
      } catch (e) {
        // Le nœud est peut-être déjà déconnecté
      }
    });
    
    this.oscillators = [];
    this.gainNodes = [];
  }

  /**
   * Génère des notes MIDI pour un accord donné
   */
  static generateMidiNotes(
    frets: number[],
    tuning: string[],
    baseFret: number = 1
  ): number[] {
    const noteToMidi: { [key: string]: number } = {
      'C': 60, 'C#': 61, 'Db': 61, 'D': 62, 'D#': 63, 'Eb': 63,
      'E': 64, 'F': 65, 'F#': 66, 'Gb': 66, 'G': 67, 'G#': 68,
      'Ab': 68, 'A': 69, 'A#': 70, 'Bb': 70, 'B': 71
    };

    return frets.map((fret, stringIndex) => {
      if (fret === -1 || fret === 0) {
        // Corde non jouée ou à vide
        if (fret === 0 && tuning[stringIndex]) {
          // Corde à vide
          const baseNote = tuning[stringIndex].replace(/\d+$/, '');
          const octave = parseInt(tuning[stringIndex].match(/\d+$/)?.[0] || '4');
          return (noteToMidi[baseNote] || 60) + (octave - 4) * 12;
        }
        return -1;
      }
      
      // Corde frettée
      const baseNote = tuning[stringIndex].replace(/\d+$/, '');
      const octave = parseInt(tuning[stringIndex].match(/\d+$/)?.[0] || '4');
      const baseMidi = (noteToMidi[baseNote] || 60) + (octave - 4) * 12;
      
      return baseMidi + fret;
    });
  }

  /**
   * Obtient la liste des patterns d'arpèges disponibles
   */
  static getAvailablePatterns(): string[] {
    return Object.keys(MidiChordPlayer.ARPEGGIO_PATTERNS);
  }

  /**
   * Obtient les détails d'un pattern d'arpège
   */
  static getPatternDetails(patternName: string): ArpeggioPattern | null {
    return MidiChordPlayer.ARPEGGIO_PATTERNS[patternName] || null;
  }

  /**
   * Nettoie les ressources
   */
  dispose(): void {
    this.stopAll();
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }
}

// Export des utilitaires
export const ChordMidiUtils = {
  /**
   * Convertit un nom de note en numéro MIDI
   */
  noteNameToMidi(noteName: string, octave: number = 4): number {
    const noteToMidi: { [key: string]: number } = {
      'C': 0, 'C#': 1, 'Db': 1, 'D': 2, 'D#': 3, 'Eb': 3,
      'E': 4, 'F': 5, 'F#': 6, 'Gb': 6, 'G': 7, 'G#': 8,
      'Ab': 8, 'A': 9, 'A#': 10, 'Bb': 10, 'B': 11
    };
    
    return (noteToMidi[noteName] || 0) + (octave + 1) * 12;
  },

  /**
   * Convertit un numéro MIDI en nom de note
   */
  midiToNoteName(midiNote: number): string {
    const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
    const octave = Math.floor(midiNote / 12) - 1;
    const note = noteNames[midiNote % 12];
    return `${note}${octave}`;
  },

  /**
   * Calcule l'intervalle entre deux notes MIDI
   */
  getInterval(note1: number, note2: number): number {
    return Math.abs(note2 - note1);
  },

  /**
   * Transpose une note MIDI
   */
  transpose(midiNote: number, semitones: number): number {
    return Math.max(0, Math.min(127, midiNote + semitones));
  }
};