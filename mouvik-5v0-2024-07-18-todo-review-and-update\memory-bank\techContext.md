# MOUVIK - Contexte Technique (Mise à jour Juin 2024)

## Stack Technique

### Frontend
- **Framework** : Next.js 14+ avec App Router
- **Langage** : TypeScript 5.0+
- **UI/UX** : 
  - React 18+
  - shadcn/ui pour les composants
  - Tailwind CSS 3.4+
- **Gestion d'état** :
  - React Context API
  - React Query pour la gestion des données serveur
- **Audio** :
  - Wavesurfer.js pour la visualisation audio
  - Web Audio API pour le traitement audio

### Backend
- **Plateforme** : Supabase (PostgreSQL 15+)
- **Authentification** : Supabase Auth avec OAuth et magic links
- **Stockage** : Supabase Storage pour les fichiers média
- **Edge Functions** : Pour les traitements côté serveur

## Configuration de Développement

### Prérequis
- Node.js 20+
- pnpm 8+
- Compte Supabase
- Docker (pour le développement local)

### Variables d'Environnement
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY` (côté serveur uniquement)
- `NEXT_PUBLIC_SITE_URL`

## Architecture des Données

### Base de Données
- **SGBD** : PostgreSQL 15+
- **Extensions** :
  - `uuid-ossp` pour les UUID
  - `pg_trgm` pour la recherche plein texte
  - `http` pour les appels HTTP depuis les fonctions

### Tables Principales
- `songs` : Morceaux musicaux (utilise `creator_user_id` comme propriétaire)
- `albums` : Albums (utilise `user_id` comme propriétaire)
- `profiles` : Profils utilisateurs
- `bands` : Groupes musicaux
- `playlists` : Listes de lecture

## Sécurité

### Politiques RLS
- Toutes les tables ont des politiques RLS activées
- Accès en lecture/écriture basé sur les rôles
- Validation des entrées côté serveur

### Bonnes Pratiques
- Validation des schémas avec Zod
- Gestion centralisée des erreurs
- Logging des actions sensibles

## Performance

### Optimisations Frontend
- Code splitting automatique avec Next.js
- Chargement paresseux des composants
- Optimisation des images avec Next/Image

### Optimisations Backend
- Indexation appropriée des tables
- Requêtes optimisées avec Supabase
- Mise en cache avec Redis (via Supabase)

## Dépendances Clés
```json
{
  "dependencies": {
    "next": "^14.1.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "@supabase/supabase-js": "^2.39.0",
    "@supabase/auth-helpers-nextjs": "^0.8.7",
    "zod": "^3.22.0",
    "@tanstack/react-query": "^5.0.0",
    "wavesurfer.js": "^7.0.0",
    "tailwindcss": "^3.4.0",
    "lucide-react": "^0.300.0"
  }
}
```

## Infrastructure
- Déploiement sur Vercel
- Base de données Supabase
- Stockage Supabase
- CDN pour les assets