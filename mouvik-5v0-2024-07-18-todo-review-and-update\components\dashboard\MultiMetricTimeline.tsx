"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  LineChart, Line, BarChart, Bar, ComposedChart, Area,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from "recharts"
import { Headphones, Eye, Heart, MessageCircle, Users, TrendingUp } from "lucide-react"

interface TimelineDataPoint {
  date: string
  plays?: number
  views?: number
  likes?: number
  comments?: number
  followers?: number
}

interface MultiMetricTimelineProps {
  dailyData: TimelineDataPoint[]
  weeklyData: TimelineDataPoint[]
  monthlyData: TimelineDataPoint[]
  title?: string
  description?: string
}

export function MultiMetricTimeline({
  dailyData,
  weeklyData,
  monthlyData,
  title = "Activité",
  description = "Évolution de vos métriques clés"
}: MultiMetricTimelineProps) {
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d">("7d")
  const [chartType, setChartType] = useState<"line" | "bar" | "composed">("line")

  // Sélectionner les données en fonction de la période
  const getDataForTimeRange = () => {
    switch (timeRange) {
      case "7d":
        return dailyData.slice(-7)
      case "30d":
        return dailyData.slice(-30)
      case "90d":
        return weeklyData.slice(-13) // ~90 jours = 13 semaines
      default:
        return dailyData.slice(-7)
    }
  }

  // Formater la date pour l'affichage
  const formatDate = (date: string) => {
    const d = new Date(date)
    if (timeRange === "90d") {
      // Format pour les données hebdomadaires
      return `${d.getDate()}/${d.getMonth() + 1}`
    } else {
      // Format pour les données quotidiennes
      return `${d.getDate()}/${d.getMonth() + 1}`
    }
  }

  // Formater les valeurs pour l'affichage dans le tooltip
  const formatValue = (value: number) => {
    return value.toLocaleString()
  }

  // Couleurs pour les différentes métriques
  const colors = {
    plays: "#4ECDC4", // cyan
    views: "#6A8EAE", // bleu
    likes: "#FF6B6B", // rose
    comments: "#FFD166", // jaune
    followers: "#9C6AFF" // violet
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-md font-medium flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              {title}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className="flex space-x-2">
            <Select
              value={timeRange}
              onValueChange={(value) => setTimeRange(value as "7d" | "30d" | "90d")}
            >
              <SelectTrigger className="w-[120px] h-8 text-xs">
                <SelectValue placeholder="Période" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 jours</SelectItem>
                <SelectItem value="30d">30 jours</SelectItem>
                <SelectItem value="90d">90 jours</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={chartType}
              onValueChange={(value) => setChartType(value as "line" | "bar" | "composed")}
            >
              <SelectTrigger className="w-[120px] h-8 text-xs">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">Lignes</SelectItem>
                <SelectItem value="bar">Barres</SelectItem>
                <SelectItem value="composed">Composé</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === "line" ? (
              <LineChart data={getDataForTimeRange()} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  tickFormatter={formatDate}
                  interval={timeRange === "7d" ? 0 : "preserveEnd"}
                  minTickGap={30}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    formatValue(value as number),
                    name === 'plays' ? 'Écoutes' :
                    name === 'views' ? 'Vues' :
                    name === 'likes' ? 'Likes' :
                    name === 'comments' ? 'Commentaires' :
                    'Abonnés'
                  ]}
                  labelFormatter={(label) => formatDate(label)}
                />
                <Legend />
                <Line type="monotone" dataKey="plays" stroke={colors.plays} name="Écoutes" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 4 }} />
                <Line type="monotone" dataKey="views" stroke={colors.views} name="Vues" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 4 }} />
                <Line type="monotone" dataKey="likes" stroke={colors.likes} name="Likes" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 4 }} />
                <Line type="monotone" dataKey="comments" stroke={colors.comments} name="Commentaires" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 4 }} />
                <Line type="monotone" dataKey="followers" stroke={colors.followers} name="Abonnés" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 4 }} />
              </LineChart>
            ) : chartType === "bar" ? (
              <BarChart data={getDataForTimeRange()} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  tickFormatter={formatDate}
                  interval={timeRange === "7d" ? 0 : "preserveEnd"}
                  minTickGap={30}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    formatValue(value as number),
                    name === 'plays' ? 'Écoutes' :
                    name === 'views' ? 'Vues' :
                    name === 'likes' ? 'Likes' :
                    name === 'comments' ? 'Commentaires' :
                    'Abonnés'
                  ]}
                  labelFormatter={(label) => formatDate(label)}
                />
                <Legend />
                <Bar dataKey="plays" fill={colors.plays} name="Écoutes" radius={[4, 4, 0, 0]} />
                <Bar dataKey="views" fill={colors.views} name="Vues" radius={[4, 4, 0, 0]} />
                <Bar dataKey="likes" fill={colors.likes} name="Likes" radius={[4, 4, 0, 0]} />
                <Bar dataKey="comments" fill={colors.comments} name="Commentaires" radius={[4, 4, 0, 0]} />
                <Bar dataKey="followers" fill={colors.followers} name="Abonnés" radius={[4, 4, 0, 0]} />
              </BarChart>
            ) : (
              <ComposedChart data={getDataForTimeRange()} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  tickFormatter={formatDate}
                  interval={timeRange === "7d" ? 0 : "preserveEnd"}
                  minTickGap={30}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    formatValue(value as number),
                    name === 'plays' ? 'Écoutes' :
                    name === 'views' ? 'Vues' :
                    name === 'likes' ? 'Likes' :
                    name === 'comments' ? 'Commentaires' :
                    'Abonnés'
                  ]}
                  labelFormatter={(label) => formatDate(label)}
                />
                <Legend />
                <Area type="monotone" dataKey="plays" fill={`${colors.plays}40`} stroke={colors.plays} name="Écoutes" />
                <Line type="monotone" dataKey="views" stroke={colors.views} name="Vues" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 4 }} />
                <Bar dataKey="likes" fill={colors.likes} name="Likes" radius={[4, 4, 0, 0]} />
                <Line type="monotone" dataKey="comments" stroke={colors.comments} name="Commentaires" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 4 }} />
                <Line type="monotone" dataKey="followers" stroke={colors.followers} name="Abonnés" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 4 }} />
              </ComposedChart>
            )}
          </ResponsiveContainer>
        </div>
        <div className="flex justify-center mt-4 gap-4 flex-wrap">
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: colors.plays }}></div>
            <span className="text-xs text-muted-foreground">Écoutes</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: colors.views }}></div>
            <span className="text-xs text-muted-foreground">Vues</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: colors.likes }}></div>
            <span className="text-xs text-muted-foreground">Likes</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: colors.comments }}></div>
            <span className="text-xs text-muted-foreground">Commentaires</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: colors.followers }}></div>
            <span className="text-xs text-muted-foreground">Abonnés</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
