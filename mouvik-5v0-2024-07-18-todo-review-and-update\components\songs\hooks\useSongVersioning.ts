import { useState, useEffect, useCallback, useRef } from 'react';
import { SupabaseClient, User } from '@supabase/supabase-js';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';
import { UseFormSetValue, UseFormReset, UseFormGetValues } from 'react-hook-form';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import { SongFormValues } from '../song-schema';
import { Song as SongData } from '@/types';


// Represents a version as stored and manipulated locally in the form/hook
export interface LocalSongVersion extends SongFormValues {
  id: string; 
  version_id: string; 
  version_name: string;
  notes?: string; 
  created_at: string;
  version_number: number;
  is_major_version?: boolean; // Optional: if you track this
}

// Represents a version structure as fetched from or saved to the database
export interface SongVersion { 
  id: string;
  song_id: string;
  version_name: string;
  version_number: number;
  song_data: Partial<SongFormValues>; 
  created_at: string;
  is_major_version?: boolean;
  version_notes?: string;
  user_id?: string; 
  parent_version_id?: string;
}

interface UseSongVersioningProps {
  songId?: string;
  initialVersionId?: string | null;
  dbSongData?: SongData | null;
  setValue: UseFormSetValue<SongFormValues>;
  reset: UseFormReset<SongFormValues>;
  getValues: UseFormGetValues<SongFormValues>;
  supabaseClient: SupabaseClient; // Changed from supabase
  user: User | null;
  router: AppRouterInstance;
  setCurrentTab: (tab: any) => void;
  onVersionLoad?: (versionData: Partial<SongFormValues>, versionId: string) => void;
  toast: typeof toast;
}

interface UseSongVersioningReturn {
  currentSongVersions: LocalSongVersion[];
  setCurrentSongVersions: React.Dispatch<React.SetStateAction<LocalSongVersion[]>>;
  newVersionName: string;
  setNewVersionName: React.Dispatch<React.SetStateAction<string>>;
  newVersionNotes: string;
  setNewVersionNotes: React.Dispatch<React.SetStateAction<string>>;
  isSaveVersionModalOpen: boolean;
  setIsSaveVersionModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isConfirmDeleteVersionModalOpen: boolean;
  setIsConfirmDeleteVersionModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  versionToDelete: LocalSongVersion | null;
  setVersionToDelete: React.Dispatch<React.SetStateAction<LocalSongVersion | null>>;
  isSubmittingVersion: boolean;
  isLoadingDeleteVersion: boolean;
  isLoadingUpdateVersion: boolean;
  songVaultActionsRef: React.RefObject<any>; // Was SongVaultHandle, but not used imperatively
  handleSaveNewVersion: () => Promise<void>;
  handleLoadVersion: (versionId: string, versionData?: SongVersion) => Promise<void>;
  handleDeleteVersion: (versionId?: string) => Promise<void>; // Allow passing versionId directly
  handleUpdateVersionDetails: (versionId: string, newName: string, newNotes: string) => Promise<void>;
  activeVersionId: string | null;
}

export const useSongVersioning = (props: UseSongVersioningProps): UseSongVersioningReturn => {
  const {
    songId,
    initialVersionId,
    dbSongData,
    setValue, // RHF setValue
    reset,    // RHF reset
    getValues, // RHF getValues
    supabaseClient, // Changed from supabase
    user,
    router,
    setCurrentTab, // To switch tab after loading a version for example
    onVersionLoad, // Callback when a version is loaded
    toast,         // Destructure toast
  } = props;

  const [currentSongVersions, setCurrentSongVersions] = useState<LocalSongVersion[]>([]);
  const [newVersionName, setNewVersionName] = useState('');
  const [newVersionNotes, setNewVersionNotes] = useState('');
  const [isSaveVersionModalOpen, setIsSaveVersionModalOpen] = useState(false);
  const [isConfirmDeleteVersionModalOpen, setIsConfirmDeleteVersionModalOpen] = useState(false);
  const [versionToDelete, setVersionToDelete] = useState<LocalSongVersion | null>(null);
  const [isSubmittingVersion, setIsSubmittingVersion] = useState(false);
  const [isLoadingDeleteVersion, setIsLoadingDeleteVersion] = useState(false);
  const [isLoadingUpdateVersion, setIsLoadingUpdateVersion] = useState(false);
  const songVaultActionsRef = useRef<any>(null); // Was SongVaultHandle, but not used imperatively
  const [activeVersionId, setActiveVersionId] = useState<string | null>(initialVersionId || null);
  const [loadingVersionId, setLoadingVersionId] = useState<string | null>(null); // Track which version is currently being loaded

  // Effect to initialize versions from dbSongData or fetch
  useEffect(() => {
    if (dbSongData?.song_versions && Array.isArray(dbSongData.song_versions)) {
      const transformedVersions = dbSongData.song_versions.map((v: any, index: number) => ({
        ...v, // Spread existing version data
        id: v.id || v.version_id || uuidv4(), // Ensure an ID exists
        version_id: v.id || v.version_id || uuidv4(), // Legacy support
        version_name: v.version_name || v.name || `Version ${index + 1}`,
        created_at: v.created_at || new Date().toISOString(),
        version_number: v.version_number || index + 1,
        // Ensure form data is present, even if partial
        ...(v.song_data || {}),
      }));
      setCurrentSongVersions(transformedVersions);
      if (initialVersionId && transformedVersions.some(v => v.id === initialVersionId)) {
        setActiveVersionId(initialVersionId);
      } else if (transformedVersions.length > 0) {
        // setActiveVersionId(transformedVersions[0].id); // Optionally load the first/latest by default
      }
    } else if (songId && supabaseClient && user) { // Changed from supabase
      // Fetch versions if not provided in dbSongData (e.g., on direct edit page load without full initial data)
      const fetchVersions = async () => {
        const { data, error } = await supabaseClient // Changed from supabase
          .from('song_versions')
          .select('*')
          .eq('song_id', songId)
          .order('created_at', { ascending: false });
        if (error) {
          console.error('Error fetching song versions:', error);
          toast.error('Erreur de chargement des versions.');
        } else if (data) {
          const transformedData = data.map((v: any, index: number) => ({
            ...v.song_data, // Spread form data from version
            id: v.id || v.version_id, // Use DB id as primary
            version_id: v.id || v.version_id,
            version_name: v.version_name || `Version ${index + 1}`,
            created_at: v.created_at,
            version_number: v.version_number || index + 1,
          }));
          setCurrentSongVersions(transformedData);
          if (initialVersionId && transformedData.some(v => v.id === initialVersionId)) {
            setActiveVersionId(initialVersionId);
          } else if (transformedData.length > 0) {
            // setActiveVersionId(transformedData[0].id);
          }
        }
      };
      fetchVersions();
    }
  }, [dbSongData, songId, initialVersionId, supabaseClient, user]); // Changed from supabase

  const handleSaveNewVersion = useCallback(async () => {
    if (!supabaseClient || !user || !songId) { // Changed from supabase
      toast.error("Erreur", { description: "Impossible de sauvegarder la version : utilisateur ou chanson non défini." });
      return;
    }
    setIsSubmittingVersion(true);
    const currentFormData = getValues();
    const versionNumber = currentSongVersions.length + 1;
    const versionName = newVersionName.trim() || `Version ${versionNumber}`;

    const newVersionData: Omit<SongVersion, 'id' | 'created_at' | 'user_id' | 'song_id'> & { song_data: SongFormValues } = {
        version_name: versionName,
        version_notes: newVersionNotes.trim(),
        song_data: currentFormData,
        version_number: versionNumber,
        parent_version_id: currentSongVersions[0]?.id ?? null,
        is_major_version: false // Default to false for now
    };

    try {
      const { data: savedVersion, error } = await supabaseClient.rpc('rpc_save_song_version', { // Changed from supabase{
        p_song_id: songId,
        p_version_name: newVersionData.version_name,
        p_song_data_snapshot: newVersionData.song_data,
        p_user_notes: newVersionData.version_notes,
        p_creator_user_id: user.id,
        p_parent_version_id: newVersionData.parent_version_id ?? null,
        p_is_major_version: newVersionData.is_major_version ?? false,
      });

      if (error) throw error;

      const versionDataFromDb = Array.isArray(savedVersion) ? savedVersion[0] : savedVersion;

      if (!versionDataFromDb || !versionDataFromDb.id) {
        throw new Error("La sauvegarde de la version n'a pas retourné de données valides ou d'ID.");
      }

      const newLocalVersion: LocalSongVersion = {
        ...currentFormData, // Spread current form values (song content)
        id: versionDataFromDb.id, // Use the ID from the database response
        version_id: versionDataFromDb.id, // Assuming version_id is the PK
        version_name: versionDataFromDb.version_name, // Use what was actually saved
        notes: versionDataFromDb.user_notes, // Map to user_notes from DB
        created_at: versionDataFromDb.created_at, // Use DB timestamp
        version_number: versionDataFromDb.version_number, // Use DB calculated/stored version number
        is_major_version: versionDataFromDb.is_major_version, // Use DB value
      };

      setCurrentSongVersions(prev => [newLocalVersion, ...prev]);
      setActiveVersionId(newLocalVersion.id ?? null); // Ensure id is string or null
      toast.success("Version Sauvegardée", { description: `La version "${versionName}" a été sauvegardée avec succès.` });
      setIsSaveVersionModalOpen(false);
      setNewVersionName('');
      setNewVersionNotes('');
    } catch (error: any) {
      console.error("[useSongVersioning] Error saving new version:", error);
      toast.error("Échec de la Sauvegarde", { description: `Impossible de sauvegarder la version : ${error.message || 'Erreur inconnue'}` });
    } finally {
      setIsSubmittingVersion(false);
    }
  }, [supabaseClient, user, songId, getValues, currentSongVersions.length, newVersionName, newVersionNotes, setCurrentSongVersions, setIsSaveVersionModalOpen, setNewVersionName, setNewVersionNotes, songVaultActionsRef, setActiveVersionId]);

  const handleUpdateVersionDetails = useCallback(async (versionId: string, newName: string, newNotes: string) => {
    if (!user) {
      toast.error("Utilisateur non authentifié.");
      return;
    }
    if (!songId) {
      toast.error("ID du morceau manquant.");
      return;
    }

    setIsLoadingUpdateVersion(true);
    try {
      const { data, error } = await supabaseClient
        .from('song_versions')
        .update({
          version_name: newName,
          user_notes: newNotes, // Assuming user_notes is the correct column for version notes
        })
        .eq('id', versionId)
        .eq('creator_user_id', user.id) // Ensure user owns the version
        .select()
        .single();

      if (error) throw error;

      if (data) {
        setCurrentSongVersions(prevVersions => 
          prevVersions.map(v => v.version_id === versionId ? { ...v, version_name: data.version_name, notes: data.user_notes } : v)
        );
        toast.success(`Version "${newName}" mise à jour avec succès.`);
      }
    } catch (error: any) {
      console.error("[useSongVersioning] Error updating version details:", error);
      toast.error(`Erreur lors de la mise à jour de la version: ${error.message}`);
    } finally {
      setIsLoadingUpdateVersion(false);
    }
  }, [supabaseClient, user, songId, setCurrentSongVersions, toast]);

  const handleLoadVersion = useCallback(async (versionIdToLoad: string, versionDetails?: SongVersion) => {
    if (!supabaseClient || !user || !songId) { 
      toast.error("Erreur", { description: "Impossible de charger la version : utilisateur non défini." });
      return;
    }
    setLoadingVersionId(versionIdToLoad);
    console.log(`[useSongVersioning] Attempting to load version ID: ${versionIdToLoad}`);

    try {
      let versionDataToLoad: SongFormValues | undefined | null = versionDetails?.song_data as SongFormValues;

      if (!versionDataToLoad) {
        const { data, error } = await supabaseClient
          .from('song_versions')
          .select('song_data, version_name, version_number, created_at, creator_user_id') 
          .eq('id', versionIdToLoad)
          .eq('creator_user_id', user.id) 
          .single();

        if (error) throw error;
        if (!data) throw new Error("Version non trouvée ou accès refusé.");
        
        versionDataToLoad = data.song_data as SongFormValues;
        versionDetails = versionDetails || { 
            id: versionIdToLoad, 
            version_name: data.version_name, 
            version_number: data.version_number, 
            created_at: data.created_at 
            // song_data will be handled by versionDataToLoad
        } as SongVersion;
      }

      if (!versionDataToLoad) {
        throw new Error("Les données du formulaire pour cette version sont vides.");
      }

      // Call onVersionLoad BEFORE reset to allow SongForm to set its flag
      if (onVersionLoad) {
        onVersionLoad(versionDataToLoad, versionIdToLoad);
      }

      reset(versionDataToLoad); 
      setActiveVersionId(versionIdToLoad || null);

      if (versionDetails) {
        toast.success(`Version chargée`, { description: `La version ${versionDetails.version_number} (${versionDetails.version_name || 'Sans nom'}) est maintenant active.` });
      } else {
        toast.success("Version chargée", { description: "La version est maintenant active."} );
      }
      setCurrentTab('general'); 

    } catch (err: any) {
      console.error("[useSongVersioning] Error loading song version data:", err);
      setActiveVersionId(null); 
      toast.error("Échec du chargement", { description: `Impossible de charger la version : ${err.message || 'Erreur inconnue'}` });
    } finally {
      setLoadingVersionId(null);
    }
  }, [supabaseClient, user, songId, reset, onVersionLoad, toast, setCurrentTab]); 

  const handleDeleteVersion = useCallback(async (versionIdToDelete?: string) => {
    const idToDelete = versionIdToDelete || versionToDelete?.id;
    if (!idToDelete || !supabaseClient || !user || !songId) { 
      toast.error("Erreur de suppression", { description: "Impossible de supprimer la version : données manquantes." });
      return;
    }
    setIsLoadingDeleteVersion(true);
    try {
      const { error } = await supabaseClient.rpc('rpc_delete_song_version', {
        p_version_id: idToDelete,
        p_requesting_user_id: user.id
      });

      if (error) throw error;

      const deletedVersionInfo = currentSongVersions.find(v => v.id === idToDelete);
      setCurrentSongVersions((prev) => prev.filter((v) => v.id !== idToDelete));
      toast.success("Version Supprimée", { description: `La version ${deletedVersionInfo?.version_number || ''} ${deletedVersionInfo?.version_name ? '('+deletedVersionInfo.version_name+')' : ''} a été supprimée.` });
      setIsConfirmDeleteVersionModalOpen(false);
      if (activeVersionId === idToDelete) {
        setActiveVersionId(null);
        reset(dbSongData ? dbSongData.song_data : undefined); 
      }
      setVersionToDelete(null);
    } catch (error: any) {
      console.error("[useSongVersioning] Error deleting version:", error);
      toast.error("Erreur de Suppression", { description: error.message || "Impossible de supprimer la version." });
    } finally {
      setIsLoadingDeleteVersion(false);
    }
  }, [versionToDelete, supabaseClient, user, songId, currentSongVersions, toast, setCurrentSongVersions]); 

  return {
    currentSongVersions,
    setCurrentSongVersions,
    newVersionName,
    setNewVersionName,
    newVersionNotes,
    setNewVersionNotes,
    isSaveVersionModalOpen,
    setIsSaveVersionModalOpen,
    isConfirmDeleteVersionModalOpen,
    setIsConfirmDeleteVersionModalOpen,
    versionToDelete,
    setVersionToDelete,
    isSubmittingVersion,
    isLoadingDeleteVersion,
    isLoadingUpdateVersion,
    songVaultActionsRef,
    handleSaveNewVersion,
    handleLoadVersion,
    handleDeleteVersion,
    handleUpdateVersionDetails,
    activeVersionId,
  };
};