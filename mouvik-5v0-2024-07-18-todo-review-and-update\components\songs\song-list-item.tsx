"use client";

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, Edit3, Trash2, PlayCircle, Music2, UserCircle, Disc, ListPlus, Heart, Copy, Loader2, Eye 
} from 'lucide-react';
import type { Song, UserProfile, Album } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useUser } from '@/contexts/user-context';
import { getSupabaseClient } from '@/lib/supabase/client';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

interface SongForListItem extends Song {
  profiles?: UserProfile | null;
  albums?: Pick<Album, 'id' | 'title' | 'cover_url'> | null;
}

interface SongListItemProps {
  song: SongForListItem;
  onDelete: (songId: string) => void; 
  onUpdateStatus?: (songId: string, newStatus: { is_public: boolean; slug: string | null }) => void;
  density?: number; // 0: compact, 1: default, 2: spacious
  // onPlay, onAddToQueue, onAddToPlaylist, onLike etc.
}

export function SongListItem({ song, onDelete, onUpdateStatus, density = 1 }: SongListItemProps) {
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);

  const viewPageUrl = song.is_public && song.slug ? `/song/${song.slug}` : `/songs/${song.id}`;
  const artistDisplay = song.profiles?.display_name || song.profiles?.username || song.artist_name || "Artiste inconnu";

  const handleDelete = () => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le morceau "${song.title}" ?`)) {
      onDelete(song.id);
    }
  };

  const handlePlay = () => toast({ title: "Lecture (Placeholder)"});
  const handleAddToQueue = () => toast({ title: "Ajout à la file (Placeholder)"});
  const handleLike = () => toast({ title: "Like (Placeholder)"});
  const handleAddToPlaylist = () => toast({ title: "Ajout à une playlist (Placeholder)"});
  const handleDuplicate = () => toast({ title: "Duplication (Placeholder)"});

  const togglePublicStatus = async (e: React.MouseEvent) => {
    e.preventDefault(); e.stopPropagation();
    if (!user || song.user_id !== user.id || isTogglingStatus || song.is_public === undefined) return;
    setIsTogglingStatus(true);
    try {
      const { data, error } = await supabase.rpc('toggle_song_public_status', { p_song_id: song.id, p_user_id: user.id });
      if (error) throw error;
      if (data && data.length > 0) {
        const { new_is_public, new_slug } = data[0];
        toast({ title: "Statut du morceau mis à jour", description: `"${song.title}" est ${new_is_public ? 'public' : 'privé'}.`});
        if (onUpdateStatus) onUpdateStatus(song.id, { is_public: new_is_public, slug: new_slug });
      } else { throw new Error("Réponse RPC invalide."); }
    } catch (err: any) { toast({ title: "Erreur", description: err.message || "Impossible de changer le statut.", variant: "destructive" });
    } finally { setIsTogglingStatus(false); }
  };
  
  const paddingClasses = { 0: 'p-2', 1: 'p-3', 2: 'p-4' };
  const imageSizeClasses = { 0: 'w-10 h-10', 1: 'w-12 h-12', 2: 'w-14 h-14' };
  const titleSizeClasses = { 0: 'text-xs', 1: 'text-sm', 2: 'text-base' };
  const textSizeClasses = { 0: 'text-xs', 1: 'text-xs', 2: 'text-sm' };


  return (
    <div className={cn("flex items-center gap-3 hover:bg-muted/50 rounded-md border", paddingClasses[density as keyof typeof paddingClasses] || 'p-3')}>
      <div className={cn("flex-shrink-0 relative", imageSizeClasses[density as keyof typeof imageSizeClasses] || 'w-12 h-12')}>
        {song.is_public !== undefined && (
            <div 
                title={song.is_public ? "Public (cliquer pour changer)" : "Privé (cliquer pour changer)"}
                onClick={togglePublicStatus}
                className={cn(
                    "absolute top-0.5 left-0.5 z-10 w-3 h-3 rounded-full border-2 border-white shadow-lg cursor-pointer flex items-center justify-center",
                    song.is_public ? "bg-green-500 hover:bg-green-600" : "bg-red-500 hover:bg-red-600"
                )}
            >
                {isTogglingStatus && <Loader2 className="h-1.5 w-1.5 animate-spin text-white" />}
            </div>
        )}
        <Link href={viewPageUrl}>
          {song.cover_art_url ? (
            <Image src={song.cover_art_url} alt={song.title} layout="fill" className="object-cover rounded-md"/>
          ) : song.albums?.cover_url ? ( // Fallback to album cover
            <Image src={song.albums.cover_url} alt={song.title} layout="fill" className="object-cover rounded-md"/>
          ) : (
            <div className="w-full h-full bg-muted rounded-md flex items-center justify-center">
              <Music2 className={cn(density === 0 ? "w-5 h-5" : "w-6 h-6", "text-muted-foreground")} />
            </div>
          )}
        </Link>
      </div>

      <div className="flex-1 min-w-0"> 
        <Link href={viewPageUrl} className="hover:underline">
          <p className={cn("font-medium truncate", titleSizeClasses[density as keyof typeof titleSizeClasses] || 'text-sm')} title={song.title}>{song.title}</p>
        </Link>
        <div className={cn("text-muted-foreground flex items-center gap-1.5 truncate", textSizeClasses[density as keyof typeof textSizeClasses] || 'text-xs')}>
          <Link href={`/artists/${song.profiles?.username || song.user_id}`} className="hover:underline truncate" title={artistDisplay}>
            {artistDisplay}
          </Link>
          {song.albums && (
            <>
              <span>•</span>
              <Link href={`/albums/${song.albums.id}`} className="hover:underline truncate" title={song.albums.title}>
                {song.albums.title}
              </Link>
            </>
          )}
        </div>
      </div>

      {density > 0 && song.duration_ms !== undefined && song.duration_ms !== null && (
        <div className={cn("hidden sm:block text-muted-foreground flex-shrink-0", textSizeClasses[density as keyof typeof textSizeClasses] || 'text-xs')}>
          {formatDuration(song.duration_ms)}
        </div>
      )}
      {density > 0 && (song.duration_ms === undefined || song.duration_ms === null) && (
        <div className={cn("hidden sm:block text-muted-foreground flex-shrink-0", textSizeClasses[density as keyof typeof textSizeClasses] || 'text-xs')}>
          -
        </div>
      )}
      
      {density > 1 && song.created_at && ( // Show created_at if spacious
        <div className={cn("hidden md:block text-muted-foreground flex-shrink-0", textSizeClasses[density as keyof typeof textSizeClasses] || 'text-xs')} title={`Créé le ${new Date(song.created_at).toLocaleDateString()}`}>
          {formatDistanceToNow(new Date(song.created_at), { locale: fr, addSuffix: true })}
        </div>
      )}
      
      <div className="flex items-center gap-0.5 flex-shrink-0">
        <Button onClick={handlePlay} size="icon" variant="ghost" className={cn("h-7 w-7", density === 0 && "h-6 w-6")} title="Écouter"><PlayCircle className={cn(density === 0 ? "h-3.5 w-3.5" : "h-4 w-4")} /></Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className={cn("h-7 w-7", density === 0 && "h-6 w-6")}><MoreHorizontal className={cn(density === 0 ? "h-3.5 w-3.5" : "h-4 w-4")} /><span className="sr-only">Options</span></Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleAddToQueue}><ListPlus className="mr-2 h-4 w-4" /> Ajouter à la file</DropdownMenuItem>
            <DropdownMenuItem onClick={handleAddToPlaylist}><ListPlus className="mr-2 h-4 w-4" /> Ajouter à une playlist</DropdownMenuItem>
            <DropdownMenuItem onClick={handleLike}><Heart className="mr-2 h-4 w-4" /> Aimer</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => router.push(`/manage-songs/${song.id}/edit`)}><Edit3 className="mr-2 h-4 w-4" /> Modifier</DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}><Copy className="mr-2 h-4 w-4" /> Dupliquer</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10"><Trash2 className="mr-2 h-4 w-4" /> Supprimer</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
