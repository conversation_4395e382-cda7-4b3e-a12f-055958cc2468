/**
 * 🎼 ENHANCED LYRICS EDITOR - Module d'Éditeur Amélioré
 * 
 * Point d'entrée pour le système d'édition paroles + accords intégré
 * Remplacement moderne de LyricsEditorWithAI
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

// Composants principaux
export { EnhancedLyricsEditor } from './EnhancedLyricsEditor';
export { AiChordSuggestions } from './AiChordSuggestions';
export { LyricsChordWorkflow } from './LyricsChordWorkflow';

// Types
export type { 
  ChordPlacement, 
  DisplayMode, 
  EnhancedLyricsEditorProps 
} from './EnhancedLyricsEditor';

export type { 
  ChordSuggestion, 
  SuggestionContext, 
  AiChordSuggestionsProps 
} from './AiChordSuggestions';

export type { 
  AiHistoryItem, 
  AiConfig, 
  SongFormValues, 
  LyricsChordWorkflowProps 
} from './LyricsChordWorkflow';

// Interface simplifiée pour l'utilisation
export const EnhancedLyricsEditorModule = {
  // Composant principal pour remplacer LyricsEditorWithAI
  Workflow: LyricsChordWorkflow,
  
  // Composants individuels pour usage avancé
  Editor: EnhancedLyricsEditor,
  AiSuggestions: AiChordSuggestions,
  
  // Utilitaires
  createChordPlacement: (
    chord: any, 
    position: number, 
    metadata?: any
  ) => ({
    id: crypto.randomUUID(),
    chord,
    textPosition: position,
    lineNumber: 0,
    wordIndex: 0,
    timestamp: new Date().toISOString(),
    metadata: metadata || { emphasis: 'medium' }
  }),
  
  // Helpers pour l'intégration
  parseChordSuggestions: (aiResponse: string) => {
    // TODO: Parser la réponse IA pour extraire les accords
    return [];
  },
  
  formatChordProgression: (chords: any[]) => {
    return chords.map(c => c.chord.chord).join(' - ');
  }
};
