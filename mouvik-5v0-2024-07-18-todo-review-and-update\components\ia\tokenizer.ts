// Utilitaire simple pour compter les tokens (approximation, compatible GPT/LLM)
// Pour une vraie précision, installer tik<PERSON><PERSON> ou gpt-3-encoder c<PERSON><PERSON> serveur

// Approche simple : 1 token ≈ 4 caractères (anglais/fr, sans emoji)
export function countTokens(str: string): number {
  if (!str) return 0;
  // Enlève les espaces multiples, normalise les retours à la ligne
  const clean = str.replace(/\s+/g, ' ').trim();
  return Math.ceil(clean.length / 4);
}

// Fonction pour tronquer un texte à un nombre max de tokens
export function truncateToTokens(str: string, maxTokens: number): string {
  if (!str) return '';
  let count = 0;
  let result = '';
  for (const word of str.split(' ')) {
    count += Math.ceil(word.length / 4);
    if (count > maxTokens) break;
    result += word + ' ';
  }
  return result.trim();
}
