import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ClientLayout } from "@/components/client-layout"
import { createSupabaseServerClient } from "@/lib/supabase/server" // Added
import type { UserProfileForSidebar } from "@/components/sidebar"; // Added
// import dynamic from 'next/dynamic'; // Added for dynamic import

// const StagewiseToolbar = dynamic(() => 
//   import('@stagewise/toolbar-next').then(mod => mod.StagewiseToolbar),
//   { ssr: false }
// ); // Added for Stagewise toolbar

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "MOUVIK - Plateforme de composition musicale assistée par IA",
  description: "C<PERSON>ez, partagez et découvrez de la musique avec l'aide de l'IA",
  generator: 'v0.dev',
  icons: {
    icon: '/Mlogo.png',
  }
}

import { cookies as nextCookies } from 'next/headers'; // Import cookies for logging

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const cookieStore = nextCookies();
  console.log("RootLayout: All cookies from next/headers cookieStore:", JSON.stringify(Array.from(cookieStore.getAll())));

  const supabase = createSupabaseServerClient();
  const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

  if (authError) {
    console.error("RootLayout auth error:", authError.message);
  } else if (!authUser) {
    console.warn("RootLayout: No authenticated user found (authUser is null/undefined), but no specific authError object was returned.");
  } else {
    console.log("RootLayout: Authenticated user found, ID:", authUser.id);
  }

  let userObj: UserProfileForSidebar | null = null;



  if (authUser) {
    const profileFieldsToSelect = [
      "id", "display_name", "username", "avatar_url", 
      "role_primary", "subscription_tier", "user_role",
      "custom_uploads_per_month", "custom_vault_space_gb", "custom_vault_max_files",
      "custom_ia_credits_month", "custom_coins_month",
      "custom_max_playlists", "custom_max_friends",
      "ia_credits", "coins_balance"
    ].join(", ");

    // Intermediate type for DB profile data
    interface DbProfileData extends Omit<UserProfileForSidebar, 'name' | 'email' | 'id'> { // Omit fields that will be sourced/overridden from authUser or mapped
      display_name: string | null; // DB has display_name
      // id is already in UserProfileForSidebar, but ensure it's from authUser
    }

    const { data: dbProfile, error: profileError } = await supabase
      .from("profiles")
      .select(profileFieldsToSelect) // profileFieldsToSelect includes display_name
      .eq("id", authUser.id)
      .single<DbProfileData>(); // Use DbProfileData for fetched data

    if (profileError) {
      console.error("RootLayout profile fetch error:", profileError.message);
      // Build a minimal userObj if profile fetch fails but authUser exists
      userObj = {
        id: authUser.id,
        email: authUser.email, // email is on authUser, not UserProfileForSidebar directly
        name: authUser.email?.split('@')[0], // Fallback name
        // Fill other fields with defaults or null
        avatar_url: null, role_primary: null, subscription_tier: 'free', user_role: 'user',
        custom_uploads_per_month: null, custom_vault_space_gb: null, custom_vault_max_files: null,
        custom_ia_credits_month: null, custom_coins_month: null, custom_max_playlists: null,
        custom_max_friends: null, ia_credits: null, coins_balance: null, username: null, // Ensure all UserProfileForSidebar fields are present
      };
    } else if (dbProfile) {
      userObj = {
        ...dbProfile, // Spread fetched profile data
        id: authUser.id, 
        email: authUser.email, 
        name: dbProfile.display_name || dbProfile.username || authUser.email?.split('@')[0], 
        // Ensure all other fields from UserProfileForSidebar are covered if not in DbProfileData spread
        // For example, if UserProfileForSidebar has fields not in profiles table, they'd be undefined here.
        // The current UserProfileForSidebar seems to mostly map to profiles columns.
      };
    }
  }

  const stagewiseConfig = {
    plugins: []
  };

  return (
    <html lang="fr" suppressHydrationWarning>
      <body className={inter.className}>
        <ClientLayout initialUser={userObj}>
          {children}
          {/* {process.env.NODE_ENV === 'development' && (
            <StagewiseToolbar config={stagewiseConfig} />
          )} */}
        </ClientLayout>
      </body>
    </html>
  )
}
