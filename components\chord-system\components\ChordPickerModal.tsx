/**
 * 🎼 CHORD PICKER MODAL - Sélection Rapide d'Accords
 * 
 * Modal optimisée pour la sélection rapide d'accords
 * Interface intuitive avec recherche et favoris
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { X, Search, Star, StarOff, Music, Filter, Grid, List } from 'lucide-react';
import { useChordSystem } from '../providers/ChordSystemProvider';
import { useChordLibrary } from '../hooks/useChordLibrary';
import { ChordDiagramViewer } from './ChordDiagramViewer';
import type { 
  UnifiedChordPosition, 
  ChordSearchFilters,
  InstrumentType,
  DifficultyLevel 
} from '../types/chord-system';

// ============================================================================
// TYPES ET INTERFACES
// ============================================================================

interface ChordPickerModalProps {
  /** Modal ouverte ou fermée */
  isOpen: boolean;
  /** Callback pour fermer la modal */
  onClose: () => void;
  /** Callback lors de la sélection d'un accord */
  onChordSelect: (chord: UnifiedChordPosition) => void;
  /** Accords favoris (IDs) */
  favoriteChords?: string[];
  /** Callback pour gérer les favoris */
  onToggleFavorite?: (chordId: string) => void;
  /** Filtres initiaux */
  initialFilters?: Partial<ChordSearchFilters>;
  /** Mode de sélection multiple */
  multiSelect?: boolean;
  /** Accords déjà sélectionnés */
  selectedChords?: UnifiedChordPosition[];
  /** Titre personnalisé */
  title?: string;
}

interface QuickFilterProps {
  label: string;
  value: string;
  isActive: boolean;
  onClick: (value: string) => void;
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Filtre rapide cliquable
 */
const QuickFilter: React.FC<QuickFilterProps> = ({ label, value, isActive, onClick }) => (
  <button
    onClick={() => onClick(value)}
    className={`
      px-3 py-1 text-sm rounded-full border transition-colors
      ${isActive 
        ? 'bg-blue-600 text-white border-blue-600' 
        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
      }
    `}
  >
    {label}
  </button>
);

/**
 * Carte d'accord compacte pour la modal
 */
const CompactChordCard: React.FC<{
  chord: UnifiedChordPosition;
  isSelected: boolean;
  isFavorite: boolean;
  onSelect: (chord: UnifiedChordPosition) => void;
  onToggleFavorite?: (chordId: string) => void;
  showFavorites: boolean;
}> = ({ chord, isSelected, isFavorite, onSelect, onToggleFavorite, showFavorites }) => {
  return (
    <div 
      className={`
        relative p-3 border rounded-lg cursor-pointer transition-all
        ${isSelected 
          ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' 
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
      `}
      onClick={() => onSelect(chord)}
    >
      {/* Bouton favori */}
      {showFavorites && onToggleFavorite && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite(chord.id);
          }}
          className={`
            absolute top-2 right-2 p-1 rounded-full transition-colors
            ${isFavorite 
              ? 'text-yellow-500 hover:text-yellow-600' 
              : 'text-gray-400 hover:text-yellow-500'
            }
          `}
        >
          {isFavorite ? <Star className="w-4 h-4 fill-current" /> : <StarOff className="w-4 h-4" />}
        </button>
      )}

      {/* Diagramme miniature */}
      <div className="w-full h-20 mb-2">
        <ChordDiagramViewer 
          chord={chord} 
          size="small" 
          interactive={false}
          showLabels={false}
          showDetails={false}
        />
      </div>

      {/* Nom de l'accord */}
      <h4 className="text-sm font-semibold text-gray-900 text-center truncate">
        {chord.chord}
      </h4>

      {/* Badge de difficulté */}
      <div className="flex justify-center mt-1">
        <span className={`
          px-2 py-0.5 text-xs rounded-full
          ${chord.difficulty === 'beginner' ? 'bg-green-100 text-green-700' :
            chord.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-700' :
            'bg-red-100 text-red-700'
          }
        `}>
          {chord.difficulty === 'beginner' ? 'Déb.' :
           chord.difficulty === 'intermediate' ? 'Int.' : 'Av.'}
        </span>
      </div>
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const ChordPickerModal: React.FC<ChordPickerModalProps> = ({
  isOpen,
  onClose,
  onChordSelect,
  favoriteChords = [],
  onToggleFavorite,
  initialFilters = {},
  multiSelect = false,
  selectedChords = [],
  title = "Sélectionner un accord"
}) => {
  const { state, actions } = useChordSystem();
  const { filteredChords, loading, searchChords } = useChordLibrary();
  
  // État local
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState<ChordSearchFilters>({
    ...state.searchFilters,
    ...initialFilters
  });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showOnlyFavorites, setShowOnlyFavorites] = useState(false);
  const [localSelectedChords, setLocalSelectedChords] = useState<UnifiedChordPosition[]>(selectedChords);

  // ============================================================================
  // EFFETS
  // ============================================================================

  // Synchroniser les filtres avec la recherche
  useEffect(() => {
    const filters = {
      ...activeFilters,
      searchTerm: searchTerm.trim()
    };
    
    searchChords(filters);
  }, [searchTerm, activeFilters, searchChords]);

  // Reset lors de l'ouverture
  useEffect(() => {
    if (isOpen) {
      setSearchTerm('');
      setLocalSelectedChords(selectedChords);
      setActiveFilters({
        ...state.searchFilters,
        ...initialFilters
      });
    }
  }, [isOpen, selectedChords, state.searchFilters, initialFilters]);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleChordSelect = useCallback((chord: UnifiedChordPosition) => {
    if (multiSelect) {
      setLocalSelectedChords(prev => {
        const isAlreadySelected = prev.some(c => c.id === chord.id);
        if (isAlreadySelected) {
          return prev.filter(c => c.id !== chord.id);
        } else {
          return [...prev, chord];
        }
      });
    } else {
      onChordSelect(chord);
      onClose();
    }
  }, [multiSelect, onChordSelect, onClose]);

  const handleConfirmSelection = useCallback(() => {
    if (multiSelect && localSelectedChords.length > 0) {
      localSelectedChords.forEach(chord => onChordSelect(chord));
    }
    onClose();
  }, [multiSelect, localSelectedChords, onChordSelect, onClose]);

  const handleFilterChange = useCallback((key: keyof ChordSearchFilters, value: any) => {
    setActiveFilters(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const handleQuickFilter = useCallback((type: string, value: string) => {
    if (type === 'key') {
      handleFilterChange('key', activeFilters.key === value ? '' : value);
    } else if (type === 'difficulty') {
      handleFilterChange('difficulty', activeFilters.difficulty === value ? 'all' : value);
    }
  }, [activeFilters.key, activeFilters.difficulty, handleFilterChange]);

  // ============================================================================
  // DONNÉES CALCULÉES
  // ============================================================================

  const displayedChords = useMemo(() => {
    let chords = filteredChords;
    
    // Filtrer par favoris si activé
    if (showOnlyFavorites) {
      chords = chords.filter(chord => favoriteChords.includes(chord.id));
    }
    
    return chords;
  }, [filteredChords, showOnlyFavorites, favoriteChords]);

  const selectedChordIds = useMemo(() => 
    new Set(localSelectedChords.map(c => c.id)), 
    [localSelectedChords]
  );

  // ============================================================================
  // RENDU
  // ============================================================================

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] flex flex-col">
          {/* En-tête */}
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center">
              <Music className="w-6 h-6 text-blue-600 mr-2" />
              <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
              {multiSelect && localSelectedChords.length > 0 && (
                <span className="ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                  {localSelectedChords.length} sélectionné{localSelectedChords.length > 1 ? 's' : ''}
                </span>
              )}
            </div>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Barre de recherche et filtres */}
          <div className="p-6 border-b space-y-4">
            {/* Recherche principale */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Rechercher un accord..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                autoFocus
              />
            </div>

            {/* Filtres rapides */}
            <div className="flex flex-wrap gap-4">
              {/* Tonalités */}
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">Tonalité:</span>
                <div className="flex flex-wrap gap-1">
                  {['C', 'D', 'E', 'F', 'G', 'A', 'B'].map(key => (
                    <QuickFilter
                      key={key}
                      label={key}
                      value={key}
                      isActive={activeFilters.key === key}
                      onClick={(value) => handleQuickFilter('key', value)}
                    />
                  ))}
                </div>
              </div>

              {/* Difficultés */}
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">Difficulté:</span>
                <div className="flex gap-1">
                  {[
                    { label: 'Débutant', value: 'beginner' },
                    { label: 'Intermédiaire', value: 'intermediate' },
                    { label: 'Avancé', value: 'advanced' }
                  ].map(({ label, value }) => (
                    <QuickFilter
                      key={value}
                      label={label}
                      value={value}
                      isActive={activeFilters.difficulty === value}
                      onClick={(val) => handleQuickFilter('difficulty', val)}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Contrôles d'affichage */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Instrument */}
                <select
                  value={activeFilters.instrument || ''}
                  onChange={(e) => handleFilterChange('instrument', e.target.value as InstrumentType)}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
                >
                  {state.availableInstruments.map(config => (
                    <option key={config.type} value={config.type}>
                      {config.name}
                    </option>
                  ))}
                </select>

                {/* Favoris */}
                {onToggleFavorite && (
                  <button
                    onClick={() => setShowOnlyFavorites(!showOnlyFavorites)}
                    className={`
                      flex items-center px-3 py-1 text-sm rounded-lg border transition-colors
                      ${showOnlyFavorites 
                        ? 'bg-yellow-100 text-yellow-800 border-yellow-300' 
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-yellow-50'
                      }
                    `}
                  >
                    <Star className="w-4 h-4 mr-1" />
                    Favoris
                  </button>
                )}
              </div>

              {/* Mode d'affichage */}
              <div className="flex items-center space-x-1 border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Contenu principal */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading && (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Chargement...</span>
              </div>
            )}

            {!loading && displayedChords.length === 0 && (
              <div className="text-center py-12">
                <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun accord trouvé</h3>
                <p className="text-gray-600">
                  {showOnlyFavorites 
                    ? "Aucun accord favori ne correspond à vos critères."
                    : "Essayez de modifier vos critères de recherche."
                  }
                </p>
              </div>
            )}

            {!loading && displayedChords.length > 0 && (
              <div className={`
                ${viewMode === 'grid' 
                  ? 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3' 
                  : 'space-y-2'
                }
              `}>
                {displayedChords.map((chord) => (
                  <CompactChordCard
                    key={chord.id}
                    chord={chord}
                    isSelected={selectedChordIds.has(chord.id)}
                    isFavorite={favoriteChords.includes(chord.id)}
                    onSelect={handleChordSelect}
                    onToggleFavorite={onToggleFavorite}
                    showFavorites={!!onToggleFavorite}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Pied de page */}
          {multiSelect && (
            <div className="flex items-center justify-between p-6 border-t bg-gray-50">
              <div className="text-sm text-gray-600">
                {localSelectedChords.length} accord{localSelectedChords.length !== 1 ? 's' : ''} sélectionné{localSelectedChords.length !== 1 ? 's' : ''}
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={handleConfirmSelection}
                  disabled={localSelectedChords.length === 0}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Confirmer ({localSelectedChords.length})
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
