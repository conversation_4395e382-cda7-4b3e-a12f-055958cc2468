import { Button } from '@/components/ui/button';
import { Loader2, Save } from 'lucide-react'; // Assuming lucide-react for icons

interface SongFormFooterProps {
  mode: 'create' | 'edit';
  onCancel: () => void;
  onSaveNewVersion?: () => void; // Optional, only for edit mode
  isSubmittingGlobal: boolean;
  isFormSubmitting: boolean;
  isAudioUploading?: boolean;
  isLoadingSaveVersion?: boolean;
  lastSavedTimestamp?: string | null;
  currentSubmitStatus?: 'draft' | 'publish' | 'update' | 'create'; // More granular status
}

export const SongFormFooter: React.FC<SongFormFooterProps> = ({
  mode,
  onCancel,
  onSaveNewVersion,
  isSubmittingGlobal,
  isFormSubmitting,
  isAudioUploading,
  isLoadingSaveVersion,
  lastSavedTimestamp,
  currentSubmitStatus
}) => {
  const getSubmitButtonText = () => {
    if (currentSubmitStatus === 'draft') return 'Enregistrer le Brouillon';
    if (currentSubmitStatus === 'publish') return 'Publier le Morceau';
    if (mode === 'create') return 'Créer le Morceau';
    return 'Mettre à Jour';
  };

  return (
    <div className="sticky bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-t p-4 mt-auto">
      <div className="max-w-4xl mx-auto flex items-center justify-end space-x-3">
        {lastSavedTimestamp && (
          <span className="text-sm text-muted-foreground">
            Dernière sauvegarde: {new Date(lastSavedTimestamp).toLocaleTimeString()}
          </span>
        )}
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmittingGlobal || isFormSubmitting}>
          Annuler
        </Button>
        {mode === 'edit' && onSaveNewVersion && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onSaveNewVersion} 
            disabled={isSubmittingGlobal || isFormSubmitting || isLoadingSaveVersion}
          >
            {isLoadingSaveVersion ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
            Enregistrer une Nouvelle Version
          </Button>
        )}
        <Button 
          type="submit" 
          disabled={isSubmittingGlobal || isFormSubmitting || isAudioUploading || isLoadingSaveVersion}
        >
          {(isFormSubmitting || isAudioUploading) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {getSubmitButtonText()}
        </Button>
      </div>
    </div>
  );
};