'use client';

import React, { useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { 
  <PERSON>Text, Guitar, Palette, Brain, BarChart3, AlertCircle, CheckCircle2
} from 'lucide-react';
// Import du nouveau système unifié
import { LyricsEditorUnified } from '../unified/LyricsEditorUnified';
import AIChordIntegration from '../AIChordIntegration';
import { StyleThemeConfig } from '../StyleThemeConfig';
import { AIInsightsPanel } from '../AIInsightsPanel';
import { AiConfigMenu } from '@/components/ia/ai-config-menu';
import { AiQuickActions } from '@/components/ia/ai-quick-actions';
import { UnifiedSongStructureTimeline } from '../UnifiedSongStructureTimeline';
import type { 
  LyricsSection, 
  ChordPosition, 
  StyleConfig, 
  CurrentSong 
} from './AIComposerCore';

interface AIComposerTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  
  // États principaux
  lyricsContent: string;
  setLyricsContent: (content: string) => void;
  songSections: LyricsSection[];
  setSongSections: (sections: LyricsSection[]) => void;
  selectedSection: string;
  setSelectedSection: (section: string) => void;
  styleConfig: StyleConfig;
  setStyleConfig: (config: StyleConfig) => void;
  currentSong: CurrentSong;
  setCurrentSong: (song: CurrentSong) => void;
  
  // Configuration IA
  isConfigured: boolean;
  aiHistory: { role: string; content: string }[];
  setAiHistory: (history: { role: string; content: string }[]) => void;
  lastAiResult: string;
  setLastAiResult: (result: string) => void;
  
  // Fonctions
  handleAIGenerate: (prompt: string, type: string) => Promise<void>;
  handleLyricsChange: (content: string) => void;
  handleChordAdd: (sectionId: string, chord: ChordPosition) => void;
  handleChordRemove: (sectionId: string, chordId: string) => void;
  handleChordUpdate: (sectionId: string, chordId: string, updates: Partial<ChordPosition>) => void;
  
  // Instruments disponibles
  availableInstruments: Array<{
    id: string;
    name: string;
    tunings: Array<{
      id: string;
      name: string;
      notes: string[];
    }>;
  }>;
}

export const AIComposerTabs: React.FC<AIComposerTabsProps> = ({
  activeTab,
  setActiveTab,
  lyricsContent,
  setLyricsContent,
  songSections,
  setSongSections,
  selectedSection,
  setSelectedSection,
  styleConfig,
  setStyleConfig,
  currentSong,
  setCurrentSong,
  isConfigured,
  aiHistory,
  setAiHistory,
  lastAiResult,
  setLastAiResult,
  handleAIGenerate,
  handleLyricsChange,
  handleChordAdd,
  handleChordRemove,
  handleChordUpdate,
  availableInstruments
}) => {

  // Gestionnaires pour les fonctions IA
  const handleAIGeneralSuggestions = useCallback(async () => {
    if (!isConfigured) {
      return;
    }

    try {
      const prompt = `Analysez cette structure de chanson et donnez des suggestions d'amélioration :\n${JSON.stringify(songSections, null, 2)}`;
      const systemPrompt = 'Vous êtes un compositeur expert. Analysez la structure musicale fournie et donnez des conseils constructifs pour l\'améliorer.';
      
      await handleAIGenerate(prompt, 'structure');
      
    } catch (error) {
      console.error('Erreur IA suggestions:', error);
    }
  }, [isConfigured, handleAIGenerate, songSections]);

  const handleAIMelodySuggestion = useCallback(async () => {
    if (!isConfigured) return;

    try {
      const currentChords = songSections.map(s => s.chords).flat().map(c => c.chord).join(', ');
      const prompt = `Suggérez une mélodie pour ces accords: ${currentChords}. Tonalité: ${styleConfig.key || 'C'}`;
      
      await handleAIGenerate(prompt, 'melody');
    } catch (error) {
      console.error('Erreur IA mélodie:', error);
    }
  }, [isConfigured, handleAIGenerate, songSections, styleConfig.key]);

  const handleAIArrangementAdvice = useCallback(async () => {
    if (!isConfigured) return;

    try {
      const prompt = `Donnez des conseils d'arrangement pour cette structure: ${JSON.stringify(songSections, null, 2)}`;
      
      await handleAIGenerate(prompt, 'arrangement');
    } catch (error) {
      console.error('Erreur IA arrangement:', error);
    }
  }, [isConfigured, handleAIGenerate, songSections]);

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
      <div className="border-b bg-card px-4 py-2">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="compose" className="gap-2">
            <FileText className="h-4 w-4" />
            Composer
          </TabsTrigger>
          <TabsTrigger value="chords" className="gap-2">
            <Guitar className="h-4 w-4" />
            Accords
          </TabsTrigger>
          <TabsTrigger value="style" className="gap-2">
            <Palette className="h-4 w-4" />
            Style
          </TabsTrigger>
          <TabsTrigger value="ai-assistant" className="gap-2">
            <Brain className="h-4 w-4" />
            Assistant IA
            {!isConfigured && <AlertCircle className="h-3 w-3 text-orange-500" />}
            {isConfigured && <CheckCircle2 className="h-3 w-3 text-green-500" />}
          </TabsTrigger>
          <TabsTrigger value="preview" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            Timeline
          </TabsTrigger>
        </TabsList>
      </div>
      
      <div className="flex-1 overflow-hidden">
        <TabsContent value="compose" className="h-full m-0 p-0">
          <LyricsEditorUnified
            content={lyricsContent}
            onContentChange={handleLyricsChange}
            selectedSection={selectedSection}
            sections={songSections.map(section => ({
              ...section,
              chords: section.chords.map(chord => ({
                position: chord.position || 0,
                chord: chord.chord,
                instrument: chord.instrument
              }))
            }))}
            onAIGenerate={(prompt, type) => handleAIGenerate(prompt, type)}
          />
        </TabsContent>
        
        <TabsContent value="chords" className="h-full m-0 p-0">
          <AIChordIntegration
            sections={songSections.map(section => ({
              ...section,
              chords: section.chords.map(chord => ({
                id: chord.id,
                position: chord.position || 0,
                chord: chord.chord,
                instrument: chord.instrument,
                tuning: chord.tuning,
                fret: chord.fret,
                fingering: chord.fingering,
                preview: chord.preview
              }))
            }))}
            selectedSection={selectedSection}
            onSectionUpdate={(sectionId: string, updates: any) => {
              setSongSections(prev => prev.map(section => 
                section.id === sectionId 
                  ? { ...section, ...updates }
                  : section
              ));
            }}
            onChordAdd={handleChordAdd}
            onChordRemove={handleChordRemove}
            onChordUpdate={handleChordUpdate}
            availableInstruments={availableInstruments}
            onAIGenerateChords={async (prompt: string, sectionId: string) => {
              await handleAIGenerate(prompt, 'chords');
            }}
          />
        </TabsContent>
        
        <TabsContent value="style" className="h-full m-0 p-0">
          <div className="h-full p-4">
            <StyleThemeConfig
              initialConfig={styleConfig}
              onConfigChange={(config) => {
                setStyleConfig(config);
                // Synchroniser avec currentSong
                setCurrentSong(prev => ({
                  ...prev,
                  key: config.key,
                  tempo: config.bpm,
                  timeSignature: config.timeSignature
                }));
              }}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="ai-assistant" className="h-full m-0 p-0">
          <div className="h-full p-4 space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
              {/* Configuration et Actions IA */}
              <div className="space-y-4">
                <AiConfigMenu />
                
                <AiQuickActions
                  onGeneralSuggestions={handleAIGeneralSuggestions}
                  onMelodySuggestion={handleAIMelodySuggestion}
                  onArrangementAdvice={handleAIArrangementAdvice}
                  isConfigured={isConfigured}
                />
              </div>
              
              {/* Insights et Résultats */}
              <div className="space-y-4">
                <AIInsightsPanel
                  aiHistory={aiHistory}
                  lastResult={lastAiResult}
                  songSections={songSections}
                  styleConfig={styleConfig}
                />
              </div>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="preview" className="h-full m-0 p-0">
          <div className="h-full p-4">
            <UnifiedSongStructureTimeline
              structure={{
                sections: songSections.map(s => ({
                  id: s.id,
                  type: s.type,
                  title: s.title,
                  duration: s.duration || 16,
                  startTime: s.startTime || 0,
                  key: styleConfig.key,
                  tempo: styleConfig.bpm
                })),
                totalDuration: songSections.reduce((total, section) => total + (section.duration || 16), 0),
                key: styleConfig.key,
                tempo: styleConfig.bpm,
                timeSignature: styleConfig.timeSignature
              }}
              onStructureChange={(structure) => {
                const newSections = structure.sections.map(s => {
                  const existing = songSections.find(existing => existing.id === s.id);
                  return existing ? { 
                    ...existing, 
                    type: s.type, 
                    title: s.title,
                    duration: s.duration,
                    startTime: s.startTime
                  } : {
                    id: s.id,
                    type: s.type,
                    title: s.title,
                    duration: s.duration,
                    startTime: s.startTime,
                    content: '',
                    chords: []
                  };
                });
                setSongSections(newSections);
              }}
              onSectionSelect={setSelectedSection}
              selectedSectionId={selectedSection}
              viewMode="list"
            />
          </div>
        </TabsContent>
      </div>
    </Tabs>
  );
};
