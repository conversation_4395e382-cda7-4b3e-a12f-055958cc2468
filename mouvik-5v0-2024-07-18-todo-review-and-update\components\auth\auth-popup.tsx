"use client"

import React, { useState, forwardRef, useImperativeHandle } from 'react'
import { createBrowserClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"

export const AuthPopup = forwardRef(function AuthPopup(props, ref) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [username, setUsername] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [open, setOpen] = useState(false)
  const [showForgot, setShowForgot] = useState(false)
  const [forgotEmail, setForgotEmail] = useState('')
  const [forgotMsg, setForgotMsg] = useState<string | null>(null)
  const router = useRouter()
  const supabase = createBrowserClient()
  const { toast } = useToast()

  useImperativeHandle(ref, () => ({
    openAuth: () => setOpen(true)
  }))

  const handleAuth = async (action: 'login' | 'signup') => {
    setLoading(true)
    setError(null)
    try {
      let response
      if (action === 'login') {
        response = await supabase.auth.signInWithPassword({
          email,
          password,
        })
      } else {
        if (!username || username.length < 3) {
          setError('Veuillez choisir un nom d\'artiste (3 caractères min).')
          setLoading(false)
          return
        }
        response = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: { username },
          },
        })
      }
      if (response.error) {
        throw response.error
      }
      toast({
        title: action === 'login' ? "Connexion réussie !" : "Inscription réussie !",
        description: action === 'login' ? "Bienvenue !" : "Vérifiez vos emails pour confirmer votre compte.",
      })
      if (action === 'login') {
        router.push('/dashboard')
        router.refresh()
      }
      setOpen(false)
    } catch (err: any) {
      setError(err.message || `Erreur lors de la ${action}.`)
      toast({
        title: `Erreur de ${action}`,
        description: err.message || `Une erreur est survenue.`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleForgot = async (e: React.FormEvent) => {
    e.preventDefault()
    setForgotMsg(null)
    setLoading(true)
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(forgotEmail)
      if (error) throw error
      setForgotMsg('Un email de réinitialisation a été envoyé si ce compte existe.')
    } catch (err: any) {
      setForgotMsg(err.message || "Erreur lors de la demande.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Connexion / Inscription</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-black !bg-black shadow-2xl border border-slate-800 p-0">
        <DialogHeader className="p-6 text-center border-b border-slate-700">
          <DialogTitle className="text-xl font-semibold text-white">Accès à votre compte Mouvik</DialogTitle>
          <DialogDescription className="text-sm text-slate-400">
            Connectez-vous ou créez un compte pour continuer.
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col items-center pt-4 pb-2">
          <img src="/LOGO_Mouvik.png" alt="Logo Mouvik" className="h-12 w-auto mb-2 drop-shadow-lg" />
        </div>
        <Tabs defaultValue="login" className="w-full px-6 pb-6">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="login">Connexion</TabsTrigger>
            <TabsTrigger value="signup">Inscription</TabsTrigger>
          </TabsList>
          <TabsContent value="login">
            <form onSubmit={e => { e.preventDefault(); handleAuth('login') }} className="space-y-4">
              <div>
                <Label htmlFor="email-login" className="text-white">Email</Label>
                <Input
                  id="email-login"
                  type="email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  className="bg-slate-900/90 border-slate-700 text-white placeholder:text-slate-400 shadow-sm"
                  disabled={loading}
                  autoComplete="email"
                  required
                />
              </div>
              <div>
                <Label htmlFor="password-login" className="text-white">Mot de passe</Label>
                <Input
                  id="password-login"
                  type="password"
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                  className="bg-slate-900/90 border-slate-700 text-white placeholder:text-slate-400 shadow-sm"
                  disabled={loading}
                  autoComplete="current-password"
                  required
                />
              </div>
              <div className="flex items-center justify-between mt-1 mb-2">
                <button type="button" className="text-xs text-teal-400 hover:underline" onClick={() => setShowForgot(v => !v)}>
                  Mot de passe oublié ?
                </button>
              </div>
              {showForgot && (
                <form onSubmit={handleForgot} className="mb-2 flex flex-col gap-2 bg-[#1b2230] p-3 rounded-lg">
                  <Label htmlFor="forgot-email" className="text-white text-xs">Email pour réinitialiser</Label>
                  <Input
                    id="forgot-email"
                    type="email"
                    value={forgotEmail}
                    onChange={e => setForgotEmail(e.target.value)}
                    className="bg-[#181f2a] border-slate-700 text-white placeholder:text-slate-400 shadow-sm rounded-lg"
                    required
                  />
                  <Button type="submit" className="btn-outline-teal w-full mt-1" disabled={loading}>
                    Envoyer le lien
                  </Button>
                  {forgotMsg && <div className="text-xs text-center text-teal-300 mt-1">{forgotMsg}</div>}
                </form>
              )}
              {error && <div className="text-red-500 text-sm text-center pt-1">{error}</div>}
              <DialogFooter className="pt-2">
                <Button type="submit" className="btn-gradient text-white font-bold w-full py-3 rounded-lg shadow-lg" disabled={loading}>
                  {loading ? "Connexion en cours..." : "Se connecter"}
                </Button>
              </DialogFooter>
            </form>
          </TabsContent>
          <TabsContent value="signup">
            <form onSubmit={e => { e.preventDefault(); handleAuth('signup') }} className="space-y-4">
              <div>
                <Label htmlFor="pseudo-signup" className="text-white">Nom d'artiste</Label>
                <Input
                  id="pseudo-signup"
                  type="text"
                  value={username}
                  onChange={e => setUsername(e.target.value)}
                  className="bg-slate-900/90 border-slate-700 text-white placeholder:text-slate-400 shadow-sm"
                  disabled={loading}
                  minLength={3}
                  autoComplete="username"
                  required
                />
              </div>
              <div>
                <Label htmlFor="email-signup" className="text-white">Email</Label>
                <Input
                  id="email-signup"
                  type="email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  className="bg-slate-900/90 border-slate-700 text-white placeholder:text-slate-400 shadow-sm"
                  disabled={loading}
                  autoComplete="email"
                  required
                />
              </div>
              <div>
                <Label htmlFor="password-signup" className="text-white">Mot de passe</Label>
                <Input
                  id="password-signup"
                  type="password"
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                  className="bg-slate-900/90 border-slate-700 text-white placeholder:text-slate-400 shadow-sm"
                  disabled={loading}
                  autoComplete="new-password"
                  required
                />
              </div>
              {error && <div className="text-red-500 text-sm text-center pt-1">{error}</div>}
              <DialogFooter className="pt-2">
                <Button type="submit" className="btn-gradient text-white font-bold w-full py-3 rounded-lg shadow-lg" disabled={loading}>
                  {loading ? "Inscription en cours..." : "S'inscrire"}
                </Button>
              </DialogFooter>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
})
