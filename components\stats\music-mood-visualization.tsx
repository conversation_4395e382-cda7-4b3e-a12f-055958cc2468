"use client"

import { useState, useEffect, useRef } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2, Music } from "lucide-react"
import { Button } from "@/components/ui/button"
// import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip" // Non utilisé actuellement

interface MusicMoodVisualizationProps {
  userId: string
}

interface SongWithMetadata {
  id: string
  title: string
  moods?: string[] // Made optional as it's not directly selected
  genres?: string[] // Made optional as it's not directly selected
  bpm: number | null
  key: string | null
  plays: number // Changed from plays
  // likes: number; // Retiré car songs.likes n'existe pas
  duration: number | null
  release_date: string | null
  color?: string
}

// Define the structure of song data fetched from Supabase for this component
interface FetchedSongFromQuery {
  id: string;
  title: string | null;
  genre: string[] | string | null; // Adjust if type is known to be string or string[] specifically
  mood: string[] | string | null;  // Adjust if type is known to be string or string[] specifically
  duration_ms: number | null;
  plays: number | null;
  cover_image_url: string | null;
  audio_file_url: string | null;
  created_at: string;
  user_id: string;
  profiles: { username: string | null } | null; // Assuming one profile per song user_id
}

const GENRE_COLORS: Record<string, string> = {
  "Électronique": "#4ECDC4", "Hip-Hop": "#FF6B6B", "Rock": "#C44D58", "Jazz": "#556270",
  "Classique": "#1A535C", "Ambient": "#FFE66D", "Pop": "#6B5B95", "Folk": "#88B04B",
  "R&B": "#F7B801", "Metal": "#7D4E57", "Country": "#D4B483", "Reggae": "#7CAA2D"
};
const MOOD_COLORS: Record<string, string> = {
  "Énergique": "#FF6B6B", "Calme": "#4ECDC4", "Mélancolique": "#556270", "Joyeux": "#FFE66D",
  "Agressif": "#C44D58", "Romantique": "#6B5B95", "Inspirant": "#88B04B", "Sombre": "#1A535C"
};

const getDefaultColor = (index: number): string => {
  const defaultColors = Object.values(GENRE_COLORS);
  return defaultColors[index % defaultColors.length];
};

export function MusicMoodVisualization({ userId }: MusicMoodVisualizationProps) {
  const [songs, setSongs] = useState<SongWithMetadata[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [colorBy, setColorBy] = useState<'genre' | 'mood'>('genre')
  const [xAxis, setXAxis] = useState<'bpm' | 'release_date'>('bpm')
  const [yAxis, setYAxis] = useState<'key' | 'duration'>('key')
  const [selectedSong, setSelectedSong] = useState<SongWithMetadata | null>(null)
  
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const keyToNumber = (key: string | null): number => {
    if (!key) return 0;
    const keyMap: Record<string, number> = {
      'C': 0, 'C#': 1, 'Db': 1, 'D': 2, 'D#': 3, 'Eb': 3, 'E': 4, 'F': 5, 
      'F#': 6, 'Gb': 6, 'G': 7, 'G#': 8, 'Ab': 8, 'A': 9, 'A#': 10, 'Bb': 10, 'B': 11
    };
    const match = key.match(/([A-G][#b]?)([mM])?/);
    if (!match) return 0;
    const note = match[1];
    const mode = match[2] || 'M';
    return (keyMap[note] || 0) + (mode === 'm' ? 12 : 0);
  };
  
  useEffect(() => {
    const fetchSongs = async () => {
      if (!userId) {
        setIsLoading(false);
        return;
      }
      try {
        setIsLoading(true);
        setError(null);
        const supabase = createBrowserClient();
        
        const { data, error: fetchError } = await supabase
          .from('songs')
          .select('id, title, bpm, key, plays, duration_ms, release_date') // Use duration_ms from schema
          .eq('creator_user_id', userId)
          .order('created_at', { ascending: false }); 
        
        if (fetchError) throw fetchError;

        if (data) {
          // Map duration_ms to duration for SongWithMetadata compatibility
          const mappedData = data.map((song: {
  id: string;
  title: string | null;
  bpm: number | null;
  key: string | null;
  plays: number | null;
  duration_ms: number | null;
  release_date: string | null;
}) => ({
  id: song.id,
  title: song.title ?? '',
  bpm: song.bpm ?? null,
  key: song.key ?? null,
  plays: song.plays ?? 0,
  duration: song.duration_ms ?? null,
  release_date: song.release_date ?? null,
  genres: [], // or null if that's what your UI expects
  moods: [], // or null if that's what your UI expects
  color: undefined
}));
setSongs(mappedData as Omit<SongWithMetadata, 'likes'>[]);
        } else {
          setSongs([]);
        }
      } catch (err) {
        console.error("Erreur lors de la récupération des morceaux:", err);
        setError(err instanceof Error ? err.message : "Une erreur est survenue");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSongs();
  }, [userId]);
  
  useEffect(() => {
    if (!canvasRef.current || songs.length === 0 || !containerRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const margin = { top: 20, right: 20, bottom: 40, left: 40 };
    const chartWidth = canvas.width - margin.left - margin.right;
    const chartHeight = canvas.height - margin.top - margin.bottom;
    
    const getXValue = (song: SongWithMetadata) => {
      if (xAxis === 'bpm') return song.bpm || 0;
      return song.release_date ? new Date(song.release_date).getTime() : 0;
    };
    const getYValue = (song: SongWithMetadata) => {
      if (yAxis === 'key') return keyToNumber(song.key);
      return song.duration || 0;
    };
    
    const xValues = songs.map(getXValue).filter(v => v !== 0); 
    const yValues = songs.map(getYValue);

    const xMin = xValues.length > 0 ? Math.min(...xValues) : 0;
    const xMax = xValues.length > 0 ? Math.max(...xValues) : 1; 
    const yMin = yValues.length > 0 ? Math.min(...yValues) : 0;
    const yMax = yValues.length > 0 ? Math.max(...yValues) : 1;

    const xScale = (value: number) => margin.left + (xMax === xMin ? chartWidth / 2 : ((value - xMin) / (xMax - xMin)) * chartWidth);
    const yScale = (value: number) => margin.top + chartHeight - (yMax === yMin ? chartHeight / 2 : ((value - yMin) / (yMax - yMin)) * chartHeight);
    
    ctx.strokeStyle = '#888';
    ctx.lineWidth = 1;
    ctx.beginPath(); ctx.moveTo(margin.left, margin.top + chartHeight); ctx.lineTo(margin.left + chartWidth, margin.top + chartHeight); ctx.stroke(); 
    ctx.beginPath(); ctx.moveTo(margin.left, margin.top); ctx.lineTo(margin.left, margin.top + chartHeight); ctx.stroke(); 

    ctx.fillStyle = '#888'; ctx.font = '12px sans-serif'; ctx.textAlign = 'center';
    ctx.fillText(xAxis === 'bpm' ? 'BPM' : 'Date de sortie', margin.left + chartWidth / 2, canvas.height - 10);
    ctx.save(); ctx.translate(10, margin.top + chartHeight / 2); ctx.rotate(-Math.PI / 2);
    ctx.fillText(yAxis === 'key' ? 'Tonalité' : 'Durée (sec)', 0, 0);
    ctx.restore();
    
    songs.forEach((song, index) => {
      const x = xScale(getXValue(song));
      const y = yScale(getYValue(song));
      const radius = Math.max(3, 5 + (song.plays / 200));
      
      let color;
      if (colorBy === 'genre' && song.genres && song.genres.length > 0) color = GENRE_COLORS[song.genres[0]] || getDefaultColor(index);
      else if (colorBy === 'mood' && song.moods && song.moods.length > 0) color = MOOD_COLORS[song.moods[0]] || getDefaultColor(index);
      else color = getDefaultColor(index);
      
      song.color = color;
      
      ctx.beginPath(); ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fillStyle = color; ctx.globalAlpha = 0.7; ctx.fill();
      ctx.globalAlpha = 1; ctx.strokeStyle = '#fff'; ctx.lineWidth = 1; ctx.stroke();
    });
  }, [songs, colorBy, xAxis, yAxis, containerRef.current?.clientWidth]); 
  
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || songs.length === 0 || !containerRef.current) return;
    const canvas = canvasRef.current; const rect = canvas.getBoundingClientRect();
    const clickX = e.clientX - rect.left; const clickY = e.clientY - rect.top;
    const margin = { top: 20, right: 20, bottom: 40, left: 40 };
    const chartWidth = canvas.width - margin.left - margin.right;
    const chartHeight = canvas.height - margin.top - margin.bottom;
    const getXValue = (song: SongWithMetadata) => xAxis === 'bpm' ? song.bpm || 0 : (song.release_date ? new Date(song.release_date).getTime() : 0);
    const getYValue = (song: SongWithMetadata) => yAxis === 'key' ? keyToNumber(song.key) : song.duration || 0;
    const xValues = songs.map(getXValue).filter(v => v !== 0); const yValues = songs.map(getYValue);
    const xMin = xValues.length > 0 ? Math.min(...xValues) : 0; const xMax = xValues.length > 0 ? Math.max(...xValues) : 1;
    const yMin = yValues.length > 0 ? Math.min(...yValues) : 0; const yMax = yValues.length > 0 ? Math.max(...yValues) : 1;
    const xScale = (value: number) => margin.left + (xMax === xMin ? chartWidth / 2 : ((value - xMin) / (xMax - xMin)) * chartWidth);
    const yScale = (value: number) => margin.top + chartHeight - (yMax === yMin ? chartHeight / 2 : ((value - yMin) / (yMax - yMin)) * chartHeight);
    let closestSong: SongWithMetadata | null = null; let minDistance = Infinity;
    songs.forEach(song => {
      const songX = xScale(getXValue(song)); const songY = yScale(getYValue(song));
      const distance = Math.sqrt(Math.pow(clickX - songX, 2) + Math.pow(clickY - songY, 2));
      if (distance < minDistance) { minDistance = distance; closestSong = song; }
    });
    if (closestSong && minDistance < 20) setSelectedSong(closestSong); else setSelectedSong(null);
  };
  
  if (isLoading) { 
    return (
      <Card>
        <CardHeader><CardTitle>Cartographie musicale</CardTitle><CardDescription>Chargement...</CardDescription></CardHeader>
        <CardContent className="flex justify-center items-center h-[400px]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></CardContent>
      </Card>
    );
  }
  if (error) { 
    return (
      <Card>
        <CardHeader><CardTitle>Cartographie musicale</CardTitle><CardDescription>Erreur</CardDescription></CardHeader>
        <CardContent><p className="text-destructive">{error}</p></CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle className="flex items-center"><Music className="h-4 w-4 mr-2" />Cartographie musicale</CardTitle>
            <CardDescription>Visualisation interactive de vos morceaux</CardDescription>
          </div>
          <div className="flex flex-wrap gap-2">
            <Select value={colorBy} onValueChange={(value: 'genre' | 'mood') => setColorBy(value)}>
              <SelectTrigger className="w-[120px]"><SelectValue placeholder="Couleur par" /></SelectTrigger>
              <SelectContent><SelectItem value="genre">Genre</SelectItem><SelectItem value="mood">Mood</SelectItem></SelectContent>
            </Select>
            <Select value={xAxis} onValueChange={(value: 'bpm' | 'release_date') => setXAxis(value)}>
              <SelectTrigger className="w-[120px]"><SelectValue placeholder="Axe X" /></SelectTrigger>
              <SelectContent><SelectItem value="bpm">BPM</SelectItem><SelectItem value="release_date">Date</SelectItem></SelectContent>
            </Select>
            <Select value={yAxis} onValueChange={(value: 'key' | 'duration') => setYAxis(value)}>
              <SelectTrigger className="w-[120px]"><SelectValue placeholder="Axe Y" /></SelectTrigger>
              <SelectContent><SelectItem value="key">Tonalité</SelectItem><SelectItem value="duration">Durée</SelectItem></SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="relative h-[400px] w-full" ref={containerRef}>
          <canvas ref={canvasRef} className="w-full h-full cursor-pointer" onClick={handleCanvasClick} />
          {selectedSong && (
            <div className="absolute p-3 bg-card border rounded-md shadow-md" style={{ top: '20px', right: '20px', maxWidth: '250px' }}>
              <h4 className="font-medium text-sm">{selectedSong.title}</h4>
              <div className="text-xs space-y-1 mt-2">
                <p><span className="text-muted-foreground">Genre:</span> {selectedSong.genres?.join(', ')}</p>
                <p><span className="text-muted-foreground">Mood:</span> {selectedSong.moods?.join(', ')}</p>
                <p><span className="text-muted-foreground">BPM:</span> {selectedSong.bpm}</p>
                <p><span className="text-muted-foreground">Tonalité:</span> {selectedSong.key}</p>
                <p><span className="text-muted-foreground">Écoutes:</span> {selectedSong.plays}</p>
                {/* <p><span className="text-muted-foreground">Likes:</span> {selectedSong.likes}</p> Retiré */}
                {selectedSong.duration && (<p><span className="text-muted-foreground">Durée:</span> {Math.floor(selectedSong.duration / 60)}:{(selectedSong.duration % 60).toString().padStart(2, '0')}</p>)}
                {selectedSong.release_date && (<p><span className="text-muted-foreground">Sortie:</span> {new Date(selectedSong.release_date).toLocaleDateString()}</p>)}
              </div>
              <Button variant="link" size="sm" className="mt-2 p-0 h-auto text-xs" onClick={() => setSelectedSong(null)}>Fermer</Button>
            </div>
          )}
        </div>
        <div className="mt-4 flex flex-wrap gap-3">
          <div className="text-xs text-muted-foreground"><span className="font-medium">Légende:</span> Taille = nombre d'écoutes</div>
          {colorBy === 'genre' && (<div className="flex flex-wrap gap-2">{Object.entries(GENRE_COLORS).map(([genre, color]) => (<div key={genre} className="flex items-center gap-1 text-xs"><div className="w-3 h-3 rounded-full" style={{ backgroundColor: color }}></div><span>{genre}</span></div>))}</div>)}
          {colorBy === 'mood' && (<div className="flex flex-wrap gap-2">{Object.entries(MOOD_COLORS).map(([mood, color]) => (<div key={mood} className="flex items-center gap-1 text-xs"><div className="w-3 h-3 rounded-full" style={{ backgroundColor: color }}></div><span>{mood}</span></div>))}</div>)}
        </div>
      </CardContent>
    </Card>
  )
}
