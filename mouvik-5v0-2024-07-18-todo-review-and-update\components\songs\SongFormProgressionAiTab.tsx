import { Control, FieldErrors } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RHFSelect } from '@/components/hook-form'; 
import { Slider } from '@/components/ui/slider';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { SelectItem } from '@/components/ui/select';
import { SongFormValues } from './song-schema';
import { CREATION_PROCESS_OPTIONS, PROGRESS_STATUS_OPTIONS } from './song-options';

interface SongFormProgressionAiTabProps {
  control: Control<SongFormValues>;
  errors?: FieldErrors<SongFormValues>;
  // Add any other props needed from the parent SongForm
}

export const SongFormProgressionAiTab: React.FC<SongFormProgressionAiTabProps> = ({ 
  control,
  // errors 
}) => {
  return (
    <Card>
      <CardHeader><CardTitle>Progression & Assistance IA</CardTitle></CardHeader>
      <CardContent className="space-y-8">
        
        {/* Section: AI Assistance */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Assistance IA</h3>
          <RHFSelect 
            name="creation_process_type" 
            label="Type de processus de création"
            control={control}
          >
            {CREATION_PROCESS_OPTIONS.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
          </RHFSelect>

          <FormField
            control={control}
            name="ai_collaboration_level"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Niveau de collaboration IA ({field.value ?? 0}%)</FormLabel>
                <FormControl>
                  <Slider
                    defaultValue={[field.value ?? 0]}
                    onValueChange={(value) => field.onChange(value[0])}
                    max={100}
                    step={1}
                    className="py-2"
                  />
                </FormControl>
                <FormDescription>Pourcentage de l'implication de l'IA dans la création.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Section: Song Progression */}
        <div className="space-y-4 pt-6 border-t">
          <h3 className="text-lg font-semibold">Progression du Morceau</h3>
          <FormField
            control={control}
            name="completion_percentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Complétion globale du morceau ({field.value ?? 0}%)</FormLabel>
                <FormControl>
                  <Slider
                    defaultValue={[field.value ?? 0]}
                    onValueChange={(value) => field.onChange(value[0])}
                    max={100}
                    step={1}
                    className="py-2"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 pt-4">
            <RHFSelect name="recording_status" label="Statut Enregistrement" control={control}>
              {PROGRESS_STATUS_OPTIONS.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
            </RHFSelect>
            <RHFSelect name="arrangement_status" label="Statut Arrangement" control={control}>
              {PROGRESS_STATUS_OPTIONS.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
            </RHFSelect>
            <RHFSelect name="mixing_status" label="Statut Mixage" control={control}>
              {PROGRESS_STATUS_OPTIONS.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
            </RHFSelect>
            <RHFSelect name="mastering_status" label="Statut Mastering" control={control}>
              {PROGRESS_STATUS_OPTIONS.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
            </RHFSelect>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
