'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Play, Pause, Volume2, Filter, Search, Music } from 'lucide-react';
import { MidiChordPlayer } from '@/lib/chords/midi-chord-player';

// Types pour les données d'accords
interface ChordPosition {
  frets: number[];
  fingers: number[];
  barres: Array<{
    fret: number;
    fromString: number;
    toString: number;
  }>;
  midi: number[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  baseFret: number;
  position: string;
  notes: string[];
}

interface ChordVariation {
  suffix: string;
  name: string;
  positions: ChordPosition[];
}

interface ChordLibrary {
  instrument: string;
  tuning: string[];
  strings: number;
  fretRange: [number, number];
  keys: string[];
  suffixes: string[];
  chords: Record<string, ChordVariation[]>;
  metadata?: {
    version: string;
    created: string;
    description: string;
    features: string[];
  };
}

interface CompleteChordGridProps {
  chordLibrary?: ChordLibrary;
  onChordSelect?: (chord: ChordPosition) => void;
}

const CompleteChordGrid: React.FC<CompleteChordGridProps> = ({
  chordLibrary,
  onChordSelect
}) => {
  const [selectedKey, setSelectedKey] = useState<string>('C');
  const [selectedSuffix, setSelectedSuffix] = useState<string>('major');
  const [selectedPosition, setSelectedPosition] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [difficultyFilter, setDifficultyFilter] = useState<string>('all');
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [playMode, setPlayMode] = useState<'chord' | 'arpeggio'>('chord');
  const [arpeggioPattern, setArpeggioPattern] = useState<string>('ascending');
  const [midiPlayer, setMidiPlayer] = useState<MidiChordPlayer | null>(null);
  const [currentChord, setCurrentChord] = useState<ChordPosition | null>(null);
  const [loadedLibraries, setLoadedLibraries] = useState<Record<string, ChordLibrary>>({});
  const [selectedInstrument, setSelectedInstrument] = useState<string>('guitar-standard');

  // Initialisation du lecteur MIDI
  useEffect(() => {
    const initMidi = async () => {
      try {
        const player = new MidiChordPlayer();
        setMidiPlayer(player);
      } catch (error) {
        console.warn('MIDI non disponible:', error);
      }
    };
    initMidi();
  }, []);

  // Chargement des bibliothèques d'accords
  useEffect(() => {
    const loadChordLibraries = async () => {
      const libraries: Record<string, ChordLibrary> = {};
      
      try {
        // Chargement des différents instruments et accordages
        const instrumentFiles = [
          { file: 'guitar_complete_extended.json', key: 'guitar-standard' },
          { file: 'guitar_open_g_complete.json', key: 'guitar-open-g' },
          { file: 'guitar_drop_d_complete.json', key: 'guitar-drop-d' },
          { file: 'ukulele_gcea_complete.json', key: 'ukulele-gcea' },
          { file: 'ukulele_complete_all_keys.json', key: 'ukulele-gcea-complete' },
          { file: 'guitar.json', key: 'guitar-basic' },
          { file: 'guitar_open_g.json', key: 'guitar-open-g-basic' },
          { file: 'guitar_standard_tuning.json', key: 'guitar-standard-basic' }
        ];

        for (const { file, key } of instrumentFiles) {
          try {
            const response = await fetch(`/lib/chords/${file}`);
            if (response.ok) {
              const data = await response.json();
              libraries[key] = data;
            }
          } catch (error) {
            console.warn(`Erreur lors du chargement de ${file}:`, error);
          }
        }
        
        setLoadedLibraries(libraries);
      } catch (error) {
        console.error('Erreur lors du chargement des bibliothèques:', error);
      }
    };

    if (!chordLibrary) {
      loadChordLibraries();
    }
  }, [chordLibrary]);

  // Bibliothèque active
  const activeLibrary = chordLibrary || loadedLibraries[selectedInstrument] || loadedLibraries['guitar'];

  // Obtenir l'accord actuel
  const getCurrentChord = (): ChordPosition | null => {
    if (!activeLibrary?.chords[selectedKey]) return null;
    
    const variations = activeLibrary.chords[selectedKey];
    const variation = variations.find(v => v.suffix === selectedSuffix);
    
    if (!variation || !variation.positions[selectedPosition]) return null;
    
    return variation.positions[selectedPosition];
  };

  // Filtrer les accords selon les critères
  const getFilteredChords = () => {
    if (!activeLibrary) return [];
    
    const allChords: Array<{
      key: string;
      variation: ChordVariation;
      position: ChordPosition;
      positionIndex: number;
    }> = [];

    Object.entries(activeLibrary.chords).forEach(([key, variations]) => {
      variations.forEach(variation => {
        variation.positions.forEach((position, index) => {
          const matchesSearch = searchTerm === '' || 
            variation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            key.toLowerCase().includes(searchTerm.toLowerCase());
          
          const matchesDifficulty = difficultyFilter === 'all' || 
            position.difficulty === difficultyFilter;
          
          if (matchesSearch && matchesDifficulty) {
            allChords.push({ key, variation, position, positionIndex: index });
          }
        });
      });
    });

    return allChords;
  };

  // Jouer l'accord ou l'arpège
  const playChord = async (chord: ChordPosition) => {
    if (!midiPlayer || !chord) return;

    setIsPlaying(true);
    try {
      if (playMode === 'chord') {
        await midiPlayer.playChord(chord, 2000);
      } else {
        await midiPlayer.playArpeggio(chord, 'ascending', 100, 2000);
      }
    } catch (error) {
      console.error('Erreur lors de la lecture:', error);
    } finally {
      setIsPlaying(false);
    }
  };

  // Arrêter la lecture
  const stopPlayback = () => {
    if (midiPlayer) {
      midiPlayer.stopAll();
      setIsPlaying(false);
    }
  };

  // Rendu du diagramme d'accord
  const renderChordDiagram = (chord: ChordPosition) => {
    if (!activeLibrary) return null;

    const strings = activeLibrary.strings;
    const frets = chord.frets;
    const fingers = chord.fingers;
    const barres = chord.barres;

    return (
      <div className="chord-diagram bg-white border rounded-lg p-4 shadow-sm">
        <svg width="200" height="240" viewBox="0 0 200 240">
          {/* Cordes */}
          {Array.from({ length: strings }, (_, i) => (
            <line
              key={`string-${i}`}
              x1={30 + i * 28}
              y1={40}
              x2={30 + i * 28}
              y2={200}
              stroke="#333"
              strokeWidth="1"
            />
          ))}
          
          {/* Frettes */}
          {Array.from({ length: 6 }, (_, i) => (
            <line
              key={`fret-${i}`}
              x1={30}
              y1={40 + i * 26}
              x2={30 + (strings - 1) * 28}
              y2={40 + i * 26}
              stroke="#333"
              strokeWidth={i === 0 ? "3" : "1"}
            />
          ))}
          
          {/* Barrés */}
          {barres.map((barre, index) => {
            const fretY = 40 + (barre.fret - chord.baseFret + 1) * 26 - 13;
            const startX = 30 + (barre.fromString - 1) * 28;
            const endX = 30 + (barre.toString - 1) * 28;
            
            return (
              <g key={`barre-${index}`}>
                <line
                  x1={startX}
                  y1={fretY}
                  x2={endX}
                  y2={fretY}
                  stroke="#e74c3c"
                  strokeWidth="8"
                  strokeLinecap="round"
                />
                <text
                  x={startX - 15}
                  y={fretY + 4}
                  fontSize="12"
                  fill="#e74c3c"
                  fontWeight="bold"
                >
                  {barre.fret}
                </text>
              </g>
            );
          })}
          
          {/* Doigtés */}
          {frets.map((fret, stringIndex) => {
            if (fret === -1) {
              // Corde non jouée
              return (
                <g key={`finger-${stringIndex}`}>
                  <text
                    x={30 + stringIndex * 28}
                    y={25}
                    fontSize="16"
                    textAnchor="middle"
                    fill="#e74c3c"
                    fontWeight="bold"
                  >
                    ×
                  </text>
                </g>
              );
            } else if (fret === 0) {
              // Corde à vide
              return (
                <g key={`finger-${stringIndex}`}>
                  <circle
                    cx={30 + stringIndex * 28}
                    cy={20}
                    r="8"
                    fill="none"
                    stroke="#27ae60"
                    strokeWidth="2"
                  />
                </g>
              );
            } else {
              // Doigt sur frette
              const fretY = 40 + (fret - chord.baseFret + 1) * 26 - 13;
              const isInBarre = barres.some(barre => 
                barre.fret === fret && 
                stringIndex + 1 >= barre.fromString && 
                stringIndex + 1 <= barre.toString
              );
              
              if (isInBarre) return null;
              
              return (
                <g key={`finger-${stringIndex}`}>
                  <circle
                    cx={30 + stringIndex * 28}
                    cy={fretY}
                    r="10"
                    fill="#3498db"
                    stroke="#2980b9"
                    strokeWidth="2"
                  />
                  <text
                    x={30 + stringIndex * 28}
                    y={fretY + 4}
                    fontSize="12"
                    textAnchor="middle"
                    fill="white"
                    fontWeight="bold"
                  >
                    {fingers[stringIndex] || ''}
                  </text>
                </g>
              );
            }
          })}
          
          {/* Numéros de frettes */}
          <text x="10" y="70" fontSize="12" fill="#666">{chord.baseFret}</text>
          <text x="10" y="96" fontSize="12" fill="#666">{chord.baseFret + 1}</text>
          <text x="10" y="122" fontSize="12" fill="#666">{chord.baseFret + 2}</text>
          <text x="10" y="148" fontSize="12" fill="#666">{chord.baseFret + 3}</text>
          <text x="10" y="174" fontSize="12" fill="#666">{chord.baseFret + 4}</text>
          
          {/* Notes */}
          <text x="100" y="230" fontSize="10" textAnchor="middle" fill="#666">
            {chord.notes.filter(note => note !== '').join(' - ')}
          </text>
        </svg>
      </div>
    );
  };

  const currentChordData = getCurrentChord();
  const filteredChords = getFilteredChords();

  if (!activeLibrary) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-6">
          <div className="text-center">
            <Music className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500">Chargement des bibliothèques d'accords...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* En-tête avec sélecteurs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Music className="h-6 w-6" />
            Grille Complète d'Accords - {activeLibrary.instrument}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* Sélection d'instrument */}
            <div>
              <label className="text-sm font-medium mb-2 block">Instrument</label>
              <Select value={selectedInstrument} onValueChange={setSelectedInstrument}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.keys(loadedLibraries).map(instrument => {
                    const displayName = {
                      'guitar-standard': 'Guitare - Accordage Standard (EADGBE)',
                      'guitar-open-g': 'Guitare - Open G (DGDGBD)',
                      'guitar-drop-d': 'Guitare - Drop D (DADGBE)',
                      'ukulele-gcea': 'Ukulélé - GCEA',
                      'ukulele-gcea-complete': 'Ukulélé - GCEA Complet',
                      'guitar-basic': 'Guitare - Base',
                      'guitar-open-g-basic': 'Guitare - Open G Base',
                      'guitar-standard-basic': 'Guitare - Standard Base'
                    }[instrument] || instrument.charAt(0).toUpperCase() + instrument.slice(1);
                    
                    return (
                      <SelectItem key={instrument} value={instrument}>
                        {displayName}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Sélection de tonalité */}
            <div>
              <label className="text-sm font-medium mb-2 block">Tonalité</label>
              <Select value={selectedKey} onValueChange={setSelectedKey}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {activeLibrary.keys.map(key => (
                    <SelectItem key={key} value={key}>{key}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sélection de type d'accord */}
            <div>
              <label className="text-sm font-medium mb-2 block">Type d'accord</label>
              <Select value={selectedSuffix} onValueChange={setSelectedSuffix}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {activeLibrary.suffixes.map(suffix => (
                    <SelectItem key={suffix} value={suffix}>
                      {suffix === 'major' ? 'Majeur' : 
                       suffix === 'minor' ? 'Mineur' : 
                       suffix === '7' ? 'Septième' :
                       suffix === 'maj7' ? 'Majeur 7' :
                       suffix === 'm7' ? 'Mineur 7' :
                       suffix === 'sus2' ? 'Sus2' :
                       suffix === 'sus4' ? 'Sus4' :
                       suffix === 'dim' ? 'Diminué' :
                       suffix === 'aug' ? 'Augmenté' :
                       suffix === '5' ? 'Power Chord' :
                       suffix}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sélection de position */}
            <div>
              <label className="text-sm font-medium mb-2 block">Position</label>
              <Select 
                value={selectedPosition.toString()} 
                onValueChange={(value) => setSelectedPosition(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {activeLibrary.chords[selectedKey]?.find(v => v.suffix === selectedSuffix)?.positions.map((pos, index) => (
                    <SelectItem key={index} value={index.toString()}>
                      Position {index + 1} ({pos.position})
                    </SelectItem>
                  )) || []}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Filtres et recherche */}
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <Input
                placeholder="Rechercher un accord..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous niveaux</SelectItem>
                  <SelectItem value="beginner">Débutant</SelectItem>
                  <SelectItem value="intermediate">Intermédiaire</SelectItem>
                  <SelectItem value="advanced">Avancé</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contenu principal avec onglets */}
      <Tabs defaultValue="current" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="current">Accord Actuel</TabsTrigger>
          <TabsTrigger value="browse">Parcourir ({filteredChords.length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="current" className="space-y-4">
          {currentChordData ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Diagramme d'accord */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>
                      {activeLibrary.chords[selectedKey]?.find(v => v.suffix === selectedSuffix)?.name}
                    </span>
                    <div className="flex gap-2">
                      <Badge variant={currentChordData.difficulty === 'beginner' ? 'default' : 
                                   currentChordData.difficulty === 'intermediate' ? 'secondary' : 'destructive'}>
                        {currentChordData.difficulty === 'beginner' ? 'Débutant' :
                         currentChordData.difficulty === 'intermediate' ? 'Intermédiaire' : 'Avancé'}
                      </Badge>
                      <Badge variant="outline">{currentChordData.position}</Badge>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {renderChordDiagram(currentChordData)}
                </CardContent>
              </Card>

              {/* Contrôles MIDI et informations */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Volume2 className="h-5 w-5" />
                    Lecture MIDI
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Contrôles de lecture */}
                  <div className="flex gap-2">
                    <div className="flex flex-col gap-2 w-full">
                      <div className="flex gap-2">
                        <Select value={playMode} onValueChange={(value: 'chord' | 'arpeggio') => setPlayMode(value)}>
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="chord">Accord</SelectItem>
                            <SelectItem value="arpeggio">Arpège</SelectItem>
                          </SelectContent>
                        </Select>
                        
                        {playMode === 'arpeggio' && (
                          <Select value={arpeggioPattern} onValueChange={setArpeggioPattern}>
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="ascending">Montant</SelectItem>
                              <SelectItem value="descending">Descendant</SelectItem>
                              <SelectItem value="alternating">Alterné</SelectItem>
                              <SelectItem value="up">Haut</SelectItem>
                              <SelectItem value="down">Bas</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                      <Button
                        onClick={() => playChord(currentChordData)}
                        disabled={isPlaying || !midiPlayer}
                        className="flex-1"
                      >
                        {isPlaying ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                        {playMode === 'chord' ? 'Jouer Accord' : 'Jouer Arpège'}
                      </Button>
                    </div>
                    
                    {isPlaying && (
                      <Button onClick={stopPlayback} variant="outline">
                        <Pause className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {/* Mode de lecture */}
                  <div className="flex gap-2">
                    <Button
                      variant={playMode === 'chord' ? 'default' : 'outline'}
                      onClick={() => setPlayMode('chord')}
                      className="flex-1"
                    >
                      Accord
                    </Button>
                    <Button
                      variant={playMode === 'arpeggio' ? 'default' : 'outline'}
                      onClick={() => setPlayMode('arpeggio')}
                      className="flex-1"
                    >
                      Arpège
                    </Button>
                  </div>

                  {/* Informations sur l'accord */}
                  <div className="space-y-2 text-sm">
                    <div><strong>Frette de base:</strong> {currentChordData.baseFret}</div>
                    <div><strong>Notes MIDI:</strong> {currentChordData.midi.filter(n => n !== -1).join(', ')}</div>
                    <div><strong>Notes:</strong> {currentChordData.notes.filter(n => n !== '').join(' - ')}</div>
                    <div><strong>Doigtés:</strong> {currentChordData.fingers.filter(f => f !== 0).join('-')}</div>
                    {currentChordData.barres.length > 0 && (
                      <div><strong>Barrés:</strong> Frette {currentChordData.barres.map(b => b.fret).join(', ')}</div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">Aucun accord trouvé pour cette combinaison</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="browse" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredChords.map((item, index) => (
              <Card key={`${item.key}-${item.variation.suffix}-${item.positionIndex}`} className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => {
                      setSelectedKey(item.key);
                      setSelectedSuffix(item.variation.suffix);
                      setSelectedPosition(item.positionIndex);
                      onChordSelect?.(item.position);
                    }}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center justify-between">
                    <span>{item.variation.name}</span>
                    <Badge variant={item.position.difficulty === 'beginner' ? 'default' : 
                                 item.position.difficulty === 'intermediate' ? 'secondary' : 'destructive'}
                           className="text-xs">
                      {item.position.difficulty === 'beginner' ? 'Débutant' :
                       item.position.difficulty === 'intermediate' ? 'Inter.' : 'Avancé'}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-center mb-2">
                    <div className="scale-75 origin-center">
                      {renderChordDiagram(item.position)}
                    </div>
                  </div>
                  <div className="text-center text-sm text-gray-600">
                    <div>Position: {item.position.position}</div>
                    <div>Frette: {item.position.baseFret}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {filteredChords.length === 0 && (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">Aucun accord trouvé avec ces critères</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CompleteChordGrid;