import Link from "next/link"
import { Play } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

interface PlaylistCardProps {
  playlist: {
    id: number
    title: string
    description: string
    cover_url: string | null; // Changed from image to cover_url, allow null
  }
}

export function PlaylistCard({ playlist }: PlaylistCardProps) {
  return (
    <Link href={`/playlists/${playlist.id}`}>
      <Card className="overflow-hidden group">
        <div className="aspect-square relative">
          <img src={playlist.cover_url || "/images/placeholder-song.svg"} alt={playlist.title} className="w-full h-full object-cover" />
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <Button size="icon" className="h-12 w-12 rounded-full">
              <Play className="h-6 w-6" />
            </Button>
          </div>
        </div>
        <CardContent className="p-3">
          <h3 className="font-medium line-clamp-1">{playlist.title}</h3>
          <p className="text-sm text-muted-foreground line-clamp-1">{playlist.description}</p>
        </CardContent>
      </Card>
    </Link>
  )
}
