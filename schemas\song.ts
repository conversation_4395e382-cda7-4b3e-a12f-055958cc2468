import { z } from 'zod';

// Enums
export const VisibilityType = z.enum(['public', 'private', 'unlisted']);
export type VisibilityType = z.infer<typeof VisibilityType>;

export const AIContentOrigin = z.enum(['100%_ia', 'hybrid', 'full_human']);
export type AIContentOrigin = z.infer<typeof AIContentOrigin>;

export const SongStatus = z.enum(['draft', 'published', 'archived']);
export type SongStatus = z.infer<typeof SongStatus>;

// Common schemas
export const ChordDiagramSchema = z.object({
  instrument: z.string(), // 'guitar', 'piano', 'ukulele', etc.
  key: z.string(), // Chord root note (e.g., 'C', 'D#', 'F')
  suffix: z.string(), // Chord type (e.g., 'm', '7', 'maj7', 'sus4')
  variantIdx: z.number().int().nonnegative(), // Index of the chord variant
  positions: z.array(z.number().int()), // Fret positions or MIDI notes
  fingering: z.string().optional(), // Fingering pattern (e.g., 'T1234')
  barres: z.array(z.object({
    fromString: z.number().int(),
    toString: z.number().int(),
    fret: z.number().int()
  })).optional(),
  baseFret: z.number().int().optional(),
  midi: z.array(z.number().int()).optional(), // MIDI note numbers
  frets: z.array(z.number().int()).optional(), // Fret positions for each string
  tuning: z.array(z.string()).optional(), // Tuning of the instrument
  position: z.number().int().optional(), // Capo position
});

export const AudioAnalysisSchema = z.object({
  bpm: z.number().positive().nullable(),
  key: z.string().nullable(),
  duration: z.number().positive(),
  waveform: z.array(z.number()).optional(),
  loudness: z.number().optional(),
  tempoConfidence: z.number().optional(),
  keyConfidence: z.number().optional(),
  timeSignature: z.number().int().positive().optional(),
  sections: z.array(z.any()).optional(),
  segments: z.array(z.any()).optional(),
  bars: z.array(z.any()).optional(),
  beats: z.array(z.any()).optional(),
  tatums: z.array(z.any()).optional(),
});

// Main song form schema
export const songFormSchema = z.object({
  // Core metadata
  id: z.string().uuid().optional(),
  creator_user_id: z.string().uuid(),
  band_id: z.string().uuid().nullable().optional(),
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().max(2000).default(''),
  
  // Categorization
  genres: z.array(z.string()).default([]),
  moods: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  
  // Media
  cover_art_url: z.string().url().nullable().default(null),
  audio_file_path: z.string().min(1, 'Audio file path is required'),
  audio_url: z.string().url().optional(),
  
  // Musical attributes
  duration_ms: z.number().int().nonnegative().default(0),
  bpm: z.number().int().positive().nullable().default(null),
  musical_key: z.string().default(''),
  time_signature: z.string().default('4/4'),
  
  // Content
  lyrics: z.string().default(''),
  chords: z.string().default(''),
  chords_diagrams: z.record(ChordDiagramSchema).optional(),
  structure: z.string().default(''),
  
  // Visibility and status
  is_public: z.boolean().default(false),
  is_explicit: z.boolean().default(false),
  visibility: VisibilityType.default('private'),
  status: SongStatus.default('draft'),
  is_archived: z.boolean().default(false),
  
  // AI and metadata
  ai_content_origin: AIContentOrigin.optional(),
  ai_assist_percentage: z.number().min(0).max(100).optional(),
  studio_name: z.string().optional(),
  
  // External links
  spotify_url: z.string().url().optional(),
  youtube_url: z.string().url().optional(),
  
  // Versioning
  version_notes: z.string().optional(),
  is_major_version: z.boolean().optional(),
  
  // User preferences
  allow_comments: z.boolean().default(true),
  allow_downloads: z.boolean().default(false),
  
  // Timestamps
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
  
  // Editor state
  editor_data: z.record(z.any()).optional(),
  
  // Audio analysis
  audio_analysis: AudioAnalysisSchema.optional(),
  
  // Legacy/compatibility fields
  artist_name: z.string().optional(), // Legacy, use creator_user_id/band_id instead
  genre: z.array(z.string()).optional(), // Legacy alias for genres
  mood: z.string().optional(), // Legacy alias for moods[0]
  
  // Additional metadata (kept for backward compatibility)
  featured_artists: z.string().optional(),
  composer_name: z.string().optional(),
  writers: z.string().optional(),
  producers: z.string().optional(),
  record_label: z.string().optional(),
  isrc: z.string().optional(),
  upc: z.string().optional(),
  release_date: z.string().datetime().nullable().optional(),
  
  // Additional content
  story: z.string().optional(),
  custom_fields: z.record(z.any()).optional(),
  
  // For form state (not persisted to DB)
  _file: z.any().optional(),
  _coverFile: z.any().optional(),
  _isDirty: z.boolean().default(false).optional(),
});

// Types
export type SongFormValues = z.infer<typeof songFormSchema>;

// Partial version for updates
export const partialSongFormSchema = songFormSchema.partial();
export type PartialSongFormValues = z.infer<typeof partialSongFormSchema>;

// Schema for creating a new song
export const createSongSchema = songFormSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true,
  creator_user_id: true, // Will be set by the server
  audio_file_path: true, // Will be set after upload
});

// Schema for updating an existing song
export const updateSongSchema = songFormSchema.partial().required({ id: true });

// Schema for song version
export const songVersionSchema = z.object({
  id: z.string().uuid(),
  song_id: z.string().uuid(),
  version_number: z.number().int().positive(),
  version_name: z.string().nullable().optional(),
  song_data: songFormSchema,
  created_at: z.string().datetime(),
  creator_user_id: z.string().uuid(),
  description: z.string().nullable().optional(),
  parent_version_id: z.string().uuid().nullable().optional(),
  is_major_version: z.boolean().default(false),
  user_notes: z.string().nullable().optional(),
});

export type SongVersion = z.infer<typeof songVersionSchema>;

// Schema for song filters
export const songFiltersSchema = z.object({
  search: z.string().optional(),
  genres: z.array(z.string()).optional(),
  moods: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  min_bpm: z.number().int().positive().optional(),
  max_bpm: z.number().int().positive().optional(),
  key: z.string().optional(),
  is_public: z.boolean().optional(),
  is_explicit: z.boolean().optional(),
  status: z.array(SongStatus).optional(),
  created_after: z.string().datetime().optional(),
  created_before: z.string().datetime().optional(),
  sort_by: z.enum(['created_at', 'updated_at', 'title', 'bpm']).optional(),
  sort_order: z.enum(['asc', 'desc']).optional(),
  limit: z.number().int().positive().default(20),
  offset: z.number().int().nonnegative().default(0),
});

export type SongFilters = z.infer<typeof songFiltersSchema>;
