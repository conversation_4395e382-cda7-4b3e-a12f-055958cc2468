import { FieldErrors, Control } from 'react-hook-form';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RHFTextField, RHFSelect, RHFMultiSelectChip, RHFTextarea } from '@/components/hook-form'; // RHFDatePicker might not be needed here
import { SelectItem } from '@/components/ui/select';
import { SongFormValues } from './song-schema'; 
import { GENRES_OPTIONS, SUBGENRES_OPTIONS, MOODS_OPTIONS, THEMES_OPTIONS, INSTRUMENTS_OPTIONS, MUSICAL_KEY_OPTIONS as MUSICAL_KEYS, TIME_SIGNATURE_OPTIONS } from './song-options';
import React from 'react';

interface SongFormProductionDetailsTabProps {
  control: Control<SongFormValues>;
  errors?: FieldErrors<SongFormValues>;
  // Add any other specific props if needed, e.g., for dynamic options
}

export const SongFormProductionDetailsTab: React.FC<SongFormProductionDetailsTabProps> = ({ 
  control,
  errors,
}) => {
  return (
    <Card>
      <CardHeader><CardTitle>Production & Détails</CardTitle></CardHeader>
      <CardContent className="space-y-6">
        
        {/* Section: Musical Characteristics */}
        <h3 className="text-lg font-semibold mb-2">Caractéristiques Musicales</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <RHFMultiSelectChip name="genres" label="Genre(s) musical(aux)" options={GENRES_OPTIONS} placeholder="Ajouter un genre..." />
          <RHFMultiSelectChip name="subgenre" label="Sous-genre(s)" options={SUBGENRES_OPTIONS} placeholder="Ajouter un sous-genre..." />
          <RHFMultiSelectChip name="moods" label="Ambiance(s)" options={MOODS_OPTIONS} placeholder="Ajouter une ambiance..." />
          <RHFMultiSelectChip name="themes" label="Thème(s)" options={THEMES_OPTIONS} placeholder="Ajouter un thème..." />
          <RHFTextField name="bpm" label="BPM" type="number" placeholder="Ex: 120"/>
          <RHFSelect name="musical_key" label="Tonalité">
            {MUSICAL_KEYS.map(key => <SelectItem key={key.value} value={key.value}>{key.label}</SelectItem>)}
          </RHFSelect>
          <RHFSelect name="time_signature" label="Signature rythmique">
            {TIME_SIGNATURE_OPTIONS.map((sig) => <SelectItem key={sig.value} value={sig.value}>{sig.label}</SelectItem>)}
          </RHFSelect>
          <RHFTextField name="tuning_frequency" label="Fréquence d'accordage (Hz)" type="number" placeholder="Ex: 440"/>
          <RHFMultiSelectChip name="instruments" label="Instruments principaux" options={INSTRUMENTS_OPTIONS} placeholder="Ajouter un instrument..." />
        </div>

        {/* Section: Credits & Publishing */}
        <h3 className="text-lg font-semibold pt-4 border-t mt-6 mb-2">Crédits & Publication</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <RHFTextField name="composer_name" label="Compositeur(s)" placeholder="Nom du ou des compositeurs"/>
          <RHFMultiSelectChip name="writers" label="Auteur(s) (Paroliers)" options={[]} placeholder="Ajouter un auteur..." />
          <RHFMultiSelectChip name="producers" label="Producteur(s)" options={[]} placeholder="Ajouter un producteur..." />
          <RHFTextField name="mixing_engineer" label="Ingénieur Mixage" placeholder="Nom de l'ingénieur mixage"/>
          <RHFTextField name="mastering_engineer" label="Ingénieur Mastering" placeholder="Nom de l'ingénieur mastering"/>
          <RHFTextarea name="artwork_credits" label="Crédits Pochette" rows={2} placeholder="Artiste, photographe..."/>
          <RHFTextField name="record_label" label="Label / Maison de disque" placeholder="Nom du label"/>
          <RHFTextField name="publisher_name" label="Éditeur musical" placeholder="Nom de l'éditeur"/>
          <RHFTextarea name="copyright_notice" label="Avis de Copyright" rows={2} placeholder="Ex: © 2024 Artiste"/>
          <RHFTextarea name="licensing_info" label="Informations de licence" rows={2} placeholder="Ex: Creative Commons, Tous droits réservés..."/>
          <RHFTextField name="isrc_code" label="Code ISRC" placeholder="Ex: CC-XXX-YY-NNNNN"/>
          <RHFTextField name="iswc_code" label="Code ISWC" placeholder="Ex: T-123.456.789-Z"/>
          <RHFTextField name="upc_code" label="Code UPC/EAN (Code barre)" placeholder="Ex: 123456789012"/>
        </div>

        {/* Section: Notes & Advanced */}
        <h3 className="text-lg font-semibold pt-4 border-t mt-6 mb-2">Notes & Avancé</h3>
        <RHFTextarea name="bloc_note" label="Bloc-note général (privé)" rows={5} placeholder="Vos notes personnelles, idées, TODOs..."/>
        <RHFTextarea name="performance_notes" label="Notes de performance (privé)" rows={3} placeholder="Indications pour le live, difficultés..."/>
        <RHFTextarea name="custom_css" label="CSS Personnalisé (Avancé)" rows={3} placeholder=".my-custom-class { color: red; }"/>
        
      </CardContent>
    </Card>
  );
};
