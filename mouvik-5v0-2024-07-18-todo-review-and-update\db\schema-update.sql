-- Mise à jour du schéma pour les albums
ALTER TABLE albums 
ADD COLUMN IF NOT EXISTS artist_name <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS is_explicit BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS upc VARCHAR(50),
ADD COLUMN IF NOT EXISTS label VARCHAR(100),
ADD COLUMN IF NOT EXISTS total_duration INTEGER,
ADD COLUMN IF NOT EXISTS release_type VARCHAR(50) CHECK (release_type IN ('album', 'ep', 'single', 'compilation')),
ADD COLUMN IF NOT EXISTS copyright_text VARCHAR(255),
ADD COLUMN IF NOT EXISTS publishing_rights VARCHAR(255),
ADD COLUMN IF NOT EXISTS visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'private', 'unlisted')),
ADD COLUMN IF NOT EXISTS scheduled_for TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS language VARCHAR(50),
ADD COLUMN IF NOT EXISTS original_release_date DATE,
ADD COLUMN IF NOT EXISTS recording_location VARCHAR(255);

-- Mise à jour du schéma pour les morceaux
ALTER TABLE songs 
ADD COLUMN IF NOT EXISTS artist_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS isrc VARCHAR(50),
ADD COLUMN IF NOT EXISTS language VARCHAR(50),
ADD COLUMN IF NOT EXISTS mood VARCHAR(100),
ADD COLUMN IF NOT EXISTS tempo VARCHAR(50),
ADD COLUMN IF NOT EXISTS lyrics_language VARCHAR(50),
ADD COLUMN IF NOT EXISTS recording_location VARCHAR(255),
ADD COLUMN IF NOT EXISTS recording_date DATE,
ADD COLUMN IF NOT EXISTS writers TEXT[],
ADD COLUMN IF NOT EXISTS producers TEXT[],
ADD COLUMN IF NOT EXISTS featured_artists TEXT[],
ADD COLUMN IF NOT EXISTS visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'private', 'unlisted')),
ADD COLUMN IF NOT EXISTS scheduled_for TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS stems_available BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS instrumental_version_url TEXT,
ADD COLUMN IF NOT EXISTS acapella_version_url TEXT,
ADD COLUMN IF NOT EXISTS original_song_id UUID REFERENCES songs(id),
ADD COLUMN IF NOT EXISTS is_remix BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS remix_of UUID REFERENCES songs(id),
ADD COLUMN IF NOT EXISTS allow_downloads BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS allow_comments BOOLEAN DEFAULT TRUE;

-- Table pour les collaborateurs (artistes, producteurs, etc.)
CREATE TABLE IF NOT EXISTS collaborators (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('song', 'album')),
  resource_id UUID NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  name VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les crédits (plus détaillée que collaborators)
CREATE TABLE IF NOT EXISTS credits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('song', 'album')),
  resource_id UUID NOT NULL,
  credit_type VARCHAR(50) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(100),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les versions d'un morceau
CREATE TABLE IF NOT EXISTS song_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  song_id UUID NOT NULL REFERENCES songs(id) ON DELETE CASCADE,
  version_name VARCHAR(100) NOT NULL,
  audio_url TEXT,
  version_number INTEGER NOT NULL,
  is_current BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les pistes d'un morceau (stems)
CREATE TABLE IF NOT EXISTS song_stems (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  song_id UUID NOT NULL REFERENCES songs(id) ON DELETE CASCADE,
  stem_type VARCHAR(50) NOT NULL,
  audio_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les relations entre albums et morceaux
CREATE TABLE IF NOT EXISTS album_songs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  album_id UUID NOT NULL REFERENCES albums(id) ON DELETE CASCADE,
  song_id UUID NOT NULL REFERENCES songs(id) ON DELETE CASCADE,
  track_number INTEGER NOT NULL,
  disc_number INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(album_id, song_id)
);

-- Mise à jour des triggers
DO $$
BEGIN
  -- Trigger pour song_versions
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_song_versions_updated_at') THEN
    CREATE TRIGGER update_song_versions_updated_at
    BEFORE UPDATE ON song_versions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;
