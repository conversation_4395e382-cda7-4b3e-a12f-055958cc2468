/**
 * 🎼 CHORD PROGRESSION BUILDER - Construction de Progressions
 * 
 * Composant pour construire des progressions d'accords avec drag & drop
 * Intégration complète avec AI Composer Workflow
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useCallback, useMemo, useRef } from 'react';
import { 
  Plus, Play, Pause, Save, Download, Upload, Trash2, 
  Copy, RotateCcw, RotateCw, Music, Sparkles, FileText,
  Grid3X3, List, Clock, Volume2
} from 'lucide-react';
import { useChordSystem } from '../providers/ChordSystemProvider';
import { useChordLibrary } from '../hooks/useChordLibrary';
import { ChordDiagramViewer } from './ChordDiagramViewer';
import { ChordPickerModal } from './ChordPickerModal';
import type { 
  UnifiedChordPosition, 
  ChordProgression,
  ChordPlacement,
  ArpeggioPattern 
} from '../types/chord-system';

// ============================================================================
// TYPES POUR L'INTÉGRATION AI COMPOSER
// ============================================================================

interface AIComposerIntegration {
  /** Callback pour insérer dans l'éditeur de texte */
  onInsertToTextEditor?: (chordText: string, position?: number) => void;
  /** Callback pour ajouter à la structure du morceau */
  onAddToSongStructure?: (progression: ChordProgression, sectionType: string) => void;
  /** Callback pour synchroniser avec la timeline */
  onSyncWithTimeline?: (progression: ChordProgression) => void;
  /** Mode d'intégration actuel */
  integrationMode?: 'standalone' | 'text-editor' | 'song-structure' | 'timeline';
  /** Position actuelle dans l'éditeur de texte */
  textEditorPosition?: number;
  /** Section active dans la structure */
  activeSongSection?: string;
}

interface ChordProgressionBuilderProps extends AIComposerIntegration {
  /** Progression initiale */
  initialProgression?: ChordProgression;
  /** Mode d'affichage */
  viewMode?: 'compact' | 'detailed' | 'timeline';
  /** Afficher les contrôles d'intégration */
  showIntegrationControls?: boolean;
  /** Afficher les suggestions IA */
  showAISuggestions?: boolean;
  /** Callback lors des changements */
  onProgressionChange?: (progression: ChordProgression) => void;
  /** Classe CSS personnalisée */
  className?: string;
}

interface DraggedChord {
  chord: UnifiedChordPosition;
  sourceIndex?: number;
  dragOffset: { x: number; y: number };
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Carte d'accord dans la progression avec drag & drop
 */
const ProgressionChordCard: React.FC<{
  chord: UnifiedChordPosition;
  index: number;
  isPlaying: boolean;
  isSelected: boolean;
  viewMode: string;
  onSelect: (index: number) => void;
  onRemove: (index: number) => void;
  onDragStart: (chord: UnifiedChordPosition, index: number, event: React.DragEvent) => void;
  onDragEnd: () => void;
  onPlay: (chord: UnifiedChordPosition) => void;
}> = ({ 
  chord, index, isPlaying, isSelected, viewMode, 
  onSelect, onRemove, onDragStart, onDragEnd, onPlay 
}) => {
  const handleDragStart = (e: React.DragEvent) => {
    onDragStart(chord, index, e);
  };

  if (viewMode === 'compact') {
    return (
      <div
        draggable
        onDragStart={handleDragStart}
        onDragEnd={onDragEnd}
        className={`
          relative p-2 border rounded-lg cursor-move transition-all
          ${isSelected ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'}
          ${isPlaying ? 'ring-2 ring-green-400' : ''}
        `}
        onClick={() => onSelect(index)}
      >
        {/* Numéro de position */}
        <div className="absolute -top-2 -left-2 w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
          {index + 1}
        </div>

        {/* Nom de l'accord */}
        <div className="text-center">
          <h4 className="text-lg font-bold text-gray-900 mb-1">{chord.chord}</h4>
          <p className="text-xs text-gray-500 capitalize">{chord.instrument}</p>
        </div>

        {/* Contrôles */}
        <div className="flex justify-center mt-2 space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onPlay(chord);
            }}
            className="p-1 text-gray-600 hover:text-blue-600 rounded"
            title="Écouter"
          >
            <Volume2 className="w-3 h-3" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onRemove(index);
            }}
            className="p-1 text-gray-600 hover:text-red-600 rounded"
            title="Supprimer"
          >
            <Trash2 className="w-3 h-3" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      onDragEnd={onDragEnd}
      className={`
        relative p-4 border rounded-lg cursor-move transition-all
        ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
        ${isPlaying ? 'ring-2 ring-green-400' : ''}
      `}
      onClick={() => onSelect(index)}
    >
      {/* Numéro de position */}
      <div className="absolute -top-2 -left-2 w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
        {index + 1}
      </div>

      {/* Diagramme */}
      <div className="w-full h-24 mb-2">
        <ChordDiagramViewer 
          chord={chord} 
          size="small" 
          interactive={false}
          showLabels={false}
        />
      </div>

      {/* Informations */}
      <div className="text-center">
        <h4 className="text-sm font-semibold text-gray-900">{chord.chord}</h4>
        <p className="text-xs text-gray-500 capitalize">{chord.instrument} • {chord.difficulty}</p>
      </div>

      {/* Contrôles */}
      <div className="flex justify-center mt-2 space-x-1">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onPlay(chord);
          }}
          className="p-1 text-gray-600 hover:text-blue-600 rounded"
          title="Écouter"
        >
          <Volume2 className="w-4 h-4" />
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onRemove(index);
          }}
          className="p-1 text-gray-600 hover:text-red-600 rounded"
          title="Supprimer"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

/**
 * Zone de drop pour ajouter des accords
 */
const DropZone: React.FC<{
  onDrop: (chord: UnifiedChordPosition, targetIndex: number) => void;
  targetIndex: number;
  isDragOver: boolean;
  onDragOver: (e: React.DragEvent) => void;
  onDragLeave: () => void;
}> = ({ onDrop, targetIndex, isDragOver, onDragOver, onDragLeave }) => {
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const chordData = e.dataTransfer.getData('application/json');
    if (chordData) {
      try {
        const chord = JSON.parse(chordData);
        onDrop(chord, targetIndex);
      } catch (error) {
        console.error('Erreur lors du drop:', error);
      }
    }
  };

  return (
    <div
      onDrop={handleDrop}
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      className={`
        w-16 h-20 border-2 border-dashed rounded-lg flex items-center justify-center transition-colors
        ${isDragOver 
          ? 'border-blue-500 bg-blue-50' 
          : 'border-gray-300 hover:border-gray-400'
        }
      `}
    >
      <Plus className={`w-6 h-6 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
    </div>
  );
};

/**
 * Contrôles d'intégration AI Composer
 */
const IntegrationControls: React.FC<{
  progression: ChordProgression;
  integrationMode?: string;
  onInsertToTextEditor?: (text: string) => void;
  onAddToSongStructure?: (progression: ChordProgression, sectionType: string) => void;
  onSyncWithTimeline?: (progression: ChordProgression) => void;
}> = ({ 
  progression, integrationMode, 
  onInsertToTextEditor, onAddToSongStructure, onSyncWithTimeline 
}) => {
  const [selectedSectionType, setSelectedSectionType] = useState('verse');

  const generateChordText = useCallback((format: 'simple' | 'detailed' = 'simple') => {
    if (format === 'simple') {
      return progression.chords.map(chord => chord.chord).join(' - ');
    }
    
    return progression.chords.map((chord, index) => 
      `${index + 1}. ${chord.chord} (${chord.instrument}, ${chord.difficulty})`
    ).join('\n');
  }, [progression.chords]);

  return (
    <div className="bg-gray-50 rounded-lg p-4 space-y-3">
      <h4 className="font-medium text-gray-900 flex items-center">
        <Sparkles className="w-4 h-4 mr-2" />
        Intégration AI Composer
      </h4>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {/* Insérer dans l'éditeur de texte */}
        {onInsertToTextEditor && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Éditeur de texte
            </label>
            <div className="space-y-1">
              <button
                onClick={() => onInsertToTextEditor(generateChordText('simple'))}
                className="w-full px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm flex items-center justify-center"
              >
                <FileText className="w-4 h-4 mr-2" />
                Insérer simple
              </button>
              <button
                onClick={() => onInsertToTextEditor(generateChordText('detailed'))}
                className="w-full px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 text-sm flex items-center justify-center"
              >
                <List className="w-4 h-4 mr-2" />
                Insérer détaillé
              </button>
            </div>
          </div>
        )}

        {/* Ajouter à la structure */}
        {onAddToSongStructure && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Structure du morceau
            </label>
            <select
              value={selectedSectionType}
              onChange={(e) => setSelectedSectionType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="verse">Couplet</option>
              <option value="chorus">Refrain</option>
              <option value="bridge">Pont</option>
              <option value="intro">Intro</option>
              <option value="outro">Outro</option>
              <option value="solo">Solo</option>
            </select>
            <button
              onClick={() => onAddToSongStructure(progression, selectedSectionType)}
              className="w-full px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm flex items-center justify-center"
            >
              <Grid3X3 className="w-4 h-4 mr-2" />
              Ajouter section
            </button>
          </div>
        )}

        {/* Synchroniser avec timeline */}
        {onSyncWithTimeline && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Timeline
            </label>
            <button
              onClick={() => onSyncWithTimeline(progression)}
              className="w-full px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-sm flex items-center justify-center"
            >
              <Clock className="w-4 h-4 mr-2" />
              Synchroniser
            </button>
            <p className="text-xs text-gray-500">
              Tempo: {progression.tempo} BPM
            </p>
          </div>
        )}
      </div>

      {/* Informations de la progression */}
      <div className="pt-2 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Tonalité: {progression.key}</span>
          <span>Mesure: {progression.timeSignature}</span>
          <span>{progression.chords.length} accord{progression.chords.length !== 1 ? 's' : ''}</span>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const ChordProgressionBuilder: React.FC<ChordProgressionBuilderProps> = ({
  initialProgression,
  viewMode = 'detailed',
  showIntegrationControls = true,
  showAISuggestions = true,
  onProgressionChange,
  integrationMode = 'standalone',
  onInsertToTextEditor,
  onAddToSongStructure,
  onSyncWithTimeline,
  textEditorPosition,
  activeSongSection,
  className = ''
}) => {
  const { state, actions } = useChordSystem();
  const { searchChords } = useChordLibrary();
  
  // État local
  const [showChordPicker, setShowChordPicker] = useState(false);
  const [selectedChordIndex, setSelectedChordIndex] = useState<number | null>(null);
  const [draggedChord, setDraggedChord] = useState<DraggedChord | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [currentViewMode, setCurrentViewMode] = useState(viewMode);
  
  const dropZoneRefs = useRef<(HTMLDivElement | null)[]>([]);

  // ============================================================================
  // PROGRESSION ACTUELLE
  // ============================================================================

  const currentProgression = useMemo(() => {
    return state.currentProgression || initialProgression || {
      id: crypto.randomUUID(),
      name: 'Nouvelle progression',
      chords: [],
      key: 'C',
      tempo: 120,
      timeSignature: '4/4',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }, [state.currentProgression, initialProgression]);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleAddChord = useCallback((chord: UnifiedChordPosition, targetIndex?: number) => {
    const newChords = [...currentProgression.chords];
    
    if (targetIndex !== undefined) {
      newChords.splice(targetIndex, 0, chord);
    } else {
      newChords.push(chord);
    }
    
    const updatedProgression = {
      ...currentProgression,
      chords: newChords,
      updatedAt: new Date().toISOString()
    };
    
    actions.setCurrentProgression?.(updatedProgression);
    onProgressionChange?.(updatedProgression);
  }, [currentProgression, actions, onProgressionChange]);

  const handleRemoveChord = useCallback((index: number) => {
    const newChords = currentProgression.chords.filter((_, i) => i !== index);
    
    const updatedProgression = {
      ...currentProgression,
      chords: newChords,
      updatedAt: new Date().toISOString()
    };
    
    actions.setCurrentProgression?.(updatedProgression);
    onProgressionChange?.(updatedProgression);
  }, [currentProgression, actions, onProgressionChange]);

  const handleReorderChords = useCallback((fromIndex: number, toIndex: number) => {
    const newChords = [...currentProgression.chords];
    const [movedChord] = newChords.splice(fromIndex, 1);
    newChords.splice(toIndex, 0, movedChord);
    
    const updatedProgression = {
      ...currentProgression,
      chords: newChords,
      updatedAt: new Date().toISOString()
    };
    
    actions.setCurrentProgression?.(updatedProgression);
    onProgressionChange?.(updatedProgression);
  }, [currentProgression, actions, onProgressionChange]);

  const handlePlayProgression = useCallback(async () => {
    if (currentProgression.chords.length === 0) return;
    
    try {
      await actions.playProgression(currentProgression);
    } catch (error) {
      console.error('Erreur lecture progression:', error);
    }
  }, [currentProgression, actions]);

  const handlePlayChord = useCallback(async (chord: UnifiedChordPosition) => {
    try {
      await actions.playChord(chord, { mode: state.playMode });
    } catch (error) {
      console.error('Erreur lecture accord:', error);
    }
  }, [actions, state.playMode]);

  // Drag & Drop handlers
  const handleDragStart = useCallback((chord: UnifiedChordPosition, sourceIndex: number, event: React.DragEvent) => {
    setDraggedChord({ chord, sourceIndex, dragOffset: { x: 0, y: 0 } });
    event.dataTransfer.setData('application/json', JSON.stringify(chord));
    event.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleDragEnd = useCallback(() => {
    setDraggedChord(null);
    setDragOverIndex(null);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(targetIndex);
  }, []);

  const handleDrop = useCallback((chord: UnifiedChordPosition, targetIndex: number) => {
    if (draggedChord?.sourceIndex !== undefined) {
      // Réorganisation interne
      handleReorderChords(draggedChord.sourceIndex, targetIndex);
    } else {
      // Ajout d'un nouvel accord
      handleAddChord(chord, targetIndex);
    }
    
    setDragOverIndex(null);
    setDraggedChord(null);
  }, [draggedChord, handleReorderChords, handleAddChord]);

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <div className={`chord-progression-builder ${className}`}>
      {/* En-tête */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Music className="w-6 h-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">
            {currentProgression.name}
          </h2>
          <span className="ml-3 px-2 py-1 bg-gray-100 text-gray-600 text-sm rounded-full">
            {currentProgression.chords.length} accord{currentProgression.chords.length !== 1 ? 's' : ''}
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* Contrôles de vue */}
          <div className="flex items-center border border-gray-300 rounded-lg">
            <button
              onClick={() => setCurrentViewMode('compact')}
              className={`p-2 ${currentViewMode === 'compact' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
              title="Vue compacte"
            >
              <List className="w-4 h-4" />
            </button>
            <button
              onClick={() => setCurrentViewMode('detailed')}
              className={`p-2 ${currentViewMode === 'detailed' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
              title="Vue détaillée"
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
          </div>

          {/* Contrôles de lecture */}
          <button
            onClick={handlePlayProgression}
            disabled={currentProgression.chords.length === 0}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {state.isPlaying ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
            {state.isPlaying ? 'Pause' : 'Jouer'}
          </button>

          {/* Ajouter accord */}
          <button
            onClick={() => setShowChordPicker(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Ajouter
          </button>
        </div>
      </div>

      {/* Zone de construction */}
      <div className="mb-6">
        {currentProgression.chords.length === 0 ? (
          <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
            <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Progression vide</h3>
            <p className="text-gray-600 mb-4">
              Commencez par ajouter des accords à votre progression
            </p>
            <button
              onClick={() => setShowChordPicker(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Ajouter le premier accord
            </button>
          </div>
        ) : (
          <div className={`
            ${currentViewMode === 'compact' 
              ? 'flex flex-wrap gap-3' 
              : 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4'
            }
          `}>
            {currentProgression.chords.map((chord, index) => (
              <React.Fragment key={`${chord.id}-${index}`}>
                {/* Zone de drop avant */}
                <DropZone
                  onDrop={(droppedChord) => handleDrop(droppedChord, index)}
                  targetIndex={index}
                  isDragOver={dragOverIndex === index}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragLeave={() => setDragOverIndex(null)}
                />
                
                {/* Carte d'accord */}
                <ProgressionChordCard
                  chord={chord}
                  index={index}
                  isPlaying={state.isPlaying && state.currentChord?.id === chord.id}
                  isSelected={selectedChordIndex === index}
                  viewMode={currentViewMode}
                  onSelect={setSelectedChordIndex}
                  onRemove={handleRemoveChord}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  onPlay={handlePlayChord}
                />
              </React.Fragment>
            ))}
            
            {/* Zone de drop finale */}
            <DropZone
              onDrop={(droppedChord) => handleDrop(droppedChord, currentProgression.chords.length)}
              targetIndex={currentProgression.chords.length}
              isDragOver={dragOverIndex === currentProgression.chords.length}
              onDragOver={(e) => handleDragOver(e, currentProgression.chords.length)}
              onDragLeave={() => setDragOverIndex(null)}
            />
          </div>
        )}
      </div>

      {/* Contrôles d'intégration AI Composer */}
      {showIntegrationControls && (
        <IntegrationControls
          progression={currentProgression}
          integrationMode={integrationMode}
          onInsertToTextEditor={onInsertToTextEditor}
          onAddToSongStructure={onAddToSongStructure}
          onSyncWithTimeline={onSyncWithTimeline}
        />
      )}

      {/* Modal de sélection d'accords */}
      <ChordPickerModal
        isOpen={showChordPicker}
        onClose={() => setShowChordPicker(false)}
        onChordSelect={(chord) => {
          handleAddChord(chord);
          setShowChordPicker(false);
        }}
        title="Ajouter un accord à la progression"
      />
    </div>
  );
};
