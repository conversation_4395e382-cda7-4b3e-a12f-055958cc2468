"use client";

import Link from 'next/link';
import Image from 'next/image';
import type { Song, UserProfile, Album } from '@/types';
import { Music2, PlayCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import { useUser } from '@/contexts/user-context';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface SongForCompactCard extends Song {
  profiles?: UserProfile | null;
  albums?: Pick<Album, 'id' | 'title' | 'cover_url'> | null;
}

interface SongCardCompactProps {
  song: SongForCompactCard;
  onUpdateStatus?: (songId: string, newStatus: { is_public: boolean; slug: string | null }) => void;
  // No delete or other complex actions on compact card usually
}

export function SongCardCompact({ song, onUpdateStatus }: SongCardCompactProps) {
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const { toast } = useToast();
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);

  const viewPageUrl = song.is_public && song.slug ? `/song/${song.slug}` : `/songs/${song.id}`;
  const artistDisplay = song.profiles?.display_name || song.profiles?.username || song.artist_name || "Artiste inconnu";

  const togglePublicStatus = async (e: React.MouseEvent) => {
    e.preventDefault(); e.stopPropagation();
    if (!user || song.creator_user_id !== user.id || isTogglingStatus || song.is_public === undefined) return;
    setIsTogglingStatus(true);
    try {
      const { data, error } = await supabase.rpc('toggle_song_public_status', { p_song_id: song.id, p_user_id: user.id });
      if (error) throw error;
      if (data && data.length > 0) {
        const { new_is_public, new_slug } = data[0];
        toast({ title: "Statut du morceau mis à jour", description: `"${song.title}" est ${new_is_public ? 'public' : 'privé'}.`});
        if (onUpdateStatus) onUpdateStatus(song.id, { is_public: new_is_public, slug: new_slug });
      } else { throw new Error("Réponse RPC invalide."); }
    } catch (err: any) { toast({ title: "Erreur", description: err.message || "Impossible de changer le statut.", variant: "destructive" });
    } finally { setIsTogglingStatus(false); }
  };

  return (
    <div className="block group rounded-md overflow-hidden shadow hover:shadow-lg transition-shadow duration-200 bg-card relative">
      {/* Clickable LED for public/private status */}
      {song.is_public !== undefined && (
        <div 
          title={song.is_public ? "Public (cliquer pour changer)" : "Privé (cliquer pour changer)"}
          onClick={togglePublicStatus}
          className={cn(
            "absolute top-1 left-1 z-20 w-3 h-3 rounded-full border-2 border-white shadow-md cursor-pointer flex items-center justify-center",
            song.is_public ? "bg-green-500 hover:bg-green-600" : "bg-red-500 hover:bg-red-600"
          )}
        >
          {isTogglingStatus && <Loader2 className="h-1.5 w-1.5 animate-spin text-white" />}
        </div>
      )}
      <Link href={viewPageUrl} className="block">
        <div className="relative aspect-square">
          {song.cover_url ? (
            <Image src={song.cover_url} alt={song.title} layout="fill" className="object-cover w-full h-full"/>
          ) : song.albums?.cover_url ? (
            <Image src={song.albums.cover_url} alt={song.title} layout="fill" className="object-cover w-full h-full"/>
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Music2 className="w-10 h-10 text-muted-foreground" />
            </div>
          )}
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100 z-10">
            <PlayCircle className="w-10 h-10 text-white" />
          </div>
        </div>
        <div className="p-1.5"> {/* Reduced padding for compact card */}
          <p className="text-xs font-medium truncate group-hover:underline" title={song.title}>
            {song.title}
          </p>
          <p className="text-xs text-muted-foreground truncate" title={artistDisplay}>
            {artistDisplay}
          </p>
        </div>
      </Link>
    </div>
  );
}
