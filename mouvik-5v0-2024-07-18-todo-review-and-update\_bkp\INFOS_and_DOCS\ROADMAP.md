# Roadmap de Développement - Nouvelles Fonctionnalités

Cette roadmap décrit les phases de développement prévues pour l'implémentation des modules "Découvrir & Playlists" et "Communauté".

## Phase 1 : Fondations (Basée sur P0)

**Objectif :** Établir la structure de base de données nécessaire.

- **Tâches Principales :**
    - [ ] Mod<PERSON>liser et créer les tables Supabase : `music_resources`, `tags`, `resource_tags`, `playlists`, `playlist_resources`.
    - [ ] Modéliser et créer/valider les tables Supabase : `activity`, `hashtags`, `activity_hashtags`, `likes`.
    - [ ] Définir et peupler la taxonomie `tags` initiale.
- **Livrable :** Schéma de base de données complet et validé pour les nouvelles fonctionnalités.

## Phase 2 : MVP Découvrir & Import (Basée sur P1)

**Objectif :** Permettre l'import de ressources externes (Spotify) et leur ajout à la base.

- **Tâches Principales :**
    - [ ] Développer le service d'extraction pour Spotify (API).
    - [ ] Créer l'UI du formulaire d'ajout/import (URL, preview, tags auto-complétés).
    - [ ] Implémenter l'API backend (`POST /api/discover/resources`, `POST /api/discover/preview`, `GET /api/tags/suggest`).
    - [ ] Intégrer la génération d'activité (`type='add_resource'`) lors de l'import.
- **Livrable :** Fonctionnalité d'import de liens Spotify fonctionnelle avec validation utilisateur et stockage en BDD.

## Phase 3 : MVP Communauté (Basée sur P1)

**Objectif :** Mettre en place un mur d'activité simple pour les posts texte et l'affichage des imports.

- **Tâches Principales :**
    - [ ] Implémenter l'API backend (`POST /api/community/activity`) pour les posts texte et la gestion des hashtags.
    - [ ] Créer l'UI de base du mur d'activité (`ActivityFeed`, `ActivityCard`).
    - [ ] Implémenter l'API backend (`GET /api/community/activity`) pour lister les activités.
    - [ ] Assurer l'affichage des activités `add_resource` générées en Phase 2.
- **Livrable :** Un mur d'activité affichant les posts texte et les ressources ajoutées.

## Phase 4 : Amélioration Découvrir (Basée sur P2)

**Objectif :** Afficher le contenu importé et ajouter d'autres sources.

- **Tâches Principales :**
    - [ ] Implémenter l'affichage du feed Découvrir (`ResourceList`, `ResourceCard`).
    - [ ] Implémenter l'API (`GET /api/discover/resources`) pour lister les ressources.
    - [ ] Développer le service d'extraction pour Suno/Udio.
    - [ ] Intégrer les badges de provenance et les players (interne/embed) dans l'UI.
- **Livrable :** Section Découvrir affichant les ressources internes/externes avec players et infos de source.

## Phase 5 : Amélioration Communauté (Basée sur P2)

**Objectif :** Ajouter les interactions sociales de base et la navigation par hashtag.

- **Tâches Principales :**
    - [ ] Implémenter l'API et l'UI pour les commentaires.
    - [ ] Implémenter les Server Actions/API et l'UI pour les likes.
    - [ ] Mettre en place le système de hashtags complet (API + page dédiée).
- **Livrable :** Mur d'activité avec commentaires, likes, et navigation par hashtags.

## Phase 6 : Fonctionnalités Avancées & Finalisation (Basée sur P3)

**Objectif :** Compléter les fonctionnalités, ajouter les filtres, la gestion des groupes/playlists, la modération et les tests.

- **Tâches Principales :**
    - [ ] Implémenter les filtres avancés et la recherche (Découvrir & Communauté).
    - [ ] Développer la gestion complète des playlists (UI + API).
    - [ ] Développer la gestion complète des groupes (UI + API/Filtres).
    - [ ] Mettre en place les systèmes de modération (tags & contenu).
    - [ ] Implémenter un système de notifications basique.
    - [ ] Rédiger les tests unitaires et d'intégration.
    - [ ] Ajouter d'autres sources d'import ou types d'activité si pertinent.
- **Livrable :** Fonctionnalités complètes, stables et testées.

*Cette roadmap est indicative et pourra être ajustée en fonction des priorités et des ressources.*