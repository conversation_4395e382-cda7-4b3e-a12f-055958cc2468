"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation" 
import { getSupabaseClient } from "@/lib/supabase/client"
import { Disc, Save, X, Plus, Info, Upload, Music } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { ImageUploader } from "@/components/ui/image-uploader"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MultiSelect } from "@/components/ui/multi-select"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { genreOptions, moodOptions, instrumentationOptions, albumTypeOptions } from '@/lib/constants/song-options';
import { AlbumGalleryUploader } from '@/components/ui/album-gallery-uploader'; // Import the new uploader

// Define UserBand interface
interface UserBand {
  id: string;
  name: string;
}

const aiContentOriginOptions = [
  { value: 'full_human', label: '100% Humain' },
  { value: 'hybrid', label: 'Hybride (Humain + IA)' },
  { value: '100%_ia', label: '100% IA' },
];

export default function CreateAlbumPage() {
  const router = useRouter()
  const { toast } = useToast()
  const searchParams = useSearchParams(); 
  const bandIdFromQuery = searchParams.get('bandId'); 

  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("info")
  const [userBands, setUserBands] = useState<UserBand[]>([]);
  const [isLoadingBands, setIsLoadingBands] = useState(true);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    genre: [] as string[], // Changed from genres to genre
    moods: [] as string[], 
    instrumentation: [] as string[], 
    album_type: "", 
    releaseDate: "",
    isExplicit: false,
    status: "draft",
    coverUrl: "",
    tags: [] as string[],
    songs: [] as string[], // Array of song IDs
    // author: "", // Remove author
    attribution_type: bandIdFromQuery ? 'band' : 'user', 
    band_id: bandIdFromQuery || null,
    ai_content_origin: null as '100%_ia' | 'hybrid' | 'full_human' | null,
    create_playlist_from_album: true,
    isPublic: true, 
    label: "", // Added
    upc: "", // Added
    notifyFollowers: true, 
    addToDiscovery: true, 
    are_comments_public: false, 
    gallery_image_urls: [] as string[], 
    is_gallery_public: true, // Added for gallery visibility, default public
  })

  const [currentTag, setCurrentTag] = useState("")
  const [availableSongs, setAvailableSongs] = useState<any[]>([]) // Songs user owns
  const [selectedSongsForDisplay, setSelectedSongsForDisplay] = useState<any[]>([]) // Full song objects for display in tracklist
  // formData.songs will store array of song IDs for saving

  const [isAddSongModalOpen, setIsAddSongModalOpen] = useState(false);
  const [songsToAddInModal, setSongsToAddInModal] = useState<Set<string>>(new Set()); // IDs of songs selected in modal


  useEffect(() => {
    // Removed fetchUser logic that set author
    loadAvailableSongs();
  }, []);

  // Add useEffect for fetching bands
  useEffect(() => {
    const fetchUserBands = async () => {
      setIsLoadingBands(true);
      const supabase = getSupabaseClient();
      const { data: userSessionData, error: sessionError } = await supabase.auth.getUser();
      if (sessionError || !userSessionData?.user) {
        setIsLoadingBands(false);
        return;
      }
      const userId = userSessionData.user.id;

      try {
        const { data: bandMemberships, error: membershipError } = await supabase
          .from('band_members')
          .select('band_id')
          .eq('user_id', userId);

        if (membershipError) throw membershipError;

        if (bandMemberships && bandMemberships.length > 0) {
          const bandIds = bandMemberships.map(m => m.band_id);
          const { data: bandsData, error: bandsError } = await supabase
            .from('bands')
            .select('id, name')
            .in('id', bandIds);

          if (bandsError) throw bandsError;
          setUserBands(bandsData || []);
        } else {
          setUserBands([]);
        }
      } catch (error: any) {
        console.error('Error fetching user bands:', error);
        toast({
          title: "Erreur chargement groupes",
          description: `Impossible de charger les groupes: ${error.message}`,
          variant: "destructive",
        });
        setUserBands([]);
      } finally {
        setIsLoadingBands(false);
      }
    };

    fetchUserBands();
  }, [toast]); // Dependency array

  const loadAvailableSongs = async () => {
    try {
      const supabase = getSupabaseClient()
      const { data: userData } = await supabase.auth.getUser();
      if (!userData?.user) return

      const { data, error } = await supabase
        .from("songs")
        .select("id, title, cover_url, duration, status")
        .eq("user_id", userData.user.id)
        .eq("status", "published")
        .order("title", { ascending: true })

      if (error) throw error
      setAvailableSongs(data || [])
    } catch (error) {
      console.error("Erreur lors du chargement des morceaux:", error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleCoverUpload = (url: string) => {
    setFormData((prev) => ({ ...prev, coverUrl: url }))
  }

  const addTag = () => {
    if (currentTag && !formData.tags.includes(currentTag)) {
      setFormData((prev) => ({ ...prev, tags: [...prev.tags, currentTag] }))
      setCurrentTag("")
    }
  }

  const removeTag = (tag: string) => {
    setFormData((prev) => ({ ...prev, tags: prev.tags.filter((t) => t !== tag) }))
  }

  const handleConfirmAddSongsFromModal = () => {
    const newSongDetails = availableSongs.filter(s => songsToAddInModal.has(s.id) && !selectedSongsForDisplay.some(sd => sd.id === s.id));
    setSelectedSongsForDisplay(prev => [...prev, ...newSongDetails]);
    
    const newSongIds = Array.from(songsToAddInModal).filter(id => !formData.songs.includes(id));
    setFormData(prev => ({ ...prev, songs: [...prev.songs, ...newSongIds] }));
    
    setIsAddSongModalOpen(false);
    setSongsToAddInModal(new Set());
  };

  const removeSongFromAlbum = (songIdToRemove: string) => {
    setSelectedSongsForDisplay((prev) => prev.filter((s) => s.id !== songIdToRemove));
    setFormData((prev) => ({ ...prev, songs: prev.songs.filter((id) => id !== songIdToRemove) }));
  };

  const handleSave = async (status: "draft" | "published") => {
    setIsLoading(true);
    if (!formData.title) {
      toast({ title: "Erreur", description: "Le titre est obligatoire", variant: "destructive" });
      setIsLoading(false); return;
    }
    if (status === "published" && !formData.coverUrl) {
      toast({ title: "Erreur", description: "Vous devez ajouter une pochette pour publier l'album", variant: "destructive" });
      setIsLoading(false); return;
    }
    // Removed check: if (status === "published" && formData.songs.length === 0)

    const supabase = getSupabaseClient();
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError || !userData?.user) {
      toast({ title: "Erreur d'authentification", description: "Vous devez être connecté pour créer un album", variant: "destructive" });
      setIsLoading(false); return;
    }
    const user = userData.user;

    let slugToSave: string | null = null;
    if (formData.isPublic && formData.title) {
      const { data: slugData, error: slugError } = await supabase.rpc('slugify', { value: formData.title });
      if (slugError || !slugData) {
        console.error("Error generating slug:", slugError);
        toast({ title: "Erreur de slug", description: "Impossible de générer un slug.", variant: "destructive" });
        setIsLoading(false); return;
      }
      let tempSlug = slugData;
      let isUnique = false;
      let attempt = 0;
      // Check for uniqueness (simplified for create, more robust check might be needed for high concurrency)
      while(!isUnique && attempt < 5) {
        const {data: existing} = await supabase.from('albums').select('id').eq('slug', tempSlug).maybeSingle();
        if(!existing) { isUnique = true; slugToSave = tempSlug; }
        else { attempt++; tempSlug = `${slugData}-${Math.random().toString(36).substring(2,7)}`; }
      }
      if(!isUnique) { // Fallback if unique slug not found after attempts
        slugToSave = `${slugData}-${Date.now()}`;
      }
    }

    try {
      const { data: albumData, error: albumError } = await supabase
        .from("albums")
        .insert({
          user_id: user.id,
          title: formData.title,
          description: formData.description,
          genre: formData.genre.length > 0 ? formData.genre : null, // Changed from genres to genre
          moods: formData.moods.length > 0 ? formData.moods : null, 
          instrumentation: formData.instrumentation.length > 0 ? formData.instrumentation : null, 
          album_type: formData.album_type || null,
          release_date: formData.releaseDate || null,
          cover_url: formData.coverUrl,
          status: status,
          is_explicit: formData.isExplicit,
          band_id: formData.band_id,
          ai_content_origin: formData.ai_content_origin,
          is_public: formData.isPublic, 
          slug: slugToSave, 
          are_comments_public: formData.are_comments_public, 
          gallery_image_urls: formData.gallery_image_urls.length > 0 ? formData.gallery_image_urls : null, 
          is_gallery_public: formData.is_gallery_public, // Save gallery visibility
        })
        .select()
        .single(); 

      if (albumError) throw albumError;
      if (!albumData) throw new Error("La création de l'album a échoué et n'a retourné aucune donnée.");
      
      const albumId = albumData.id;

      if (formData.songs.length > 0) {
        const albumSongs = formData.songs.map((songId, index) => ({
          album_id: albumId,
          song_id: songId,
          track_number: index + 1,
        }));
        const { error: songsError } = await supabase.from("album_songs").insert(albumSongs);
        if (songsError) {
          console.error("Erreur critique lors de l'ajout des morceaux à l'album:", songsError);
          toast({ title: "Erreur partielle", description: `L'album a été créé, mais une erreur est survenue lors de l'ajout des morceaux: ${songsError.message}. Veuillez modifier l'album pour corriger.`, variant: "destructive", duration: 10000 });
        }
      }

      if (formData.tags.length > 0) {
        // Tag handling logic (remains the same)
        // This part was missing in the original provided file, assuming it's similar to song form tag handling
        const tagObjects = formData.tags.map(tag => ({ name: tag, user_id: user.id }));
        const { data: insertedTags, error: tagsErrorUpsert } = await supabase.from('tags').upsert(tagObjects, { onConflict: 'name, user_id' }).select('id, name');
        if (tagsErrorUpsert) console.error("Error upserting tags:", tagsErrorUpsert);
        if (insertedTags) {
          const resourceTags = insertedTags.map(tag => ({ resource_id: albumId, tag_id: tag.id, resource_type: 'album', user_id: user.id }));
          const { error: resourceTagsError } = await supabase.from('resource_tags').insert(resourceTags);
          if (resourceTagsError) console.error("Error inserting resource_tags:", resourceTagsError);
        }
      }

      // Create playlist from album if checked
      if (formData.create_playlist_from_album && albumData) {
        // Check playlist quota (simplified check, ideally use usePlanLimits hook if available here)
        const { data: countData } = await supabase.rpc('get_user_playlist_count', { user_id_param: user.id });
        const userPlaylistCount = typeof countData === 'number' ? countData : 0;
        // Fetch plan limits for max_playlists (simplified, ideally from context/hook)
        const { data: profileForTier } = await supabase.from('profiles').select('subscription_tier, custom_max_playlists').eq('id', user.id).single();
        const { data: planLimitsData } = await supabase.from('plan_limits').select('max_playlists').eq('tier', profileForTier?.subscription_tier || 'free').single();
        const effectiveMaxPlaylists = profileForTier?.custom_max_playlists ?? planLimitsData?.max_playlists ?? null;
        
          if (effectiveMaxPlaylists === null || userPlaylistCount < effectiveMaxPlaylists) {
            let playlistSlug: string | null = null;
            // Playlist is public if album is public AND published
            const isPlaylistPublic = formData.isPublic && status === "published"; 
            if (isPlaylistPublic) { 
              const { data: slugData } = await supabase.rpc('slugify', { value: `Album: ${albumData.title}` });
              playlistSlug = slugData;
             // Basic uniqueness for slug
            let isUnique = false; let attempt = 0; let tempSlug = playlistSlug;
            while(!isUnique && attempt < 3) {
                const {data: existing} = await supabase.from('playlists').select('id').eq('slug', tempSlug).maybeSingle();
                if(!existing) { isUnique = true; playlistSlug = tempSlug; }
                else { attempt++; tempSlug = `${slugData}-${Math.random().toString(36).substring(2,5)}`;}
            }
            if(!isUnique) playlistSlug = `${slugData}-${Date.now()}`; // Fallback
          }

          const { data: newPlaylist, error: playlistCreationError } = await supabase
            .from('playlists')
            .insert({
              user_id: user.id,
              name: `Album: ${albumData.title}`,
              description: `Playlist générée automatiquement pour l'album "${albumData.title}".`,
              cover_url: albumData.cover_url,
              is_public: isPlaylistPublic, 
              slug: playlistSlug,
              source_album_id: albumId,
            })
            .select('id')
            .single();

          if (playlistCreationError) {
            console.error("Error creating playlist from album:", playlistCreationError);
            toast({ title: "Erreur Playlist", description: "L'album a été créé, mais la playlist automatique n'a pas pu l'être.", variant: "default" }); // Changed to default
          } else if (newPlaylist && formData.songs.length > 0) {
            const playlistSongs = formData.songs.map((songId, index) => ({
              playlist_id: newPlaylist.id,
              song_id: songId,
              user_id: user.id, // Assuming user owns these playlist_song entries
              track_order: index + 1,
            }));
            const { error: playlistSongsError } = await supabase.from('playlist_songs').insert(playlistSongs);
            if (playlistSongsError) {
              console.error("Error adding songs to album-playlist:", playlistSongsError);
              toast({ title: "Erreur Playlist", description: "La playlist de l'album a été créée, mais les morceaux n'ont pas pu y être ajoutés.", variant: "default" }); // Changed to default
            } else {
              toast({ title: "Playlist d'album créée", description: `La playlist "Album: ${albumData.title}" a été créée.` }); // Default variant
            }
          }
        } else {
          toast({ title: "Quota de Playlists Atteint", description: "L'album a été créé, mais la playlist automatique n'a pas pu l'être car vous avez atteint votre limite.", variant: "default" }); // Changed to default
        }
      }

      await supabase.from("activities").insert({ 
        user_id: user.id, 
        type: "album_created", 
        target_type: "album",   
        target_id: albumId,     
        content: `a créé un nouvel album: ${formData.title}` 
      });
      toast({ title: status === "published" ? "Album publié" : "Brouillon enregistré", description: status === "published" ? "Votre album a été publié avec succès" : "Votre brouillon a été enregistré" });
      router.push(status === "published" ? `/albums/${albumId}` : "/albums");

    } catch (error: any) {
      toast({ title: "Erreur", description: error.message || "Une erreur s'est produite lors de la création de l'album", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Créer un nouvel album</h1>
          <p className="text-muted-foreground">Ajoutez les détails de votre album et sélectionnez vos morceaux</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.back()}>Annuler</Button>
          <Button variant="outline" onClick={() => handleSave("draft")} disabled={isLoading}>
            <Save className="mr-2 h-4 w-4" />Enregistrer comme brouillon
          </Button>
          <Button onClick={() => handleSave("published")} disabled={isLoading}>Publier</Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="info">Informations</TabsTrigger>
          <TabsTrigger value="songs">Morceaux</TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-6 mt-6"> {/* Added mt-6 for spacing after TabsList */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center"><Info className="mr-2 h-5 w-5" />Informations de l'album</CardTitle>
                  <CardDescription>Ajoutez les informations de base de votre album</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* ... existing fields ... */}
                  <div className="space-y-2">
                    <Label htmlFor="title">Titre de l'album *</Label>
                    <Input id="title" name="title" value={formData.title} onChange={handleInputChange} placeholder="Entrez le titre de votre album" required />
                  </div>
                  {/* Attribution Selection */}
                  <div className="space-y-2">
                    <Label>Publié par</Label>
                    <Select
                      value={formData.attribution_type}
                      onValueChange={(value: 'user' | 'band') => {
                        setFormData(prev => ({
                          ...prev,
                          attribution_type: value,
                          band_id: value === 'user' ? null : (userBands?.[0]?.id || null) // Default to first band if switching to band
                        }));
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner l'auteur" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">Moi</SelectItem> {/* Simplified label */}
                        <SelectItem value="band" disabled={!userBands || userBands.length === 0}>
                          Un de mes groupes {isLoadingBands ? '(Chargement...)' : ''}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {formData.attribution_type === 'band' && (
                    <div className="space-y-2">
                      <Label htmlFor="band_id">Groupe</Label>
                      <Select
                        value={formData.band_id || undefined}
                        onValueChange={(value) => {
                          setFormData(prev => ({ ...prev, band_id: value }));
                        }}
                        disabled={isLoadingBands || !userBands || userBands.length === 0}
                      >
                        <SelectTrigger id="band_id">
                          <SelectValue placeholder="Sélectionner un groupe" />
                        </SelectTrigger>
                        <SelectContent>
                          {userBands.map(band => (
                            <SelectItem key={band.id} value={band.id}>{band.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {isLoadingBands && <p className="text-xs text-muted-foreground">Chargement des groupes...</p>}
                      {!isLoadingBands && (!userBands || userBands.length === 0) && <p className="text-xs text-muted-foreground">Vous n'êtes membre d'aucun groupe.</p>}
                    </div>
                  )}
                  {/* End Attribution Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" name="description" value={formData.description} onChange={handleInputChange} placeholder="Décrivez votre album" rows={4} />
                  </div>
                  {/* ... genres, moods, instrumentation, album_type ... */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="genre">Genres</Label> 
                      <MultiSelect options={genreOptions} selected={formData.genre} onChange={(selected) => setFormData((prev) => ({ ...prev, genre: selected }))} placeholder="Sélectionnez des genres" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="moods">Ambiances / Moods</Label>
                      <MultiSelect options={moodOptions} selected={formData.moods} onChange={(selected) => setFormData((prev) => ({ ...prev, moods: selected }))} placeholder="Sélectionnez des ambiances" />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="instrumentation">Instrumentation</Label>
                      <MultiSelect options={instrumentationOptions} selected={formData.instrumentation} onChange={(selected) => setFormData((prev) => ({ ...prev, instrumentation: selected }))} placeholder="Sélectionnez des instruments" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="album_type">Type d'album</Label>
                      <Select value={formData.album_type} onValueChange={(value) => setFormData(prev => ({ ...prev, album_type: value }))}>
                        <SelectTrigger id="album_type"><SelectValue placeholder="Sélectionnez un type" /></SelectTrigger>
                        <SelectContent>{albumTypeOptions.map(option => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}</SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="releaseDate">Date de sortie</Label>
                      <Input id="releaseDate" name="releaseDate" type="date" value={formData.releaseDate} onChange={handleInputChange} />
                    </div>
                    <div className="flex items-center space-x-2 pt-5">
                      <Switch id="isExplicit" checked={formData.isExplicit} onCheckedChange={(checked) => handleSwitchChange("isExplicit", checked)} />
                      <Label htmlFor="isExplicit" className="cursor-pointer">Contenu explicite</Label>
                    </div>
                     <div className="flex items-center space-x-2 pt-5"> {/* isPublic switch */}
                      <Switch id="isPublic" checked={formData.isPublic} onCheckedChange={(checked) => handleSwitchChange("isPublic", checked)} />
                      <Label htmlFor="isPublic" className="cursor-pointer">Rendre l'album public</Label>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ai_content_origin">Origine du Contenu IA</Label>
                    <Select value={formData.ai_content_origin || undefined} onValueChange={(value: '100%_ia' | 'hybrid' | 'full_human') => setFormData(prev => ({ ...prev, ai_content_origin: value }))}>
                      <SelectTrigger id="ai_content_origin"><SelectValue placeholder="Sélectionner l'origine" /></SelectTrigger>
                      <SelectContent>
                        {aiContentOriginOptions.map(option => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">Indiquez la part de l'IA dans la création de cet album.</p>
                  </div>
                  <div className="flex items-center space-x-2 pt-2">
                    <Switch id="create_playlist_from_album" checked={formData.create_playlist_from_album} onCheckedChange={(checked) => handleSwitchChange("create_playlist_from_album", checked)} />
                    <Label htmlFor="create_playlist_from_album" className="cursor-pointer">Créer une playlist à partir de cet album</Label>
                  </div>
                  {/* Switch for are_comments_public */}
                  <div className="flex items-center space-x-2 pt-2">
                    <Switch 
                      id="are_comments_public" 
                      checked={formData.are_comments_public} 
                      onCheckedChange={(checked) => handleSwitchChange("are_comments_public", checked)} 
                    />
                    <Label htmlFor="are_comments_public" className="cursor-pointer">Rendre les commentaires publics ?</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Si coché, les commentaires seront visibles par tous. Sinon, ils seront privés.
                  </p>
                  {/* Switch for is_gallery_public */}
                  <div className="flex items-center space-x-2 pt-2">
                    <Switch 
                      id="is_gallery_public" 
                      checked={formData.is_gallery_public} 
                      onCheckedChange={(checked) => handleSwitchChange("is_gallery_public", checked)} 
                    />
                    <Label htmlFor="is_gallery_public" className="cursor-pointer">Rendre la galerie d'images publique ?</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Si coché, la galerie d'images sera visible sur la page publique de l'album.
                  </p>
                  
                  <div className="space-y-2 pt-4">
                    <Label>Galerie d'images de l'album (jusqu'à 12 images)</Label>
                    <AlbumGalleryUploader
                      bucketName="album-gallery" 
                      initialFileUrls={formData.gallery_image_urls}
                      onUpdate={(urls) => setFormData(prev => ({ ...prev, gallery_image_urls: urls }))}
                      maxFiles={12}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex flex-wrap gap-2 mb-2">{formData.tags.map((tag) => ( <Badge key={tag} variant="secondary" className="flex items-center gap-1">{tag}<X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} /></Badge> ))}</div>
                    <div className="flex gap-2"><Input value={currentTag} onChange={(e) => setCurrentTag(e.target.value)} placeholder="Ajouter un tag" onKeyDown={(e) => { if (e.key === "Enter") { e.preventDefault(); addTag(); }}} /><Button type="button" variant="outline" onClick={addTag}><Plus className="h-4 w-4" /></Button></div>
                  </div>
                </CardContent>
              </Card>
              {/* ... Pochette Card ... */}
              <Card>
                <CardHeader><CardTitle className="flex items-center"><Upload className="mr-2 h-5 w-5" />Pochette de l'album</CardTitle><CardDescription>Téléchargez une image pour votre album</CardDescription></CardHeader>
                <CardContent><ImageUploader onImageUploaded={handleCoverUpload} existingImageUrl={formData.coverUrl} aspectRatio="square" maxWidth={1000} maxHeight={1000} /></CardContent>
              </Card>
            </div>
            {/* ... Preview and Conseils Cards ... */}
            <div className="space-y-6">
              <Card>
                <CardHeader><CardTitle>Prévisualisation</CardTitle><CardDescription>Aperçu de votre album</CardDescription></CardHeader>
                <CardContent className="flex flex-col items-center">
                  <div className="w-full max-w-[240px] aspect-square rounded-md overflow-hidden bg-muted mb-4">{formData.coverUrl ? <img src={formData.coverUrl} alt="Pochette" className="w-full h-full object-cover" /> : <div className="w-full h-full flex items-center justify-center bg-primary/10"><Disc className="h-16 w-16 text-primary/40" /></div>}</div>
                  <h3 className="text-xl font-bold">{formData.title || "Titre de l'album"}</h3>
                  <p className="text-muted-foreground text-sm text-center">Genres: {formData.genre.length > 0 ? formData.genre.map((g) => genreOptions.find((opt) => opt.value === g)?.label || g).join(", ") : "N/A"}</p>
                  <p className="text-muted-foreground text-sm text-center">Moods: {formData.moods.length > 0 ? formData.moods.map((m) => moodOptions.find((opt) => opt.value === m)?.label || m).join(", ") : "N/A"}</p>
                  <p className="text-muted-foreground text-xs text-center">Instruments: {formData.instrumentation.length > 0 ? formData.instrumentation.map((i) => instrumentationOptions.find((opt) => opt.value === i)?.label || i).join(", ") : "N/A"}</p>
                  {formData.ai_content_origin && <Badge variant="outline" className="mt-2 capitalize">{formData.ai_content_origin.replace('_', ' ')}</Badge>}
                  {formData.isExplicit && <Badge variant="outline" className="mt-2">Explicite</Badge>}
                  <div className="flex flex-wrap gap-1 mt-4 justify-center">{formData.tags.map((tag) => <Badge key={tag} variant="secondary">{tag}</Badge>)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader><CardTitle>Conseils</CardTitle></CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2"><h4 className="font-medium">Pochette</h4><p className="text-sm text-muted-foreground">Utilisez une image carrée de haute qualité (minimum 1000x1000 pixels).</p></div>
                  <div className="space-y-2"><h4 className="font-medium">Morceaux</h4><p className="text-sm text-muted-foreground">Ajoutez au moins un morceau à votre album avant de le publier.</p></div>
                  <div className="space-y-2"><h4 className="font-medium">Tags</h4><p className="text-sm text-muted-foreground">Ajoutez des tags pertinents pour améliorer la découvrabilité de votre album.</p></div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="songs" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center"><Disc className="mr-2 h-5 w-5" />Morceaux de l'album</CardTitle>
                  <CardDescription>Ajoutez et organisez les morceaux de votre album.</CardDescription>
                </div>
                <Dialog open={isAddSongModalOpen} onOpenChange={setIsAddSongModalOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline"><Plus className="mr-2 h-4 w-4" />Ajouter des morceaux</Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[625px]">
                    <DialogHeader><DialogTitle>Ajouter des morceaux à l'album</DialogTitle><DialogDescription>Sélectionnez des morceaux depuis votre bibliothèque.</DialogDescription></DialogHeader>
                    <div className="max-h-[60vh] overflow-y-auto p-1 space-y-1">
                      {availableSongs.filter(s => !formData.songs.includes(s.id)).length > 0 ? 
                        availableSongs.filter(s => !formData.songs.includes(s.id)).map(song => (
                          <div key={song.id} className="flex items-center justify-between p-2 hover:bg-muted/50 rounded-md border">
                            <div className="flex items-center gap-2">
                              <Checkbox 
                                id={`song-to-add-create-${song.id}`}
                                checked={songsToAddInModal.has(song.id)}
                                onCheckedChange={(checked) => {
                                  setSongsToAddInModal(prev => {
                                    const newSet = new Set(prev);
                                    if (checked) newSet.add(song.id);
                                    else newSet.delete(song.id);
                                    return newSet;
                                  });
                                }}
                              />
                              {song.cover_url && <img src={song.cover_url} alt={song.title} className="w-10 h-10 rounded object-cover" />}
                              {!song.cover_url && <div className="w-10 h-10 rounded bg-muted flex items-center justify-center"><Music className="w-5 h-5 text-muted-foreground"/></div>}
                              <div>
                                <Label htmlFor={`song-to-add-create-${song.id}`} className="font-medium cursor-pointer">{song.title}</Label>
                                <p className="text-xs text-muted-foreground">{song.artist_name || 'Artiste inconnu'} - {formatDuration(song.duration)}</p>
                              </div>
                            </div>
                          </div>
                        )) : <p className="text-sm text-muted-foreground text-center py-4">Aucun autre morceau disponible ou tous ont déjà été ajoutés.</p>
                      }
                    </div>
                    <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => {setIsAddSongModalOpen(false); setSongsToAddInModal(new Set());}}>Annuler</Button>
                        <Button type="button" onClick={handleConfirmAddSongsFromModal} disabled={songsToAddInModal.size === 0}>Ajouter les morceaux ({songsToAddInModal.size})</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              {selectedSongsForDisplay.length > 0 ? (
                <div className="space-y-2">
                  {selectedSongsForDisplay.map((song, index) => (
                    <div key={song.id} className="flex items-center justify-between p-2 rounded-md border bg-background hover:bg-muted/80">
                      <div className="flex items-center gap-3">
                        <span className="font-mono text-xs text-muted-foreground w-5 text-right">{index + 1}.</span>
                        {song.cover_url ? <img src={song.cover_url} alt={song.title} className="w-10 h-10 rounded-md object-cover" /> : <div className="w-10 h-10 rounded-md bg-muted flex items-center justify-center"><Music className="w-5 h-5 text-muted-foreground"/></div>}
                        <div>
                          <p className="font-medium">{song.title}</p>
                          <p className="text-xs text-muted-foreground">{formatDuration(song.duration)}</p>
                        </div>
                      </div>
                      <Button variant="ghost" size="icon" onClick={() => removeSongFromAlbum(song.id)} className="h-8 w-8 text-destructive hover:text-destructive"><X className="h-4 w-4" /></Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">Aucun morceau ajouté. Cliquez sur "Ajouter des morceaux".</p>
              )}
            </CardContent>
            <CardFooter>
              <div className="w-full flex justify-between items-center">
                <div className="text-sm text-muted-foreground">{selectedSongsForDisplay.length} morceau{selectedSongsForDisplay.length !== 1 ? "x" : ""}</div>
                <div className="text-sm text-muted-foreground">Durée totale: {formatDuration(selectedSongsForDisplay.reduce((acc, song) => acc + (song.duration || 0), 0))}</div>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
