"use client";

import React, { useEffect, useState, use<PERSON><PERSON>back, useRef, useImperativeHandle } from 'react';
import { useRouter } from 'next/navigation'; 
import { SupabaseClient } from '@supabase/supabase-js'; 
import { useForm, FormProvider as RHFFormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from "@/hooks/use-toast"; 
import { useUser } from '@/contexts/user-context'; // Corrected import

import { Button } from "@/components/ui/button"; 
import { MultiSelect } from '@/components/ui/multi-select';
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ScrollArea } from '@/components/ui/scroll-area'; 
import { Cog, Loader2 as LoadingSpinner, Music2, UploadCloud, History, ChevronDown, ChevronUp, Languages, Users as UsersIcon, Brain, Layers, PanelRightClose } from 'lucide-react'; 
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from '@/components/ui/switch'; 
import { DatePicker } from "@/components/ui/date-picker"; 
import Image from 'next/image';
import { AiConfigMenu } from "@/components/ia/ai-config-menu";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { AiQuickActions } from "@/components/ia/ai-quick-actions";
import AudioWaveformPreview from '@/components/audio-waveform-preview';
import { RichLyricsEditor } from "@/components/ui/rich-lyrics-editor";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"; 
import type Quill from 'quill'; 
import { genreOptions, moodOptions, tagOptions, instrumentationOptions, languageOptions } from '@/lib/constants/song-options';
import { MetadataFields, MetadataFieldConfig } from '@/components/shared/MetadataFields';
import type { Song } from '@/types';
import SongVault, { SongVaultActions } from '@/components/song-vault'; 
import SongProgressTracker, { SongProgressData } from './SongProgressTracker';
import { usePlanLimits } from '@/hooks/use-plan-limits';

export const musicalKeys = [
  "C", "C#", "Db", "D", "D#", "Eb", "E", "F", "F#", "Gb", "G", "G#", "Ab", "A", "A#", "Bb", "B",
  "Am", "A#m", "Bbm", "Bm", "Cm", "C#m", "Dbm", "Dm", "D#m", "Ebm", "Em", "Fm", "F#m", "Gbm", "Gm", "G#m", "Abm"
];
export const timeSignatures = ["2/4", "3/4", "4/4", "5/4", "6/8", "7/8", "9/8", "12/8", "Autre"];
export const NO_ALBUM_SELECTED_VALUE = "__NO_ALBUM__";
// ... other constants ...

export interface AiConfig {
  provider: 'ollama' | 'openai' | 'openrouter' | 'anthropic' | string;
  model: string;
  temperature: number;
}

export interface AiHistoryItem {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: Date; 
}

export const songFormSchema = z.object({
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist_name: z.string().min(1, { message: "Le nom de l'artiste principal est requis." }),
  featured_artists: z.string().optional(),
  genres: z.array(z.string()).optional(), 
  moods: z.array(z.string()).optional(),
  instrumentation: z.array(z.string()).optional(),
  key: z.string().optional(),
  bpm: z.number().min(0).nullable().optional(),
  time_signature: z.string().optional(),
  duration: z.number().min(0).nullable().optional(), 
  capo: z.number().min(0).nullable().optional(),
  tuning_frequency: z.number().min(0).nullable().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  audio_url: z.string().optional().nullable(),
  cover_url: z.string().optional().nullable(),
  album_id: z.string().nullable().optional(),
  composer_name: z.string().optional(),
  writers: z.string().optional(), 
  producers: z.string().optional(), 
  release_date: z.date().optional().nullable(),
  recording_date: z.date().optional().nullable(), 
  record_label: z.string().optional(),
  distributor: z.string().optional(),
  isrc: z.string().optional(),
  upc: z.string().optional(),
  lyrics_language: z.array(z.string()).optional(), 
  is_explicit: z.boolean().default(false).optional(),
  is_ai_generated: z.boolean().default(false).optional(), 
  stems_available: z.boolean().default(false).optional(),
  allow_downloads: z.boolean().default(false).optional(),
  allow_comments: z.boolean().default(true).optional(),
  lyrics: z.string().optional(),
  bloc_note: z.string().optional(), 
  right_column_notepad: z.string().optional(), 
  status: z.enum(['draft', 'published']).optional(),
  progress_data: z.any().optional(),
  band_id: z.string().uuid().optional().nullable(), 
  custom_css: z.string().optional(), 
  ai_content_origin: z.enum(['100%_ia', 'hybrid', 'full_human']).optional().nullable(), 
  is_public: z.boolean().default(false).optional(), 
  slug: z.string().optional().nullable(), 
  attribution_type: z.enum(['user', 'band']).default('user').optional(), 
});
export type SongFormValues = z.infer<typeof songFormSchema>;

export interface Album {
  id: string;
  title: string;
}

interface SongFormProps {
  mode: 'create' | 'edit';
  initialValues?: Partial<SongFormValues & { id?: string; user_id?: string; tempo?: string }>; 
  onFormSubmit: (data: SongFormValues, status?: 'draft' | 'published') => Promise<void>;
  isSubmitting: boolean;
  albums: Album[];
  isLoadingAlbums: boolean;
  supabaseClient: SupabaseClient;
  initialBandId?: string | null;
  userBands?: Array<{ id: string; name: string }>;
  isLoadingBands?: boolean; // Add this line
}

export interface SongFormHandle {
  getVaultActions: () => SongVaultActions | null;
}

export const SongForm = React.forwardRef<SongFormHandle, SongFormProps>(({
  mode,
  initialValues,
  onFormSubmit,
  isSubmitting,
  albums,
  isLoadingAlbums,
  supabaseClient,
  initialBandId, 
  userBands = [], 
}, ref) => {
  const router = useRouter();
  const { user } = useUser(); 

  const form = useForm<SongFormValues>({
    resolver: zodResolver(songFormSchema),
    defaultValues: mode === 'create' ? {
      title: "", artist_name: user?.display_name || user?.email || "", 
      featured_artists: "", genres: [], moods: [], instrumentation: [], key: "",
      bpm: undefined, time_signature: "", duration: null, capo: undefined, tuning_frequency: 440, description: "", tags: [],
      audio_url: null, cover_url: null, album_id: null, composer_name: "", writers: "", producers: "",
      release_date: null, recording_date: null, record_label: "", distributor: "", isrc: "", upc: "",
      lyrics_language: [], is_explicit: false, is_ai_generated: false, stems_available: false, 
      allow_downloads: false, allow_comments: true, lyrics: "", bloc_note: "", right_column_notepad: "", status: 'draft',
      progress_data: {},
      attribution_type: initialBandId ? 'band' : 'user', 
      band_id: initialBandId || null, 
      is_public: false, 
      slug: null, 
    } : { 
      ...initialValues,
      attribution_type: initialValues?.band_id ? 'band' : 'user',
      band_id: initialValues?.band_id || initialBandId || null, 
      is_public: initialValues?.is_public || false,
      slug: initialValues?.slug || null,
      right_column_notepad: initialValues?.right_column_notepad || "", 
      lyrics_language: Array.isArray(initialValues?.lyrics_language) ? initialValues.lyrics_language : (initialValues?.lyrics_language ? [initialValues.lyrics_language] : []), 
      progress_data: initialValues?.progress_data || {}, 
      release_date: initialValues?.release_date ? new Date(initialValues.release_date) : null,
      recording_date: initialValues?.recording_date ? new Date(initialValues.recording_date as any) : null,
      featured_artists: Array.isArray(initialValues?.featured_artists) ? initialValues.featured_artists.join(', ') : initialValues?.featured_artists || '',
      writers: Array.isArray(initialValues?.writers) ? initialValues.writers.join(', ') : initialValues?.writers || '', 
      producers: Array.isArray(initialValues?.producers) ? initialValues.producers.join(', ') : initialValues?.producers || '', 
      duration: initialValues?.duration || null,
    }, 
    mode: 'onChange',
  });

  // ... (rest of the state, refs, and functions like handleWaveformReady, AI handlers, file upload, etc. remain the same as before)
  const [uploadingAudio, setUploadingAudio] = useState(false);
  const [uploadedAudioUrl, setUploadedAudioUrl] = useState<string | null>(initialValues?.audio_url || null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadedCoverUrl, setUploadedCoverUrl] = useState<string | null>(initialValues?.cover_url || null); 
  const audioFileInputRef = useRef<HTMLInputElement>(null);
  const imageFileInputRef = useRef<HTMLInputElement>(null);
  const quillRef = useRef<any>(null); 
  const [lyricsContent, setLyricsContent] = useState<string>(initialValues?.lyrics || "");
  const [currentSelectionRange, setCurrentSelectionRange] = useState<any>(null); 
  const [aiConfig, setAiConfig] = useState<AiConfig>({ provider: 'ollama', model: '', temperature: 0.7 });
  const [aiLoading, setAiLoading] = useState(false);
  const [aiLastResult, setAiLastResult] = useState<string | undefined>(undefined);
  const [aiError, setAiError] = useState<string | undefined>(undefined);
  const [aiHistory, setAiHistory] = useState<AiHistoryItem[]>([]);
  const [aiMaxOutputTokens, setAiMaxOutputTokens] = useState<number>(256);
  const [aiGeneralPrompt, setAiGeneralPrompt] = useState<string>("You are a helpful assistant for songwriting.");
  const [showAiConfigMenu, setShowAiConfigMenu] = useState(false);
  const [isAiPanelCollapsed, setIsAiPanelCollapsed] = useState(false);
  const [showAiHistory, setShowAiHistory] = useState(false); 
  const [isVaultPanelCollapsed, setIsVaultPanelCollapsed] = useState(true); 
  const songVaultActionsRef = useRef<SongVaultActions | null>(null); 
  const { limits: planLimits, isLoading: isLoadingPlanLimits } = usePlanLimits(); 

  useImperativeHandle(ref, () => ({ getVaultActions: () => songVaultActionsRef.current }));
  const handleWaveformReady = useCallback((duration: number) => { /* ... */ }, [form]);
  const stripHtml = (html: string): string => { /* ... */ return html.replace(/<[^>]+>/g, ''); };
  const handleLyricsChange = (newContent: string) => { /* ... */ };
  const handleEditorSelectionChange = (range: any, source: any, editor: any) => { /* ... */ };
  const addAiHistory = (userPrompt: string, assistantResponse: string) => { /* ... */ };
  const handleAiFormatLayout = async () => { /* ... */ };
  const handleAiGeneralSuggestions = async () => { /* ... */ };
  const handleAiRhymeSuggestions = async () => { /* ... */ };
  const handleAiMelodySuggestion = async () => { /* ... */ };
  const handleAiRecordingAdvice = async () => { /* ... */ };
  const handleAiInstrumentationSuggestion = async () => { /* ... */ };
  const handleAiCreativeFx = async () => { /* ... */ };
  const handleAiArrangementAdvice = async () => { /* ... */ };
  const handleAiAnalyzeTone = async () => { /* ... */ };
  const handleAiCorrect = async () => { /* ... */ };
  const handleAiTranslate = async (lang: string) => { /* ... */ };
  const handleAiEditGeneralPrompt = useCallback((newPrompt: string) => { /* ... */ }, []);
  const handleAiConfigChange = (newPartialConfig: Partial<AiConfig>) => { /* ... */ };
  const handleFileUpload = async (file: File, type: 'audio' | 'image') => { /* ... */ };
  const onSubmitWithStatus = (status?: 'draft' | 'published') => { /* ... */ };
  const handleAiGenerate = async (customPrompt?: string) => { /* ... */ };
  useEffect(() => { /* for edit mode initialValues */ }, [mode, initialValues, form]); 
  useEffect(() => { /* for audio/cover/lyrics initialValues */ }, [initialValues, mode]);
  useEffect(() => { /* AI Config loading logic */ }, []);


  return (
    <RHFFormProvider {...form}>
      <form className="h-full flex flex-col" onSubmit={form.handleSubmit(data => onFormSubmit(data))}>
        {/* Header Section with Cover, Title, Audio, Actions */}
        <div className="w-full bg-background/90 py-6 px-0 md:px-4 flex flex-col md:flex-row md:items-center gap-8 border-b mb-8 max-w-none">
          {/* ... Cover Image Upload ... */}
          <div className="flex flex-col items-center md:items-start gap-4 w-full md:w-1/4">
            <div className="w-full flex flex-col items-center">
              <FormField control={form.control} name="cover_url" render={({ field }) => ( <FormItem><FormLabel className="text-sm">Image de couverture</FormLabel><FormControl><div className="flex flex-col items-center gap-2"><Input type="file" accept="image/*" ref={imageFileInputRef} onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'image')} className="hidden" id="image-upload"/><Button type="button" variant="outline" size="sm" onClick={() => imageFileInputRef.current?.click()} disabled={uploadingImage || isSubmitting}>{uploadingImage ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}{uploadedCoverUrl ? 'Changer' : 'Choisir une image'}</Button>{uploadedCoverUrl && uploadedCoverUrl.startsWith('http') && ( <div className="w-[180px] h-[180px] mt-2 rounded-md overflow-hidden relative border"><Image src={uploadedCoverUrl} alt="Preview" fill className="object-cover" /></div>)}</div></FormControl></FormItem>)}/>
            </div>
          </div>
          {/* ... Title, Artist Name, Audio Upload, Action Buttons ... */}
          <div className="flex-1 flex flex-col items-center md:items-start gap-4 w-full">
            <FormField control={form.control} name="title" render={({ field }) => ( <FormItem className="w-full"><FormLabel className="sr-only">Titre du morceau</FormLabel><FormControl><Input className="text-3xl md:text-4xl font-bold w-full bg-transparent border-none shadow-none px-0 mb-2" placeholder="Titre du morceau *" {...field} /></FormControl><FormMessage /></FormItem> )}/>
            {(form.watch('artist_name') ?? '').trim() !== '' && ( <div className="flex items-center mt-1 mb-2"><span className="px-3 py-1 rounded-full bg-secondary text-base font-medium text-foreground/80 shadow-sm">{form.watch('artist_name')}</span></div>)}
            <FormField 
              control={form.control} 
              name="audio_url" 
              render={({ field }) => ( 
                <FormItem className="w-full">
                  <FormLabel className="text-sm">Fichier audio</FormLabel>
                  <FormControl>
                    <div className="flex flex-col gap-2">
                      <Input type="file" accept="audio/*" ref={audioFileInputRef} onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0], 'audio')} className="hidden" id="audio-upload"/>
                      <Button type="button" variant="outline" size="sm" onClick={() => audioFileInputRef.current?.click()} disabled={uploadingAudio || isSubmitting}>
                        {uploadingAudio ? <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" /> : <UploadCloud className="mr-2 h-4 w-4" />}
                        {uploadedAudioUrl ? 'Changer' : 'Choisir un fichier audio'}
                      </Button>
                      {uploadedAudioUrl && typeof uploadedAudioUrl === 'string' && uploadedAudioUrl.startsWith('http') ? ( 
                        <AudioWaveformPreview 
                          audioUrl={uploadedAudioUrl} 
                          song={initialValues && 'id' in initialValues && 'user_id' in initialValues ? (initialValues as unknown as Song) : undefined} 
                          onReady={handleWaveformReady}
                          allowDownload={form.watch('allow_downloads')} 
                        /> 
                      ) : null}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem> 
              )}
            />
            <div className="flex gap-2 mt-4">
              <Button variant="outline" onClick={() => router.back()} disabled={isSubmitting || uploadingAudio || uploadingImage}>Annuler</Button>
              {mode === 'create' ? (
                <>
                  <Button type="button" onClick={() => onSubmitWithStatus('draft')} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="secondary">{(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}Enregistrer en Brouillon</Button>
                  <Button type="button" onClick={() => onSubmitWithStatus('published')} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg">{(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}Publier</Button>
                </>
              ) : (
                <Button type="button" onClick={() => onSubmitWithStatus(form.getValues('status'))} disabled={isSubmitting || uploadingAudio || uploadingImage} variant="default" className="bg-primary text-white font-semibold shadow-lg">{(isSubmitting || uploadingAudio || uploadingImage) && <LoadingSpinner className="mr-2 h-4 w-4 animate-spin" />}Sauvegarder</Button>
              )}
            </div>
          </div>
        </div>

        {/* Main content area with Song Form and Song Vault */}
        <div className="flex flex-row flex-grow overflow-hidden relative">
          <ScrollArea className="flex-1 p-4 overflow-y-auto">
            <div className="w-full space-y-6"> 
              <Tabs defaultValue="general" className="w-full">
                <TabsList className="grid w-full grid-cols-4 mb-4"> 
                  <TabsTrigger value="general">Général</TabsTrigger>
                  <TabsTrigger value="lyrics-ia">Paroles / IA</TabsTrigger>
                  <TabsTrigger value="progress-stats">Progression & Stats</TabsTrigger> 
                  <TabsTrigger value="publication">Publication & Options</TabsTrigger>
                </TabsList>

                <TabsContent value="general" className="w-full">
                  <div className="space-y-4 w-full"> 
                    <FormField control={form.control} name="title" render={({ field }) => ( <FormItem><FormLabel>Titre du morceau *</FormLabel><FormControl><Input placeholder="Ex: Mon Super Hit" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    
                    <FormField
                      control={form.control}
                      name="attribution_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Publié par</FormLabel>
                          <Select onValueChange={(value) => {
                            field.onChange(value);
                            if (value === 'user') {
                              form.setValue('band_id', null); 
                              form.setValue('artist_name', user?.display_name || user?.email || ""); 
                            } else {
                              const firstBand = userBands?.[0];
                              form.setValue('artist_name', firstBand?.name || ""); 
                              if(firstBand) form.setValue('band_id', firstBand.id); // Also set band_id if selecting band
                            }
                          }} defaultValue={field.value}>
                            <FormControl><SelectTrigger><SelectValue placeholder="Sélectionner l'auteur" /></SelectTrigger></FormControl>
                            <SelectContent>
                              <SelectItem value="user">Moi ({user?.display_name || user?.email})</SelectItem>
                              <SelectItem value="band" disabled={!userBands || userBands.length === 0}>Un de mes groupes</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch('attribution_type') === 'band' ? (
                      <FormField
                        control={form.control}
                        name="band_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Groupe</FormLabel>
                            <Select onValueChange={(value) => {
                                field.onChange(value);
                                const selectedBand = userBands.find(b => b.id === value);
                                form.setValue('artist_name', selectedBand?.name || "");
                            }} value={field.value || undefined}>
                              <FormControl><SelectTrigger disabled={!userBands || userBands.length === 0}><SelectValue placeholder="Sélectionner un groupe" /></SelectTrigger></FormControl>
                              <SelectContent>
                                {userBands.map(band => <SelectItem key={band.id} value={band.id}>{band.name}</SelectItem>)}
                              </SelectContent>
                            </Select>
                            {(!userBands || userBands.length === 0) && <FormDescription>Vous n'êtes membre d'aucun groupe ou aucun groupe n'est disponible.</FormDescription>}
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : ( 
                       <FormField control={form.control} name="artist_name" render={({ field }) => ( <FormItem><FormLabel>Nom d'artiste (pour ce morceau) *</FormLabel><FormControl><Input placeholder="Ex: Mon Nom d'Artiste" {...field} /></FormControl><FormMessage /></FormItem> )}/>
                    )}
                    
                    <FormField control={form.control} name="featured_artists" render={({ field }) => ( <FormItem><FormLabel>Artistes en featuring (Optionnel)</FormLabel><FormControl><Input placeholder="Artiste 1, Artiste 2, ..." {...field} value={Array.isArray(field.value) ? field.value.join(', ') : field.value || ''} onChange={e => field.onChange(e.target.value)} /></FormControl><FormDescription>Séparez les noms par des virgules.</FormDescription><FormMessage /></FormItem> )}/>
                    
                    <MetadataFields
                       control={form.control}
                       configs={[
                         { name: 'genres', label: 'Genres', options: genreOptions, placeholder: 'Ajouter des genres' },
                         { name: 'moods', label: 'Ambiances / Moods', options: moodOptions, placeholder: 'Ajouter des ambiances' },
                         { name: 'tags', label: 'Tags', options: tagOptions, placeholder: 'Ajouter des tags' },
                         { name: 'instrumentation', label: 'Instrumentation', options: instrumentationOptions, placeholder: 'Ajouter des instruments' },
                         { name: 'lyrics_language', label: 'Langues des paroles', options: languageOptions, placeholder: 'Ajouter des langues' },
                       ]}
                       gridCols="grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
                       gap="gap-6"
                     />

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> 
                      <FormField control={form.control} name="key" render={({ field }) => ( <FormItem><FormLabel>Tonalité</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value || ''}><SelectTrigger><SelectValue placeholder="Sélectionner une tonalité" /></SelectTrigger><SelectContent className="bg-background border shadow-md">{musicalKeys.map(k => <SelectItem key={k} value={k}>{k}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="bpm" render={({ field }) => ( <FormItem><FormLabel>BPM</FormLabel><FormControl><Input type="number" placeholder="120" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="time_signature" render={({ field }) => ( <FormItem><FormLabel>Signature Rythmique</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value || ''}><SelectTrigger><SelectValue placeholder="Sélectionner une signature" /></SelectTrigger><SelectContent className="bg-background border shadow-md">{timeSignatures.map(ts => <SelectItem key={ts} value={ts}>{ts}</SelectItem>)}</SelectContent></Select><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="capo" render={({ field }) => ( <FormItem><FormLabel>Capo</FormLabel><FormControl><Input type="number" placeholder="0" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="tuning_frequency" render={({ field }) => ( <FormItem><FormLabel>Fréquence d'accordage (Hz)</FormLabel><FormControl><Input type="number" placeholder="440" {...field} onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                    </div>
                    
                    <FormField control={form.control} name="description" render={({ field }) => ( <FormItem><FormLabel>Description</FormLabel><FormControl><Textarea placeholder="Décrivez votre morceau..." className="min-h-[100px]" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                    
                    <Separator className="my-6" />
                    <h3 className="text-lg font-medium">Crédits & Rôles</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField control={form.control} name="composer_name" render={({ field }) => ( <FormItem><FormLabel>Compositeur</FormLabel><FormControl><Input placeholder="Ex: Jean Dupont" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="writers" render={({ field }) => ( <FormItem><FormLabel>Auteurs/Paroliers</FormLabel><FormControl><Input placeholder="Auteur 1, Auteur 2, ..." {...field} value={Array.isArray(field.value) ? field.value.join(', ') : field.value || ''} onChange={e => field.onChange(e.target.value)} /></FormControl><FormDescription>Séparez les noms par des virgules.</FormDescription><FormMessage /></FormItem> )}/>
                      <FormField control={form.control} name="producers" render={({ field }) => ( <FormItem><FormLabel>Producteurs</FormLabel><FormControl><Input placeholder="Producteur 1, Producteur 2, ..." {...field} value={Array.isArray(field.value) ? field.value.join(', ') : field.value || ''} onChange={e => field.onChange(e.target.value)} /></FormControl><FormDescription>Séparez les noms par des virgules.</FormDescription><FormMessage /></FormItem> )}/>
                    </div>

                    <Separator className="my-6" />
                    <h3 className="text-lg font-medium">Notes</h3>
                     <FormField control={form.control} name="bloc_note" render={({ field }) => ( <FormItem><FormLabel>Notes de Production</FormLabel><FormControl><ScrollArea className="h-40 w-full rounded-md border p-2"><Textarea placeholder="Notes sur la composition, l'enregistrement..." className="min-h-[120px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-1" {...field} value={field.value ?? ''} /></ScrollArea></FormControl><FormMessage /></FormItem> )}/>
                    <FormField control={form.control} name="right_column_notepad" render={({ field }) => ( <FormItem><FormLabel>Bloc-notes (colonne de droite)</FormLabel><FormControl><ScrollArea className="h-40 w-full rounded-md border p-2"><Textarea placeholder="Notes additionnelles pour la colonne de droite..." className="min-h-[120px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-1" {...field} value={field.value ?? ''} /></ScrollArea></FormControl><FormDescription>Sera affiché dans la colonne de droite sur la page publique du morceau.</FormDescription><FormMessage /></FormItem> )}/>
                    
                    {planLimits?.page_customisation && (
                      <>
                        <Separator className="my-6" />
                        <h3 className="text-lg font-medium">Personnalisation Avancée (Pro/Studio)</h3>
                        <FormField control={form.control} name="custom_css" render={({ field }) => ( <FormItem><FormLabel>CSS Personnalisé</FormLabel><FormControl><Textarea placeholder="Entrez votre CSS personnalisé ici..." className="min-h-[150px] font-mono text-sm" {...field} value={field.value ?? ''} /></FormControl><FormDescription>Ajoutez des styles CSS Tailwind pour personnaliser l'apparence de la page du morceau.</FormDescription><FormMessage /></FormItem> )}/>
                      </>
                    )}
                </div>
              </TabsContent>

              {/* ... Other TabsContent (lyrics-ia, publication, progress-stats) remain the same ... */}
              <TabsContent value="lyrics-ia" className="w-full p-0">{/* ... */}</TabsContent>
              <TabsContent value="publication">{/* ... */}</TabsContent>
              <TabsContent value="progress-stats" className="w-full">{/* ... */}</TabsContent>
              
              </Tabs>
            </div>
          </ScrollArea>

          {/* Right Column: Song Vault (Collapsible) */}
          {!isVaultPanelCollapsed && (
            <div className="w-1/3 min-w-[320px] max-w-[480px] border-l bg-card flex flex-col overflow-hidden">
              <div className="flex items-center justify-between p-2 border-b sticky top-0 bg-card z-10">
                <h3 className="font-semibold text-sm ml-1">Coffre à Chansons</h3>
                <Button variant="ghost" size="icon" onClick={() => setIsVaultPanelCollapsed(true)} className="h-7 w-7" title="Réduire le Coffre">
                  <PanelRightClose className="h-4 w-4" />
                </Button>
              </div>
              <ScrollArea className="flex-grow">
                <div className="p-3"> 
                  <SongVault songId={initialValues?.id} userId={initialValues?.user_id} supabaseClient={supabaseClient} onReady={(actions) => { songVaultActionsRef.current = actions; }} />
                </div>
              </ScrollArea>
            </div>
          )}
          {isVaultPanelCollapsed && (
            <div className="absolute top-0 right-0 mt-2 mr-2 z-20">
              <Button variant="outline" size="icon" onClick={() => setIsVaultPanelCollapsed(false)} className="rounded-full shadow-lg bg-background hover:bg-muted h-8 w-8" title="Ouvrir le Coffre à Chansons (Vault)">
                <Layers className="h-4 w-4" />
                <span className="sr-only">Ouvrir le Coffre à Chansons</span>
              </Button>
            </div>
          )}
        </div>
      </form>
    </RHFFormProvider>
  );
});

SongForm.displayName = 'SongForm';

export default SongForm;
