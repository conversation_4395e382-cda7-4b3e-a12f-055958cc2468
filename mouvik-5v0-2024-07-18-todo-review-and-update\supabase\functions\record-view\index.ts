import { serve } from 'https://deno.land/std@0.192.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// WARNING: It's generally not recommended to expose the service_role key directly in client-side accessible Edge Functions
// without proper row-level security (RLS) on the target tables.
// For incrementing counts, it's often safer to use an RPC function called with the user's session,
// or ensure RLS prevents unauthorized increments if using service_role.
// However, if this function is simple and RLS is in place on 'playlists', 'songs', etc. to prevent direct updates,
// this might be acceptable for a simple counter.
// Consider the security implications carefully.

// Standard CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Or specific origin like 'http://localhost:3000'
  'Access-Control-Allow-Methods': 'POST, OPTIONS', // OPTIONS is crucial for preflight
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle OPTIONS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Client sends snake_case: { resource_id: "...", resource_type: "..." }
    // Destructure with renaming to use camelCase internally in the function.
    const { resource_id: resourceId, resource_type: resourceType } = await req.json();

    if (!resourceId || !resourceType) {
      // This error message will now be more accurate if the client sends one but not the other,
      // or if the destructuring failed for other reasons (e.g. not valid JSON).
      return new Response(JSON.stringify({ error: 'Missing resource_id or resource_type in request body' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    let tableName = '';
    switch (resourceType) {
      case 'playlist':
        tableName = 'playlists';
        break;
      case 'song':
        tableName = 'songs';
        break;
      case 'album':
        tableName = 'albums';
        break;
      // Add other resource types as needed
      default:
        return new Response(JSON.stringify({ error: 'Invalid resourceType' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        });
    }

    // Call the RPC function to increment view_count atomically
    const { data: rpcData, error: rpcError } = await supabaseAdmin.rpc('increment_view_count', {
      p_table_name: tableName,
      p_resource_id: resourceId,
    });

    if (rpcError) {
      console.error(`Error calling increment_view_count RPC for ${resourceType} ${resourceId}:`, rpcError);
      return new Response(JSON.stringify({ error: `Failed to increment view count: ${rpcError.message}` }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }
    
    // rpcData should be the new view_count directly if the RPC returns INTEGER
    // If the RPC returns a record or array, adjust accordingly. Assuming it returns the integer.
    const newViewCount = rpcData; 

    return new Response(JSON.stringify({ success: true, newViewCount: newViewCount }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (e: any) { 
    console.error('Error in record-view function:', e);
    return new Response(JSON.stringify({ error: e.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
