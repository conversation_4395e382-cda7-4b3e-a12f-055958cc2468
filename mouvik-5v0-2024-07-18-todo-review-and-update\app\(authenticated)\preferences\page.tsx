import React from "react";
import { redirect } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { FullProfileForm } from '@/components/full-profile-form';
import { LanguageForm } from '../../../components/language-form';
import { SettingsForm } from '../../../components/settings-form';
import { SubscriptionForm } from "@/components/subscription-form";
import { ThemeForm } from "@/components/theme-form";
import { createSupabaseServerClient } from '@/lib/supabase/server';

export default async function PreferencesPage() {
  const supabase = createSupabaseServerClient();

  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    // This should ideally be handled by middleware in the (authenticated) group
    console.error('Error fetching user in preferences or no user found:', error);
    redirect('/login'); // Redirect if no user
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Préférences</h1>
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full grid-cols-5 mb-6">
          <TabsTrigger value="profile">Profil</TabsTrigger>
          <TabsTrigger value="subscription">Abonnements & Crédits</TabsTrigger>
          <TabsTrigger value="theme">Thème & Apparence</TabsTrigger>
          <TabsTrigger value="language">Langue</TabsTrigger>
          <TabsTrigger value="settings">Paramètres avancés</TabsTrigger>
        </TabsList>
        <TabsContent value="profile">
          <FullProfileForm user={user} />
        </TabsContent>
        <TabsContent value="subscription">
          <SubscriptionForm />
        </TabsContent>
        <TabsContent value="theme">
          <ThemeForm />
        </TabsContent>
        <TabsContent value="language">
          <LanguageForm />
        </TabsContent>
        <TabsContent value="settings">
          <SettingsForm />
        </TabsContent>
      </Tabs>
    </div>
  );
}
