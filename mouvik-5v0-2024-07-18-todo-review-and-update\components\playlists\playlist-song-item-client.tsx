"use client";

import Link from "next/link";
import Image from "next/image";
import { Card } from "@/components/ui/card";
import { Music } from "lucide-react";
import { AudioSliderPlayer } from '@/components/audio-slider-player';
import { useAudio } from '@/contexts/audio-context';
import { usePlaySong } from '@/hooks/use-play-song';
import type { Song } from "@/types"; // Assuming Song type is comprehensive

// Interface for the song prop, matching what PublicPlaylistPage provides
interface PlaylistSongForClient {
  id: string;
  title: string;
  duration: number | null;
  cover_url: string | null;
  audio_url?: string | null;
  creator_user_id?: string; // ID of the user who created the song
  artist_name?: string; // Needed for play context
  profiles: { // Assuming profiles is always present from the join
    username: string | null;
    display_name: string | null;
  } | null;
  slug?: string | null;
}

interface PlaylistSongItemClientProps {
  song: PlaylistSongForClient;
  index: number;
  artistDisplayName: string; // Fallback artist name from playlist creator or album artist
}

export function PlaylistSongItemClient({ song, index, artistDisplayName }: PlaylistSongItemClientProps) {
  const { currentSong, isPlaying } = useAudio();
  const { play } = usePlaySong();

  // Construct a more complete Song object for the play hook if necessary
  const songForPlayer: Song = {
    id: song.id,
    title: song.title,
    duration: song.duration || 0,
    cover_url: song.cover_url || null,
    audio_url: song.audio_url || null,
    creator_user_id: song.creator_user_id || '', // Ensure creator_user_id is a string
    artist_name: song.artist_name || song.profiles?.display_name || song.profiles?.username || artistDisplayName,
    slug: song.slug || null,
    // Add other fields from Song type with defaults if not available in PlaylistSongForClient
    genres: [], // Default or fetch if needed
    moods: [],  // Default or fetch if needed
    instrumentation: [], // Default or fetch if needed
    is_public: true, // Assuming public context
    // ... other Song fields with defaults
  };

  return (
    <Card className="flex items-center p-3 gap-4 hover:bg-muted/50">
      <span className="text-sm text-muted-foreground w-6 text-center">{index + 1}</span>
      <Link href={`/song/${song.slug || song.id}`} className="flex-shrink-0">
        {song.cover_url ? (
          <Image src={song.cover_url} alt={song.title} width={48} height={48} className="h-12 w-12 object-cover rounded-md" />
        ) : (
          <div className="h-12 w-12 bg-muted rounded-md flex items-center justify-center">
            <Music className="h-6 w-6 text-muted-foreground" />
          </div>
        )}
      </Link>
      <div className="flex-1">
        <Link href={`/song/${song.slug || song.id}`} className="font-medium hover:underline">{song.title}</Link> 
        <p className="text-xs text-muted-foreground">
          {song.profiles?.display_name || song.profiles?.username || "Artiste inconnu"}
        </p>
        {song.audio_url && (
          <div className="mt-1">
            <AudioSliderPlayer
              audioSrc={song.audio_url}
              isPlaying={currentSong?.id === song.id && isPlaying}
              onPlayPause={() => play(songForPlayer)}
            />
          </div>
        )}
      </div>
      {song.duration && (
        <span className="text-sm text-muted-foreground">{Math.floor(song.duration / 60)}:{String(song.duration % 60).padStart(2, '0')}</span>
      )}
    </Card>
  );
}
