"use client"

import { useEffect, useRef } from "react"

interface LineChartProps {
  data: { date: string; écoutes: number }[]
}

export function LineChart({ data }: LineChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Définir les dimensions du canvas
    const dpr = window.devicePixelRatio || 1
    const rect = canvas.getBoundingClientRect()
    canvas.width = rect.width * dpr
    canvas.height = rect.height * dpr
    ctx.scale(dpr, dpr)

    // Définir les marges
    const margin = { top: 20, right: 20, bottom: 30, left: 40 }
    const width = rect.width - margin.left - margin.right
    const height = rect.height - margin.top - margin.bottom

    // Calculer les échelles
    const xScale = width / (data.length - 1)
    const yMax = Math.max(...data.map((d) => d.écoutes))
    const yScale = height / yMax

    // Dessiner l'axe des y
    ctx.beginPath()
    ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
    for (let i = 0; i <= 5; i++) {
      const y = margin.top + height - (i * height) / 5
      ctx.moveTo(margin.left, y)
      ctx.lineTo(margin.left + width, y)
      ctx.stroke()

      // Étiquettes de l'axe des y
      ctx.fillStyle = "rgba(255, 255, 255, 0.5)"
      ctx.font = "10px sans-serif"
      ctx.textAlign = "right"
      ctx.fillText(Math.round((i * yMax) / 5).toString(), margin.left - 5, y + 3)
    }

    // Dessiner l'axe des x
    ctx.beginPath()
    ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
    for (let i = 0; i < data.length; i += Math.ceil(data.length / 5)) {
      const x = margin.left + i * xScale
      ctx.moveTo(x, margin.top)
      ctx.lineTo(x, margin.top + height)
      ctx.stroke()

      // Étiquettes de l'axe des x
      ctx.fillStyle = "rgba(255, 255, 255, 0.5)"
      ctx.font = "10px sans-serif"
      ctx.textAlign = "center"
      ctx.fillText(data[i].date, x, margin.top + height + 15)
    }

    // Dessiner la ligne du graphique
    ctx.beginPath()
    ctx.strokeStyle = "rgb(0, 200, 200)"
    ctx.lineWidth = 2
    data.forEach((d, i) => {
      const x = margin.left + i * xScale
      const y = margin.top + height - d.écoutes * yScale
      if (i === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.stroke()

    // Dessiner le gradient sous la ligne
    const gradient = ctx.createLinearGradient(0, margin.top, 0, margin.top + height)
    gradient.addColorStop(0, "rgba(0, 200, 200, 0.2)")
    gradient.addColorStop(1, "rgba(0, 200, 200, 0)")
    ctx.fillStyle = gradient
    ctx.beginPath()
    data.forEach((d, i) => {
      const x = margin.left + i * xScale
      const y = margin.top + height - d.écoutes * yScale
      if (i === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.lineTo(margin.left + (data.length - 1) * xScale, margin.top + height)
    ctx.lineTo(margin.left, margin.top + height)
    ctx.closePath()
    ctx.fill()

    // Dessiner les points
    data.forEach((d, i) => {
      const x = margin.left + i * xScale
      const y = margin.top + height - d.écoutes * yScale
      ctx.beginPath()
      ctx.arc(x, y, 4, 0, 2 * Math.PI)
      ctx.fillStyle = "rgb(0, 200, 200)"
      ctx.fill()
      ctx.strokeStyle = "white"
      ctx.lineWidth = 1
      ctx.stroke()
    })
  }, [data])

  return <canvas ref={canvasRef} className="h-full w-full" />
}
