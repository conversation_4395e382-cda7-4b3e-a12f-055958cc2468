"use client";

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit3, Trash2, PlayCircle, Disc, UserCircle, Copy, Loader2 } from 'lucide-react'; // Added Loader2
import type { Album, UserProfile } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useState } from 'react'; 
import { useUser } from '@/contexts/user-context'; 
import { getSupabaseClient } from '@/lib/supabase/client'; 
import { format, formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils'; 

interface AlbumForListItem extends Album {
  songs_count?: number;
  profiles?: UserProfile | null; 
  is_public?: boolean; 
}

interface AlbumListItemProps {
  album: AlbumForListItem;
  onDelete: (albumId: string) => void; 
  onUpdateStatus?: (albumId: string, newStatus: { is_public: boolean; slug: string | null }) => void;
  density?: number; // 0: compact, 1: default, 2: spacious
}

export function AlbumListItem({ album, onDelete, onUpdateStatus, density = 1 }: AlbumListItemProps) {
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);

  const viewPageUrl = `/albums/${album.id}`; 

  const handleDelete = () => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'album "${album.title}" ?`)) {
      onDelete(album.id);
    }
  };

  const handlePlay = () => {
    toast({ title: "Lecture (Placeholder)", description: `Lancer la lecture de l'album "${album.title}".` });
  };

  const handleDuplicate = () => {
    toast({ title: "Duplication (Placeholder)", description: `L'album "${album.title}" serait dupliqué.` });
  };
  
  const artistProfile = album.profiles;

  const togglePublicStatus = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!user || !album.user_id || user.id !== album.user_id || isTogglingStatus) {
      if (user?.id !== album.user_id) {
        toast({ title: "Action non autorisée", description: "Seul le créateur peut changer la visibilité.", variant: "destructive" });
      }
      return;
    }
    setIsTogglingStatus(true);
    try {
      const { data, error } = await supabase.rpc('toggle_album_public_status', {
        p_album_id: album.id,
        p_user_id: user.id,
      });
      if (error) throw error;
      if (data && data.length > 0) {
        const { new_is_public, new_slug } = data[0]; // Expect new_slug
        toast({
          title: "Statut de l'album mis à jour",
          description: `L'album "${album.title}" est maintenant ${new_is_public ? 'public' : 'privé'}.`,
        });
        if (onUpdateStatus) {
          onUpdateStatus(album.id, { is_public: new_is_public, slug: new_slug });
        }
      } else {
        throw new Error("Réponse invalide de la fonction RPC.");
      }
    } catch (err: any) {
      console.error("Error toggling album status:", err);
      toast({
        title: "Erreur",
        description: err.message || "Impossible de changer le statut de l'album.",
        variant: "destructive",
      });
    } finally {
      setIsTogglingStatus(false);
    }
  };

  const paddingClasses = {
    0: 'p-2', // compact
    1: 'p-3', // default
    2: 'p-4', // spacious
  };

  return (
    <div className={cn("flex items-center gap-4 hover:bg-muted/50 rounded-md border", paddingClasses[density as keyof typeof paddingClasses] || 'p-3')}>
      <div className="flex-shrink-0 relative">
        {album.is_public !== undefined && (
            <div 
                title={album.is_public ? "Album public (cliquer pour changer)" : "Album privé (cliquer pour changer)"}
                onClick={togglePublicStatus}
                className={cn(
                    "absolute top-0.5 left-0.5 z-10 w-3.5 h-3.5 rounded-full border-2 border-white shadow-lg cursor-pointer flex items-center justify-center", // Enhanced visibility
                    album.is_public ? "bg-green-500 hover:bg-green-600" : "bg-red-500 hover:bg-red-600"
                )}
            >
                {isTogglingStatus && <Loader2 className="h-2 w-2 animate-spin text-white" />}
            </div>
        )}
        <Link href={viewPageUrl}>
          {album.cover_url ? (
            <Image
              src={album.cover_url}
              alt={album.title}
              width={48}
              height={48}
              className="object-cover w-12 h-12 rounded-md"
            />
          ) : (
            <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
              <Disc className="w-6 h-6 text-muted-foreground" />
            </div>
          )}
        </Link>
      </div>

      <div className="flex-1 min-w-0">
        <Link href={viewPageUrl} className="hover:underline">
          <p className="text-sm font-medium truncate" title={album.title}>{album.title}</p>
        </Link>
        <div className="text-xs text-muted-foreground flex items-center gap-1.5 truncate">
          {artistProfile ? (
            <Link href={`/artists/${artistProfile.username}`} className="hover:underline">
              {artistProfile.display_name || artistProfile.username}
            </Link>
          ) : (
            <span>{album.artist_name || "Artiste inconnu"}</span>
          )}
          {album.songs_count !== undefined && (
            <>
              <span className="mx-1">•</span>
              <span>{album.songs_count} morceau{album.songs_count === 1 ? '' : 'x'}</span>
            </>
          )}
        </div>
      </div>

      <div className="hidden md:flex items-center gap-3 text-xs text-muted-foreground flex-shrink-0">
        {album.release_date && (
          <span title={`Sorti le ${format(new Date(album.release_date), 'PPP', { locale: fr })}`}>
            {format(new Date(album.release_date), 'd MMM yyyy', { locale: fr })}
          </span>
        )}
        {album.created_at && !album.release_date && ( 
             <span title={`Créé ${new Date(album.created_at).toLocaleDateString()}`}>
             {formatDistanceToNow(new Date(album.created_at), { locale: fr, addSuffix: true })}
           </span>
        )}
      </div>
      
      <div className="flex items-center gap-1 flex-shrink-0">
        <Button onClick={handlePlay} size="sm" variant="ghost" className="h-8 px-2">
            <PlayCircle className="mr-1.5 h-4 w-4" /> Écouter
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Options</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => router.push(`/albums/${album.id}/edit`)}>
              <Edit3 className="mr-2 h-4 w-4" /> Modifier
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}>
              <Copy className="mr-2 h-4 w-4" /> Dupliquer
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10">
              <Trash2 className="mr-2 h-4 w-4" /> Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
