"use client"

import React, { useEffect, useRef, useState, useCallback } from "react" // Added useCallback
import WaveSurfer from "wavesurfer.js"
import { Play, Pause } from "lucide-react";
import '../../styles/wavesurfer-halo.css';
import type { Song } from "@/types"; // Import Song type
import { useAudioPlayerStore } from "@/lib/stores/audioPlayerStore"; // Import store

interface WaveformPlayerProps {
  song: Song; // Changed from audioUrl to song object
  height?: number;
  waveColor?: string;
  progressColor?: string;
}

export function WaveformPlayer({
  song, // Use song prop
  height = 64,
  waveColor = "#38bdf8",
  progressColor = "#0ea5e9"
}: WaveformPlayerProps) {
  const isInitializedRef = useRef(false); // Ref to track initialization for Strict Mode
  const instanceHadErrorRef = useRef(false);
  const waveformRef = useRef<HTMLDivElement>(null);
  const wavesurfer = useRef<WaveSurfer | null>(null);
  
  // States from global store
  const { 
    playSong: playSongGlobal, 
    togglePlayPause: togglePlayPauseGlobal,
    seek: seekGlobal,
    currentSong: currentGlobalSong, 
    isPlaying: isGlobalPlaying,
    currentTime: globalCurrentTime,
    duration: globalDuration,
  } = useAudioPlayerStore();

  // Local state for WaveSurfer's own duration (can differ slightly or be used for display)
  const [wsDuration, setWsDuration] = useState(0);
  const [isInstanceDestroyed, setIsInstanceDestroyed] = useState(false);

  // Determine if this waveform player instance is the one currently playing globally
  const isActivePlayer = currentGlobalSong?.id === song.id;
  const displayIsPlaying = isActivePlayer && isGlobalPlaying;

  // Current time to display: if active player, use global time, else 0 or WaveSurfer's time
  const displayCurrentTime = isActivePlayer ? globalCurrentTime : 0;
  // Duration to display: if active player, use global duration, else WaveSurfer's duration or 0
  const displayDuration = isActivePlayer ? globalDuration : wsDuration;

  useEffect(() => {
    // Strict Mode guard: if already initialized for this audio_url in dev, skip re-init by second effect call
    if (isInitializedRef.current && process.env.NODE_ENV === 'development') {
      console.log(`WaveformPlayer: StrictMode Guard - Skipping re-initialization for ${song.audio_url}`);
      // Important: We still need to return a cleanup function that will reset the ref
      // if the component truly unmounts or song.audio_url changes for real.
      // However, the main instance creation is skipped.
      // The cleanup from the *first* invocation of this effect (in StrictMode) will handle the actual wsInstance.
      // This second invocation's cleanup should essentially be a no-op for wsInstance but reset the ref.
      return () => {
        if (process.env.NODE_ENV === 'development') {
          // This cleanup is for the second effect run in strict mode if it was skipped.
          // It might also run if audio_url changes *after* being skipped once.
          // Resetting here ensures that if audio_url *actually* changes, a new init can occur.
          isInitializedRef.current = false; 
        }
      };
    }

    if (!song.audio_url || !waveformRef.current) {
      if (wavesurfer.current) {
        wavesurfer.current.destroy(); // Simplified cleanup
        wavesurfer.current = null;
      }
      return;
    }

    console.log(`WaveformPlayer: PROPS RECEIVED - song.id: ${song?.id}, song.audio_url: '${song?.audio_url}'`);
    // Reset destroyed state for new instance
    setIsInstanceDestroyed(false);

    // Create a new Wavesurfer instance for this effect invocation
    instanceHadErrorRef.current = false; // Reset for this new instance
    const wsInstance = WaveSurfer.create({
      container: waveformRef.current,
      waveColor,
      progressColor,
      height,
      barWidth: 2,
      barRadius: 2,
      cursorColor: progressColor,
      cursorWidth: 2,
      normalize: true,
      interact: true,
    });

    wavesurfer.current = wsInstance;

    if (process.env.NODE_ENV === 'development') {
      isInitializedRef.current = true; // Mark as initialized for this audio_url in this effect run
    }
    
    // Load audio into WaveSurfer for visualization only, not for playback control by WaveSurfer
    const isValidUrl = song.audio_url && 
                       typeof song.audio_url === 'string' &&
                       (song.audio_url.startsWith('http://') || 
                        song.audio_url.startsWith('https://') || 
                        song.audio_url.startsWith('blob:'));

    console.log(`WaveformPlayer: VALIDATION - audio_url: '${song?.audio_url}', isValidUrl: ${isValidUrl}`);

    if (!isValidUrl || song.audio_url.includes("placeholder")) {
      if (process.env.NODE_ENV !== 'production') {
        console.warn(`WaveformPlayer: SKIPPING LOAD for '${song?.audio_url}'. Reason: Invalid, placeholder, or malformed.`);
      }
      instanceHadErrorRef.current = true;
    } else {
      console.log(`WaveformPlayer: ATTEMPTING LOAD - song ${song.id} with URL: '${song.audio_url}' (type: ${typeof song.audio_url})`, JSON.parse(JSON.stringify(song)));

      instanceHadErrorRef.current = false; // Reset error flag on successful load attempt
      wsInstance.load(song.audio_url);
    }

    wsInstance.on("ready", () => {
      setWsDuration(wsInstance.getDuration());
      // If this is the active player, reflect WaveSurfer's seek position from global store
      if (isActivePlayer && wavesurfer.current) {
         const progress = globalDuration > 0 ? globalCurrentTime / globalDuration : 0;
         wavesurfer.current.seekTo(progress);
      }
    });
    
    wsInstance.on("interaction", () => { // User clicks on waveform to seek
      if (wavesurfer.current && globalDuration > 0) {
        // Calculate new time based on WaveSurfer's internal duration and click position
        const clickProgress = wavesurfer.current.getCurrentTime() / wavesurfer.current.getDuration();
        seekGlobal(clickProgress * globalDuration);
      } else if (wavesurfer.current && wavesurfer.current.getDuration() > 0) {
        // Fallback if globalDuration is not yet set, use WaveSurfer's duration
        // This might happen if the song is loaded in WaveSurfer but not yet fully in global player
        const clickProgress = wavesurfer.current.getCurrentTime() / wavesurfer.current.getDuration();
        // If not the active player, make it active then seek
        if (!isActivePlayer) {
          playSongGlobal(song); // This will set it as current and start playing
          // seekGlobal might need to be called after a short delay or once globalDuration is set
          // For now, let's assume playSongGlobal handles initial play, then user can seek again if needed
        } else {
           seekGlobal(clickProgress * wavesurfer.current.getDuration());
        }
      }
    });

    wsInstance.on("error", (err: Error) => {
      console.error("Wavesurfer error:", err);
      instanceHadErrorRef.current = true;
      setIsInstanceDestroyed(true); // Keep for any other logic relying on this state
    });

    return () => {
      // wsInstance is the instance created by this specific effect run.
      // We should always attempt to clean it up.
      try {
        if (process.env.NODE_ENV !== 'production') {
          console.log(`WaveformPlayer: Cleanup - Attempting to destroy instance for ${song.audio_url ? song.audio_url : 'unknown audio URL'}`);
        }
        if (wavesurfer.current === wsInstance) {
          wavesurfer.current = null;
        }
        wsInstance.unAll();
        wsInstance.stop();
        wsInstance.destroy();
        if (process.env.NODE_ENV !== 'production') {
          console.log(`WaveformPlayer: Cleanup - Successfully destroyed instance for ${song.audio_url ? song.audio_url : 'unknown audio URL'}`);
        }
      } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
          console.warn(`WaveformPlayer: Cleanup - Caught AbortError during destroy for '${song.audio_url || 'unknown audio URL'}'. This is often benign during rapid cleanup.`, error);
        } else {
          console.error(`WaveformPlayer: Cleanup - Failed to destroy instance for '${song.audio_url || 'unknown audio URL'}':`, error);
        }
      }

      // wavesurfer.current should have been nullified above if it matched wsInstance.
      // Consider if setIsInstanceDestroyed(true) is still needed or how it should be managed.
      // For now, the primary goal is robust cleanup of wsInstance.

      // Reset initialized flag if the component unmounts or audio_url genuinely changes
      if (process.env.NODE_ENV === 'development') {
        isInitializedRef.current = false;
      }
    };
  }, [song.id, song.audio_url, waveColor, progressColor, height]); // Re-run if song ID, audio_url or visual props change

  // Effect to update WaveSurfer's visual seek position when global current time changes
  // for the active player
  useEffect(() => {
    if (isActivePlayer && wavesurfer.current && wavesurfer.current.getDuration() > 0) {
      const currentWaveSurferProgress = wavesurfer.current.getCurrentTime() / wavesurfer.current.getDuration();
      const globalPlayerProgress = globalDuration > 0 ? globalCurrentTime / globalDuration : 0;
      // Only seek if substantially different to avoid jitter and infinite loops
      if (Math.abs(currentWaveSurferProgress - globalPlayerProgress) > 0.02) { // Increased threshold slightly
         wavesurfer.current.seekTo(globalPlayerProgress);
      }
    }
  }, [globalCurrentTime, globalDuration, isActivePlayer]);

  // Update cursor halo based on displayIsPlaying
  useEffect(() => {
    const container = waveformRef.current;
    if (!container) return;
    const cursor = container.querySelector('.wavesurfer-cursor') as HTMLElement;
    if (cursor) {
      if (displayIsPlaying) {
        cursor.classList.add('halo-cursor');
      } else {
        cursor.classList.remove('halo-cursor');
      }
    }
  }, [displayIsPlaying]);

  const handleTogglePlay = useCallback(() => {
    if (currentGlobalSong?.id !== song.id) {
      playSongGlobal(song); // Play this new song
    } else {
      togglePlayPauseGlobal(); // Toggle current song in global player
    }
  }, [song, currentGlobalSong, playSongGlobal, togglePlayPauseGlobal]);

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    if (isActivePlayer) {
      seekGlobal(value);
    } else {
      // If not the active player, make it active and then seek.
      // This ensures the global player takes over.
      playSongGlobal(song); // This will make it the current song.
      // It's tricky to seek immediately after this if audio isn't loaded in global player.
      // A robust solution might involve a state in store like 'seekToAfterLoad'.
      // For now, playSongGlobal will start it, and user can seek again on next interaction.
      // For this iteration, starting the song is the primary action.
      // If the song is already loaded in global player but paused, this will just play it.
      // If it's a different song, it will load and play.
      // playSongGlobal will start it, and user can seek again on next interaction.
      // Or, if seekGlobal is designed to handle this scenario correctly:
      seekGlobal(value); // Attempt to seek in global store
    }
  };

  return (
    <div className="w-full flex flex-col items-center">
      <div
        ref={waveformRef}
        className="w-full bg-transparent rounded-lg shadow-md cursor-pointer"
        style={{ height }}
      />
      <div className="flex items-center gap-2 w-full mt-2">
        {/* Modern round play/pause icon button */}
        <button
          onClick={handleTogglePlay}
          aria-label={displayIsPlaying ? "Pause" : "Play"}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-cyan-500 text-white hover:bg-cyan-600 transition shadow-lg focus:outline-none focus:ring-2 focus:ring-cyan-400"
        >
          {displayIsPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
        </button>
        <input
          type="range"
          min={0}
          max={displayDuration > 0 ? displayDuration : 0} // Ensure max is not NaN or negative
          step={0.01}
          value={displayCurrentTime}
          onChange={handleSliderChange}
          className="flex-1 accent-cyan-500"
          disabled={!song.audio_url} // Disable if no audio_url for this song
        />
        <span className="text-xs tabular-nums w-16 text-right">
          {formatTime(displayCurrentTime)} / {formatTime(displayDuration)}
        </span>
      </div>
    </div>
  )
}

function formatTime(time: number) {
  const min = Math.floor(time / 60)
  const sec = Math.floor(time % 60)
  return `${min}:${sec.toString().padStart(2, "0")}`
}
