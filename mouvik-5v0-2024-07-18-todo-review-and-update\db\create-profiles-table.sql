-- Vérifions d'abord si la table profiles existe
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public'
   AND table_name = 'profiles'
);

-- Si elle n'existe pas, créons-la avec les colonnes nécessaires
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  display_name VARCHAR(255),
  avatar_url TEXT,
  bio TEXT,
  location VARCHAR(255),
  website VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Créons également une table pour les followers si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS followers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  following_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(follower_id, following_id)
);

-- Maintenant créons la vue avec les bonnes colonnes
CREATE OR REPLACE VIEW artist_profiles AS
SELECT 
  p.id,
  p.display_name,
  p.avatar_url,
  p.bio,
  p.location,
  p.website,
  p.created_at,
  p.updated_at,
  COUNT(DISTINCT a.id) AS album_count,
  COUNT(DISTINCT s.id) AS song_count,
  COUNT(DISTINCT pl.id) AS playlist_count,
  COALESCE(SUM(s.plays), 0) AS total_plays,
  COUNT(DISTINCT f.follower_id) AS follower_count
FROM 
  profiles p
LEFT JOIN 
  albums a ON p.id = a.artist_id
LEFT JOIN 
  songs s ON p.id = s.user_id
LEFT JOIN 
  playlists pl ON p.id = pl.user_id
LEFT JOIN 
  followers f ON p.id = f.following_id
GROUP BY 
  p.id, p.display_name, p.avatar_url, p.bio, p.location, p.website, p.created_at, p.updated_at;
