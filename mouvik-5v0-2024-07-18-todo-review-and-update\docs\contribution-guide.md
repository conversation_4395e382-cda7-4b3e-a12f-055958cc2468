# Contribution Guide

## Getting Started

Thank you for considering contributing to MOUVIK! This document provides guidelines and instructions for contributing to the project.

## Development Setup

1. **Clone the repository**

\`\`\`bash
git clone https://github.com/yourusername/mouvik.git
cd mouvik
\`\`\`

2. **Install dependencies**

\`\`\`bash
npm install
\`\`\`

3. **Set up environment variables**

Copy the `.env.example` file to `.env.local` and fill in the required values:

\`\`\`bash
cp .env.example .env.local
\`\`\`

4. **Start the development server**

\`\`\`bash
npm run dev
\`\`\`

## Project Structure

Please refer to the [README.md](../README.md) for an overview of the project structure.

## Coding Standards

### General Guidelines

- Use TypeScript for all new code
- Follow the existing code style and patterns
- Write self-documenting code with clear variable and function names
- Add comments for complex logic
- Use JSDoc comments for functions and components

### Component Guidelines

- Use functional components with hooks
- Keep components focused on a single responsibility
- Extract reusable logic to custom hooks
- Use proper prop typing with TypeScript interfaces
- Follow the naming convention for files and components

### Styling Guidelines

- Use Tailwind CSS for styling
- Follow the design system defined in `tailwind.config.ts`
- Use the shadcn/ui components as building blocks
- Ensure responsive design for all components

## Git Workflow

1. **Create a new branch for your feature or bugfix**

\`\`\`bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/your-bugfix-name
\`\`\`

2. **Make your changes and commit them**

\`\`\`bash
git add .
git commit -m "feat: add your feature description"
\`\`\`

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages:

- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding or updating tests
- `chore:` for maintenance tasks

3. **Push your branch to the remote repository**

\`\`\`bash
git push origin feature/your-feature-name
\`\`\`

4. **Create a pull request**

Go to the repository on GitHub and create a pull request from your branch to the main branch.

## Testing

- Write tests for new features and bug fixes
- Run existing tests before submitting a pull request
- Ensure all tests pass

\`\`\`bash
npm run test
\`\`\`

## Documentation

- Update documentation for new features or changes
- Add JSDoc comments to functions and components
- Update the README.md if necessary

## Code Review

All pull requests will be reviewed by the maintainers. Please be patient and be prepared to make changes to your code based on the feedback.

## License

By contributing to MOUVIK, you agree that your contributions will be licensed under the project's license.
