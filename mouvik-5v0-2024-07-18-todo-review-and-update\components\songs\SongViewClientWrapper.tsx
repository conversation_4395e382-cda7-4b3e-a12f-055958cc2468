"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { createBrowserClient } from '@/lib/supabase/client';
import type { Song } from '@/types';
import { Button } from "@/components/ui/button";
import { PlayButton } from '@/components/audio/play-button';
import { LikeButton } from '@/components/social/like-button';
import AudioWaveformPreview from '@/components/audio-waveform-preview';
import { Eye, Heart, PlayCircle, Share2, Download, ThumbsDown, PlusCircle } from 'lucide-react'; // Added ThumbsDown, PlusCircle
import { ViewRecorder } from '@/components/stats/view-recorder';
import { Badge } from "@/components/ui/badge"; 
import { AddToPlaylistButton } from "@/components/playlists/add-to-playlist-button";
import { DislikeButton } from '@/components/social/dislike-button'; // Added DislikeButton import
import { getResourceInteractionStoreState } from '@/lib/stores/resource-interaction-store';

// Define props including the song data and server-fetched counts
interface SongViewClientWrapperProps {
  song: Song & { albums: { title: string } | null };
  serverLikesCount: number;
  serverPlaysCount: number;
  serverViewsCount: number;
  serverDislikesCount: number; // Added
  serverIsLikedByCurrentUser: boolean; // Added
  serverIsDislikedByCurrentUser: boolean; // Added
}

export default function SongViewClientWrapper({ 
  song, 
  serverLikesCount,
  serverPlaysCount,
  serverViewsCount,
  serverDislikesCount,
  serverIsLikedByCurrentUser,
  serverIsDislikedByCurrentUser
}: SongViewClientWrapperProps) {
  const supabase = createBrowserClient();
  const [userId, setUserId] = useState<string | undefined>(undefined);
  
  const [isLiked, setIsLiked] = useState(serverIsLikedByCurrentUser);
  const [displayLikes, setDisplayLikes] = useState(serverLikesCount);
  
  const [isDisliked, setIsDisliked] = useState(serverIsDislikedByCurrentUser);
  const [displayDislikes, setDisplayDislikes] = useState(serverDislikesCount);

  useEffect(() => {
    const fetchUserId = async () => {
      const { data: sessionData } = await supabase.auth.getSession();
      setUserId(sessionData?.session?.user?.id);
    };
    fetchUserId();
  }, [supabase]);

  useEffect(() => {
    if (song && song.id) {
      const storeActions = getResourceInteractionStoreState();
      storeActions.setResourceStatus('song', song.id, {
        likeCount: serverLikesCount,
        isLiked: serverIsLikedByCurrentUser,
        dislikeCount: serverDislikesCount,
        isDisliked: serverIsDislikedByCurrentUser,
      });
    }
  }, [song, serverLikesCount, serverIsLikedByCurrentUser, serverDislikesCount, serverIsDislikedByCurrentUser]);

  // This will be called by LikeButton when a like is toggled
  // It now receives the new dislike count from the RPC if a dislike was removed
  const handleLikeToggle = (newIsLikedState: boolean, newLikeCount: number, newDislikeCount?: number) => {
    setIsLiked(newIsLikedState);
    setDisplayLikes(newLikeCount);
    if (newDislikeCount !== undefined) {
      setIsDisliked(false); // If a like was added/toggled, any dislike is removed
      setDisplayDislikes(newDislikeCount);
    }
  };

  // This will be called by DislikeButton when a dislike is toggled
  // It now receives the new like count from the RPC if a like was removed
  const handleDislikeToggle = (newIsDislikedState: boolean, newDislikeCount: number, newLikeCount?: number) => {
    setIsDisliked(newIsDislikedState);
    setDisplayDislikes(newDislikeCount);
    if (newLikeCount !== undefined) {
      setIsLiked(false); // If a dislike was added/toggled, any like is removed
      setDisplayLikes(newLikeCount);
    }
  };

  const getAiOriginLabel = (originKey?: 'none' | 'assisted' | 'hybrid' | '100%_ia' | 'full_human' | null) => {
    if (!originKey) return null;
    const options = [
      { value: 'full_human', label: '100% Humain' },
      { value: 'hybrid', label: 'Hybride (Humain + IA)' },
      { value: '100%_ia', label: '100% IA' },
      { value: 'assisted', label: 'Assisté par IA' },
      { value: 'none', label: 'Aucune IA (Contenu Humain)' }, // Or simply 'Aucune IA' if preferred
    ];
    return options.find(o => o.value === originKey)?.label || originKey.replace('_', ' ');
  };

  return (
    <div className="flex flex-col h-full justify-between text-white">
      <ViewRecorder resourceId={song.id} resourceType="song" /> {/* Record view */}
      {/* Top section: Title, Artist, Stats */}
      <div>
        <div className="flex items-center justify-between">
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight truncate" title={song.title || 'Morceau sans titre'}>
            {song.title || 'Morceau sans titre'}
          </h1>
          {/* AI Content Origin Badge */}
          {song.ai_content_origin && (
            <Badge variant="outline" className="ml-2 border-amber-400 text-amber-400 text-xs px-2 py-1 self-start">
              IA: {getAiOriginLabel(song.ai_content_origin)}
            </Badge>
          )}
        </div>
        {song.creator_user_id && song.artist_name ? (
          <Link href={`/profile/${song.creator_user_id}`} className="mt-1 text-lg text-slate-300 hover:text-slate-100 hover:underline truncate block" title={song.artist_name}> {/* Added block */}
            Par {song.artist_name}
          </Link>
        ) : (
          <p className="mt-1 text-lg text-slate-300 truncate" title={song.artist_name || 'Artiste inconnu'}>
            Par {song.artist_name || 'Artiste inconnu'}
          </p>
        )}
        
        {/* Stats Display - Integrated into header */}
        <div className="flex items-center gap-4 text-sm text-slate-400 mt-3 mb-1">
          <div className="flex items-center gap-1" title="Lectures">
            <PlayCircle className="h-4 w-4"/> 
            <span>{serverPlaysCount}</span>
          </div>
          <div className="flex items-center gap-1" title="Likes">
            <Heart className="h-4 w-4"/> 
            <span>{displayLikes}</span>
          </div>
          <div className="flex items-center gap-1" title="Je n'aime pas">
            <ThumbsDown className="h-4 w-4"/>
            <span>{displayDislikes}</span>
          </div>
          <div className="flex items-center gap-1" title="Vues">
            <Eye className="h-4 w-4"/> 
            <span>{serverViewsCount}</span>
          </div>
        </div>
      </div>

      {/* Middle section: Waveform Player - Removed wrapper styling for more breathing room */}
      <div className="my-4"> 
        {song.audio_url ? (
          <AudioWaveformPreview audioUrl={song.audio_url} song={song} height={80} />
        ) : (
          <div className="h-20 md:h-24 flex items-center justify-center text-slate-400 bg-slate-700/50 rounded-lg border border-slate-600">
            <p>Lecteur Waveform (Audio manquant)</p>
          </div>
        )}
      </div>

      {/* Bottom section: Action Buttons */}
      <div className="flex items-center gap-2 md:gap-3">
        <PlayButton song={song} size="lg" className="bg-orange-500 hover:bg-orange-600 text-white flex-grow md:flex-none md:px-6" /> 
        <LikeButton 
          resourceId={song.id} 
          resourceType="song" 
          initialLikes={displayLikes} // Use state for initialLikes
          initialIsLiked={isLiked} // Use state for initialIsLiked
          userId={userId} 
          onLikeToggle={handleLikeToggle}
          size="lg"
          className="border-slate-600 hover:bg-slate-700 text-slate-300 hover:text-white"
        />
        <DislikeButton
          resourceId={song.id}
          resourceType="song"
          initialDislikes={displayDislikes}
          initialIsDisliked={isDisliked}
          userId={userId}
          onDislikeToggle={handleDislikeToggle}
          size="lg"
          className="border-slate-600 hover:bg-slate-700 text-slate-300 hover:text-white"
        />
        <Button variant="outline" size="icon" title="Partager (Bientôt)" className="border-slate-600 hover:bg-slate-700 text-slate-300 hover:text-white">
          <Share2 className="h-5 w-5" />
        </Button>
        {userId && <AddToPlaylistButton songId={song.id} />} {/* Add button only if user is logged in */}
        {song.audio_url && song.allow_downloads ? (
          <a href={song.audio_url} download target="_blank" rel="noopener noreferrer">
            <Button variant="outline" size="icon" title="Télécharger" className="border-slate-600 hover:bg-slate-700 text-slate-300 hover:text-white">
              <Download className="h-5 w-5" />
            </Button>
          </a>
        ) : (
          <Button variant="outline" size="icon" title="Téléchargement non autorisé" disabled className="border-slate-600 text-slate-500">
            <Download className="h-5 w-5" />
          </Button>
        )}
        {/* Edit button has been moved to the page level */}
      </div>
    </div>
  );
}
