// Correction 2025-05-30 : Remplacement de status -> is_public (plus robuste pour le modèle actuel)
import { SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseClient } from '@/lib/supabase/client'; // Using the same client setup

export interface PlatformKeyMetrics {
  totalPublishedSongs: number;
  totalPublishedAlbums: number;
  totalPlatformPlays: number;
  totalPlatformLikes: number;
  totalRegisteredUsers: number;
  totalPublishedPlaylists: number;
  totalPlatformViews: number; // Added for platform views
}

export const getPlatformKeyMetrics = async (supabaseClient?: SupabaseClient): Promise<PlatformKeyMetrics> => {
  const supabase = supabaseClient || getSupabaseClient();

  // Total Published Songs
  const { count: publishedSongsCount, error: songsError } = await supabase
    .from('songs') // Changed from 'my_songs' based on previous code analysis
    .select('id', { count: 'exact', head: true })
    .eq('status', 'published');

  // Total Published Albums
  const { count: publishedAlbumsCount, error: albumsError } = await supabase
    .from('albums')
    .select('id', { count: 'exact', head: true })
    .eq('is_public', true); // Correction: status supprimé, on utilise is_public

  // Total Platform Plays from songs.plays column
  // We assume songs.plays is reliably updated (e.g., by a trigger on the 'plays' table)
  const { data: totalPlaysData, error: totalPlaysError } = await supabase
    .from('songs')
    .select('plays')
    .eq('is_public', true); // Correction: status supprimé, on utilise is_public
  const totalPlatformPlays = totalPlaysData?.reduce((sum, song) => sum + (song.plays || 0), 0) || 0;

  // Total Platform Likes (across all resource types for now - check resource_type for more specific counts if needed)
  const { count: platformLikesCount, error: likesError } = await supabase
    .from('likes')
    .select('id', { count: 'exact', head: true });

  // Total Registered Users
  const { count: registeredUsersCount, error: usersError } = await supabase
    .from('profiles') // Assuming 'profiles' table holds all user profiles
    .select('id', { count: 'exact', head: true });

  // Total Published Playlists
  const { count: publishedPlaylistsCount, error: playlistsError } = await supabase
    .from('playlists')
    .select('id', { count: 'exact', head: true })
    .eq('is_public', true); // Assuming is_public determines published status

  // Total Platform Views
  const { count: platformViewsCount, error: viewsError } = await supabase
    .from('views')
    .select('id', { count: 'exact', head: true });

  if (songsError) console.error('Error fetching total published songs:', songsError.message);
  if (albumsError) console.error('Error fetching total published albums:', albumsError.message);
  if (totalPlaysError) console.error('Error fetching sum of song plays for platform total:', totalPlaysError.message);
  if (likesError) console.error('Error fetching total platform likes:', likesError.message);
  if (usersError) console.error('Error fetching total registered users:', usersError.message);
  if (playlistsError) console.error('Error fetching total published playlists:', playlistsError.message);
  if (viewsError) console.error('Error fetching total platform views:', viewsError.message);

  return {
    totalPublishedSongs: publishedSongsCount || 0,
    totalPublishedAlbums: publishedAlbumsCount || 0,
    totalPlatformPlays: totalPlatformPlays,
    totalPlatformLikes: platformLikesCount || 0,
    totalRegisteredUsers: registeredUsersCount || 0,
    totalPublishedPlaylists: publishedPlaylistsCount || 0,
    totalPlatformViews: platformViewsCount || 0, // Added platform views
  };
};

// Future functions for platform-wide top content and trends will be added here.
