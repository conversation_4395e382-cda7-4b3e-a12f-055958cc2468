import { createSupabaseServerClient } from "@/lib/supabase/server"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import Link from "next/link"
import { Music, Disc, Play, MoreHorizontal, MessageSquare, Headphones } from "lucide-react"

import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { LineChart } from "@/components/charts/line-chart";
import { QuickActions } from "@/components/quick-actions";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger } from "@/components/ui/tabs";

// TypeScript Interfaces for Dashboard Data
interface DashboardMetricItem {
  date: string;
  plays: number;
  views: number;
}

interface DashboardComment {
  id: string;
  content: string;
  created_at: string;
  resource_type: string;
  resource_id: string;
  resource_slug: string | null; // Added for linking via slug
  comment_status: string; // From comm.status in SQL
  profiles: {
    username: string;
    avatar_url: string | null;
  };
}

interface DashboardSong {
  id: string;
  title: string;
  cover_art_url: string | null;
  genres: string[] | null; // Changed to string[] based on SQL
  plays?: number;
  created_at: string;
  is_public: boolean;
  slug: string | null; // Added for linking via slug
}

interface DashboardAlbum {
  id: string;
  title: string;
  album_art_url: string | null; // Note: album_art_url from SQL
  genres: string[] | null; // Changed to string[] based on SQL
  created_at: string;
  is_public: boolean;
  total_plays?: number; // For topAlbums
}

// Nouvelle interface pour les playlists
interface DashboardPlaylist {
  id: string;
  name: string;
  cover_url: string | null;
  is_public: boolean;
  created_at: string;
  genres: string[] | null; // Assumed TEXT[] from SQL
  song_count?: number; // For topPlaylists
  slug: string | null; // Added for linking via slug
}

interface DashboardData {
  userProfile: {
    id: string;
    username: string;
    display_name: string;
    avatar_url: string; // Mapped from profile_picture_url
    bio: string | null;
    website: string | null; // Mapped from website_url
    location: string | null;
    coins_balance: number;
  };
  songs_summary: {
    total_count: number;
    published_count: number;
  };
  albums_summary: {
    total_count: number;
    published_count: number;
  };
  todos_summary: { // Assuming this is still relevant
    total_count: number;
    completed_count: number;
  };
  totalPlays: number;
  totalViews: number;
  totalLikes: number;
  totalFollowers: number;
  daily_metrics_for_dashboard: DashboardMetricItem[];
  weekly_metrics_for_dashboard: DashboardMetricItem[]; // Placeholder
  monthly_metrics_for_dashboard: DashboardMetricItem[]; // Placeholder
  recentSongs: DashboardSong[];
  recentAlbums: DashboardAlbum[]; // This will likely be for "Recent Albums"
  topSongs: DashboardSong[];
  topAlbums: DashboardAlbum[]; // Nouvelle propriété
  recentPlaylists: DashboardPlaylist[]; // Nouvelle propriété
  topPlaylists: DashboardPlaylist[]; // Nouvelle propriété
  recent_comments: DashboardComment[];
}

// Données simulées pour le graphique d'écoutes
const listeningData = [
  { date: "2024-05-01", écoutes: 1200 },
  { date: "2024-05-05", écoutes: 1500 },
  { date: "2024-05-10", écoutes: 1350 },
  { date: "2024-05-15", écoutes: 2000 },
  { date: "2024-05-20", écoutes: 1800 },
  { date: "2024-05-25", écoutes: 2200 },
  { date: "2024-05-30", écoutes: 2500 },
  { date: "2024-06-05", écoutes: 2800 },
  { date: "2024-06-10", écoutes: 3000 },
  { date: "2024-06-15", écoutes: 3200 },
]

const activityData = listeningData;
const playsData = listeningData;

// --- Reusable Card Item Components ---

interface SongCardItemProps {
  song: DashboardSong;
  itemType: 'song'; // To help with link construction or specific song logic
}

const SongCardItem: React.FC<SongCardItemProps> = ({ song }) => {
  let coverArtUrl = song.cover_art_url;
  if (!coverArtUrl || (coverArtUrl.startsWith('artworks/placeholder_song') && coverArtUrl.endsWith('.png'))) {
    coverArtUrl = '/images/placeholder-song.svg';
  } else if (!coverArtUrl.startsWith('http') && !coverArtUrl.startsWith('/') && coverArtUrl.startsWith('artworks/')) {
    coverArtUrl = `/${coverArtUrl}`;
  }

  return (
    <Card className="overflow-hidden">
      <Link href={song.slug ? `/songs/${song.slug}` : `/songs/${song.id}`} className="block hover:bg-muted/50 transition-colors">
        <CardHeader className="p-0">
          {coverArtUrl ? (
            <img src={coverArtUrl} alt={song.title} width={300} height={300} className="aspect-square object-cover w-full" />
          ) : (
            <div className="aspect-square w-full bg-muted flex items-center justify-center">
              <Music className="w-12 h-12 text-muted-foreground" />
            </div>
          )}
        </CardHeader>
        <CardContent className="p-4">
          <h3 className="font-semibold truncate text-lg">{song.title}</h3>
          {song.genres && song.genres.length > 0 && (
            <div className="mt-1 flex flex-wrap gap-1">
              {song.genres.slice(0, 2).map(genre => (
                <Badge key={genre} variant="secondary" className="text-xs">{genre}</Badge>
              ))}
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-2">
            {song.plays !== undefined ? `${song.plays} écoutes` : `Créé ${formatDistanceToNow(new Date(song.created_at), { addSuffix: true, locale: fr })}`}
          </p>
        </CardContent>
      </Link>
    </Card>
  );
};

interface AlbumCardItemProps {
  album: DashboardAlbum;
}

const AlbumCardItem: React.FC<AlbumCardItemProps> = ({ album }) => {
  let albumArtUrl = album.album_art_url;
  if (!albumArtUrl || (albumArtUrl.startsWith('artworks/placeholder_song') && albumArtUrl.endsWith('.png'))) {
    albumArtUrl = '/images/placeholder-song.svg';
  } else if (!albumArtUrl.startsWith('http') && !albumArtUrl.startsWith('/') && albumArtUrl.startsWith('artworks/')) {
    albumArtUrl = `/${albumArtUrl}`;
  }

  return (
    <Card className="overflow-hidden">
      <Link href={`/albums/${album.id}`} className="block hover:bg-muted/50 transition-colors">
        <CardHeader className="p-0">
          {albumArtUrl ? (
            <img src={albumArtUrl} alt={album.title} width={300} height={300} className="aspect-square object-cover w-full" />
          ) : (
            <div className="aspect-square w-full bg-muted flex items-center justify-center">
              <Disc className="w-12 h-12 text-muted-foreground" />
            </div>
          )}
        </CardHeader>
        <CardContent className="p-4">
          <h3 className="font-semibold truncate text-lg">{album.title}</h3>
          {album.genres && album.genres.length > 0 && (
            <div className="mt-1 flex flex-wrap gap-1">
              {album.genres.slice(0, 2).map(genre => (
                <Badge key={genre} variant="secondary" className="text-xs">{genre}</Badge>
              ))}
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-2">
            {album.total_plays !== undefined ? `${album.total_plays} écoutes totales` : `Créé ${formatDistanceToNow(new Date(album.created_at), { addSuffix: true, locale: fr })}`}
          </p>
        </CardContent>
      </Link>
    </Card>
  );
};

interface PlaylistCardItemProps {
  playlist: DashboardPlaylist;
}

const PlaylistCardItem: React.FC<PlaylistCardItemProps> = ({ playlist }) => {
  let playlistCoverUrl = playlist.cover_url;
  if (!playlistCoverUrl || (playlistCoverUrl.startsWith('artworks/placeholder_song') && playlistCoverUrl.endsWith('.png'))) {
    playlistCoverUrl = '/images/placeholder-song.svg';
  } else if (!playlistCoverUrl.startsWith('http') && !playlistCoverUrl.startsWith('/') && playlistCoverUrl.startsWith('artworks/')) {
    playlistCoverUrl = `/${playlistCoverUrl}`;
  }

  return (
    <Card className="overflow-hidden">
      <Link href={playlist.slug ? `/playlists/${playlist.slug}` : `/playlists/${playlist.id}`} className="block hover:bg-muted/50 transition-colors">
        <CardHeader className="p-0">
          {playlistCoverUrl ? (
            <img src={playlistCoverUrl} alt={playlist.name} width={300} height={300} className="aspect-square object-cover w-full" />
          ) : (
            <div className="aspect-square w-full bg-muted flex items-center justify-center">
              <Headphones className="w-12 h-12 text-muted-foreground" />
            </div>
          )}
        </CardHeader>
        <CardContent className="p-4">
          <h3 className="font-semibold truncate text-lg">{playlist.name}</h3>
          {playlist.genres && playlist.genres.length > 0 && (
            <div className="mt-1 flex flex-wrap gap-1">
              {playlist.genres.slice(0, 2).map(genre => (
                <Badge key={genre} variant="secondary" className="text-xs">{genre}</Badge>
              ))}
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-2">
            {playlist.song_count !== undefined ? `${playlist.song_count} morceaux` : `Créé ${formatDistanceToNow(new Date(playlist.created_at), { addSuffix: true, locale: fr })}`}
          </p>
        </CardContent>
      </Link>
    </Card>
  );
};

export default async function DashboardPage() {
  const supabase = createSupabaseServerClient()

  // Récupérer les statistiques
  const { data: { user } } = await supabase.auth.getUser();

  // Initialize dashboardData and dashboardError
  let dashboardData: DashboardData | null = null;
  let dashboardError: string | null = null;

  // Default structure for dashboardData to prevent errors in JSX
  const defaultDashboardData: DashboardData = {
    userProfile: { id: '', username: '', display_name: '', avatar_url: '', bio: null, website: null, location: null, coins_balance: 0 },
    songs_summary: { total_count: 0, published_count: 0 },
    albums_summary: { total_count: 0, published_count: 0 },
    todos_summary: { total_count: 0, completed_count: 0 }, // Assuming this is still relevant
    totalPlays: 0,
    totalViews: 0,
    totalLikes: 0,
    totalFollowers: 0,
    daily_metrics_for_dashboard: [],
    weekly_metrics_for_dashboard: [], // Placeholder
    monthly_metrics_for_dashboard: [], // Placeholder
    recentSongs: [],
    recentAlbums: [],
    topSongs: [],
    topAlbums: [], // Ajouté
    recentPlaylists: [], // Ajouté
    topPlaylists: [], // Ajouté
    recent_comments: [],
  };

  // Fetch data using RPC call
  if (user?.id) {
    const { data: rpcData, error: rpcError } = await supabase.rpc(
      "get_dashboard_data",
      { p_user_id: user.id }
    );
    if (rpcError) {
      console.error("Error fetching dashboard data:", rpcError.message);
      dashboardError = rpcError.message;
      dashboardData = defaultDashboardData; // Use default data on error
    } else {
      dashboardData = rpcData;
    }
  } else {
    console.error("User not available for dashboard data fetch.");
    dashboardError = "User not available.";
    dashboardData = defaultDashboardData; // Use default data if user is not available
  }
  // Fetches for songs, albums, todos, and recentComments are now part of the 'get_dashboard_data' RPC call.
  // Ensure dashboardData.todos_summary and dashboardData.recent_comments are used below.

  const activityChartData = dashboardData?.daily_metrics_for_dashboard?.map(m => ({ date: m.date, écoutes: m.views })) || [];
  const playsChartData = dashboardData?.daily_metrics_for_dashboard?.map(m => ({ date: m.date, écoutes: m.plays })) || [];

  const formatDate = (date: string | undefined) => {
    if (!date) return '';
    return formatDistanceToNow(new Date(date), { addSuffix: true, locale: fr });
  };

  return (
    <div className="min-h-screen w-full bg-transparent">
      <div className="frosted p-6 rounded-xl w-full">
        <div className="flex flex-col gap-6 w-full">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Tableau de bord</h1>
            <p className="text-muted-foreground">Bienvenue sur votre espace de création musicale</p>
          </div>
          {dashboardError && (
            <div className="mb-4 p-4 bg-destructive/15 text-destructive border border-destructive rounded-md">
              <h3 className="font-semibold">Erreur lors du chargement du tableau de bord :</h3>
              <p>{dashboardError}</p>
            </div>
          )}

          <QuickActions />

          <div className="grid gap-6 md:grid-cols-4 w-full">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Morceaux</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.songs_summary?.total_count ?? 0}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.songs_summary?.published_count ?? 0} publiés
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Albums</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.albums_summary?.total_count ?? 0}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.albums_summary?.published_count ?? 0} publiés
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Écoutes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.totalPlays ?? 0}</div>
                <p className="text-xs text-muted-foreground">Total des écoutes</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Tâches</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData?.todos_summary?.total_count ?? 0}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.todos_summary?.completed_count ?? 0} complétées
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Section Graphiques Activité et Écoutes */}
          <div className="grid gap-6 md:grid-cols-2 w-full">
            <Card>
              <CardHeader>
                <CardTitle>Activité (7 derniers jours)</CardTitle>
                <CardDescription>Nouvelles créations et interactions.</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <LineChart data={activityChartData} />
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Écoutes (7 derniers jours)</CardTitle>
                <CardDescription>Performance de vos morceaux.</CardDescription>
              </CardHeader>
              <CardContent className="pl-2">
                <LineChart data={playsChartData} />
              </CardContent>
            </Card>
          </div>

          {/* Section Contenu Populaire et Récent avec Onglets */}
          <div className="mt-8">
            <h2 className="text-2xl font-bold tracking-tight mb-6">Votre Contenu en un Clin d'Œil</h2>
            <Tabs defaultValue="songs" className="w-full">
              <TabsList className="grid w-full grid-cols-3 mb-6">
                <TabsTrigger value="songs">Morceaux</TabsTrigger>
                <TabsTrigger value="albums">Albums</TabsTrigger>
                <TabsTrigger value="playlists">Playlists</TabsTrigger>
              </TabsList>

              {/* Onglet Morceaux */}
              <TabsContent value="songs">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Top Morceaux (les plus écoutés)</h3>
                    {dashboardData?.topSongs && dashboardData.topSongs.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {dashboardData.topSongs.map((song) => (
                          <SongCardItem key={`top-song-${song.id}`} song={song} itemType="song" />
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Aucun morceau populaire pour le moment.</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Morceaux Récents</h3>
                    {dashboardData?.recentSongs && dashboardData.recentSongs.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {dashboardData.recentSongs.map((song) => (
                          <SongCardItem key={`recent-song-${song.id}`} song={song} itemType="song" />
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Aucun morceau récent à afficher.</p>
                    )}
                  </div>
                </div>
              </TabsContent>

              {/* Onglet Albums */}
              <TabsContent value="albums">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Top Albums (plus d'écoutes)</h3>
                    {dashboardData?.topAlbums && dashboardData.topAlbums.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {dashboardData.topAlbums.map((album) => (
                          <AlbumCardItem key={`top-album-${album.id}`} album={album} />
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Aucun album populaire pour le moment.</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Albums Récents</h3>
                    {dashboardData?.recentAlbums && dashboardData.recentAlbums.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {dashboardData.recentAlbums.map((album) => (
                          <AlbumCardItem key={`recent-album-${album.id}`} album={album} />
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Aucun album récent à afficher.</p>
                    )}
                  </div>
                </div>
              </TabsContent>

              {/* Onglet Playlists */}
              <TabsContent value="playlists">
                <div className="space-y-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Top Playlists (plus de morceaux)</h3>
                    {dashboardData?.topPlaylists && dashboardData.topPlaylists.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {dashboardData.topPlaylists.map((playlist) => (
                          <PlaylistCardItem key={`top-playlist-${playlist.id}`} playlist={playlist} />
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Aucune playlist populaire pour le moment.</p>
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Playlists Récentes</h3>
                    {dashboardData?.recentPlaylists && dashboardData.recentPlaylists.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {dashboardData.recentPlaylists.map((playlist) => (
                          <PlaylistCardItem key={`recent-playlist-${playlist.id}`} playlist={playlist} />
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">Aucune playlist récente à afficher.</p>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Section Commentaires Récents (maintenue après les onglets) */}
          <div className="mt-8">
            <h2 className="text-2xl font-bold tracking-tight mb-4">Commentaires Récents</h2>
            <Card>
              <CardHeader>
                <CardTitle>Dernières Interactions</CardTitle>
                <CardDescription>Les plus récents commentaires sur vos créations.</CardDescription>
              </CardHeader>
              <CardContent>
                {dashboardData?.recent_comments && dashboardData.recent_comments.length > 0 ? (
                  <div className="space-y-4">
                    {dashboardData.recent_comments.map((comment: DashboardComment) => (
                      <div key={comment.id} className="flex items-start space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={comment.profiles?.avatar_url || undefined} alt={comment.profiles?.username} />
                          <AvatarFallback>{comment.profiles?.username?.charAt(0).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{comment.profiles?.username}</p>
                            <p className="text-xs text-muted-foreground">{formatDate(comment.created_at)}</p>
                          </div>
                          <p className="text-sm text-muted-foreground truncate max-w-xs">
                            {comment.content}
                          </p>
                          <Link href={comment.resource_type === 'song' ? (comment.resource_slug ? `/songs/${comment.resource_slug}` : `/songs/${comment.resource_id}`) : `/${comment.resource_type === 'album' ? 'albums' : 'unknown'}/${comment.resource_id}`} className="text-xs text-primary hover:underline">
                            Voir la ressource
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    <MessageSquare className="mx-auto h-12 w-12 opacity-50" />
                    <p className="mt-2">Aucun commentaire récent.</p>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" className="w-full" asChild>
                  <Link href="/dashboard/comments">Voir tous les commentaires</Link>
                </Button>
              </CardFooter>
            </Card>
          </div> {/* Closing the second content grid (Top songs, Recent comments) */}

        </div> {/* Closing flex flex-col gap-6 w-full */}
      </div> {/* Closing frosted p-6 rounded-xl w-full */}
    </div> /* Closing min-h-screen w-full bg-transparent */
  );
}
