"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from "recharts"
import { Loader2, Music, Tags } from "lucide-react"

interface GenreAnalysisProps {
  userId: string
  // timeRange est optionnel ici, car la RPC get_genre_performance ne le prend pas encore
  // Si la RPC est modifiée pour prendre timeRange, il faudra l'ajouter ici.
  timeRange?: '7d' | '30d' | '90d' | '1y' 
}

interface GenreStats {
  name: string // Correspond à 'genre' dans la RPC que nous allons définir
  total_plays: number
  unique_listeners?: number // Optionnel, car pas dans la simulation actuelle
  likes: number
  engagement_rate?: number // Optionnel, calculable
  percentage?: number // Optionnel, calculable
  songCount?: number // Ajouté, utile et souvent retourné par de telles RPC
  color?: string
}

// Type pour les données retournées par la RPC get_genre_performance
interface GenrePerformanceRpcDataPoint {
  genre: string; // 'name' dans GenreStats
  song_count: number;
  total_plays: number;
  total_likes: number;
  // Pourcentage et autres peuvent être calculés côté client ou par la RPC
}


const COLORS = ["#4ECDC4", "#FF6B6B", "#C44D58", "#556270", "#1A535C", "#FFE66D", "#6B5B95", "#88B04B"]

export function GenreAnalysis({ userId, timeRange }: GenreAnalysisProps) {
  const [genreData, setGenreData] = useState<GenreStats[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchGenreData = async () => {
      if (!userId) {
        setIsLoading(false);
        return;
      }
      setIsLoading(true)
      setError(null)
      const supabase = createBrowserClient()
      
      // Appel réel à la fonction RPC Supabase
      // La RPC 'get_genre_performance' doit être créée.
      // Elle devrait accepter p_user_id et optionnellement p_time_range.
      const rpcParams: { p_user_id: string; p_time_range?: string } = { p_user_id: userId };
      if (timeRange) {
        rpcParams.p_time_range = timeRange;
      }

      const { data, error: rpcError } = await supabase.rpc('get_genre_performance', rpcParams)
      
      if (rpcError) {
        console.error("Erreur RPC get_genre_performance:", rpcError)
        setError(rpcError.message)
        setGenreData([])
      } else if (data) {
        const rpcResult = data as GenrePerformanceRpcDataPoint[];
        
        // Calculer le total des écoutes pour les pourcentages
        const totalPlaysAllGenres = rpcResult.reduce((sum, genre) => sum + (genre.total_plays || 0), 0);

        const dataWithColorsAndPercentages = rpcResult.map((item, index) => ({
          name: item.genre,
          songCount: item.song_count || 0,
          total_plays: item.total_plays || 0,
          likes: item.total_likes || 0,
          percentage: totalPlaysAllGenres > 0 ? ((item.total_plays || 0) / totalPlaysAllGenres) * 100 : 0,
          color: COLORS[index % COLORS.length]
        }));
        
        setGenreData(dataWithColorsAndPercentages)
      } else {
        setGenreData([])
      }
      setIsLoading(false)
    }
    
    fetchGenreData()
  }, [userId, timeRange])
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance par genre</CardTitle>
          <CardDescription>Chargement des données...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    )
  }
  
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance par genre</CardTitle>
          <CardDescription>Erreur lors du chargement: {error}</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">Veuillez vérifier que la fonction RPC 'get_genre_performance' existe et fonctionne correctement.</p>
        </CardContent>
      </Card>
    )
  }
  
  if (genreData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance par genre</CardTitle>
          <CardDescription>Aucune donnée disponible</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Ajoutez des genres à vos morceaux pour voir des statistiques par genre.</p>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Tags className="h-4 w-4 mr-2" />
          Performance par genre
        </CardTitle>
        <CardDescription>Analyse de l'engagement par genre musical</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="bar" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="bar">Barres</TabsTrigger>
            <TabsTrigger value="pie">Camembert</TabsTrigger>
          </TabsList>
          
          <TabsContent value="bar">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={genreData} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis 
                    dataKey="name" 
                    angle={-45} 
                    textAnchor="end" 
                    height={60} 
                    interval={0} 
                    tick={{ fontSize: 10 }}
                  />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      value, 
                      name === 'total_plays' ? 'Écoutes' : 
                      name === 'likes' ? 'Likes' : 
                      'Valeur'
                    ]}
                  />
                  <Legend />
                  <Bar dataKey="total_plays" name="Écoutes" fill="#4ECDC4" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="likes" name="Likes" fill="#FF6B6B" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          
          <TabsContent value="pie">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={genreData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="percentage" // Assurez-vous que 'percentage' est calculé et présent
                    nameKey="name"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                  >
                    {genreData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${typeof value === 'number' ? value.toFixed(1) : value}%`, 'Pourcentage des écoutes']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
        </Tabs>
        
        <div className="mt-6 space-y-4">
          <h4 className="text-sm font-medium">Détails par genre</h4>
          <div className="space-y-4">
            {genreData.map((genre, index) => ( // Ajout de index pour une clé unique si genre.name n'est pas garanti unique
              <div key={genre.name || index} className="space-y-2"> 
                <div className="flex justify-between items-center">
                  <span className="font-medium">{genre.name}</span>
                  <span className="text-sm text-muted-foreground">{genre.total_plays} écoutes</span>
                </div>
                <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                  <div 
                    className="h-full" 
                    style={{ 
                      width: `${genre.percentage || 0}%`, 
                      backgroundColor: genre.color || '#4ECDC4' 
                    }} 
                  />
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{genre.likes} likes</span>
                  {/* Engagement rate n'est pas dans GenrePerformanceRpcDataPoint, à calculer si besoin */}
                  {/* <span>Engagement: {genre.engagement_rate?.toFixed(1)}%</span> */}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
