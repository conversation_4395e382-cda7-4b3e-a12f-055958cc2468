'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Music, User, Clock, Hash, Guitar, Volume2, 
  Edit3, Save, X, Plus, Settings, Info
} from 'lucide-react';

interface SongInfoHeaderProps {
  currentSong: any;
  setCurrentSong: (song: any) => void;
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  onSave: () => Promise<void>;
}

const GENRES = [
  'Pop', 'Rock', 'Hip-Hop', 'R&B', 'Country', 'Folk', 'Jazz', 'Blues',
  'Electronic', 'Reggae', 'Funk', 'Soul', 'Alternative', 'Indie', 'Metal'
];

const MOODS = [
  'Joyeux', 'Mélancolique', 'Énergique', 'Romantique', 'Nostalgique',
  'Motivant', 'Relaxant', 'Intense', 'Mystérieux', 'Optimiste'
];

const KEYS = [
  'C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'
];

const TIME_SIGNATURES = ['4/4', '3/4', '2/4', '6/8', '12/8', '5/4', '7/8'];

export const SongInfoHeader: React.FC<SongInfoHeaderProps> = ({
  currentSong,
  setCurrentSong,
  styleConfig,
  setStyleConfig,
  onSave
}) => {
  
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    title: currentSong.title || '',
    artist: currentSong.artist || '',
    bpm: styleConfig.bpm || 120,
    key: styleConfig.key || 'C',
    capo: styleConfig.capo || 0,
    timeSignature: styleConfig.timeSignature || '4/4',
    genres: styleConfig.genres || [],
    mood: styleConfig.mood || '',
    description: currentSong.description || ''
  });

  const handleSave = async () => {
    // Mettre à jour currentSong
    setCurrentSong({
      ...currentSong,
      title: editData.title,
      artist: editData.artist,
      description: editData.description
    });

    // Mettre à jour styleConfig
    setStyleConfig({
      ...styleConfig,
      bpm: editData.bpm,
      key: editData.key,
      capo: editData.capo,
      timeSignature: editData.timeSignature,
      genres: editData.genres,
      mood: editData.mood
    });

    setIsEditing(false);
    await onSave();
  };

  const handleCancel = () => {
    setEditData({
      title: currentSong.title || '',
      artist: currentSong.artist || '',
      bpm: styleConfig.bpm || 120,
      key: styleConfig.key || 'C',
      capo: styleConfig.capo || 0,
      timeSignature: styleConfig.timeSignature || '4/4',
      genres: styleConfig.genres || [],
      mood: styleConfig.mood || '',
      description: currentSong.description || ''
    });
    setIsEditing(false);
  };

  const addGenre = (genre: string) => {
    if (!editData.genres.includes(genre)) {
      setEditData({
        ...editData,
        genres: [...editData.genres, genre]
      });
    }
  };

  const removeGenre = (genre: string) => {
    setEditData({
      ...editData,
      genres: editData.genres.filter(g => g !== genre)
    });
  };

  if (isEditing) {
    return (
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Informations du morceau
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4" />
                Annuler
              </Button>
              <Button variant="default" size="sm" onClick={handleSave}>
                <Save className="h-4 w-4" />
                Sauvegarder
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Ligne 1: Titre et Artiste */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title" className="text-white">Titre</Label>
              <Input
                id="title"
                value={editData.title}
                onChange={(e) => setEditData({...editData, title: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="Titre de la chanson"
              />
            </div>
            <div>
              <Label htmlFor="artist" className="text-white">Artiste</Label>
              <Input
                id="artist"
                value={editData.artist}
                onChange={(e) => setEditData({...editData, artist: e.target.value})}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="Nom de l'artiste"
              />
            </div>
          </div>

          {/* Ligne 2: Paramètres musicaux */}
          <div className="grid grid-cols-5 gap-4">
            <div>
              <Label htmlFor="bpm" className="text-white">BPM</Label>
              <Input
                id="bpm"
                type="number"
                value={editData.bpm}
                onChange={(e) => setEditData({...editData, bpm: parseInt(e.target.value) || 120})}
                className="bg-slate-700 border-slate-600 text-white"
                min="60"
                max="200"
              />
            </div>
            <div>
              <Label htmlFor="key" className="text-white">Tonalité</Label>
              <Select value={editData.key} onValueChange={(value) => setEditData({...editData, key: value})}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {KEYS.map(key => (
                    <SelectItem key={key} value={key}>{key}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="capo" className="text-white">Capo</Label>
              <Input
                id="capo"
                type="number"
                value={editData.capo}
                onChange={(e) => setEditData({...editData, capo: parseInt(e.target.value) || 0})}
                className="bg-slate-700 border-slate-600 text-white"
                min="0"
                max="12"
              />
            </div>
            <div>
              <Label htmlFor="timeSignature" className="text-white">Mesure</Label>
              <Select value={editData.timeSignature} onValueChange={(value) => setEditData({...editData, timeSignature: value})}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TIME_SIGNATURES.map(sig => (
                    <SelectItem key={sig} value={sig}>{sig}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="mood" className="text-white">Ambiance</Label>
              <Select value={editData.mood} onValueChange={(value) => setEditData({...editData, mood: value})}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="Choisir..." />
                </SelectTrigger>
                <SelectContent>
                  {MOODS.map(mood => (
                    <SelectItem key={mood} value={mood}>{mood}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Ligne 3: Genres */}
          <div>
            <Label className="text-white">Genres</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {editData.genres.map(genre => (
                <Badge key={genre} variant="secondary" className="gap-1">
                  {genre}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeGenre(genre)}
                    className="h-4 w-4 p-0 hover:bg-red-500"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
            <Select onValueChange={addGenre}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Ajouter un genre..." />
              </SelectTrigger>
              <SelectContent>
                {GENRES.filter(g => !editData.genres.includes(g)).map(genre => (
                  <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Ligne 4: Description */}
          <div>
            <Label htmlFor="description" className="text-white">Description</Label>
            <Textarea
              id="description"
              value={editData.description}
              onChange={(e) => setEditData({...editData, description: e.target.value})}
              className="bg-slate-700 border-slate-600 text-white"
              placeholder="Description du morceau, inspiration, notes..."
              rows={2}
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Infos principales */}
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                <Music className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-white">
                  {currentSong.title || 'Nouvelle chanson'}
                </h2>
                <p className="text-sm text-slate-400 flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {currentSong.artist || 'Artiste inconnu'}
                </p>
              </div>
            </div>

            {/* Paramètres musicaux */}
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1 text-slate-300">
                <Clock className="h-4 w-4" />
                <span>{styleConfig.bpm || 120} BPM</span>
              </div>
              <div className="flex items-center gap-1 text-slate-300">
                <Hash className="h-4 w-4" />
                <span>{styleConfig.key || 'C'} {styleConfig.timeSignature || '4/4'}</span>
              </div>
              {styleConfig.capo > 0 && (
                <div className="flex items-center gap-1 text-slate-300">
                  <Guitar className="h-4 w-4" />
                  <span>Capo {styleConfig.capo}</span>
                </div>
              )}
              {styleConfig.mood && (
                <div className="flex items-center gap-1 text-slate-300">
                  <Volume2 className="h-4 w-4" />
                  <span>{styleConfig.mood}</span>
                </div>
              )}
            </div>

            {/* Genres */}
            <div className="flex items-center gap-2">
              {(styleConfig.genres || []).slice(0, 3).map(genre => (
                <Badge key={genre} variant="outline" className="text-xs">
                  {genre}
                </Badge>
              ))}
              {(styleConfig.genres || []).length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{(styleConfig.genres || []).length - 3}
                </Badge>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="gap-1"
            >
              <Edit3 className="h-4 w-4" />
              Modifier
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              title="Informations détaillées"
            >
              <Info className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Description si présente */}
        {currentSong.description && (
          <div className="mt-3 pt-3 border-t border-slate-700">
            <p className="text-sm text-slate-300">{currentSong.description}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
