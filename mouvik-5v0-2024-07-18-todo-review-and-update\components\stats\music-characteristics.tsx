"use client"

import { useState, useEffect, useRef } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer, Tooltip } from "recharts"
import { Loader2, Music } from "lucide-react"

interface MusicCharacteristicsProps {
  userId: string
  // timeRange?: '7d' | '30d' | '90d' | '1y'; // Non utilisé par ce composant pour l'instant
}

interface SongOption {
  id: string
  title: string
}

interface CharacteristicData {
  energy: number
  danceability: number
  valence: number
  acousticness: number
  instrumentalness: number
  speechiness: number
  liveness: number
}

export function MusicCharacteristics({ userId }: MusicCharacteristicsProps) {
  const [songId, setSongId] = useState<string | null>(null)
  const [songOptions, setSongOptions] = useState<SongOption[]>([])
  const [characteristics, setCharacteristics] = useState<CharacteristicData | null>(null)
  const [isLoading, setIsLoading] = useState(true) // Pour la liste des chansons
  const [isLoadingCharacteristics, setIsLoadingCharacteristics] = useState(false) // Pour les caractéristiques d'une chanson
  const [error, setError] = useState<string | null>(null)

  // Charger la liste des morceaux de l'utilisateur
  useEffect(() => {
    const fetchSongs = async () => {
      if (!userId) {
        setIsLoading(false);
        return;
      }
      try {
        setIsLoading(true)
        setError(null)
        const supabase = createBrowserClient()
        
        const { data, error: fetchError } = await supabase
          .from('songs')
          .select('id, title')
          .eq('creator_user_id', userId)
          .order('title', { ascending: true })
        
        if (fetchError) throw fetchError;

        if (data) {
          setSongOptions(data as SongOption[]);
          if (data.length > 0 && !songId) { // Sélectionner le premier par défaut
            setSongId(data[0].id);
          } else if (data.length === 0) {
            setSongId(null); // Pas de chansons, pas de sélection
            setCharacteristics(null); // Effacer les caractéristiques précédentes
          }
        } else {
          setSongOptions([]);
          setSongId(null);
          setCharacteristics(null);
        }
      } catch (err) {
        console.error("Erreur lors de la récupération des morceaux:", err)
        setError(err instanceof Error ? err.message : "Une erreur est survenue lors de la récupération des morceaux")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchSongs()
  }, [userId]) // songId retiré des dépendances pour éviter boucle si songId est setté ici
  
  // Charger les caractéristiques du morceau sélectionné
  useEffect(() => {
    if (!songId) {
      setCharacteristics(null); // Effacer si aucun morceau n'est sélectionné
      setIsLoadingCharacteristics(false);
      return;
    }
    
    const fetchCharacteristics = async () => {
      try {
        setIsLoadingCharacteristics(true)
        setError(null)
        const supabase = createBrowserClient()
        
        // La RPC 'get_song_audio_characteristics' doit être créée.
        // Elle devrait retourner les données de la table 'audio_analysis' pour un song_id.
        const { data, error: rpcError } = await supabase.rpc('get_song_audio_characteristics', {
          p_song_id: songId
        })
        
        if (rpcError) {
          console.error("Erreur RPC get_song_audio_characteristics:", rpcError)
          // Ne pas setter d'erreur globale ici si la liste des chansons a chargé
          // Peut-être une erreur spécifique pour ce fetch
          setCharacteristics(null); 
        } else if (data) {
          // Supposons que la RPC retourne un objet unique ou un tableau avec un seul objet
          const charData = Array.isArray(data) ? data[0] : data;
          setCharacteristics(charData as CharacteristicData);
        } else {
          setCharacteristics(null);
        }
      } catch (err) {
        console.error("Erreur lors de la récupération des caractéristiques:", err)
        // setError(err instanceof Error ? err.message : "Une erreur est survenue")
        setCharacteristics(null);
      } finally {
        setIsLoadingCharacteristics(false)
      }
    }
    
    fetchCharacteristics()
  }, [songId])
  
  const formatCharacteristicsForChart = () => {
    if (!characteristics) return []
    
    return [
      { name: "Énergie", value: (characteristics.energy || 0) * 100, fullMark: 100 },
      { name: "Dansabilité", value: (characteristics.danceability || 0) * 100, fullMark: 100 },
      { name: "Positivité", value: (characteristics.valence || 0) * 100, fullMark: 100 },
      { name: "Acoustique", value: (characteristics.acousticness || 0) * 100, fullMark: 100 },
      { name: "Instrumental", value: (characteristics.instrumentalness || 0) * 100, fullMark: 100 },
      { name: "Vocal", value: (characteristics.speechiness || 0) * 100, fullMark: 100 },
      { name: "Live", value: (characteristics.liveness || 0) * 100, fullMark: 100 }
    ].filter(item => typeof item.value === 'number'); // S'assurer que value est un nombre
  }
  
  if (isLoading) { /* ... Loader pour la liste des chansons ... */ 
    return (
      <Card>
        <CardHeader><CardTitle>Caractéristiques musicales</CardTitle><CardDescription>Chargement de la liste des morceaux...</CardDescription></CardHeader>
        <CardContent className="flex justify-center items-center h-[300px]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></CardContent>
      </Card>
    );
  }
  
  if (error && songOptions.length === 0) { /* ... Erreur si la liste des chansons ne charge pas ... */ 
    return (
      <Card>
        <CardHeader><CardTitle>Caractéristiques musicales</CardTitle><CardDescription>Erreur: {error}</CardDescription></CardHeader>
        <CardContent><p className="text-destructive">Impossible de charger la liste des morceaux.</p></CardContent>
      </Card>
    );
  }
  
  if (songOptions.length === 0) {
    return (
      <Card>
        <CardHeader><CardTitle>Caractéristiques musicales</CardTitle><CardDescription>Aucun morceau disponible</CardDescription></CardHeader>
        <CardContent><p className="text-muted-foreground">Créez des morceaux pour voir leurs caractéristiques.</p></CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle className="flex items-center"><Music className="h-4 w-4 mr-2" />Caractéristiques musicales</CardTitle>
            <CardDescription>Analyse des attributs audio de vos morceaux</CardDescription>
          </div>
          <Select value={songId || ''} onValueChange={setSongId} disabled={songOptions.length === 0}>
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Sélectionner un morceau" />
            </SelectTrigger>
            <SelectContent>
              {songOptions.map((song) => (
                <SelectItem key={song.id} value={song.id}>
                  {song.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        {isLoadingCharacteristics ? (
          <div className="flex justify-center items-center h-[300px]">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : characteristics ? (
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart cx="50%" cy="50%" outerRadius="80%" data={formatCharacteristicsForChart()}>
                <PolarGrid />
                <PolarAngleAxis dataKey="name" />
                <PolarRadiusAxis angle={30} domain={[0, 100]} />
                <Radar name="Caractéristiques" dataKey="value" stroke="#4ECDC4" fill="#4ECDC4" fillOpacity={0.6}/>
                <Tooltip formatter={(value) => [`${typeof value === 'number' ? value.toFixed(1) : value}%`, 'Valeur']} />
              </RadarChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="flex justify-center items-center h-[300px]">
            <p className="text-muted-foreground">
              {songId ? "Aucune caractéristique trouvée pour ce morceau." : "Sélectionnez un morceau pour voir ses caractéristiques."}
            </p>
          </div>
        )}
        
        {characteristics && (
          <div className="mt-6 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {formatCharacteristicsForChart().map(char => (
              <div key={char.name} className="space-y-1">
                <p className="text-sm font-medium">{char.name}</p>
                <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                  <div className="h-full bg-cyan-500" style={{ width: `${char.value}%` }} />
                </div>
                <p className="text-xs text-muted-foreground">{char.value.toFixed(1)}%</p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
