"use client";

import { useEffect, useState } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import { PlanLimitDetails } from '@/hooks/use-plan-limits'; // Re-use this interface
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from '@/hooks/use-toast';
import { Loader2, Save } from 'lucide-react';

// Define a type for the editable fields, making them all potentially string for form handling
type EditablePlanLimit = Omit<PlanLimitDetails, 'tier' | 'page_customisation' | 'analytics_level'> & {
  page_customisation: boolean; // Keep as boolean for Switch
  analytics_level: string; // Keep as string for Select
  // Numeric fields will be string in form, converted on save
  uploads_per_month_str?: string | null;
  vault_space_gb_str?: string | null;
  vault_max_files_str?: string | null;
  ia_credits_month_str?: string | null;
  coins_month_str?: string | null;
  max_playlists_str?: string | null;
  max_friends_str?: string | null;
};

export function PlanLimitsManager() {
  const supabase = getSupabaseClient();
  const [planLimits, setPlanLimits] = useState<PlanLimitDetails[]>([]);
  const [editableLimits, setEditableLimits] = useState<Record<string, EditablePlanLimit>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const fetchLimits = async () => {
      setIsLoading(true);
      const { data, error } = await supabase.from('plan_limits').select('*').order('tier');
      if (error) {
        toast({ title: "Erreur", description: "Impossible de charger les limites des plans.", variant: "destructive" });
        console.error("Error fetching plan limits:", error);
      } else {
        setPlanLimits(data as PlanLimitDetails[]);
        const initialEditable: Record<string, EditablePlanLimit> = {};
        (data as PlanLimitDetails[]).forEach(limit => {
          initialEditable[limit.tier] = {
            ...limit,
            uploads_per_month_str: limit.uploads_per_month?.toString() ?? '',
            vault_space_gb_str: limit.vault_space_gb?.toString() ?? '',
            vault_max_files_str: limit.vault_max_files?.toString() ?? '',
            ia_credits_month_str: limit.ia_credits_month?.toString() ?? '',
            coins_month_str: limit.coins_month?.toString() ?? '',
            max_playlists_str: limit.max_playlists?.toString() ?? '',
            max_friends_str: limit.max_friends?.toString() ?? '',
          };
        });
        setEditableLimits(initialEditable);
      }
      setIsLoading(false);
    };
    fetchLimits();
  }, [supabase]);

  const handleInputChange = (tier: string, field: keyof EditablePlanLimit, value: string | boolean | number | null) => {
    setEditableLimits(prev => ({
      ...prev,
      [tier]: {
        ...prev[tier],
        [field]: value,
      }
    }));
  };
  
  const handleNumericInputChange = (tier: string, field: keyof EditablePlanLimit, strField: keyof EditablePlanLimit, value: string) => {
     setEditableLimits(prev => ({
      ...prev,
      [tier]: {
        ...prev[tier],
        [strField]: value, // Store the string representation
      }
    }));
  };


  const handleSaveChanges = async (tier: string) => {
    setIsSaving(prev => ({ ...prev, [tier]: true }));
    const limitsToSave = editableLimits[tier];
    if (!limitsToSave) {
      toast({ title: "Erreur", description: "Données de plan non trouvées.", variant: "destructive" });
      setIsSaving(prev => ({ ...prev, [tier]: false }));
      return;
    }

    const parseNumeric = (valStr: string | undefined | null): number | null => {
      if (valStr === undefined || valStr === null || valStr.trim() === '') return null;
      const num = parseFloat(valStr);
      return isNaN(num) ? null : num; // Or handle error if not a valid number
    };
    
    const parseIntStrict = (valStr: string | undefined | null): number | null => {
      if (valStr === undefined || valStr === null || valStr.trim() === '') return null;
      const num = parseInt(valStr, 10);
      return isNaN(num) ? null : num;
    };


    const updatePayload: Partial<PlanLimitDetails> = {
      uploads_per_month: parseIntStrict(limitsToSave.uploads_per_month_str),
      vault_space_gb: parseNumeric(limitsToSave.vault_space_gb_str),
      vault_max_files: parseIntStrict(limitsToSave.vault_max_files_str),
      ia_credits_month: parseIntStrict(limitsToSave.ia_credits_month_str),
      coins_month: parseIntStrict(limitsToSave.coins_month_str),
      page_customisation: limitsToSave.page_customisation,
      analytics_level: limitsToSave.analytics_level as PlanLimitDetails['analytics_level'],
      max_playlists: parseIntStrict(limitsToSave.max_playlists_str),
      max_friends: parseIntStrict(limitsToSave.max_friends_str),
    };
    
    // Filter out undefined values if any field was not in editableLimits (should not happen with current setup)
    Object.keys(updatePayload).forEach(key => updatePayload[key as keyof typeof updatePayload] === undefined && delete updatePayload[key as keyof typeof updatePayload]);


    const { error } = await supabase
      .from('plan_limits')
      .update(updatePayload)
      .eq('tier', tier);

    if (error) {
      toast({ title: "Erreur de sauvegarde", description: error.message, variant: "destructive" });
      console.error("Error updating plan limits:", error);
    } else {
      toast({ title: "Succès", description: `Limites pour le plan ${tier} sauvegardées.` });
      // Optionally re-fetch or update local state to reflect saved data accurately
      setPlanLimits(prev => prev.map(p => p.tier === tier ? {...p, ...updatePayload} : p));
    }
    setIsSaving(prev => ({ ...prev, [tier]: false }));
  };

  if (isLoading) {
    return <div className="flex items-center justify-center p-8"><Loader2 className="h-6 w-6 animate-spin mr-2" /> Chargement des limites de plans...</div>;
  }

  const analyticsLevels: PlanLimitDetails['analytics_level'][] = ['basic', 'extended', 'pro'];

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">Plan</TableHead>
            <TableHead>Uploads/mois</TableHead>
            <TableHead>Vault (Go)</TableHead>
            <TableHead>Vault (fichiers)</TableHead>
            <TableHead>Crédits IA/mois</TableHead>
            <TableHead>Coins/mois</TableHead>
            <TableHead>Perso. Page</TableHead>
            <TableHead>Niv. Analytics</TableHead>
            <TableHead>Max Playlists</TableHead>
            <TableHead>Max Amis</TableHead>
            <TableHead className="w-[120px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {planLimits.map((limit) => (
            <TableRow key={limit.tier}>
              <TableCell className="font-medium capitalize">{limit.tier}</TableCell>
              <TableCell><Input type="text" placeholder="N/A ou nombre" value={editableLimits[limit.tier]?.uploads_per_month_str ?? ''} onChange={(e) => handleNumericInputChange(limit.tier, 'uploads_per_month', 'uploads_per_month_str', e.target.value)} className="w-24 h-8" /></TableCell>
              <TableCell><Input type="text" placeholder="N/A ou nombre" value={editableLimits[limit.tier]?.vault_space_gb_str ?? ''} onChange={(e) => handleNumericInputChange(limit.tier, 'vault_space_gb', 'vault_space_gb_str', e.target.value)} className="w-24 h-8" /></TableCell>
              <TableCell><Input type="text" placeholder="N/A ou nombre" value={editableLimits[limit.tier]?.vault_max_files_str ?? ''} onChange={(e) => handleNumericInputChange(limit.tier, 'vault_max_files', 'vault_max_files_str', e.target.value)} className="w-24 h-8" /></TableCell>
              <TableCell><Input type="text" placeholder="N/A ou nombre" value={editableLimits[limit.tier]?.ia_credits_month_str ?? ''} onChange={(e) => handleNumericInputChange(limit.tier, 'ia_credits_month', 'ia_credits_month_str', e.target.value)} className="w-24 h-8" /></TableCell>
              <TableCell><Input type="text" placeholder="N/A ou nombre" value={editableLimits[limit.tier]?.coins_month_str ?? ''} onChange={(e) => handleNumericInputChange(limit.tier, 'coins_month', 'coins_month_str', e.target.value)} className="w-24 h-8" /></TableCell>
              <TableCell><Switch checked={editableLimits[limit.tier]?.page_customisation ?? false} onCheckedChange={(checked) => handleInputChange(limit.tier, 'page_customisation', checked)} /></TableCell>
              <TableCell>
                <Select value={editableLimits[limit.tier]?.analytics_level ?? 'basic'} onValueChange={(value) => handleInputChange(limit.tier, 'analytics_level', value)}>
                  <SelectTrigger className="w-[120px] h-8"><SelectValue /></SelectTrigger>
                  <SelectContent>
                    {analyticsLevels.map(level => <SelectItem key={level} value={level} className="capitalize">{level}</SelectItem>)}
                  </SelectContent>
                </Select>
              </TableCell>
              <TableCell><Input type="text" placeholder="N/A ou nombre" value={editableLimits[limit.tier]?.max_playlists_str ?? ''} onChange={(e) => handleNumericInputChange(limit.tier, 'max_playlists', 'max_playlists_str', e.target.value)} className="w-24 h-8" /></TableCell>
              <TableCell><Input type="text" placeholder="N/A ou nombre" value={editableLimits[limit.tier]?.max_friends_str ?? ''} onChange={(e) => handleNumericInputChange(limit.tier, 'max_friends', 'max_friends_str', e.target.value)} className="w-24 h-8" /></TableCell>
              <TableCell>
                <Button size="sm" onClick={() => handleSaveChanges(limit.tier)} disabled={isSaving[limit.tier]} className="h-8">
                  {isSaving[limit.tier] ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
