DROP FUNCTION IF EXISTS public.get_user_overview_stats(uuid, text);

CREATE OR REPLACE FUNCTION public.get_user_overview_stats(p_user_id uuid, p_time_range text)
 RETURNS TABLE(total_plays bigint, total_songs bigint, total_albums bigint, total_followers bigint, total_views bigint, total_likes bigint, total_comments bigint, total_duration_seconds numeric)
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_start_date DATE;
    v_end_date DATE := CURRENT_DATE + INTERVAL '1 day';
BEGIN
    IF p_time_range = '7d' THEN
        v_start_date := CURRENT_DATE - INTERVAL '7 days';
    ELSIF p_time_range = '30d' THEN
        v_start_date := CURRENT_DATE - INTERVAL '30 days';
    ELSIF p_time_range = '90d' THEN
        v_start_date := CURRENT_DATE - INTERVAL '90 days';
    ELSIF p_time_range = 'all' THEN
        v_start_date := '1970-01-01'::DATE;
    ELSE
        v_start_date := CURRENT_DATE - INTERVAL '30 days'; -- Default
    END IF;

    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM song_plays sp JOIN my_songs ms_plays ON sp.song_id = ms_plays.id WHERE ms_plays.user_id = p_user_id AND sp.played_at >= v_start_date AND sp.played_at < v_end_date) AS total_plays,
        (SELECT COUNT(*) FROM my_songs ms_songs WHERE ms_songs.user_id = p_user_id AND ms_songs.created_at >= v_start_date AND ms_songs.created_at < v_end_date) AS total_songs,
        (SELECT COUNT(DISTINCT alb.id) FROM albums alb WHERE alb.user_id = p_user_id AND alb.created_at >= v_start_date AND alb.created_at < v_end_date) AS total_albums,
        (SELECT COUNT(*) FROM public.profile_followers pf WHERE pf.followed_profile_id = p_user_id AND pf.created_at >= v_start_date AND pf.created_at < v_end_date) AS total_followers,
        (
            (SELECT COUNT(rv.id)
             FROM resource_views rv
             JOIN my_songs ms_view ON rv.resource_id = ms_view.id AND rv.resource_type = 'song'
             WHERE ms_view.user_id = p_user_id AND rv.viewed_at >= v_start_date AND rv.viewed_at < v_end_date)
            +
            (SELECT COUNT(rv.id)
             FROM resource_views rv
             JOIN albums a_view ON rv.resource_id = a_view.id AND rv.resource_type = 'album'
             WHERE a_view.user_id = p_user_id AND rv.viewed_at >= v_start_date AND rv.viewed_at < v_end_date)
        ) AS total_views,
        (SELECT COUNT(*) FROM song_likes sl JOIN my_songs ms_likes ON sl.song_id = ms_likes.id WHERE ms_likes.user_id = p_user_id AND sl.created_at >= v_start_date AND sl.created_at < v_end_date) AS total_likes,
        (SELECT COUNT(*) FROM song_comments sc JOIN my_songs ms_comments ON sc.song_id = ms_comments.id WHERE ms_comments.user_id = p_user_id AND sc.created_at >= v_start_date AND sc.created_at < v_end_date) AS total_comments,
        (SELECT COALESCE(SUM(ms_duration.duration)::numeric / 1000.0, 0) FROM my_songs ms_duration WHERE ms_duration.user_id = p_user_id AND ms_duration.created_at >= v_start_date AND ms_duration.created_at < v_end_date) AS total_duration_seconds;
END;
$function$;

COMMENT ON FUNCTION public.get_user_overview_stats(uuid, text) IS 'Retrieves overview statistics for a user. Corrected duration to use duration_ms and convert to seconds.';
