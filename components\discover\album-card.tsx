"use client";

import Link from "next/link";
import Image from 'next/image'; // Import Next/Image
import { useState, useEffect } from 'react'; // Import useState and useEffect
import { Play } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import type { Album } from "@/types";

interface AlbumCardProps {
  album: Album;
}

export function AlbumCard({ album }: AlbumCardProps) {
  const initialImageSrc = album.cover_url;
  const fallbackImageSrc = '/images/placeholder-song.svg'; // Ensure this image exists in public/images
  const [currentImageSrc, setCurrentImageSrc] = useState(initialImageSrc || fallbackImageSrc);
  const [hasLoadError, setHasLoadError] = useState(false);

  useEffect(() => {
    setCurrentImageSrc(initialImageSrc || fallbackImageSrc);
    setHasLoadError(false); // Reset error state when album prop changes
  }, [initialImageSrc, fallbackImageSrc]);

  const handleImageError = () => {
    if (!hasLoadError) { // Prevent infinite loop if fallback also fails
      setCurrentImageSrc(fallbackImageSrc);
      setHasLoadError(true);
    }
  };

  return (
    <Link href={`/albums/${album.id}`}>
      <Card className="overflow-hidden group">
        <div className="aspect-square relative">
          <Image
            src={currentImageSrc}
            alt={album.title || 'Album cover'}
            width={300} // Or appropriate dimensions
            height={300} // Or appropriate dimensions
            className="w-full h-full object-cover"
            onError={handleImageError}
          />
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <Button size="icon" className="h-12 w-12 rounded-full">
              <Play className="h-6 w-6" />
            </Button>
          </div>
        </div>
        <CardContent className="p-3">
          <h3 className="font-medium line-clamp-1" title={album.title}>{album.title}</h3>
          <div className="text-xs text-muted-foreground mb-1 line-clamp-1" title={album.artist_name || album.profiles?.display_name || album.profiles?.username || 'Artiste inconnu'}>
            Par : {album.artist_name || album.profiles?.display_name || album.profiles?.username || "Artiste inconnu"}
          </div>
          <p className="text-sm text-muted-foreground line-clamp-1">
            {album.release_date ? new Date(album.release_date).getFullYear() : "Date inconnue"}
            {/* songs_count removed as it's not standard on Album type, can be added later if needed */}
          </p>
        </CardContent>
      </Card>
    </Link>
  )
}
