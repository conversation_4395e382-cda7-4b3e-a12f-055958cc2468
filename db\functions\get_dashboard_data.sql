CREATE OR REPLACE FUNCTION get_dashboard_data(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_profile JSONB;
    v_songs_summary JSONB;
    v_albums_summary JSONB;
    v_todos_summary JSONB;
    v_total_plays BIGINT;
    v_total_views BIGINT;
    v_total_likes BIGINT;
    v_total_followers BIGINT;
    v_recent_songs JSONB;
    v_recent_albums JSONB;
    v_top_songs JSONB;
    v_recent_comments JSONB;
    v_daily_metrics_for_dashboard JSONB;
BEGIN
    IF p_user_id IS NULL THEN
        RAISE WARNING 'get_dashboard_data called with NULL p_user_id. Returning default empty data.';
        RETURN jsonb_build_object(
            'error', 'User ID is missing. Cannot fetch dashboard data.',
            'userProfile', jsonb_build_object('id', null, 'username', 'N/A', 'display_name', 'N/A', 'avatar_url', '', 'bio', null, 'website', null, 'location', null, 'coins_balance', 0),
            'songs_summary', jsonb_build_object('total_count', 0, 'published_count', 0),
            'albums_summary', jsonb_build_object('total_count', 0, 'published_count', 0),
            'todos_summary', jsonb_build_object('total_count', 0, 'completed_count', 0),
            'totalPlays', 0,
            'totalViews', 0,
            'totalLikes', 0,
            'totalFollowers', 0,
            'daily_metrics_for_dashboard', '[]'::jsonb,
            'weekly_metrics_for_dashboard', '[]'::jsonb,
            'monthly_metrics_for_dashboard', '[]'::jsonb,
            'recentSongs', '[]'::jsonb,
            'recentAlbums', '[]'::jsonb,
            'topSongs', '[]'::jsonb,
            'recent_comments', '[]'::jsonb
        );
    END IF;

    -- 1. Profil utilisateur
    SELECT
        jsonb_build_object(
            'id', p.id, 'username', p.username, 'display_name', p.display_name,
            'avatar_url', p.avatar_url, 'bio', p.bio, 'website', p.website,
            'location', p.location, 'coins_balance', COALESCE(p.coins_balance, 0)
        )
    INTO v_user_profile
    FROM public.profiles p WHERE p.id = p_user_id;

    -- 2. Résumé des morceaux (songs.creator_user_id)
    SELECT jsonb_build_object('total_count', COUNT(*), 'published_count', COUNT(*) FILTER (WHERE is_public = TRUE))
    INTO v_songs_summary FROM public.songs WHERE creator_user_id = p_user_id;

    -- 3. Résumé des albums (albums.user_id)
    SELECT jsonb_build_object('total_count', COUNT(*), 'published_count', COUNT(*) FILTER (WHERE is_public = TRUE))
    INTO v_albums_summary FROM public.albums WHERE user_id = p_user_id;

    -- 4. Résumé des tâches (todos.user_id)
    SELECT jsonb_build_object('total_count', COUNT(*), 'completed_count', COUNT(*) FILTER (WHERE is_completed = TRUE))
    INTO v_todos_summary FROM public.todos WHERE user_id = p_user_id;

    -- 5. Total des écoutes des morceaux de l'utilisateur (basé sur la somme de la colonne songs.plays)
    --    NOTE: Pour que ce total soit correct, la colonne 'plays' de la table 'public.songs' 
    --    doit être incrémentée par l'application à chaque écoute d'un morceau.
    SELECT COALESCE(SUM(s.plays), 0) INTO v_total_plays
    FROM public.songs s
    WHERE s.creator_user_id = p_user_id;

    -- 6. Total des vues (des contenus de l'utilisateur via songs.creator_user_id et albums.user_id)
    SELECT COALESCE(COUNT(v.id), 0) INTO v_total_views
    FROM public.views v
    WHERE (v.resource_type = 'song' AND v.resource_id IN (SELECT id FROM public.songs WHERE creator_user_id = p_user_id))
       OR (v.resource_type = 'album' AND v.resource_id IN (SELECT id FROM public.albums WHERE user_id = p_user_id));

    -- 7. Total des likes (des contenus de l'utilisateur via songs.creator_user_id et albums.user_id)
    SELECT COALESCE(COUNT(l.id), 0) INTO v_total_likes
    FROM public.likes l
    WHERE (l.resource_type = 'song' AND l.resource_id IN (SELECT id FROM public.songs WHERE creator_user_id = p_user_id))
       OR (l.resource_type = 'album' AND l.resource_id IN (SELECT id FROM public.albums WHERE user_id = p_user_id));

    -- 8. Total des followers (profiles.id)
    SELECT COUNT(*) INTO v_total_followers FROM public.follows WHERE following_id = p_user_id;

    -- 9. Morceaux récents (songs.creator_user_id)
    WITH recent_s AS (
        SELECT s.id, s.title, s.cover_art_url, s.genre AS genres, s.created_at, s.updated_at, s.is_public
        FROM public.songs s WHERE s.creator_user_id = p_user_id ORDER BY s.created_at DESC LIMIT 5
    )
    SELECT COALESCE(jsonb_agg(jsonb_build_object('id', rs.id, 'title', rs.title, 'cover_art_url', rs.cover_art_url, 'genres', rs.genres, 'created_at', rs.created_at, 'updated_at', rs.updated_at, 'is_public', rs.is_public)), '[]'::jsonb)
    INTO v_recent_songs FROM recent_s rs;

    -- 10. Albums récents (albums.user_id)
    WITH recent_a AS (
        SELECT a.id, a.title, a.cover_url AS album_art_url, a.genre AS genres, a.created_at, a.updated_at, a.is_public
        FROM public.albums a WHERE a.user_id = p_user_id ORDER BY a.created_at DESC LIMIT 5
    )
    SELECT COALESCE(jsonb_agg(jsonb_build_object('id', ra.id, 'title', ra.title, 'album_art_url', ra.album_art_url, 'genres', ra.genres, 'created_at', ra.created_at, 'updated_at', ra.updated_at, 'is_public', ra.is_public)), '[]'::jsonb)
    INTO v_recent_albums FROM recent_a ra;

    -- 11. Top morceaux (songs.creator_user_id)
    WITH top_s AS (
        SELECT s.id, s.title, s.cover_art_url, s.genre AS genres, s.created_at, s.updated_at, s.is_public, s.plays
        FROM public.songs s
        WHERE s.creator_user_id = p_user_id ORDER BY s.plays DESC NULLS LAST LIMIT 5
    )
    SELECT COALESCE(jsonb_agg(jsonb_build_object('id', ts.id, 'title', ts.title, 'cover_art_url', ts.cover_art_url, 'genres', ts.genres, 'plays', ts.plays, 'created_at', ts.created_at, 'updated_at', ts.updated_at, 'is_public', ts.is_public)), '[]'::jsonb)
    INTO v_top_songs FROM top_s ts;

    -- 12. Commentaires récents (resource_id lié à songs.creator_user_id ou albums.user_id)
    WITH recent_c AS (
      SELECT comm.id, comm.content, comm.created_at, comm.resource_type, comm.resource_id, comm.status as comment_status,
             prof.username as commenter_username, prof.avatar_url as commenter_avatar_url
      FROM public.comments comm JOIN public.profiles prof ON comm.user_id = prof.id
      WHERE (comm.resource_type = 'song' AND comm.resource_id IN (SELECT id FROM public.songs WHERE creator_user_id = p_user_id))
         OR (comm.resource_type = 'album' AND comm.resource_id IN (SELECT id FROM public.albums WHERE user_id = p_user_id))
      ORDER BY comm.created_at DESC LIMIT 5
    )
    SELECT COALESCE(jsonb_agg(jsonb_build_object('id', rc.id, 'content', rc.content, 'created_at', rc.created_at, 'resource_type', rc.resource_type, 'resource_id', rc.resource_id, 'comment_status', rc.comment_status, 'profiles', jsonb_build_object('username', rc.commenter_username, 'avatar_url', rc.commenter_avatar_url))), '[]'::jsonb)
    INTO v_recent_comments FROM recent_c rc;

    -- Daily metrics for the last 7 days
    WITH last_7_days AS (
        SELECT generate_series(
                   CURRENT_DATE - INTERVAL '6 days',
                   CURRENT_DATE,
                   INTERVAL '1 day'
               )::DATE AS metric_date
    ),
    daily_song_creations AS (
        SELECT
            DATE(s.created_at) AS creation_date,
            COUNT(s.id) AS new_songs_count
        FROM public.songs s
        WHERE s.creator_user_id = p_user_id
          AND DATE(s.created_at) >= CURRENT_DATE - INTERVAL '6 days'
          AND DATE(s.created_at) <= CURRENT_DATE
        GROUP BY DATE(s.created_at)
    ),
    daily_song_plays AS (
        -- Compte les événements d'écoute individuels des 7 derniers jours depuis la table public.plays
        -- pour le graphique 'Écoutes (7 derniers jours)'.
        -- Chaque enregistrement dans public.plays est considéré comme une écoute.
        SELECT
            DATE(pl.created_at) AS play_date,
            COUNT(*) AS plays_count -- Count individual play events
        FROM public.plays pl
        JOIN public.songs s ON pl.song_id = s.id
        WHERE s.creator_user_id = p_user_id -- Plays for songs owned by the user
          AND DATE(pl.created_at) >= CURRENT_DATE - INTERVAL '6 days'
          AND DATE(pl.created_at) <= CURRENT_DATE
        GROUP BY DATE(pl.created_at)
    )
    SELECT COALESCE(
        jsonb_agg(
            jsonb_build_object(
                'date', TO_CHAR(d.metric_date, 'YYYY-MM-DD'),
                'plays', COALESCE(dsp.plays_count, 0),       -- For "Écoutes (7 derniers jours)" chart
                'views', COALESCE(dsc.new_songs_count, 0)    -- For "Activité de création" chart (new songs)
            ) ORDER BY d.metric_date ASC
        ),
        '[]'::jsonb
    )
    INTO v_daily_metrics_for_dashboard
    FROM last_7_days d
    LEFT JOIN daily_song_creations dsc ON d.metric_date = dsc.creation_date
    LEFT JOIN daily_song_plays dsp ON d.metric_date = dsp.play_date;

    RETURN jsonb_build_object(
        'userProfile', COALESCE(v_user_profile, jsonb_build_object('id', p_user_id, 'username', 'N/A', 'display_name', 'N/A', 'avatar_url', '', 'bio', null, 'website', null, 'location', null, 'coins_balance', 0)),
        'songs_summary', COALESCE(v_songs_summary, jsonb_build_object('total_count', 0, 'published_count', 0)),
        'albums_summary', COALESCE(v_albums_summary, jsonb_build_object('total_count', 0, 'published_count', 0)),
        'todos_summary', COALESCE(v_todos_summary, jsonb_build_object('total_count', 0, 'completed_count', 0)),
        'totalPlays', COALESCE(v_total_plays, 0),
        'totalViews', COALESCE(v_total_views, 0),
        'totalLikes', COALESCE(v_total_likes, 0),
        'totalFollowers', COALESCE(v_total_followers, 0),
        'daily_metrics_for_dashboard', COALESCE(v_daily_metrics_for_dashboard, '[]'::jsonb),
        'weekly_metrics_for_dashboard', '[]'::jsonb,
        'monthly_metrics_for_dashboard', '[]'::jsonb,
        'recentSongs', COALESCE(v_recent_songs, '[]'::jsonb),
        'recentAlbums', COALESCE(v_recent_albums, '[]'::jsonb),
        'topSongs', COALESCE(v_top_songs, '[]'::jsonb),
        'recent_comments', COALESCE(v_recent_comments, '[]'::jsonb)
    );
END;
$$;
