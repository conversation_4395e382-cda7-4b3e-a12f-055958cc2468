'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Music, Play, Pause, Square, SkipBack, SkipForward, Volume2,
  Mic, Headphones, Settings, Save, Share2, Download, Upload,
  Sparkles, Brain, Guitar, Piano, Drum, Layers, Waveform,
  Eye, EyeOff, Maximize2, Minimize2, RotateCcw, RotateCw,
  Heart, Star, Bookmark, MessageCircle, Users, Clock
} from 'lucide-react';

// Import des composants optimisés
import { MusicWorkspace } from './MusicWorkspace';
import { AIStudioAssistant } from './AIStudioAssistant';
import { InstrumentPanel } from './InstrumentPanel';
import { StudioControls } from './StudioControls';

interface AIComposerStudioProProps {
  // États principaux
  songId?: string;
  currentSong: any;
  setCurrentSong: (song: any) => void;
  
  // Contenu musical
  lyricsContent: string;
  setLyricsContent: (content: string) => void;
  songSections: any[];
  setSongSections: (sections: any[]) => void;
  selectedSection: string;
  setSelectedSection: (section: string) => void;
  
  // Configuration
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  
  // IA
  isConfigured: boolean;
  aiHistory: any[];
  setAiHistory: (history: any[]) => void;
  lastAiResult: string;
  setLastAiResult: (result: string) => void;
  aiLoading: boolean;
  aiError: string | null;
  aiConfig?: any;
  setAiConfig?: (config: any) => void;
  
  // Actions
  handleAIGenerate: (prompt: string, type: string) => Promise<void>;
  handleLyricsChange: (content: string) => void;
  handleSave: () => Promise<void>;
  
  // Instruments
  availableInstruments: any[];
}

type StudioMode = 'compose' | 'arrange' | 'mix' | 'master';
type ViewMode = 'focus' | 'split' | 'full';

export const AIComposerStudioPro: React.FC<AIComposerStudioProProps> = ({
  songId,
  currentSong,
  setCurrentSong,
  lyricsContent,
  setLyricsContent,
  songSections,
  setSongSections,
  selectedSection,
  setSelectedSection,
  styleConfig,
  setStyleConfig,
  isConfigured,
  aiHistory,
  setAiHistory,
  lastAiResult,
  setLastAiResult,
  aiLoading,
  aiError,
  aiConfig,
  setAiConfig,
  handleAIGenerate,
  handleLyricsChange,
  handleSave,
  availableInstruments
}) => {
  
  // États du studio
  const [studioMode, setStudioMode] = useState<StudioMode>('compose');
  const [viewMode, setViewMode] = useState<ViewMode>('split');
  const [isPlaying, setIsPlaying] = useState(false);
  const [showAI, setShowAI] = useState(true);
  const [showInstruments, setShowInstruments] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(240); // 4 minutes par défaut

  // Métriques du projet
  const projectMetrics = React.useMemo(() => {
    const totalWords = lyricsContent.trim().split(/\s+/).filter(Boolean).length;
    const totalSections = songSections.length;
    const completionScore = Math.min(100, (totalWords / 100) * 100);
    
    return {
      totalWords,
      totalSections,
      completionScore,
      estimatedDuration: totalSections * 30, // 30s par section
      lastModified: new Date().toLocaleTimeString()
    };
  }, [lyricsContent, songSections]);

  // Modes du studio avec couleurs et icônes
  const studioModes = [
    {
      id: 'compose' as StudioMode,
      label: 'Composer',
      icon: Music,
      color: 'bg-blue-500',
      description: 'Écriture et création'
    },
    {
      id: 'arrange' as StudioMode,
      label: 'Arranger',
      icon: Layers,
      color: 'bg-green-500',
      description: 'Structure et instruments'
    },
    {
      id: 'mix' as StudioMode,
      label: 'Mixer',
      icon: Waveform,
      color: 'bg-purple-500',
      description: 'Balance et effets'
    },
    {
      id: 'master' as StudioMode,
      label: 'Master',
      icon: Sparkles,
      color: 'bg-orange-500',
      description: 'Finalisation'
    }
  ];

  // Gestionnaire de lecture
  const handlePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  // Gestionnaire de sauvegarde
  const handleQuickSave = useCallback(async () => {
    try {
      await handleSave();
    } catch (error) {
      console.error('Erreur sauvegarde:', error);
    }
  }, [handleSave]);

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* Header Studio Pro */}
      <div className="border-b border-slate-700 bg-slate-800/80 backdrop-blur-sm">
        <div className="flex items-center justify-between p-4">
          {/* Logo et titre */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
                <Music className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  AI Studio Pro
                </h1>
                <p className="text-sm text-slate-400">{currentSong.title || 'Nouvelle composition'}</p>
              </div>
            </div>
            
            {/* Modes du studio */}
            <div className="flex items-center gap-1 bg-slate-700/50 rounded-lg p-1">
              {studioModes.map((mode) => {
                const Icon = mode.icon;
                return (
                  <Button
                    key={mode.id}
                    variant={studioMode === mode.id ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setStudioMode(mode.id)}
                    className={`gap-2 ${studioMode === mode.id ? mode.color : 'hover:bg-slate-600'}`}
                    title={mode.description}
                  >
                    <Icon className="h-4 w-4" />
                    {mode.label}
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Contrôles et statut */}
          <div className="flex items-center gap-4">
            {/* Métriques projet */}
            <div className="flex items-center gap-3 text-sm text-slate-400">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {Math.floor(projectMetrics.estimatedDuration / 60)}:{(projectMetrics.estimatedDuration % 60).toString().padStart(2, '0')}
              </div>
              <div className="flex items-center gap-1">
                <Layers className="h-4 w-4" />
                {projectMetrics.totalSections} sections
              </div>
              <div className="flex items-center gap-1">
                <MessageCircle className="h-4 w-4" />
                {projectMetrics.totalWords} mots
              </div>
            </div>

            {/* Statut IA */}
            {isConfigured && (
              <Badge variant="default" className="gap-1 bg-green-500">
                <Brain className="h-3 w-3" />
                IA Active
              </Badge>
            )}

            {/* Actions rapides */}
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleQuickSave} className="gap-1">
                <Save className="h-4 w-4" />
                Sauver
              </Button>
              <Button variant="outline" size="sm" className="gap-1">
                <Share2 className="h-4 w-4" />
                Partager
              </Button>
              <Button variant="outline" size="sm" className="gap-1">
                <Download className="h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
        </div>

        {/* Contrôles de transport */}
        <div className="border-t border-slate-700 px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Transport */}
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="sm">
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button 
                variant={isPlaying ? "default" : "outline"} 
                size="sm" 
                onClick={handlePlayPause}
                className="gap-1"
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                {isPlaying ? 'Pause' : 'Play'}
              </Button>
              <Button variant="ghost" size="sm">
                <Square className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <SkipForward className="h-4 w-4" />
              </Button>
              
              {/* Timeline */}
              <div className="flex items-center gap-2 ml-4">
                <span className="text-sm text-slate-400 font-mono">
                  {Math.floor(currentTime / 60)}:{(currentTime % 60).toString().padStart(2, '0')}
                </span>
                <div className="w-32 h-1 bg-slate-600 rounded-full relative">
                  <div 
                    className="h-full bg-blue-500 rounded-full transition-all"
                    style={{ width: `${(currentTime / duration) * 100}%` }}
                  />
                </div>
                <span className="text-sm text-slate-400 font-mono">
                  {Math.floor(duration / 60)}:{(duration % 60).toString().padStart(2, '0')}
                </span>
              </div>
            </div>

            {/* Contrôles vue */}
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 bg-slate-700/50 rounded-lg p-1">
                <Button
                  variant={viewMode === 'focus' ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode('focus')}
                  title="Mode Focus"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'split' ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode('split')}
                  title="Mode Split"
                >
                  <Layers className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'full' ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode('full')}
                  title="Vue complète"
                >
                  <Minimize2 className="h-4 w-4" />
                </Button>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAI(!showAI)}
                title="Assistant IA"
              >
                {showAI ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowInstruments(!showInstruments)}
                title="Instruments"
              >
                <Guitar className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Workspace principal */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'focus' ? (
          // Mode Focus - Workspace seul
          <MusicWorkspace
            studioMode={studioMode}
            lyricsContent={lyricsContent}
            setLyricsContent={setLyricsContent}
            songSections={songSections}
            setSongSections={setSongSections}
            selectedSection={selectedSection}
            setSelectedSection={setSelectedSection}
            styleConfig={styleConfig}
            setStyleConfig={setStyleConfig}
            handleLyricsChange={handleLyricsChange}
            handleAIGenerate={handleAIGenerate}
            availableInstruments={availableInstruments}
          />
        ) : (
          // Mode Split/Full - Layout avec panneaux
          <div className="flex h-full">
            {/* Panneau gauche - Instruments */}
            {showInstruments && viewMode !== 'focus' && (
              <div className="w-80 border-r border-slate-700 bg-slate-800/50">
                <InstrumentPanel
                  studioMode={studioMode}
                  songSections={songSections}
                  selectedSection={selectedSection}
                  availableInstruments={availableInstruments}
                  styleConfig={styleConfig}
                  setStyleConfig={setStyleConfig}
                />
              </div>
            )}

            {/* Workspace central */}
            <div className="flex-1">
              <MusicWorkspace
                studioMode={studioMode}
                lyricsContent={lyricsContent}
                setLyricsContent={setLyricsContent}
                songSections={songSections}
                setSongSections={setSongSections}
                selectedSection={selectedSection}
                setSelectedSection={setSelectedSection}
                styleConfig={styleConfig}
                setStyleConfig={setStyleConfig}
                handleLyricsChange={handleLyricsChange}
                handleAIGenerate={handleAIGenerate}
                availableInstruments={availableInstruments}
              />
            </div>

            {/* Panneau droit - Assistant IA */}
            {showAI && viewMode !== 'focus' && (
              <div className="w-96 border-l border-slate-700 bg-slate-800/50">
                <AIStudioAssistant
                  studioMode={studioMode}
                  isConfigured={isConfigured}
                  aiLoading={aiLoading}
                  aiError={aiError}
                  aiConfig={aiConfig}
                  setAiConfig={setAiConfig}
                  currentSection={selectedSection}
                  songSections={songSections}
                  styleConfig={styleConfig}
                  lyricsContent={lyricsContent}
                  aiHistory={aiHistory}
                  lastAiResult={lastAiResult}
                  setAiHistory={setAiHistory}
                  setLastAiResult={setLastAiResult}
                  onAIGenerate={handleAIGenerate}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer avec statut */}
      <div className="border-t border-slate-700 bg-slate-800/80 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-slate-400">
          <div className="flex items-center gap-4">
            <span>Mode: {studioModes.find(m => m.id === studioMode)?.label}</span>
            <span>•</span>
            <span>Tonalité: {currentSong.key || 'C'}</span>
            <span>•</span>
            <span>Tempo: {currentSong.tempo || 120} BPM</span>
            <span>•</span>
            <span>Complétude: {projectMetrics.completionScore}%</span>
          </div>
          <div className="flex items-center gap-2">
            <span>Dernière modif: {projectMetrics.lastModified}</span>
            {songId && (
              <Badge variant="outline" className="text-xs">
                Synchronisé
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
