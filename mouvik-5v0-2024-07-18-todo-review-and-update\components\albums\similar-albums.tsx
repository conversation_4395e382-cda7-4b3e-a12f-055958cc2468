"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { getSupabaseClient } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Assuming Card is used
import { Badge } from '@/components/ui/badge'; // For displaying common genres or tags
import { Disc } from 'lucide-react';

interface SimilarAlbum {
  id: string;
  title: string;
  slug: string | null;
  cover_url: string | null;
  profiles: Array<{ // Artist info - Supabase join returns an array
    display_name: string | null;
    username: string | null;
    id: string; // Artist's user_id
  }> | null;
}

interface SimilarAlbumsProps {
  currentAlbumId: string;
  currentAlbumGenres: string[] | null | undefined;
  currentAlbumArtistId: string | null | undefined;
}

export function SimilarAlbums({ currentAlbumId, currentAlbumGenres, currentAlbumArtistId }: SimilarAlbumsProps) {
  const [similarAlbums, setSimilarAlbums] = useState<SimilarAlbum[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = getSupabaseClient();

  useEffect(() => {
    const fetchSimilarAlbums = async () => {
      if (!currentAlbumId) {
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      let fetchedAlbums: SimilarAlbum[] = [];

      // Phase 1: Albums by the same artist in the same genres (if artist and genres are available)
      if (currentAlbumArtistId && currentAlbumGenres && currentAlbumGenres.length > 0) {
        const { data: artistGenreAlbums, error: artistGenreError } = await supabase
          .from('albums')
          .select(`
            id, title, slug, cover_url,
            profiles:user_id (id, display_name, username)
          `)
          .eq('user_id', currentAlbumArtistId)
          .overlaps('genre', currentAlbumGenres)
          .neq('id', currentAlbumId)
          .eq('is_public', true)
          .limit(4);
        
        if (artistGenreError) console.error("Error fetching similar albums (artist+genre):", artistGenreError);
        else if (artistGenreAlbums) fetchedAlbums = [...fetchedAlbums, ...(artistGenreAlbums as SimilarAlbum[])];
      }

      // Phase 2: If not enough albums, fetch albums by any artist in the same genres
      if (fetchedAlbums.length < 4 && currentAlbumGenres && currentAlbumGenres.length > 0) {
        const existingIds = fetchedAlbums.map(a => a.id);
        const needed = 4 - fetchedAlbums.length;

        const { data: genreAlbums, error: genreError } = await supabase
          .from('albums')
          .select(`
            id, title, slug, cover_url,
            profiles:user_id (id, display_name, username)
          `)
          .overlaps('genre', currentAlbumGenres)
          .neq('id', currentAlbumId)
          .eq('is_public', true)
          .limit(needed);
        
        if (genreError) console.error("Error fetching similar albums (genre only):", genreError);
        else if (genreAlbums) {
          genreAlbums.forEach(ga => {
            if (!fetchedAlbums.find(fa => fa.id === (ga as SimilarAlbum).id)) {
              fetchedAlbums.push(ga as SimilarAlbum);
            }
          });
        }
      }
      
      const uniqueAlbums = Array.from(new Map(fetchedAlbums.map(album => [album.id, album])).values());
      setSimilarAlbums(uniqueAlbums.slice(0, 4));
      setIsLoading(false);
    };

    fetchSimilarAlbums();
  }, [currentAlbumId, currentAlbumGenres, currentAlbumArtistId, supabase]);

  if (isLoading) {
    return (
      <div>
        <h3 className="text-xl font-semibold mb-3">Albums Similaires</h3>
        <p className="text-sm text-muted-foreground">Chargement...</p>
      </div>
    );
  }

  if (similarAlbums.length === 0) {
    return (
       <div>
        <h3 className="text-xl font-semibold mb-3">Albums Similaires</h3>
        <p className="text-sm text-muted-foreground">Aucun album similaire trouvé pour le moment.</p>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-xl font-semibold mb-4">Albums Similaires</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {similarAlbums.map(album => (
          <Link key={album.id} href={album.slug ? `/album/${album.slug}` : `/albums/${album.id}`} className="block group">
            <Card className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="aspect-square relative bg-muted">
                {album.cover_url ? (
                  <Image src={album.cover_url} alt={album.title} layout="fill" objectFit="cover" className="group-hover:scale-105 transition-transform duration-300" />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Disc className="w-16 h-16 text-muted-foreground" />
                  </div>
                )}
              </div>
              <CardContent className="p-3">
                <p className="text-sm font-semibold truncate group-hover:underline" title={album.title}>{album.title}</p>
                {album.profiles && album.profiles.length > 0 && album.profiles[0] && (
                  <p className="text-xs text-muted-foreground truncate" title={album.profiles[0].display_name || album.profiles[0].username || ''}>
                    {album.profiles[0].display_name || album.profiles[0].username || 'Artiste inconnu'}
                  </p>
                )}
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
