# Comprehensive script to remove all consecutive duplicate lines
$inputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration.tsx'
$outputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration_final.tsx'

# Read all content
$allLines = Get-Content $inputFile -Encoding UTF8
$cleanLines = @()
$previousLine = $null
$duplicatesRemoved = 0

foreach ($line in $allLines) {
    if ($line -ne $previousLine) {
        $cleanLines += $line
        $previousLine = $line
    } else {
        $duplicatesRemoved++
        Write-Host "Removing duplicate line: $line"
    }
}

# Write the cleaned content
$cleanLines | Set-Content $outputFile -Encoding UTF8

Write-Host "Processing complete:"
Write-Host "Original lines: $($allLines.Length)"
Write-Host "Clean lines: $($cleanLines.Length)"
Write-Host "Duplicates removed: $duplicatesRemoved"