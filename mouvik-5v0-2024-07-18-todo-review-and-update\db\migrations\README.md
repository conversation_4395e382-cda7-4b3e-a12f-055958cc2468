# Gestion des Migrations de Base de Données

Ce dossier contient les scripts de migration pour la base de données Mouvik.

## 📋 Fichiers Principaux

- `20240516_rename_tracks_to_songs.sql` - Script de migration principal
- `rollback_20240516_rename_tracks_to_songs.sql` - Script de rollback
- `test_migration.js` - Script de test de la migration
- `config.js` - Configuration des migrations
- `config.local.example.js` - Exemple de configuration locale

## 🚀 Utilisation

### Configuration

1. <PERSON><PERSON><PERSON> le fichier `config.local.example.js` en `config.local.js`
2. Modifiez les paramètres selon votre environnement

### Exécution d'une Migration

```bash
# Exécuter la migration
psql -U votre_utilisateur -d votre_base_de_données -f 20240516_rename_tracks_to_songs.sql

# Tester la migration
node test_migration.js
```

### Rollback

En cas de problème, utilisez le script de rollback :

```bash
psql -U votre_utilisateur -d votre_base_de_données -f rollback_20240516_rename_tracks_to_songs.sql
```

## 📝 Bonnes Pratiques

1. Toujours sauvegarder la base de données avant d'exécuter une migration
2. Tester la migration dans un environnement de développement d'abord
3. Documenter toutes les modifications apportées aux schémas
4. Utiliser des transactions pour les migrations complexes
5. Vérifier les dépendances entre les migrations

## 🔍 Vérification

Après une migration, vérifiez :

- L'intégrité des données
- Les performances des requêtes
- Le bon fonctionnement des fonctionnalités existantes

## 📚 Documentation Supplémentaire

- [Guide de Migration](../docs/MIGRATION_GUIDE.md)
- [Structure de la Base de Données](../docs/UPDATED_DATABASE_STRUCTURE.md)
- [Modifications de Code Requises](../docs/CODE_UPDATES_TRACKS_TO_SONGS.md)
