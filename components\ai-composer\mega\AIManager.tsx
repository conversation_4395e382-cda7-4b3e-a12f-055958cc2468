'use client';

import React, { useState, useCallback } from 'react';
import { AIPromptEngine, PromptContext } from './AIPromptEngine';

interface AIManagerProps {
  aiConfig: any;
  onUpdateActivity: (activity: any) => void;
  onUpdateResult: (result: string) => void;
  onUpdateError: (error: string | null) => void;
  onUpdateLoading: (loading: boolean) => void;
}

export class AIManager {
  private config: any;
  private onUpdateActivity: (activity: any) => void;
  private onUpdateResult: (result: string) => void;
  private onUpdateError: (error: string | null) => void;
  private onUpdateLoading: (loading: boolean) => void;

  constructor(props: AIManagerProps) {
    this.config = props.aiConfig;
    this.onUpdateActivity = props.onUpdateActivity;
    this.onUpdateResult = props.onUpdateResult;
    this.onUpdateError = props.onUpdateError;
    this.onUpdateLoading = props.onUpdateLoading;
  }

  // Test de connexion
  async testConnection(): Promise<boolean> {
    try {
      this.onUpdateLoading(true);
      this.onUpdateError(null);

      const testPrompt = "Réponds simplement 'Test réussi' pour confirmer la connexion.";
      const result = await this.callAI(testPrompt, 'test');
      
      return result.includes('Test réussi') || result.includes('test') || result.length > 0;
    } catch (error: any) {
      this.onUpdateError(error.message);
      return false;
    } finally {
      this.onUpdateLoading(false);
    }
  }

  // Appel IA principal
  async callAI(prompt: string, type: string = 'general'): Promise<string> {
    if (!this.config?.apiUrl || !this.config?.model) {
      throw new Error('Configuration IA incomplète');
    }

    this.onUpdateLoading(true);
    this.onUpdateError(null);

    const activity = {
      id: `activity-${Date.now()}`,
      type,
      prompt: prompt.substring(0, 100) + '...',
      status: 'processing',
      timestamp: new Date()
    };

    this.onUpdateActivity(activity);

    try {
      let result: string;

      switch (this.config.provider) {
        case 'ollama':
          result = await this.callOllama(prompt);
          break;
        case 'openai':
          result = await this.callOpenAI(prompt);
          break;
        case 'anthropic':
          result = await this.callAnthropic(prompt);
          break;
        default:
          result = await this.callCustomAPI(prompt);
      }

      this.onUpdateResult(result);
      this.onUpdateActivity({
        ...activity,
        status: 'completed',
        result,
        duration: Date.now() - activity.timestamp.getTime()
      });

      return result;

    } catch (error: any) {
      const errorMessage = error.message || 'Erreur inconnue';
      this.onUpdateError(errorMessage);
      this.onUpdateActivity({
        ...activity,
        status: 'error',
        error: errorMessage,
        duration: Date.now() - activity.timestamp.getTime()
      });
      throw error;
    } finally {
      this.onUpdateLoading(false);
    }
  }

  // Appel Ollama
  private async callOllama(prompt: string): Promise<string> {
    const response = await fetch(`${this.config.apiUrl}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.config.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: this.config.temperature || 0.7,
          num_predict: this.config.maxTokens || 1000,
        }
      }),
      signal: AbortSignal.timeout(60000) // 60s timeout
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Ollama Error ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    
    if (!data.response) {
      throw new Error('Réponse vide de Ollama');
    }

    return data.response.trim();
  }

  // Appel OpenAI
  private async callOpenAI(prompt: string): Promise<string> {
    const response = await fetch(`${this.config.apiUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: 'Tu es un expert en composition musicale et écriture de chansons. Réponds de manière créative, précise et pratique.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: this.config.temperature || 0.7,
        max_tokens: this.config.maxTokens || 1000
      }),
      signal: AbortSignal.timeout(60000)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenAI Error ${response.status}: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.choices?.[0]?.message?.content) {
      throw new Error('Réponse vide d\'OpenAI');
    }

    return data.choices[0].message.content.trim();
  }

  // Appel Anthropic Claude
  private async callAnthropic(prompt: string): Promise<string> {
    const response = await fetch(`${this.config.apiUrl}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: this.config.model,
        max_tokens: this.config.maxTokens || 1000,
        temperature: this.config.temperature || 0.7,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      }),
      signal: AbortSignal.timeout(60000)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic Error ${response.status}: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.content?.[0]?.text) {
      throw new Error('Réponse vide d\'Anthropic');
    }

    return data.content[0].text.trim();
  }

  // Appel API personnalisée
  private async callCustomAPI(prompt: string): Promise<string> {
    const response = await fetch(`${this.config.apiUrl}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
      },
      body: JSON.stringify({
        prompt: prompt,
        model: this.config.model,
        temperature: this.config.temperature || 0.7,
        max_tokens: this.config.maxTokens || 1000
      }),
      signal: AbortSignal.timeout(60000)
    });

    if (!response.ok) {
      throw new Error(`Custom API Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Essayer différents formats de réponse
    const result = data.response || data.text || data.content || data.result;
    
    if (!result) {
      throw new Error('Format de réponse API personnalisée non reconnu');
    }

    return result.trim();
  }

  // Fonctions spécialisées avec prompts optimisés
  async generateSuggestion(context: PromptContext, type: string): Promise<string> {
    const prompts = AIPromptEngine.getSuggestionPrompts(context);
    const prompt = prompts[type] || prompts.continueWriting;
    return this.callAI(prompt, `suggestion-${type}`);
  }

  async analyzeContent(context: PromptContext, dimension: string, level: string): Promise<string> {
    const prompt = AIPromptEngine.getAnalysisPrompts(context, dimension, level);
    return this.callAI(prompt, `analysis-${dimension}-${level}`);
  }

  async chatWithAI(context: PromptContext, userMessage: string): Promise<string> {
    const prompt = AIPromptEngine.getChatPrompt(context, userMessage);
    return this.callAI(prompt, 'chat');
  }

  // Fonctions spécialisées pour les accords
  async suggestChords(context: PromptContext): Promise<string> {
    const prompt = `${AIPromptEngine.generateMusicContext(context)}

🎸 MISSION : SUGGESTION D'ACCORDS OPTIMALE

Analyse le contenu et propose une progression d'accords parfaite :

1. ANALYSE HARMONIQUE :
   • Tonalité : ${context.key} majeur
   • Genre : ${context.genre}
   • Section : ${context.currentSection.type}
   • Ambiance : ${context.mood}
   • Tempo : ${context.bpm} BPM

2. PROGRESSION RECOMMANDÉE :
   • 4-6 accords principaux
   • Fonctions harmoniques claires
   • Adaptation au contenu lyrique
   • Couleurs émotionnelles appropriées

3. FORMAT DE RÉPONSE :
   • Nom des accords (ex: C - Am - F - G)
   • Position dans la section
   • Justification harmonique
   • Effet émotionnel recherché

CONSIGNE : Propose une progression d'accords concrète et applicable immédiatement.`;

    return this.callAI(prompt, 'chords-suggest');
  }

  async improveChords(context: PromptContext, currentChords: string[]): Promise<string> {
    const prompt = `${AIPromptEngine.generateMusicContext(context)}

🎸 MISSION : AMÉLIORATION D'ACCORDS

Progression actuelle : ${currentChords.join(' - ')}

Analyse et améliore cette progression :

1. ANALYSE DE L'EXISTANT :
   • Fonctions harmoniques actuelles
   • Points forts et faibles
   • Cohérence avec le style ${context.genre}
   • Adéquation à l'ambiance ${context.mood}

2. AMÉLIORATIONS PROPOSÉES :
   • Substitutions d'accords plus riches
   • Extensions harmoniques (7ème, 9ème)
   • Accords de passage
   • Variations rythmiques

3. ALTERNATIVES CRÉATIVES :
   • Version simplifiée
   • Version enrichie
   • Modulations possibles
   • Effets harmoniques spéciaux

CONSIGNE : Propose 2-3 versions améliorées avec explications détaillées.`;

    return this.callAI(prompt, 'chords-improve');
  }

  // Mise à jour de la configuration
  updateConfig(newConfig: any) {
    this.config = { ...this.config, ...newConfig };
  }
}

// Hook React pour utiliser AIManager
export const useAIManager = (aiConfig: any) => {
  const [aiLoading, setAiLoading] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [lastAiResult, setLastAiResult] = useState('');
  const [aiActivities, setAiActivities] = useState<any[]>([]);

  const aiManager = React.useMemo(() => {
    return new AIManager({
      aiConfig,
      onUpdateActivity: (activity) => {
        setAiActivities(prev => {
          const existing = prev.find(a => a.id === activity.id);
          if (existing) {
            return prev.map(a => a.id === activity.id ? activity : a);
          } else {
            return [activity, ...prev.slice(0, 9)]; // Garder 10 max
          }
        });
      },
      onUpdateResult: setLastAiResult,
      onUpdateError: setAiError,
      onUpdateLoading: setAiLoading
    });
  }, [aiConfig]);

  // Mettre à jour la config quand elle change
  React.useEffect(() => {
    aiManager.updateConfig(aiConfig);
  }, [aiConfig, aiManager]);

  const testConnection = useCallback(async () => {
    return aiManager.testConnection();
  }, [aiManager]);

  const generateSuggestion = useCallback(async (context: PromptContext, type: string) => {
    return aiManager.generateSuggestion(context, type);
  }, [aiManager]);

  const analyzeContent = useCallback(async (context: PromptContext, dimension: string, level: string) => {
    return aiManager.analyzeContent(context, dimension, level);
  }, [aiManager]);

  const chatWithAI = useCallback(async (context: PromptContext, message: string) => {
    return aiManager.chatWithAI(context, message);
  }, [aiManager]);

  const suggestChords = useCallback(async (context: PromptContext) => {
    return aiManager.suggestChords(context);
  }, [aiManager]);

  const improveChords = useCallback(async (context: PromptContext, chords: string[]) => {
    return aiManager.improveChords(context, chords);
  }, [aiManager]);

  const clearActivities = useCallback(() => {
    setAiActivities([]);
  }, []);

  return {
    // États
    aiLoading,
    aiError,
    lastAiResult,
    aiActivities,
    
    // Actions
    testConnection,
    generateSuggestion,
    analyzeContent,
    chatWithAI,
    suggestChords,
    improveChords,
    clearActivities,
    
    // Manager direct pour cas avancés
    aiManager
  };
};
