// components/songs/SongFormAudioCard.tsx
import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileAudio, Trash2, UploadCloud, Mic2, PauseCircle, PlayCircle } from 'lucide-react'; // Added Mic2, Pause, Play
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import type { LocalFileState } from './hooks/useLocalFileManagement'; // Assuming this path
import { AudioWaveformPreview } from './AudioWaveformPreview';

interface SongFormAudioCardProps {
  localAudioFile: LocalFileState;
  onAudioFileSelect: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClearAudio: () => void;
  audioInputRef: React.RefObject<HTMLInputElement>;
  // Props for recorder (simplified example)
  isRecording?: boolean;
  onToggleRecording?: () => void;
  recordedAudioPreviewUrl?: string | null;
  onClearRecordedAudio?: () => void;
  // Add any other props needed from SongForm
}

export const SongFormAudioCard: React.FC<SongFormAudioCardProps> = ({
  localAudioFile,
  onAudioFileSelect,
  onClearAudio,
  audioInputRef,
  isRecording,
  onToggleRecording,
  recordedAudioPreviewUrl,
  onClearRecordedAudio,
}) => {
  const hasUploadedAudio = localAudioFile.file || localAudioFile.previewUrl;
  const hasRecordedAudio = recordedAudioPreviewUrl;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fichier Audio Principal</CardTitle>
        <CardDescription>Gérez le fichier audio de votre morceau.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Audio Preview Section */}
        <div className="h-24 w-full rounded-md border border-dashed flex items-center justify-center bg-muted/20">
          {hasUploadedAudio && localAudioFile.previewUrl && (
            <AudioWaveformPreview key={localAudioFile.previewUrl} audioUrl={localAudioFile.previewUrl} audioFileName={localAudioFile.file?.name} />
          )}
          {hasRecordedAudio && (
            <AudioWaveformPreview audioUrl={recordedAudioPreviewUrl} audioFileName="Enregistrement" />
          )}
          {!hasUploadedAudio && !hasRecordedAudio && (
            <div className="text-center text-muted-foreground p-4">
              <FileAudio size={36} className="mx-auto mb-1" />
              <p className="text-xs">Aucun fichier audio.</p>
            </div>
          )}
        </div>

        <Input
          ref={audioInputRef}
          type="file"
          accept="audio/mpeg, audio/wav, audio/ogg, audio/flac"
          onChange={onAudioFileSelect}
          className="hidden"
          id="audio-file-input"
        />

        {/* Upload Section */}
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">Télécharger un fichier</p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => audioInputRef.current?.click()}
              className="w-full"
              type="button"
              disabled={localAudioFile.isUploading || isRecording}
            >
              <UploadCloud className="mr-2 h-4 w-4" />
              {localAudioFile.file ? 'Changer Fichier' : 'Choisir Fichier'}
            </Button>
            {hasUploadedAudio && (
              <Button
                variant="destructive"
                onClick={onClearAudio}
                className="w-full"
                type="button"
                disabled={localAudioFile.isUploading || isRecording}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Retirer Fichier
              </Button>
            )}
          </div>
          {localAudioFile.error && (
            <p className="text-sm text-destructive mt-1">{localAudioFile.error}</p>
          )}
          {localAudioFile.isUploading && (
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">Téléchargement: {localAudioFile.file?.name}</p>
              <Progress value={localAudioFile.uploadProgress || 0} className="h-2" />
            </div>
          )}
        </div>
        
        {/* Record Section (Simplified) */}
        {onToggleRecording && (
          <div className="space-y-2 pt-2 border-t">
            <p className="text-sm font-medium text-muted-foreground">Enregistrer l'audio</p>
            <div className="flex gap-2">
              <Button 
                variant={isRecording ? "destructive" : "outline"} 
                onClick={onToggleRecording}
                className="w-full"
                type="button"
                disabled={localAudioFile.isUploading || !!localAudioFile.file} // Disable if uploading or file already uploaded
              >
                {isRecording ? <PauseCircle className="mr-2 h-4 w-4" /> : <Mic2 className="mr-2 h-4 w-4" />}
                {isRecording ? 'Arrêter' : 'Enregistrer'}
              </Button>
              {hasRecordedAudio && onClearRecordedAudio && (
                 <Button 
                  variant="ghost" 
                  size="icon"
                  onClick={onClearRecordedAudio} 
                  disabled={isRecording}
                  title="Supprimer l'enregistrement"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};