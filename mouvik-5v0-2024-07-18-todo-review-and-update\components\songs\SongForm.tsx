"use client";

// React et hooks essentiels
import React, { 
  useEffect, 
  useState, 
  useCallback, 
  useRef,
  forwardRef,
  useImperativeHandle,
  useMemo
} from 'react';

// Navigation
import { useRouter } from 'next/navigation';

// Validation et formulaires
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  useForm, 
  FormProvider as RHFFormProvider, 
  useFieldArray, 
  SubmitHandler, 
  UseFormReturn,
  Controller,
  Control,
  FieldErrors,
  UseFormSetValue,
  UseFormGetValues,
  UseFormWatch
} from 'react-hook-form';

// Gestion des fichiers (Drag & Drop)
import useLocalFileManagement, { type LocalFileState } from './hooks/useLocalFileManagement'; // Import custom hooks
// import { type AudioRecorderHandle } from '@/components/AudioRecorder'; // Removed duplicate import
import { useSongVersioning, type LocalSongVersion, type SongVersion } from './hooks/useSongVersioning'; // Adjusted path if necessary
import { useSongFormActions } from './hooks/useSongFormActions'; // Import the new hook
import type Quill from 'quill'; 

// Utilitaires
import debounce from 'lodash/debounce';

import { cn } from '@/lib/utils';
import { format, isValid as isValidDate, parseISO } from 'date-fns';
import { createClientComponentClient, User, SupabaseClient } from '@supabase/auth-helpers-nextjs';
import { toast, useSonner } from "sonner"; // Replaced useToast with sonner, added useSonner
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import type { Album } from "@/types/album"; // Should now resolve
import type { Song } from '@/types';

import SongVault from '@/components/songs/SongVault';
import { SongFormGeneralInfoTab } from './SongFormGeneralInfoTab';
import { SongFormProductionDetailsTab } from './SongFormProductionDetailsTab';
import { SongFormHeader } from './SongFormHeader';
import { SongFormLyricsChordTab } from './SongFormLyricsChordTab'; // For lyrics and chords
import { SongFormStructureTab } from './SongFormStructureTab'; // For song structure
import { SongFormProgressionAiTab } from './SongFormProgressionAiTab'; // For progression and AI settings
import { SongFormAdvancedTab } from './SongFormAdvancedTab';
import { SongFormFooter } from './SongFormFooter';
import { SongVaultDisplay } from './SongVaultDisplay';
import { SongFormModals } from './SongFormModals';
import AudioRecorder, { AudioRecorderHandle } from '@/components/AudioRecorder';

// Placeholder UI Components (remove later when actual components are available)
// const SongFormHeader: React.FC<any> = (props) => <div {...props}>SongFormHeader Placeholder</div>; // Actual component is imported
// const SongFormAudioFilesTab: React.FC<any> = (props) => <div {...props}>SongFormAudioFilesTab Placeholder</div>; // Actual component is imported
// const SongFormSettingsTab: React.FC<any> = (props) => <div {...props}>SongFormSettingsTab Placeholder</div>; // Actual component is imported
import { SongFormCoverArtCard } from './SongFormCoverArtCard'; // Import new component
import { SongFormAudioCard } from './SongFormAudioCard'; // Import new component
import { fr } from 'date-fns/locale';
import { v4 as uuidv4 } from 'uuid';

// Composants UI (ShadCN)
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage
} from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { DatePicker } from "@/components/ui/date-picker";
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

// Icônes (Lucide React)
import {
  Edit3,
  ExternalLink,
  Eye,
  EyeOff,
  FileAudio,
  FileText,
  GanttChartSquare,
  Globe,
  GripVertical,
  History, // Was 'Versions', replaced with 'History'
  ImagePlus,
  Info,
  KeyRound,
  Layers,
  Link,
  ListMusic,
  Loader2,
  LoaderIcon, // Added LoaderIcon
  Lock,
  Maximize,
  Mic,
  Mic2,
  Minimize,
  Music,
  Music2,
  Music3,
  Music4,
  Palette,
  PanelRightClose,
  Paperclip,
  PauseCircle,
  Pencil,
  PlayCircle,
  PlusCircle,
  Podcast,
  Redo2,
  RotateCcw,
  Save,
  Search,
  Send,
  Settings2,
  Share2,
  Sheet,
  Sparkles,
  SplitSquareHorizontal,
  SquarePen,
  Tags,
  Trash2,
  Undo2,
  UploadCloud,
  UserPlus,
  Users,
  Verified,
  Volume2,
  Wand2,
  X,
  XCircle
} from 'lucide-react';

// Import schema and types
import { songSchema, SongFormValues, AiConfig, AiHistoryItem } from './song-schema'; // Added AiConfig, AiHistoryItem

// Import options
import {
  LANGUAGE_OPTIONS,
  SONG_STATUS_OPTIONS,
  ATTRIBUTION_TYPE_OPTIONS,
  GENRES_OPTIONS,
  SUBGENRES_OPTIONS,
  MOODS_OPTIONS,
  THEMES_OPTIONS,
  INSTRUMENTS_OPTIONS,
  MUSICAL_KEY_OPTIONS
} from './song-options';

// Props du composant SongForm
export interface SongFormProps {
  initialSongData?: Song | null;
  albumsData?: { id: string; title: string; }[];
  isLoadingAlbums?: boolean;
  onFormSubmit: (data: SongFormValues, coverArtFile?: File | null, audioFile?: File | null) => Promise<void>;
  isSubmittingGlobal?: boolean;
  mode?: 'create' | 'edit';
  onCancel?: () => void;
  onDelete?: (songId: string) => Promise<void>;
  currentUserId: string;
  supabaseClient: SupabaseClient;
  lastSavedTimestamp?: string;
}

// Interface pour les méthodes exposées par la ref du composant
export interface SongFormHandle {
  submit: () => Promise<void>;
  resetForm: (values?: Partial<SongFormValues>) => void;
  getFormValues: () => SongFormValues;
  setFieldValue: <K extends keyof SongFormValues>(name: K, value: SongFormValues[K]) => void;
}

// Types pour les sections du formulaire (pour la navigation par onglets)
export type FormSection = 'main' | 'production-details' | 'lyrics-chords' | 'structure' | 'progression-ai';

// Placeholder for ChordInstrument type (if needed elsewhere, consider moving to a types file)
export type ChordInstrument = {
  name: string;
  diagrams: { fret: number, string: number, finger?: number }[];
};

// Define props for SongFormHeader (basic structure)
interface SongFormHeaderProps {
  title: string;
  artist: string;
  coverArtUrl: string | null;
  audioUrl: string | null;
  isNewSong: boolean;
  onSave: () => void;
  onCancel?: () => void;
  onDelete?: () => void;
  isSubmitting: boolean;
  isDirty: boolean;
  lastSavedTimestamp: Date | null;
  isVaultPanelCollapsed: boolean;
  setIsVaultPanelCollapsed: (isCollapsed: boolean) => void;
  // Add other props as identified from usage
  currentSongVersions?: LocalSongVersion[];
  activeVersionId?: string | null;
  onLoadVersion?: (versionId: string) => void;
  onDeleteVersion?: (versionId: string) => void;
  onSaveNewVersion?: () => void;
  isSubmittingVersion?: boolean;
  isLoadingDeleteVersion?: boolean;
  onUpdateVersionDetails?: (versionId: string, details: Partial<LocalSongVersion>) => void;
  isLoadingUpdateVersion?: boolean;
}

// Helper function to parse chord strings (JSON or space-separated)
const parseChordString = (chordsString: string | null | undefined): any[] => {
  if (!chordsString || typeof chordsString !== 'string' || chordsString.trim() === '') {
    return [];
  }
  try {
    const parsed = JSON.parse(chordsString);
    if (Array.isArray(parsed)) {
      return parsed; // Assuming it's an array of chord objects
    }
    // If it's not an array, or some other JSON structure, treat as simple string list
  } catch (e) {
    // Not a valid JSON array, fall through to string splitting
  }
  // Fallback for space-separated or comma-separated simple chord names
  const potentialChords = chordsString.split(/[\s,]+/);
  return potentialChords.filter(name => name.trim() !== '').map(name => ({
    name: name.trim(),
    diagram: null // Or some default diagram structure
  }));
};

// Main component
export const SongForm = forwardRef<SongFormHandle, SongFormProps>(({
  initialSongData,
  albumsData = [],
  isLoadingAlbums = false,
  onFormSubmit,
  isSubmittingGlobal: isSubmittingGlobalFromProps = false,
  mode = 'create',
  onCancel,
  currentUserId,
  supabaseClient,
  onDelete,
  lastSavedTimestamp: lastSavedTimestampFromProps
}, ref) => {
  const router = useRouter();
  const supabase = supabaseClient || createClientComponentClient();
  const [user, setUser] = useState<User | null>(null);

  // Refs for file inputs
  const coverArtInputRef = useRef<HTMLInputElement>(null);
  const audioInputRef = useRef<HTMLInputElement>(null);

  // State for recorded audio
  const [recordedAudioBlob, setRecordedAudioBlob] = useState<Blob | null>(null);
  const [recordedAudioPreviewUrl, setRecordedAudioPreviewUrl] = useState<string | null>(null);
  const [uploadedAudioUrl, setUploadedAudioUrl] = useState<string | null>(initialSongData?.audio_url || null);
  const [isAudioRecordingActive, setIsAudioRecordingActive] = useState<boolean>(false);

  // Refs for form reset logic based on mode/initialData change
  const isVersionJustLoadedRef = useRef<boolean>(false);
  const prevModeRef = useRef<string | undefined>(mode || (initialSongData?.id ? 'edit' : 'create'));
  const prevInitialSongIdRef = useRef<string | null | undefined>(initialSongData?.id || null);

  // AI Feature Placeholders (to be properly implemented)
  const editorInstanceForAi = useRef<Quill | null>(null);

  const defaultSongFormValues: SongFormValues = {
    title: '',
    artist: '',
    artist_name: null,
    duration_ms: null,
    bpm: null,
    musical_key: null,
    time_signature: null,
    genre: null,
    subgenre: [],
    moods: [],
    lyrics: '', // Use empty string for nullable text fields for controlled inputs
    composer_name: null,
    writers: [],
    producers: [],
    bloc_note: '',
    right_column_notepad: '',
    status: 'draft', // Sensible default
    progress_data: {},
    slug: '',
    attribution_type: null,
    tuning_frequency: null,
    description: '',
    lyrics_language: null,
    custom_css: '',
    chords: '',
    structure: '',
    notes: '',
    is_public: false,
    is_favorite: false,
    is_incomplete: true,
    is_cover: false,
    is_instrumental: false,
    is_explicit: false,
    is_archived: false,
    release_date: null,
    audio_url: null,
    cover_art_url: null,
    cover_art_file_name: null,
    audio_file_name: null,
    creator_user_id: currentUserId || '', // Ensure this is set
    allow_comments: true,
    allow_downloads: false,
    band_id: null,
    tags: [],
    themes: [],
    instruments: [],
    featured_artists: [],
    contributors: [],
    plays: 0,
    visibility: 'private',
    iswc_code: null,
    editor_data: {},
    isrc_code: null,
    upc_code: null,
    album_id: null,
    copyright_notice: '',
    publisher_name: '',
    record_label: '',
    licensing_info: '',
    language: null,
    parental_advisory: null,
    recording_date: null,
    recording_status: null,
    mastering_status: null,
    mixing_engineer: null,
    mastering_engineer: null,
    mixing_status: null,
    ai_collaboration_level: null,
    artwork_credits: '',
    tablature: '',
    performance_notes: '',
    song_versions: [],
    audio_file_versions: [],
    external_links: [],
    completion_percentage: 0,
    creation_process_type: null,
    custom_fields: {},
    lyrics_sync_data: null,
    chords_diagrams: {},
    privacy_settings: { allow_embedding: true, allow_download: false },
    collaborators: [],
    split_sheet: null,
    last_played_at: null,
    play_count: 0,
    rating_average: 0,
    rating_count: 0,
    comments_count: 0,
    shares_count: 0,
    downloads_count: 0,
    stream_sources: [],
    purchase_links: [],
    related_songs: [],
    song_story: '',
    production_notes: '',
    gear_used: [],
    sample_credits: [],
    remix_info: '',
    version_of_song_id: null,
    ai_config: { provider: 'openai', model: 'gpt-3.5-turbo', temperature: 0.7 },
    arrangement_status: null,
    ai_history: [],
  };

  // Process initial values for the form, merging with defaults
  const processedInitialValues = useMemo(() => {
    if (!initialSongData) {
      return { ...defaultSongFormValues, creator_user_id: currentUserId };
    }
    // Create a new object to avoid mutating defaultSongFormValues
    const mergedValues: Partial<SongFormValues> = {}; 
    const arrayFieldKeys: ReadonlyArray<keyof SongFormValues> = [
      'subgenre', 'featured_artists', 'moods', 'writers', 'producers', 'tags', 'themes',
      'instruments', 'contributors', 'song_versions', 'audio_file_versions', 'external_links',
      'collaborators', 'stream_sources', 'purchase_links', 'related_songs', 'gear_used',
      'sample_credits', 'ai_history'
    ];

    for (const key in defaultSongFormValues) {
      const k = key as keyof SongFormValues;
      if (initialSongData.hasOwnProperty(k) && typeof initialSongData[k] !== 'undefined') { // Check for undefined explicitly
        if (arrayFieldKeys.includes(k) && initialSongData[k] === null) {
          // If it's an array field and DB value is null, use the default empty array from defaultSongFormValues
          // @ts-ignore
          mergedValues[k] = defaultSongFormValues[k]; 
        } else if ((k === 'release_date' || k === 'recording_date' || k === 'last_played_at') && initialSongData[k]) {
          // @ts-ignore
          mergedValues[k] = new Date(initialSongData[k] as string | number | Date); // Ensure date conversion is robust
        } else {
          // For all other cases (including nulls for non-array fields, or actual values for array fields)
          // @ts-ignore
          mergedValues[k] = initialSongData[k];
        }
      } else {
        // If initialSongData doesn't have the key or it's undefined, use the default
        // @ts-ignore
        mergedValues[k] = defaultSongFormValues[k];
      }
    }
    // Ensure specific fields that might be null/undefined in DB but need defaults are set
    mergedValues.lyrics = initialSongData.lyrics ?? defaultSongFormValues.lyrics;
    mergedValues.is_public = initialSongData.is_public ?? defaultSongFormValues.is_public;
    mergedValues.is_favorite = initialSongData.is_favorite ?? defaultSongFormValues.is_favorite;
    mergedValues.is_incomplete = initialSongData.is_incomplete ?? defaultSongFormValues.is_incomplete;
    mergedValues.creator_user_id = initialSongData.creator_user_id || currentUserId;
    // Ensure ai_config and ai_history are properly sourced or defaulted
    mergedValues.ai_config = initialSongData.ai_config || defaultSongFormValues.ai_config;
    mergedValues.ai_history = initialSongData.ai_history || defaultSongFormValues.ai_history;

    return mergedValues as SongFormValues; // Cast as SongFormValues after merging
  }, [initialSongData, currentUserId]);

  useEffect(() => {
    const fetchUser = async () => {
      const { data: { user: fetchedUser } } = await supabase.auth.getUser();
      setUser(fetchedUser);
    };
    fetchUser();
  }, [supabase]);

  // Initialize React Hook Form
  const methods = useForm<SongFormValues>({
    resolver: zodResolver(songSchema),
    defaultValues: processedInitialValues,
    mode: 'onChange', // Validate on change for better UX
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty, isSubmitting: rhfIsSubmitting, dirtyFields },
    watch,
    setValue,
    getValues,
    reset: rhfReset,
    trigger
  } = methods;

  // AI Config and History state
  const [aiConfigState, setAiConfigState] = useState<AiConfig>(initialSongData?.ai_config || defaultSongFormValues.ai_config || { provider: 'openai', model: 'gpt-3.5-turbo', temperature: 0.7 });
  const [aiHistoryState, setAiHistoryState] = useState<AiHistoryItem[]>(initialSongData?.ai_history || defaultSongFormValues.ai_history || []);
  const [aiGeneralPrompt, setAiGeneralPrompt] = useState<string>('');
  const [showAiHistory, setShowAiHistory] = useState<boolean>(false);

  const addAiHistory = useCallback((userPrompt: string, assistantResponse: string) => {
    setAiHistoryState(prev => {
      const newHistory = [...prev, { role: 'user' as const, content: userPrompt, timestamp: new Date().toISOString() }, { role: 'assistant' as const, content: assistantResponse, timestamp: new Date().toISOString() }];
      setValue('ai_history', newHistory, { shouldDirty: true });
      return newHistory;
    });
  }, [setValue]); // Removed methods and aiHistoryState from dependencies, setValue is stable

  // State for UI and component logic
  const [activeTab, setActiveTab] = useState<FormSection>('main');
  const [lyricsContent, setLyricsContent] = useState<string>(initialSongData?.lyrics || '');
  const [localChords, setLocalChords] = useState<string | null>(initialSongData?.chords || null);
  const [isVaultPanelCollapsed, setIsVaultPanelCollapsed] = useState(true);

  // Define missing handlers
  const handleRHFSubmitWrapper = async (data: SongFormValues) => {
    console.log('[SongForm] handleRHFSubmitWrapper called (VALID SUBMISSION). Data:', data);
    // Prefer recorded audio if available, otherwise use uploaded audio file
    const audioToSubmit = recordedAudioBlob ? new File([recordedAudioBlob], localAudioFile.file?.name || 'recorded_audio.wav', { type: recordedAudioBlob.type }) : localAudioFile.file;
    
    // Prevent form submission if globally submitting (e.g., parent page is busy)
    if (isSubmittingGlobalFromProps) { 
      console.warn('[SongForm] Submission prevented: Parent component is already submitting.');
      toast.warning("Sauvegarde en cours...", { description: "Une opération de sauvegarde est déjà en cours." });
      return;
    }
    // Use the correctly destructured handleFormSubmit (which is onFormSubmit from props)
    await handleFormSubmit(data, localCoverArtFile.file, audioToSubmit);
  };

  const handleRHFSubmitError = (errors: FieldErrors<SongFormValues>) => {
    console.error('[SongForm] RHF Validation Errors:', errors);
    toast.error("Erreur de validation", { 
      description: "Veuillez corriger les erreurs dans le formulaire.",
      duration: 5000 
    });
    // Optionally, focus on the first field with an error
    const firstErrorField = Object.keys(errors)[0] as keyof SongFormValues;
    if (firstErrorField && methods.setFocus) { // methods is from useForm()
      try {
        methods.setFocus(firstErrorField);
      } catch (e) {
        console.warn(`[SongForm] Could not focus on field: ${firstErrorField}`, e);
      }
    }
  };

  // The custom handleClearAudioFile wrapper was removed as useLocalFileManagement provides handleClearAudio.

  const handleDelete = async (songId: string) => {
    if (onDelete) {
      // isProcessingSubmit is from useSongFormActions, no need to set manually here
      try {
        await onDelete(songId);
        toast.success('Chanson supprimée.');
        // Optionally, redirect or call onCancel
        if (onCancel) onCancel(); else router.push('/songs'); // Example redirect
      } catch (e: any) {
        toast.error('Erreur lors de la suppression: ' + e.message);
      }
    }
  };

  const [localLastSavedTimestamp, setLocalLastSavedTimestamp] = useState<Date | null>(lastSavedTimestampFromProps ? new Date(lastSavedTimestampFromProps) : null);
  const [localIsSubmittingGlobal, setLocalIsSubmittingGlobal] = useState<boolean>(isSubmittingGlobalFromProps || false); // For global submission state if needed outside RHF

  const handleLyricsChange = useCallback((content: string, editorData?: any) => {
    methods.setValue('lyrics', content, { shouldDirty: true, shouldValidate: true });
    if (editorData) {
      methods.setValue('editor_data', editorData, { shouldDirty: true });
    }
    // setLyricsContent(content); // Redundant if initialContent watches RHF state
  }, [methods]);

  // Audio Recording State
  const audioRecorderRef = useRef<AudioRecorderHandle>(null);

  // Local file management hook
  const {
    localAudioFile,
    setLocalAudioFile,
    // clearLocalAudioFile, // Removed, use handleClearAudio
    localCoverArtFile,
    setLocalCoverArtFile,
    // clearLocalCoverArtFile, // Removed, use handleClearCoverArt
    handleClearAudio,
    handleClearCoverArt,
    handleAudioFileSelect,
    handleCoverArtSelect,
  } = useLocalFileManagement({
    initialAudioUrl: initialSongData?.audio_url || null, // Corrected prop name
    initialCoverArtUrl: initialSongData?.cover_art_url || null, // Corrected prop name
    onCoverArtChange: (file, previewUrl) => {
      setValue('cover_art_url', previewUrl || '', { shouldDirty: !!file });
      if (file) setValue('cover_art_file_name', file.name, { shouldDirty: true });
    },
    onAudioFileChange: (file, previewUrl) => {
      setValue('audio_url', previewUrl || '', { shouldDirty: !!file });
      if (file) {
        setValue('audio_file_name', file.name, { shouldDirty: true });
        // If you have a way to get duration from the file, set it here
        // e.g., getAudioDuration(file).then(duration => setValue('duration_ms', duration));
      }
    },
  });

  const handleOpenAudioSettingsInForm = useCallback(() => {
    audioRecorderRef.current?.openSettingsModal();
  }, []);

  const handleAudioRecordingComplete = (audioBlob: Blob | null, previewUrl?: string | null) => {
    console.log('SongForm: Audio recording complete. Blob:', audioBlob, 'Preview URL:', previewUrl);
    setRecordedAudioBlob(audioBlob);
    setRecordedAudioPreviewUrl(previewUrl || null); // Ensure null if undefined
    if (audioBlob) {
      setValue('audio_file_name', 'recording.wav', { shouldDirty: true }); // Or a more dynamic name
      // setValue('audio_url', previewUrl || null, { shouldDirty: true }); // This will be set by setLocalAudioFile effect or direct RHF setValue if needed
      setLocalAudioFile({ file: audioBlob as File, previewUrl: previewUrl || null, uploadProgress: 0, error: null, isUploading: false });
    } else {
      // Potentially clear if blob is null and was previously set
      // This case might need more handling if a recording is cancelled after starting
    }
    if (audioBlob && previewUrl) {
      const recordedFile = new File([audioBlob], 'recorded_audio.wav', { type: audioBlob.type });
      setLocalAudioFile({ file: recordedFile, previewUrl: previewUrl || null, uploadProgress: 0, error: null, isUploading: false });
      methods.setValue('audio_url', previewUrl, { shouldDirty: true }); // Or handle as a file to be uploaded
      methods.setValue('audio_file_name', recordedFile.name, { shouldDirty: true });
      methods.setValue('duration_ms', 0); // Placeholder, actual duration might need to be extracted
      toast.success('Enregistrement terminé', { description: 'Le fichier audio enregistré est prêt.' });
    } else {
      // If blob is null, it means recording was cleared or cancelled from within AudioRecorder
      // Or if previewUrl is null (though less likely if blob is present)
      setLocalAudioFile({ file: null, previewUrl: null, uploadProgress: 0, error: null, isUploading: false });
      methods.setValue('audio_url', null, { shouldDirty: true });
      methods.setValue('audio_file_name', '', { shouldDirty: true });
      methods.setValue('duration_ms', 0);
      toast.info('Enregistrement annulé', { description: 'Aucun fichier audio enregistré.' });
    }
  };

  const handleRecordingError = useCallback((error: string) => { // Renamed to match usage in AudioRecorder prop
    console.error('SongForm: Audio recording error:', error);
    toast.error("Erreur d'enregistrement audio", { description: error });
  }, [toast]);

  const handleAudioRecordingActiveUpdate = useCallback((isActive: boolean) => {
    setIsAudioRecordingActive(isActive);
  }, []);

  const handleClearRecordedAudio = useCallback(() => {
    const wasRecordingTheActiveAudio = localAudioFile.previewUrl === recordedAudioPreviewUrl && recordedAudioPreviewUrl !== null;

    setRecordedAudioBlob(null);
    setRecordedAudioPreviewUrl(null);

    if (wasRecordingTheActiveAudio) {
      // If the recording was the active audio source,
      // use the main clear mechanism which also resets RHF fields for audio.
      handleClearAudio(); // This comes from useLocalFileManagement
    } else {
      // If the recording was not the active audio (e.g., an uploaded file is active),
      // we've cleared the recording-specific state. No further RHF changes needed for audio_url etc.
      // as they pertain to the uploaded file.
    }
    toast.info('Enregistrement audio retiré.');
  }, [localAudioFile.previewUrl, recordedAudioPreviewUrl, handleClearAudio, setRecordedAudioBlob, setRecordedAudioPreviewUrl, toast]);

  // Wrapper for SongVault's onDeleteVersion to trigger confirmation modal
  const handleDeleteVersionWrapper = async (versionId: string): Promise<void> => {
    const version = currentSongVersions.find(v => v.version_id === versionId);
    if (version) {
      setVersionToDelete(version);
      setIsConfirmDeleteVersionModalOpen(true);
    } else {
      toast.error("Version non trouvée pour la suppression.");
    }
  };

  // Destructure preview URLs for easier use in JSX, if preferred
  const coverArtPreviewUrl = localCoverArtFile?.previewUrl;
  const audioPreviewUrl = localAudioFile?.previewUrl;

  // Wrapper handlers for file input components that expect (event: React.ChangeEvent<HTMLInputElement>) => void
  // const clearCoverArtFile was removed as coverArtFileManagement is not defined. Use handleClearCoverArt from useLocalFileManagement directly.
  const coverArtSelectHandler = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;
    handleCoverArtSelect(file); // handleCoverArtSelect is from useLocalFileManagement
    // Clear the input's value to allow selecting the same file again
    if (event.target) {
      event.target.value = '';
    }
  }, [handleCoverArtSelect]);
  const audioFileSelectHandler = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;
    handleAudioFileSelect(file);
    // Clear the input's value to allow selecting the same file again
    if (event.target) {
      event.target.value = '';
    }
  }, [handleAudioFileSelect]);

  // Form Actions Hook
  const {
    handleFormSubmit,
    handleFormError,
    isProcessingSubmit, // Destructure isProcessingSubmit from the hook
  } = useSongFormActions({
    onFormSubmit: onFormSubmit // Pass the main onFormSubmit prop to the hook
    // initialIsSubmitting can be added here if needed, e.g., initialIsSubmitting: isSubmittingGlobal
  });

  // Hook for song versioning management
  const {
    currentSongVersions,
    setCurrentSongVersions,
    newVersionName,
    setNewVersionName,
    newVersionNotes,
    setNewVersionNotes,
    isSaveVersionModalOpen,
    setIsSaveVersionModalOpen,
    isConfirmDeleteVersionModalOpen,
    setIsConfirmDeleteVersionModalOpen,
    versionToDelete,
    setVersionToDelete,
    isSubmittingVersion,
    isLoadingDeleteVersion,
    isLoadingUpdateVersion, // Added
    songVaultActionsRef,
    handleSaveNewVersion,
    handleLoadVersion,
    handleDeleteVersion,
    handleUpdateVersionDetails, // Added
    activeVersionId
  } = useSongVersioning({
    songId: initialSongData?.id,
    initialVersionId: null, 
    dbSongData: initialSongData?.id ? initialSongData as Song : undefined, // Pass only if ID exists and cast to Song
    setValue,
    reset: rhfReset, // Corrected: use the aliased rhfReset
    getValues,
    supabaseClient,
    user,
    router,
    setCurrentTab: setActiveTab, 
    toast, // Pass toast here
    onVersionLoad: (versionData: Partial<SongFormValues>, versionId: string) => {
      isVersionJustLoadedRef.current = true;
      console.log('[SongForm] onVersionLoad triggered for version:', versionId, versionData);
      // Update UI elements based on loaded version data
      if (versionData.audio_url) {
        setUploadedAudioUrl(versionData.audio_url);
        setRecordedAudioPreviewUrl(null); // Clear recorded preview if uploaded exists
        handleAudioFileSelect(null, versionData.audio_url); 
      } else {
        setUploadedAudioUrl(null);
        handleClearAudio(initialSongData?.audio_url || null); 
      }

      if (versionData.cover_art_url) {
        handleCoverArtSelect(null, versionData.cover_art_url); 
      } else {
        handleClearCoverArt(initialSongData?.cover_art_url || null); 
      }
    }
  });

  // Unified form reset logic
  const resetForm = useCallback((newValues?: Partial<SongFormValues & { cover_art_url?: string | null; audio_url?: string | null }>) => {
    // Determine the base values for RHF's reset
    // If newValues are provided (e.g. from version load), they take precedence for fields they define.
    // Otherwise, we use processedInitialValues (derived from initialSongData).
    const rhfResetValues = newValues 
      ? { ...processedInitialValues, ...newValues } // Overlay newValues on defaults for RHF
      : processedInitialValues;
    methods.reset(rhfResetValues);

    setLyricsContent((newValues?.lyrics ?? processedInitialValues.lyrics) || ''); // Corrected setter and operator precedence
    setLocalChords((newValues?.chords ?? processedInitialValues.chords) || ''); // Corrected operator precedence

    // Determine URLs: if newValues (e.g. version) provides them, use them. Else, use initialSongData's.
    const coverUrlToSet = (newValues && typeof newValues.cover_art_url !== 'undefined') ? newValues.cover_art_url : initialSongData?.cover_art_url;
    const audioUrlToSet = (newValues && typeof newValues.audio_url !== 'undefined') ? newValues.audio_url : initialSongData?.audio_url;

    setLocalCoverArtFile(prev => ({ ...prev, file: null, previewUrl: coverUrlToSet || null }));
    setLocalAudioFile(prev => ({ ...prev, file: null, previewUrl: audioUrlToSet || null }));
    setUploadedAudioUrl(audioUrlToSet || null); // State to track the primary audio URL for display/submission if not recording
    
    // Always clear recorded audio when resetting form (either to initial or to a loaded version)
    // This simplifies state management; user can re-record if needed.
    setRecordedAudioBlob(null);
    setRecordedAudioPreviewUrl(null);

    // Clear file inputs visually
    if (audioInputRef.current) audioInputRef.current.value = '';
    if (coverArtInputRef.current) coverArtInputRef.current.value = '';
    
    isVersionJustLoadedRef.current = !!newValues; // Mark if reset was due to loading new values (e.g. version load)

  }, [
    methods, processedInitialValues, initialSongData, 
    setLyricsContent, setLocalChords, // Corrected setLyricsContent dependency
    setLocalCoverArtFile, setLocalAudioFile, setUploadedAudioUrl,
    setRecordedAudioBlob, setRecordedAudioPreviewUrl
    // Refs (audioInputRef, coverArtInputRef, isVersionJustLoadedRef) are stable and don't need to be in deps
    // songVersions, setCurrentVersionIndex are for versioning UI, not core reset logic here
  ]);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    submit: handleSubmit(handleRHFSubmitWrapper),
    resetForm: resetForm,
    getFormValues: methods.getValues,
    setFieldValue: (name, value) => methods.setValue(name, value, { shouldDirty: true, shouldValidate: true }),
  }));

  // DEBUG: Zod parsing on watched values (commented out as the main issue seems resolved)
  // const watchedValues = methods.watch();
  // useEffect(() => {
  //   // console.log("[SongForm DEBUG] Watched values for Zod parse attempt:", watchedValues);
  //   try {
  //     songSchema.parse(watchedValues);
  //     // console.log("[SongForm DEBUG] Zod parse successful with current watched values.");
  //   } catch (e) {
  //     console.error("[SongForm DEBUG] Zod parse FAILED with current watched values:", e, "\nValues that failed parsing:", watchedValues);
  //   }
  // }, [watchedValues, methods]);

  if (!user && mode === 'create') { // Stricter check: if not logged in AND creating a new song.
    return <div className="p-4">Veuillez vous connecter pour créer un nouveau morceau. Si vous modifiez un morceau existant, le contenu apparaîtra une fois l'utilisateur chargé.</div>;
  }
  if (mode === 'edit' && !initialSongData && !user) {
    return <div className="p-4">Chargement des données du morceau et de l'utilisateur...</div>; // Loading state for edit mode
  }
  // It's possible to view/edit a song if initialSongData is present, even if user is briefly null during auth check
  // However, critical actions like submit/delete are protected by user checks within their handlers or hooks.
  // If initialSongData exists, we can proceed to render the form, user will populate shortly for edit actions.
  // if (mode === 'edit' && initialSongData && !user) {
  //   // This case might mean the song is public but user is not logged in. Decide if viewable or auth wall.
  //   // For now, let's assume editing requires login for actions, but viewing form is ok.
  //   // return <div className="p-4">Veuillez vous connecter pour modifier ce morceau.</div>;
  // }

  // Define watched values and display URLs after early returns
  const watchedTitle = watch('title') || (initialSongData?.title ?? 'Nouveau morceau');
  const watchedArtist = watch('artist') || (initialSongData?.artist ?? '');
  const watchedLyrics = watch('lyrics') || (initialSongData?.lyrics ?? '');

  // Determine the actual cover art URL to display
  const displayCoverArtUrl = localCoverArtFile.previewUrl || initialSongData?.cover_art_url || null;
  // Determine actual audio URL to display (can be from initial data, local file, or recorded blob)
  const displayAudioUrl = recordedAudioPreviewUrl || localAudioFile.previewUrl || initialSongData?.audio_url || null;
  
  const lastSavedDate = lastSavedTimestampFromProps ? new Date(lastSavedTimestampFromProps) : null;

  return (
    <RHFFormProvider {...methods}>
      <form onSubmit={handleSubmit(handleRHFSubmitWrapper, handleRHFSubmitError)} className="space-y-6">
        <SongFormHeader
          mode={mode} // Pass the mode from SongFormProps
          songTitle={watchedTitle} // Changed from title
          artistName={watchedArtist} // Changed from artist
          coverArtUrl={displayCoverArtUrl}
          localCoverArtFile={localCoverArtFile} // Pass localCoverArtFile for consistency
          onCoverArtSelect={handleCoverArtSelect} // Pass cover art handlers
          onClearCoverArt={handleClearCoverArt}
          coverArtInputRef={coverArtInputRef}
          lastSavedTimestamp={lastSavedDate} // Kept as Date | null
          isVaultPanelCollapsed={isVaultPanelCollapsed}
          onToggleVaultPanel={() => setIsVaultPanelCollapsed(!isVaultPanelCollapsed)} // Changed from setIsVaultPanelCollapsed
          // Audio Props for Header
          setValue={setValue} // from useForm methods
          localAudioFile={localAudioFile}
          handleAudioFileSelect={handleAudioFileSelect}
          handleClearAudio={handleClearAudio}
          recordedAudioBlob={recordedAudioBlob}
          recordedAudioPreviewUrl={recordedAudioPreviewUrl}
          onRecordingComplete={handleAudioRecordingComplete} // Renamed in SongForm
          onRecordingError={handleRecordingError} // Renamed in SongForm
          handleClearRecordedAudio={handleClearRecordedAudio}
          isSubmitting={methods.formState.isSubmitting || localIsSubmittingGlobal}
          isDirty={methods.formState.isDirty}
          onSave={handleSubmit(handleRHFSubmitWrapper)}
          onViewHistory={mode === 'edit' ? () => {
            if (isVaultPanelCollapsed) {
              setIsVaultPanelCollapsed(false); // Open the vault panel
            }
            // Future enhancement: could also scroll the vault panel into view if needed
          } : undefined}
        />
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Left Panel */}
          <div className="flex-grow">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as FormSection)} className="w-full">
              <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 gap-2 mb-4">
                <TabsTrigger value="main">Infos Générales</TabsTrigger>
                <TabsTrigger value="production-details">Production & Détails</TabsTrigger>
                <TabsTrigger value="lyrics-chords">Paroles & Accords</TabsTrigger>
                <TabsTrigger value="structure">Structure</TabsTrigger>
                <TabsTrigger value="progression-ai">Progression & IA</TabsTrigger>
              </TabsList>

              <TabsContent value="main">
                <SongFormGeneralInfoTab {...{
                  control: control, 
                  errors: errors, 
                  setValue: setValue,
                  getValues: getValues,
                  watch: watch,
                  albumsData: albumsData || [], 
                  isLoadingAlbums: isLoadingAlbums || false,
                  currentUserId: currentUserId,
                  mode: mode,
                  songTitle: watchedTitle,
                  coverArtUrl: displayCoverArtUrl, // Use the calculated displayCoverArtUrl
                  localCoverArtFile: localCoverArtFile,
                  onCoverArtSelect: handleCoverArtSelect, // Use from useLocalFileManagement
                  onClearCoverArt: handleClearCoverArt, // Use from useLocalFileManagement
                  coverArtInputRef: coverArtInputRef,
                  audioUrl: localAudioFile.previewUrl || watch('audio_url') || recordedAudioPreviewUrl, // Consistent audio URL
                  localAudioFile: localAudioFile,
                  onAudioFileSelect: handleAudioFileSelect, // Use from useLocalFileManagement
                  onClearAudio: handleClearAudio // Use the correct handler from useLocalFileManagement
                }} />
              </TabsContent>

              <TabsContent value="lyrics-chords" className="mt-6">
                <SongFormLyricsChordTab {...{
                  control: control, 
                  errors: errors,
                  lyricsContent: watchedLyrics,
                  onLyricsChange: handleLyricsChange,
                  quillRef: editorInstanceForAi, 
                  aiConfig: aiConfigState,
                  aiGeneralPrompt: aiGeneralPrompt,
                  addAiHistory: addAiHistory,
                  aiHistory: aiHistoryState,
                  showAiHistory: showAiHistory,
                  setShowAiHistory: setShowAiHistory,
                  formControl: control 
                }} />
              </TabsContent>

              <TabsContent value="production-details" className="mt-6">
                <SongFormProductionDetailsTab control={control} errors={errors} />
              </TabsContent>

              <TabsContent value="structure" className="mt-6">
                <SongFormStructureTab control={control} errors={methods.formState.errors} />
              </TabsContent>

              <TabsContent value="progression-ai" className="mt-6">
                <SongFormProgressionAiTab control={control} errors={methods.formState.errors} />
              </TabsContent>
            </Tabs>
          </div> {/* End Left Panel */} 

          {/* Right Panel: Song Vault - Conditionally rendered */} 
          {!isVaultPanelCollapsed && (
            <div className="lg:w-1/3 lg:max-w-md xl:max-w-lg flex-shrink-0">
              <SongVault
                songId={initialSongData?.id ?? ''}
                activeVersionId={activeVersionId} 
                versions={currentSongVersions} 
                onLoadVersion={handleLoadVersion} 
                onDeleteVersion={handleDeleteVersionWrapper} 
                onSaveNewVersion={handleSaveNewVersion} 
                isLoadingSaveVersion={isSubmittingVersion} 
                isLoadingDeleteVersion={isLoadingDeleteVersion}
                onUpdateVersion={handleUpdateVersionDetails} 
                isLoadingUpdateVersion={isLoadingUpdateVersion} 
              />
            </div>
          )}
        </div> {/* End flex flex-col lg:flex-row gap-6 */} 

        {/* Hidden submit button for react-hook-form if needed, or rely on header buttons */} 
        {/* <Button type="submit" className="hidden">Submit</Button> */} 
      </form>
    </RHFFormProvider>
  );
}); // This closes the forwardRef render function

SongForm.displayName = 'SongForm';

export default SongForm;
