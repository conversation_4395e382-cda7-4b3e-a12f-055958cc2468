'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Guitar, Piano, Plus, X, Play, Volume2, Music,
  Hash, Target, Layers, RotateCcw, Wand2, Copy
} from 'lucide-react';

interface ChordSystemMegaProps {
  selectedSection: string;
  sections: any[];
  setSections: (sections: any[]) => void;
  styleConfig: any;
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
}

interface Chord {
  id: string;
  name: string;
  position: number;
  instrument: string;
  fingering?: string[];
  notes?: string[];
}

// Bibliothèque d'accords de base
const CHORD_LIBRARY = {
  guitar: {
    'C': { notes: ['C', 'E', 'G'], fingering: ['0', '1', '0', '2', '3', '0'] },
    'G': { notes: ['G', 'B', 'D'], fingering: ['3', '2', '0', '0', '3', '3'] },
    'Am': { notes: ['A', 'C', 'E'], fingering: ['0', '0', '2', '2', '1', '0'] },
    'F': { notes: ['F', 'A', 'C'], fingering: ['1', '1', '3', '3', '2', '1'] },
    'Dm': { notes: ['D', 'F', 'A'], fingering: ['x', 'x', '0', '2', '3', '1'] },
    'Em': { notes: ['E', 'G', 'B'], fingering: ['0', '2', '2', '0', '0', '0'] },
    'D': { notes: ['D', 'F#', 'A'], fingering: ['x', 'x', '0', '2', '3', '2'] },
    'A': { notes: ['A', 'C#', 'E'], fingering: ['0', '0', '2', '2', '2', '0'] },
    'E': { notes: ['E', 'G#', 'B'], fingering: ['0', '2', '2', '1', '0', '0'] },
    'Bm': { notes: ['B', 'D', 'F#'], fingering: ['x', '2', '4', '4', '3', '2'] }
  },
  piano: {
    'C': { notes: ['C', 'E', 'G'] },
    'G': { notes: ['G', 'B', 'D'] },
    'Am': { notes: ['A', 'C', 'E'] },
    'F': { notes: ['F', 'A', 'C'] },
    'Dm': { notes: ['D', 'F', 'A'] },
    'Em': { notes: ['E', 'G', 'B'] },
    'D': { notes: ['D', 'F#', 'A'] },
    'A': { notes: ['A', 'C#', 'E'] },
    'E': { notes: ['E', 'G#', 'B'] },
    'Bm': { notes: ['B', 'D', 'F#'] }
  }
};

// Progressions d'accords populaires
const CHORD_PROGRESSIONS = {
  'I-V-vi-IV': ['C', 'G', 'Am', 'F'],
  'vi-IV-I-V': ['Am', 'F', 'C', 'G'],
  'I-vi-IV-V': ['C', 'Am', 'F', 'G'],
  'ii-V-I': ['Dm', 'G', 'C'],
  'I-IV-V': ['C', 'F', 'G'],
  'vi-V-IV-V': ['Am', 'G', 'F', 'G']
};

export const ChordSystemMega: React.FC<ChordSystemMegaProps> = ({
  selectedSection,
  sections,
  setSections,
  styleConfig,
  onAIGenerate
}) => {
  
  const [selectedInstrument, setSelectedInstrument] = useState<'guitar' | 'piano'>('guitar');
  const [searchChord, setSearchChord] = useState('');
  const [selectedChord, setSelectedChord] = useState<string | null>(null);

  const currentSection = sections.find(s => s.id === selectedSection);
  const sectionChords = currentSection?.chords || [];

  // Filtrer les accords selon la recherche
  const filteredChords = Object.keys(CHORD_LIBRARY[selectedInstrument]).filter(chord =>
    chord.toLowerCase().includes(searchChord.toLowerCase())
  );

  // Ajouter un accord à la section
  const handleAddChord = useCallback((chordName: string, position?: number) => {
    if (!currentSection) return;

    const newChord: Chord = {
      id: `chord-${Date.now()}`,
      name: chordName,
      position: position || sectionChords.length,
      instrument: selectedInstrument,
      fingering: CHORD_LIBRARY[selectedInstrument][chordName]?.fingering,
      notes: CHORD_LIBRARY[selectedInstrument][chordName]?.notes
    };

    const updatedSections = sections.map(section =>
      section.id === selectedSection
        ? { ...section, chords: [...sectionChords, newChord] }
        : section
    );

    setSections(updatedSections);
  }, [currentSection, selectedSection, sections, setSections, sectionChords, selectedInstrument]);

  // Supprimer un accord
  const handleRemoveChord = useCallback((chordId: string) => {
    const updatedSections = sections.map(section =>
      section.id === selectedSection
        ? { ...section, chords: sectionChords.filter(c => c.id !== chordId) }
        : section
    );

    setSections(updatedSections);
  }, [selectedSection, sections, setSections, sectionChords]);

  // Ajouter une progression d'accords
  const handleAddProgression = useCallback((progressionName: string) => {
    const chords = CHORD_PROGRESSIONS[progressionName];
    chords.forEach((chordName, index) => {
      setTimeout(() => handleAddChord(chordName, sectionChords.length + index), index * 100);
    });
  }, [handleAddChord, sectionChords.length]);

  // Actions IA pour les accords
  const handleChordAI = useCallback(async (action: string) => {
    const sectionType = currentSection?.type || 'verse';
    const key = styleConfig.key || 'C';
    const genre = styleConfig.genres?.[0] || 'pop';
    
    let prompt = '';
    
    switch (action) {
      case 'suggest':
        prompt = `Suggère une progression d'accords pour un ${sectionType} en ${key} majeur dans le style ${genre}. Donne 4-6 accords avec leur fonction harmonique.`;
        break;
      case 'improve':
        prompt = `Améliore cette progression d'accords : ${sectionChords.map(c => c.name).join(' - ')}. Suggère des variantes plus intéressantes en ${key} majeur.`;
        break;
      case 'transpose':
        prompt = `Transpose cette progression d'accords : ${sectionChords.map(c => c.name).join(' - ')} vers la tonalité de ${key} majeur.`;
        break;
      case 'analyze':
        prompt = `Analyse cette progression d'accords : ${sectionChords.map(c => c.name).join(' - ')}. Explique les fonctions harmoniques et l'effet émotionnel.`;
        break;
    }
    
    await onAIGenerate(prompt, 'chords');
  }, [currentSection, styleConfig, sectionChords, onAIGenerate]);

  return (
    <div className="h-full flex flex-col bg-slate-900/30">
      {/* En-tête */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Guitar className="h-5 w-5 text-blue-400" />
            <div>
              <h3 className="font-medium text-white">Système d'Accords</h3>
              <p className="text-sm text-slate-400">{currentSection?.title || 'Aucune section'}</p>
            </div>
          </div>
          
          {/* Sélecteur d'instrument */}
          <div className="flex items-center gap-2">
            <Button
              variant={selectedInstrument === 'guitar' ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedInstrument('guitar')}
              className="gap-1"
            >
              <Guitar className="h-4 w-4" />
              Guitare
            </Button>
            <Button
              variant={selectedInstrument === 'piano' ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedInstrument('piano')}
              className="gap-1"
            >
              <Piano className="h-4 w-4" />
              Piano
            </Button>
          </div>
        </div>
      </div>

      {/* Accords de la section */}
      <div className="border-b border-slate-700 bg-slate-800/30 p-3">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-sm font-medium text-white">Accords de la section</h4>
          <Badge variant="outline" className="text-xs">
            {sectionChords.length} accords
          </Badge>
        </div>
        
        {sectionChords.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {sectionChords.map((chord) => (
              <div key={chord.id} className="flex items-center gap-1 bg-slate-700 rounded-lg p-2">
                <span className="text-white font-medium">{chord.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveChord(chord.id)}
                  className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-slate-400 py-4">
            <Music className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Aucun accord dans cette section</p>
          </div>
        )}
      </div>

      {/* Actions IA pour accords */}
      <div className="border-b border-slate-700 bg-slate-800/20 p-3">
        <div className="flex items-center gap-2">
          <span className="text-sm text-slate-400 mr-2">IA Accords:</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleChordAI('suggest')}
            className="gap-1 bg-blue-500 text-white border-slate-600"
          >
            <Wand2 className="h-3 w-3" />
            Suggérer
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleChordAI('improve')}
            className="gap-1 bg-green-500 text-white border-slate-600"
            disabled={sectionChords.length === 0}
          >
            <Target className="h-3 w-3" />
            Améliorer
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleChordAI('analyze')}
            className="gap-1 bg-purple-500 text-white border-slate-600"
            disabled={sectionChords.length === 0}
          >
            <Hash className="h-3 w-3" />
            Analyser
          </Button>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3 space-y-4">
            
            {/* Recherche d'accords */}
            <div>
              <h4 className="text-sm font-medium text-white mb-2">Bibliothèque d'accords</h4>
              <Input
                value={searchChord}
                onChange={(e) => setSearchChord(e.target.value)}
                placeholder="Rechercher un accord..."
                className="bg-slate-800 border-slate-600 text-white"
              />
            </div>

            {/* Liste des accords */}
            <div className="grid grid-cols-2 gap-2">
              {filteredChords.map((chordName) => {
                const chordData = CHORD_LIBRARY[selectedInstrument][chordName];
                return (
                  <Card 
                    key={chordName}
                    className={`cursor-pointer transition-all bg-slate-700/50 border-slate-600 hover:border-slate-500 ${
                      selectedChord === chordName ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setSelectedChord(selectedChord === chordName ? null : chordName)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-white">{chordName}</div>
                          <div className="text-xs text-slate-400">
                            {chordData.notes.join(' - ')}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAddChord(chordName);
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      {/* Diagramme guitare */}
                      {selectedInstrument === 'guitar' && selectedChord === chordName && chordData.fingering && (
                        <div className="mt-2 pt-2 border-t border-slate-600">
                          <div className="text-xs text-slate-400 mb-1">Doigtés:</div>
                          <div className="flex gap-1">
                            {chordData.fingering.map((fret, index) => (
                              <div key={index} className="text-center">
                                <div className="w-6 h-6 bg-slate-600 rounded text-xs flex items-center justify-center text-white">
                                  {fret}
                                </div>
                                <div className="text-xs text-slate-500 mt-1">{index + 1}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Progressions populaires */}
            <div>
              <h4 className="text-sm font-medium text-white mb-2">Progressions populaires</h4>
              <div className="space-y-2">
                {Object.entries(CHORD_PROGRESSIONS).map(([name, chords]) => (
                  <Card key={name} className="bg-slate-700/50 border-slate-600">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-white text-sm">{name}</div>
                          <div className="text-xs text-slate-400">{chords.join(' - ')}</div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddProgression(name)}
                          className="gap-1"
                        >
                          <Plus className="h-3 w-3" />
                          Ajouter
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};
