-- Vérifier la structure de la table follows
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'follows';

-- Vérifier la structure de la table likes
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'likes';

-- Vérifier la structure de la table song_likes
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'song_likes';

-- Vérifier la structure de la table comments
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'comments';

-- Vérifier la structure de la table song_comments
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'song_comments';

-- Vérifier la structure de la table followers
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'followers';

-- Vérifier la structure de la table song_plays
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'song_plays';
