import React from 'react';
import SongVault, { SongVaultHandle } from '@/components/songs/SongVault'; // Corrected import
import { ScrollArea } from '@/components/ui/scroll-area';
import { type LocalSongVersion } from './hooks/useSongVersioning'; // Corrected import path to actual definition

interface SongVaultDisplayProps {
  songId: string;
  currentVersionId: string | null;
  versions: LocalSongVersion[];
  onLoadVersion: (versionId: string) => Promise<void>;
  onDeleteVersion: (versionId: string) => Promise<void>;
  onSaveNewVersion: (name: string, notes: string, isMajor: boolean) => Promise<void>;
  isLoadingSaveVersion: boolean;
  isLoadingDeleteVersion: boolean;
  onUpdateVersion: (versionId: string, newName: string, newNotes: string) => Promise<void>;
  isLoadingUpdateVersion: boolean;
  songVaultActionsRef?: React.Ref<SongVaultHandle>;
}

export const SongVaultDisplay: React.FC<SongVaultDisplayProps> = ({
  songId,
  currentVersionId,
  versions,
  onLoadVersion,
  onDeleteVersion,
  onSaveNewVersion,
  isLoadingSaveVersion,
  isLoadingDeleteVersion,
  onUpdateVersion,
  isLoadingUpdateVersion,
  songVaultActionsRef,
}) => {
  if (!songId) {
    return <div>ID de la chanson non fourni. Le coffre-fort ne peut pas être affiché.</div>;
  }

  return (
    <div className="p-1 border rounded-md">
      <ScrollArea className="flex-grow" style={{ maxHeight: '300px', minHeight: '200px' }}>
        <div className="p-3">
          {typeof SongVault === 'function' ? (
            <SongVault
              songId={songId}
              activeVersionId={currentVersionId}
              versions={versions}
              onLoadVersion={onLoadVersion}
              onDeleteVersion={onDeleteVersion}
              onSaveNewVersion={onSaveNewVersion}
              isLoadingSaveVersion={isLoadingSaveVersion}
              isLoadingDeleteVersion={isLoadingDeleteVersion}
              onUpdateVersion={onUpdateVersion}
              isLoadingUpdateVersion={isLoadingUpdateVersion}
              ref={songVaultActionsRef}
            />
          ) : (
            <div>Chargement du SongVault... (ou import/définition manquant)</div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};