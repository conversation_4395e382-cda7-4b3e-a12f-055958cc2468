-- Migration pour renommer la table tracks en songs
-- <PERSON><PERSON><PERSON> le: 2024-05-16
-- Auteur: <PERSON><PERSON><PERSON>

-- D<PERSON><PERSON> de la transaction
BEGIN;

-- 1. Vérifier si la table tracks existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tracks') THEN
        RAISE EXCEPTION 'La table tracks n''existe pas. Arrêt de la migration.';
    END IF;
END
$$;

-- 2. Créer la table songs avec la même structure que tracks
CREATE TABLE IF NOT EXISTS public.songs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL,
    title TEXT NOT NULL,
    audio_url TEXT,
    waveform_data DOUBLE PRECISION[] DEFAULT '{}',
    duration DOUBLE PRECISION DEFAULT 0,
    bpm INTEGER,
    key TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    status TEXT DEFAULT 'draft',
    metadata JSONB DEFAULT '{}'::jsonb,
    -- Ajou<PERSON> d'autres colonnes nécessaires
    
    -- Contraintes
    CONSTRAINT fk_project FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE
);

-- 3. Copier les données de tracks vers songs
INSERT INTO public.songs (
    id, project_id, title, audio_url, waveform_data, 
    duration, bpm, key, created_at, updated_at, status, metadata
)
SELECT 
    id, project_id, title, audio_url, waveform_data, 
    duration, bpm::integer, key, created_at, updated_at, 
    COALESCE(status, 'draft'), 
    jsonb_build_object(
        'original_table', 'tracks',
        'migrated_at', NOW()::text,
        'original_data', to_jsonb(t) - 'id' - 'project_id' - 'title' - 'audio_url' - 'waveform_data' - 'duration' - 'bpm' - 'key' - 'created_at' - 'updated_at' - 'status'
    )
FROM public.tracks t;

-- 4. Mettre à jour les séquences si nécessaire
PERFORM setval('songs_id_seq', (SELECT MAX(EXTRACT(epoch FROM created_at)) FROM public.songs));

-- 5. Créer les index nécessaires
CREATE INDEX IF NOT EXISTS idx_songs_project_id ON public.songs(project_id);
CREATE INDEX IF NOT EXISTS idx_songs_created_at ON public.songs(created_at);

-- 6. Mettre à jour les vues et fonctions existantes
DO $$
BEGIN
    -- Vérifier et mettre à jour les vues existantes
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_schema = 'public' AND table_name = 'project_tracks_view') THEN
        DROP VIEW public.project_tracks_view;
        CREATE VIEW public.project_songs_view AS 
        SELECT s.*, p.name as project_name
        FROM public.songs s
        JOIN public.projects p ON s.project_id = p.id;
    END IF;
END
$$;

-- 7. Vérifier l'intégrité des données
DO $$
DECLARE
    tracks_count BIGINT;
    songs_count BIGINT;
BEGIN
    SELECT COUNT(*) INTO tracks_count FROM public.tracks;
    SELECT COUNT(*) INTO songs_count FROM public.songs;
    
    IF tracks_count != songs_count THEN
        RAISE WARNING 'Le nombre d''enregistrements ne correspond pas: tracks=%, songs=%', 
                      tracks_count, songs_count;
    END IF;
END
$$;

-- 8. Créer une vue de compatibilité pour une transition en douceur
CREATE OR REPLACE VIEW public.tracks AS
SELECT * FROM public.songs;

-- Fin de la transaction
-- COMMIT; -- Ne pas décommenter avant de tester

-- Pour annuler en cas de problème:
-- ROLLBACK;

-- Instructions post-migration:
-- 1. Tester soigneusement l'application avec la nouvelle table songs
-- 2. Une fois validé, supprimer la vue de compatibilité:
--    DROP VIEW IF EXISTS public.tracks;
-- 3. Supprimer l'ancienne table (à faire uniquement après validation complète):
--    DROP TABLE IF EXISTS public.tracks;
