import Link from "next/link"
import { ChevronRight, Music } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface BandProjectsProps {
  projects: any[]
  bandId: string
  showAll?: boolean
}

export function BandProjects({ projects, bandId, showAll = false }: BandProjectsProps) {
  const displayProjects = showAll ? projects : projects.slice(0, 2)

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>Projets actifs</CardTitle>
          <CardDescription>Projets en cours de développement</CardDescription>
        </div>
        {!showAll && projects.length > 2 && (
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/bands/${bandId}?tab=projects`}>
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {displayProjects.length > 0 ? (
          <div className="space-y-4">
            {displayProjects.map((project) => (
              <div key={project.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge variant={project.type === "album" ? "default" : "secondary"}>
                      {project.type === "album" ? "Album" : "Single"}
                    </Badge>
                    <Badge variant="outline">{project.status}</Badge>
                  </div>
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/bands/${bandId}/projects/${project.id}`}>Ouvrir</Link>
                  </Button>
                </div>

                <h3 className="text-lg font-medium">{project.title}</h3>
                {project.type === "album" && (
                  <p className="text-sm text-muted-foreground mb-2">{project.track_count || 0} morceaux</p>
                )}

                <div className="mt-3">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progression totale</span>
                    <span>{project.progress || 0}%</span>
                  </div>
                  <Progress value={project.progress || 0} className="h-2" />
                </div>

                <div className="mt-4 flex items-center justify-between">
                  <div className="flex -space-x-2">
                    {[1, 2, 3].map((i) => (
                      <Avatar key={i} className="h-6 w-6 border-2 border-background">
                        <AvatarImage src={`/placeholder.svg?height=24&width=24&query=person ${i}`} />
                        <AvatarFallback>U{i}</AvatarFallback>
                      </Avatar>
                    ))}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Modifié: {new Date(project.updated_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Music className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">Aucun projet actif</h3>
            <p className="mt-2 text-muted-foreground">Créez un nouveau projet pour commencer à travailler</p>
            <Button className="mt-4">Créer un projet</Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
