# Technical Debt and To-Do List

This document tracks known issues, incomplete features, and technical debt that need to be addressed.

## Edge Functions

### 1. `record-view` Edge Function for View Counting
-   **Purpose**: To securely increment `view_count` for resources like playlists, songs, albums when they are viewed.
-   **Current Code**: Located in `supabase/functions/record-view/index.ts`. The code includes CORS handling and attempts to update `view_count` (and previously `last_viewed_at`, which was removed as the column didn't exist).
-   **Status**: **NOT DEPLOYED / DEPLOYMENT ISSUES.** (Ref: `TASKS_PROGRESS.md` - "Correction du comptage des vues" - EN COURS)
-   **Problem**: User is encountering a persistent "Le chemin d’accès spécifié est introuvable" (The specified path was not found) error when trying to deploy the function using `npx supabase functions deploy record-view --no-verify-jwt`, even after:
    -   Ensuring Docker Desktop is running.
    -   Successfully logging in and linking the project via Supabase CLI (`npx supabase login`, `npx supabase link`).
    -   The CLI previously showed a successful deployment message once, but subsequent attempts and log fetching commands failed with path or unknown flag errors, suggesting CLI version or environment issues.
-   **Last Known Error (Browser, when function call was attempted)**: HTTP 500 from the function, with Supabase dashboard logs indicating an internal error: `message: "Could not find the 'last_viewed_at' column of 'playlists' in the schema cache"`. This specific error in the function code was fixed by removing the `last_viewed_at` update. However, the deployment issue prevents testing the fix.
-   **Troubleshooting Steps Taken/Suggested**:
    1.  Verified Docker Desktop is running.
    2.  Ensured Supabase CLI is installed (via `npm install supabase --save-dev` and using `npx`).
    3.  Attempted Supabase CLI update (`npm install supabase@latest --save-dev`).
    4.  Checked Supabase Dashboard for function status (user confirmed it was listed after one successful deploy).
    5.  Attempted to view logs via CLI (`npx supabase functions logs record-view`) - failed due to outdated CLI, then path errors after presumed update.
    6.  Attempted to view logs via Supabase Dashboard - user successfully retrieved logs showing the `last_viewed_at` error.
    7.  Corrected Edge Function code to remove `last_viewed_at` update.
-   **Next Steps to Resolve Deployment**:
    1.  Thoroughly verify Supabase CLI installation and version. Consider a clean global install using Scoop (for Windows) or Homebrew (macOS/Linux) if `npx` continues to be problematic.
    2.  Ensure the terminal environment is correctly set up (PATH, Node version).
    3.  Try deploying via Supabase Dashboard by pasting the code, if available.
    4.  Seek support on Supabase forums/Discord regarding the CLI path errors.
    5.  Once deployed, verify project-level CORS settings in Supabase Dashboard (API -> CORS Configuration) allow the frontend origin.

### 2. `increment-play-count` Edge Function / RPC
-   **Purpose**: To securely increment `plays` for songs.
-   **Status**: An RPC `increment_song_play_count(p_song_id UUID)` exists (see `database-schema.md`). The need for a separate Edge Function should be evaluated. If RPC is sufficient, this task is to ensure client-side integration.
-   **Client-Side**: Logic in `AudioProvider` to call an increment function is currently commented out. Needs to be updated to call the RPC if the Edge Function is deemed unnecessary.
-   **Related**: `TASKS_PROGRESS.md` mentions a TODO for "logique de comptage des lectures d'albums".

## Database Schema & RPCs
-   **`playlist_songs` table**:
    -   **RESOLVED**: Has `id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4()` (see `database-schema.md`).
    -   **RESOLVED**: Has `created_at TIMESTAMP WITH TIME ZONE DEFAULT now()` (see `database-schema.md`).
    -   **RESOLVED**: `song_id` FK correctly points to `public.songs(id)` (see `database-schema.md`).
-   **PostgREST Schema Cache**: The project has experienced multiple issues (PGRST200, PGRST204) related to PostgREST's schema cache not updating after DDL changes (e.g., FK modifications, new columns). If such errors reappear, the user needs to force a schema refresh via Supabase Dashboard (API settings) or restart the project.

## UI / Features
-   **UI Harmonization (General)**:
    *   **COMPLETED**: Harmonize presence and functioning of Share, Edit, Stats block (views, plays, followers), Like, Dislike, Follow buttons across public pages (Songs, Albums, Playlists, Bands) and their edit views. (Ref: `TASKS_PROGRESS.md` - "Harmonisation des Statistiques et Actions" - TERMINÉ)
    *   Ensure all action buttons (like, dislike, follow) are correctly wired and functional. (Ref: `TASKS_PROGRESS.md` - "Synchronisation de l'état visuel des boutons Like/Dislike" - EN COURS)
    *   Integrate comment count into displayed statistics. (Ref: `TASKS_PROGRESS.md` - "Section Commentaires (Album Public)" - EN COURS)
    *   Add user status badges (Free, Pro, Studio) next to usernames. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 1)
-   **Toast Notifications Z-index**: Ensure toast notifications appear above the Global Player. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 1 - CSS fix suggested)
-   **Playlist Edit Page - Song Management**:
    *   **COMPLETED**: Drag-and-drop reordering of songs (see `TASKS_PROGRESS.md` - Playlist Management).
    *   **IN PROGRESS**: Functionality to add songs to playlists (see `TASKS_PROGRESS.md` - "Add to playlist modal"). This covers adding to *another* playlist and adding *new* songs.
    *   Consider if specific UI for adding *new* songs *directly from the playlist edit page* (beyond the modal) is still needed.
-   **Card System - Compact Grid View / Zoom**: Implement a "compact grid" view mode. `TODO_GENERAL_ET_CHORD_MODULE.md` (Phase 3) details a "Slider de Zoom pour Vues en Cartes" which includes simplifying card display at small zoom levels. This is highly relevant and likely covers this task.
-   **Card System - Public/Private Toggle**: **COMPLETED**: Add a quick toggle on cards. RPC `toggle_song_public_status` exists. (Ref: `TASKS_PROGRESS.md` - "Icône public/privé persistante et fonctionnelle sur les cartes" - TERMINÉ). Ensure UI sync with BDD is robust.
-   **Card System - "Duplicate" Action**: Implement the "Duplicate" functionality for playlists, songs, albums, bands. Requires specific RPCs for each.
-   **Admin-configurable costs for Albums, Bands, Songs**: Extend the coin cost system. `creation_costs` table and `create_playlist_with_coin_deduction` RPC exist for playlists. Task is to extend this to albums, bands, songs.
-   **"Buy Playlist Slot" Feature**: For users at their playlist quota limit.
-   **PGRST116 / 406 Errors (AudioProvider)**: Investigate if these persist (related to initial song loading).

## General
-   **Song Versioning System**:
    *   **DB Schema In Place**: `song_versions` table and related RPCs (`save_song_version`, `get_song_version`, `load_song_version`) are documented in `database-schema.md`.
    *   **TODO**: UI/UX for managing song versions. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 3)
-   **Direct Recording & Mini-DAW for Songs**:
    *   **TODO**: Implement interface for direct audio recording, recrop, and basic editing within song creation/editing. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 3)
-   **Video/Clips for Songs**:
    *   **DB Schema Update Needed**: Add `video_url`, `video_embed_code`, `video_metadata` to `songs` table. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 3)
    *   **TODO**: UI for adding video URL in song edit page and displaying video on public song page. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 3)
-   **AI/Human Classification for Songs**:
    *   **DB Field Exists**: `ai_content_origin_enum` and `ai_content_origin` field on `songs` table.
    *   **TODO**: Ensure UI in song creation/edit form allows setting this. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 3)
    *   **TODO**: Align form fields (e.g., `artist_name` vs `artist`, `notes` vs `description`, `duration` units, `instrumentation` array vs string, `contributors`, `themes`, `is_incomplete`) between Zod schema and DB schema. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 3)
-   **User Preferences Page**:
    *   **TODO**: Create a new "Preferences" page with tabs for Profile, Options (language), and Subscription/Mouviks. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase X)
-   **Playlist Module Enhancements**:
    *   **DB Schema Partially In Place**: `playlists`, `playlist_songs`, `playlist_followers` tables exist. `user_playlist_details` and `playlist_songs_view` views exist. `create_playlist_with_coin_deduction` RPC exists.
    *   **COMPLETED**: Playlist creation (modale, RPC, coin deduction). (Ref: `TASKS_PROGRESS.md` - "Création de playlist" - TERMINÉ)
    *   **COMPLETED**: Sidebar access. (Ref: `TASKS_PROGRESS.md` - "Sidebar: Accès aux playlists utilisateur" - TERMINÉ)
    *   **TODO**: Quotas based on user status.
    *   **IN PROGRESS**: UI for adding songs to playlists (popup via "Add to playlist modal"). (Ref: `TASKS_PROGRESS.md` - "Modale 'Ajouter à la playlist'" - EN COURS)
    *   **TODO**: Folder organization.
    *   **TODO**: Public/shareable playlists.
    *   **PARTIALLY COMPLETED**: Stats for playlists (basic info on individual playlist page). (Ref: `TASKS_PROGRESS.md` - "Page individuelle de playlist" - TERMINÉ)
    *   **TODO**: Automatic playlists ("Morceaux Likés", "Écoutes Récentes"). (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 4)
-   **Band Module Enhancements**:
    *   **COMPLETED**: Image upload flexibility (crop/optimize). (Ref: `TASKS_PROGRESS.md` - "Upload d'images pour les groupes" - TERMINÉ)
    *   **TODO**: "Preview public page" button, Public/Private toggle for bands, common multiselect fields (tags, mood, etc.), roles (Admin, Co-admin), admin interface, "Mes Bands" page, option for Band as song author. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 5)
-   **Social Features**:
    *   **TODO**: Advanced Friends/Collaborators system (invitations, online status). (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 5)
    *   **TODO**: Improved integrated messaging (1-to-1, channels, media support). (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 5)
-   **Discover Page Enhancements**:
    *   **TODO**: Highlight Pro/Studio accounts, Mouvik Coins purchase section, search by tags/moods/instruments/genres, visual stickers for preferences. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 6)
-   **Dynamic Tag Pages**:
    *   **TODO**: Create pages for each tag with activity walls and suggestions. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 6)
-   **Advanced Search**:
    *   **TODO**: Multi-filter search including AI/Hybrid/Human classification. (Ref: `TODO_GENERAL_ET_CHORD_MODULE.md` Phase 6)
-   Review all `TODO` comments in the codebase.
-   Thoroughly test all user flows once core functionalities are stable.
