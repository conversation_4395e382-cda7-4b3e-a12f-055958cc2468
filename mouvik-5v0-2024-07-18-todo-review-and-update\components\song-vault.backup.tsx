'use client'

import React, { useCallback, useState, useEffect, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import WaveSurfer from 'wavesurfer.js'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Progress } from '@/components/ui/progress'
import { FileAudio, Trash2, UploadCloud, Play, Pause } from 'lucide-react'

interface VaultFile {
  id: string
  file: File
  previewUrl: string
  annotations: string
  waveSurferInstance?: WaveSurfer
  isPlaying?: boolean
}

const MAX_FILES_GRATUIT = 5
const MAX_TOTAL_SIZE_GRATUIT = 50 * 1024 * 1024 // 50MB
// TODO: Implement Pro and Studio limits later

export default function SongVault() {
  const [vaultFiles, setVaultFiles] = useState<VaultFile[]>([])
  const [totalSize, setTotalSize] = useState(0)
  const waveformRefs = useRef<Map<string, HTMLDivElement | null>>(new Map())

  const onDrop = useCallback((acceptedFiles: File[]) => {
    let currentSize = totalSize
    const newFiles: VaultFile[] = []

    acceptedFiles.forEach(file => {
      if (vaultFiles.length + newFiles.length >= MAX_FILES_GRATUIT) {
        // TODO: Show toast error 'Max files reached'
        console.warn('Max files reached for free tier')
        return
      }
      if (currentSize + file.size > MAX_TOTAL_SIZE_GRATUIT) {
        // TODO: Show toast error 'Max storage reached'
        console.warn('Max storage reached for free tier')
        return
      }
      currentSize += file.size
      newFiles.push({
        id: `${file.name}-${Date.now()}`,
        file,
        previewUrl: URL.createObjectURL(file),
        annotations: '',
      })
    })

    setVaultFiles(prevFiles => [...prevFiles, ...newFiles])
    setTotalSize(currentSize)
  }, [vaultFiles, totalSize])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'audio/mpeg': ['.mp3'] }, // Accept only MP3 files
    multiple: true,
  })

  useEffect(() => {
    vaultFiles.forEach(vf => {
      if (vf.previewUrl && waveformRefs.current.get(vf.id) && !vf.waveSurferInstance) {
        const ws = WaveSurfer.create({
          container: waveformRefs.current.get(vf.id)!,
          waveColor: 'hsl(var(--primary))',
          progressColor: 'hsl(var(--primary) / 0.5)',
          height: 60,
          barWidth: 2,
          barGap: 1,
          cursorWidth: 2,
          cursorColor: 'hsl(var(--foreground))',
          url: vf.previewUrl,
        })
        setVaultFiles(prev => prev.map(f => f.id === vf.id ? { ...f, waveSurferInstance: ws, isPlaying: false } : f))
      }
    })
    return () => {
      vaultFiles.forEach(vf => {
        vf.waveSurferInstance?.destroy()
      })
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [vaultFiles]) // Only re-run when vaultFiles array reference changes (new files added/removed)

  const handleAnnotationChange = (id: string, value: string) => {
    setVaultFiles(prev => prev.map(f => f.id === id ? { ...f, annotations: value } : f))
  }

  const handleRemoveFile = (id: string) => {
    const fileToRemove = vaultFiles.find(f => f.id === id)
    if (fileToRemove) {
      fileToRemove.waveSurferInstance?.destroy()
      URL.revokeObjectURL(fileToRemove.previewUrl)
      setTotalSize(prev => prev - fileToRemove.file.size)
      setVaultFiles(prev => prev.filter(f => f.id !== id))
    }
  }

  const togglePlayPause = (id: string) => {
    const fileToPlay = vaultFiles.find(f => f.id === id)
    if (fileToPlay?.waveSurferInstance) {
      fileToPlay.waveSurferInstance.playPause()
      setVaultFiles(prev => prev.map(f => f.id === id ? { ...f, isPlaying: fileToPlay.waveSurferInstance!.isPlaying() } : f))
    }
  }

  const storagePercentage = (totalSize / MAX_TOTAL_SIZE_GRATUIT) * 100

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle>Espace Brouillons (Vault)</CardTitle>
        <CardDescription>
          Glissez-déposez vos fichiers MP3 ici. Limite: {MAX_FILES_GRATUIT} fichiers, {(MAX_TOTAL_SIZE_GRATUIT / (1024 * 1024)).toFixed(0)}MB.
        </CardDescription>
        <div className="mt-2">
          <Progress value={storagePercentage} className="w-full" />
          <p className="text-xs text-muted-foreground mt-1">
            { (totalSize / (1024*1024)).toFixed(2) }MB / {(MAX_TOTAL_SIZE_GRATUIT / (1024 * 1024)).toFixed(0)}MB utilisés
          </p>
        </div>
      </CardHeader>
      <CardContent className="flex-grow flex flex-col p-0">
        <div
          {...getRootProps()}
          className={`mx-4 mb-4 p-6 border-2 border-dashed rounded-md text-center cursor-pointer 
                      ${isDragActive ? 'border-primary bg-primary/10' : 'border-muted-foreground/50 hover:border-primary/80'}`}
        >
          <input {...getInputProps()} />
          <UploadCloud className="mx-auto h-10 w-10 text-muted-foreground mb-2" />
          {isDragActive ? (
            <p>Relâchez pour ajouter...</p>
          ) : (
            <p>Glissez des MP3 ici, ou cliquez pour sélectionner</p>
          )}
        </div>

        <ScrollArea className="flex-grow px-4">
          {vaultFiles.length === 0 && (
            <p className="text-sm text-muted-foreground text-center py-4">Aucun fichier pour le moment.</p>
          )}
          <div className="space-y-3 mb-4">
            {vaultFiles.map((vf) => (
              <Card key={vf.id} className="overflow-hidden">
                <CardContent className="p-3 flex flex-col space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 overflow-hidden">
                        <FileAudio className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                        <span className="text-sm font-medium truncate" title={vf.file.name}>{vf.file.name}</span>
                        <span className="text-xs text-muted-foreground">({(vf.file.size / (1024*1024)).toFixed(2)} MB)</span>
                    </div>
                    <Button variant="ghost" size="icon" onClick={() => handleRemoveFile(vf.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div 
                    ref={el => waveformRefs.current.set(vf.id, el)}
                    className="w-full h-[60px] bg-muted rounded-md cursor-pointer"
                    onClick={() => togglePlayPause(vf.id)} // Basic click to play/pause on waveform too
                  />
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="icon" onClick={() => togglePlayPause(vf.id)} disabled={!vf.waveSurferInstance}>
                        {vf.isPlaying ? <Pause className="h-4 w-4"/> : <Play className="h-4 w-4"/>}
                    </Button>
                    {/* TODO: Add more controls like volume, seek bar if needed */}
                  </div>

                  <Input
                    type="text"
                    placeholder="Ajouter une annotation..."
                    value={vf.annotations}
                    onChange={(e) => handleAnnotationChange(vf.id, e.target.value)}
                    className="text-sm"
                  />
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

// --- END OF ORIGINAL FILE ---
