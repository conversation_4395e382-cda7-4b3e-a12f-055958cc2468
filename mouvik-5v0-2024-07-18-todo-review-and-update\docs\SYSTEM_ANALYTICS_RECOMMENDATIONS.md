# Système Intégré d'Analyse, de Statistiques, de Dashboard et de Recommandations Mouvik

## 1. Introduction et Objectifs

### 1.1. Vision Générale
Ce document est la **source de vérité unique** décrivant le système intégré d'analyse, de statistiques, du dashboard utilisateur et des recommandations de Mouvik. Il consolide les informations précédemment dispersées dans `analytics_system.md`, `dashboard_analytics.md`, `DASHBOARD_ENHANCEMENTS_TODO.md`, et `STATISTICS_AND_SUGGESTIONS.md`. L'objectif est de fournir :
- Aux **utilisateurs créateurs** : Des informations précieuses sur la performance de leur contenu, l'engagement de leur audience, et des opportunités de croissance.
- À **tous les utilisateurs** : Des suggestions personnalisées pour la découverte de nouveau contenu et des collaborations potentielles.
- À **l'administration de la plateforme** : Une vue d'ensemble des tendances, de l'utilisation et de la santé de la plateforme pour guider les décisions stratégiques.

L'accès à certains niveaux de détail statistique et à des fonctionnalités de suggestion avancées pourra être modulé en fonction des plans d'abonnement des utilisateurs.

### 1.2. Objectifs Spécifiques
- **Pour les Créateurs :**
    - Comprendre quelles sont leurs œuvres les plus populaires.
    - Suivre l'évolution de leur audience et de l'engagement.
    - Identifier les caractéristiques (genres, moods, tags) qui résonnent le plus.
    - Obtenir des insights pour améliorer leur stratégie de contenu.
- **Pour l'Administration :**
    - Suivre les indicateurs clés de performance (KPIs) de la plateforme.
    - Comprendre les comportements utilisateurs globaux.
    - Identifier les contenus et créateurs populaires.
    - Évaluer l'impact des nouvelles fonctionnalités.

## 2. Architecture Générale du Système d'Analyse

Le système d'analyse et de statistiques de Mouvik est structuré en trois couches interdépendantes pour assurer la modularité, la performance et la maintenabilité.

1.  **Couche de Données (Data Layer)** :
    *   **Base de données** : PostgreSQL gérée via Supabase.
    *   **Rôle** : Stockage des données brutes d'interaction (écoutes, likes, vues, etc.), des métadonnées de contenu, et des données agrégées.
    *   **Composants Clés** :
        *   Tables primaires : `plays`, `likes`, `song_user_views`, `songs`, `albums`, `profiles`, `audience_demographics`, `audio_analysis`.
        *   Vues SQL : `resource_like_dislike_stats`, `artist_stats` pour simplifier les requêtes complexes.
        *   Fonctions SQL (RPCs) pour l'agrégation et la récupération de données spécifiques (voir section RPCs ci-dessous).
        *   Triggers : Pour la mise à jour en temps réel des compteurs agrégés (ex: `songs.plays`, `songs.like_count`).

2.  **Couche API (API Layer)** :
    *   **Technologie** : Fonctions PostgreSQL exposées comme des endpoints RPC via Supabase.
    *   **Rôle** : Fournir une interface sécurisée et optimisée pour que la couche de présentation accède aux données analytiques et statistiques.
    *   **Caractéristiques** :
        *   Exposition sélective des fonctions SQL nécessaires.
        *   Gestion de la sécurité et des permissions d'accès (Row Level Security de Supabase).
        *   Transformation des données si nécessaire avant l'envoi au client.

3.  **Couche Présentation (Presentation Layer)** :
    *   **Technologie** : Composants React (Next.js) utilisant des bibliothèques de visualisation de données.
    *   **Rôle** : Afficher les statistiques et les analyses de manière intuitive et interactive aux utilisateurs (créateurs et administrateurs).
    *   **Composants Clés** :
        *   Pages dédiées : `/stats` (pour les créateurs), section admin pour les statistiques globales.
        *   Composants de visualisation : Graphiques, tableaux, cartes (voir section Composants UI).
        *   Logique client pour les appels API, la gestion des états et l'interaction utilisateur.

```mermaid
graph TD
    subgraph Frontend (Presentation Layer)
        direction LR
        P1[Page /stats]
        P2[Dashboard Admin]
        P3[Autres Composants UI]
    end
    subgraph Backend (API Layer)
        direction LR
        API1[Supabase RPCs]
    end
    subgraph Database (Data Layer)
        direction LR
        DB1[Tables SQL]
        DB2[Vues SQL]
        DB3[Fonctions SQL]
        DB4[Triggers]
    end
    P1 -->|Appels API| API1
    P2 -->|Appels API| API1
    P3 -->|Appels API| API1
    API1 -->|Exécute| DB3
    DB3 -->|Accède/Modifie| DB1
    DB3 -->|Accède| DB2
    DB4 -->|Modifie| DB1
```

## 3. Système de Statistiques et Dashboard Utilisateur

### Data Collection and Sources
This section outlines the primary data sources that fuel the analytics, statistics, and recommendation engines. The goal is to capture a comprehensive view of user interactions, content performance, and platform engagement. Status indicators: [I] = Implemented/Existing, [P] = Partially Implemented, [C] = Conceptual/Planned.

- **User Interaction Tables:**
    - `likes`: Records user likes for songs, albums, playlists, bands. [I]
    - `dislikes`: Records user dislikes. [I] (with `dislike_count` on relevant tables)
    - `profile_followers`: Tracks user follows for profiles. [I]
    - `band_followers`: Tracks user follows for bands. [I]
    - `playlist_followers`: Tracks user follows for playlists. [I]
    - `comments` (e.g., `song_comments`, `project_comments`): User comments. [I]
    - `shares`: Tracks when users share content. [C]
    - `downloads`: Tracks content downloads. [C]
    - `song_user_views`: Tracks unique song views by users. [I] (Used by `increment_song_plays`)
    - `plays`: Detailed song play tracking (duration, completion, source, device). [I] (Table exists, population via `increment_song_plays` or similar is key)
- **Direct Counters on Core Tables:** [I]
    - `songs.plays`, `songs.like_count`, `songs.dislike_count`
    - `albums.like_count`, `albums.dislike_count`
    - `playlists.view_count`, `playlists.like_count`, `playlists.dislike_count`, `playlists.follower_count`, `playlists.plays`
    - `bands.like_count`, `bands.dislike_count`, `bands.follower_count`
    - `profiles.follower_count`
- **Aggregated Views:**
    - `public.resource_like_dislike_stats` (SQL View): Provides aggregated like/dislike counts, follower counts, plays, and views for songs, albums, playlists, bands, and profiles. This view simplifies frontend queries for basic interaction statistics and ensures consistency. [I]
        - **Objective**: Offer a unified source for common interaction metrics across different resource types.
        - **Key Structure**: Aggregates data from `likes`, `dislikes`, `profile_followers`, `band_followers`, `playlist_followers`, and direct counters on `songs`, `playlists`.
        - **Data Sources (Version 5 - Current Implementation):**
            - **Song Likes/Dislikes**: `songs.like_count`, `songs.dislike_count` (derived from `likes` table via triggers/RPCs).
            - **Song Plays/Views**: `songs.plays` (total plays), `COUNT(DISTINCT suv.user_id) AS unique_listeners` from `song_user_views suv`.
            - **Album Likes/Dislikes**: `albums.like_count`, `albums.dislike_count`.
            - **Playlist Likes/Dislikes/Followers/Plays/Views**: `playlists.like_count`, `playlists.dislike_count`, `playlists.follower_count`, `playlists.plays`, `playlists.view_count`.
            - **Band Likes/Dislikes/Followers**: `bands.like_count`, `bands.dislike_count`, `bands.follower_count`.
            - **Profile Followers**: `profiles.follower_count`.
        - **Usage**: Primarily for dashboard/statistics display, reducing complex joins on the frontend.
    - `public.artist_stats` (View): Provides aggregated statistics for artists (total songs, albums, plays, followers). [I]
    - `public.user_playlist_details` (View): Aggregates playlist info with counts and creator details. [I]
    - `public.playlist_songs_view` (View): Denormalized view of songs in playlists. [I]
    - `creator_monthly_summary_mv`: Materialized view for monthly creator stats. [C]
    - `song_trending_score_mv`: Materialized view for song trending scores. [C]
- **Content Metadata:** [I]
    - `songs`: Genre, mood, mood tags, tags, duration, release date, BPM, key, instrumentation, lyrics.
    - `albums`: Release date, genre.
    - `playlists`: Genres, moods, instrumentation.
    - `profiles`: Location (conceptual for aggregation), user-provided interests (conceptual).
- **Audio Analysis Data:**
    - `public.audio_analysis`: Table to store energy, danceability, valence, acousticness, instrumentalness, speechiness, liveness. [I] (Table structure exists)
    - **Action Item:** Develop a process (e.g., backend job, client-side analysis on upload) to populate `audio_analysis` for songs. [P]
- **User Demographics:**
    - `public.audience_demographics`: Table to store aggregated, anonymized listener data per artist (age group, gender, country, device). [I] (Table structure exists)
    - **Action Item:** Design and implement a secure and privacy-preserving mechanism to collect and aggregate this data. This is a sensitive area and requires careful planning regarding user consent and data anonymization. [P]
- **Subscription Tier Data:** [I]
    - `plan_limits`: Defines feature access based on subscription tier (e.g., `analytics_level`).

- **Optimization Strategies:**
    - **Triggers:** Used to update aggregate counts on related tables (e.g., `like_count` on `songs` when a new like is inserted). [I]
    - **RPCs (Database Functions):** Encapsulate business logic, perform multiple database operations atomically, and can be called from the frontend/backend. Key RPCs include:
        - `get_user_overview_stats(p_user_id UUID, p_time_range TEXT)`: [C] Fournit un résumé des statistiques clés pour un utilisateur (total écoutes, auditeurs uniques, likes, commentaires, engagement) avec comparaison à la période précédente.
        - `get_activity_timeline(p_user_id UUID, p_metrics TEXT[], p_interval TEXT, p_days INTEGER)`: [C] Récupère les données pour une chronologie d'activité (écoutes, likes, commentaires, vues, nouveaux followers).
        - `get_genre_performance(p_user_id UUID)`: [C] Analyse la performance du contenu de l'utilisateur par genre musical.
        - `get_song_characteristics(p_song_id UUID)`: [C] Récupère les caractéristiques audio (énergie, dansabilité, etc.) d'un morceau depuis la table `audio_analysis`.
        - `get_top_content(p_user_id UUID, p_time_range TEXT, p_content_type TEXT, p_limit INTEGER)`: [C] Liste le contenu le plus populaire d'un utilisateur (morceaux, albums) basé sur divers critères.
        - `get_audience_demographics(p_user_id UUID)`: [P] Récupère les données démographiques agrégées de l'audience d'un artiste depuis `audience_demographics`.
        - `get_platform_key_metrics()`: [C] Pour l'administration, récupère les KPIs globaux de la plateforme.
        - `get_platform_activity_summary(period TEXT)`: [C] Pour l'administration, résume l'activité globale sur une période donnée.
        - (Plus d'exemples existants listés précédemment dans la section "Data Collection and Sources")
        - `increment_song_plays(song_id_param uuid, p_user_id uuid)`: Atomically increments play count on `songs` table and records/updates view in `song_user_views`. Critical for song statistics. [I]
        - `toggle_song_public_status(p_song_id uuid, p_user_id uuid)`: Toggles `is_public` for a song. [I]
        - `rpc_save_song_version(...)`: Saves a new version of a song. [I]
        - `rpc_get_song_versions(p_song_id uuid)`: Retrieves versions for a song. [I]
        - `rpc_load_song_version_data(p_song_version_id uuid)`: Fetches data for a specific song version. [I]
        - `toggle_profile_follow(p_profile_id UUID, p_user_id UUID)`: Manages follow status between profiles and updates `follower_count`. [I]
        - `toggle_band_follow(p_band_id UUID, p_user_id UUID)`: Manages follow status for bands and updates `follower_count`. [I]
        - `create_playlist_with_coin_deduction(...)`: Creates a playlist, handles coin deduction. [I]
        - `get_playlist_details_for_view(p_playlist_id UUID, p_requesting_user_id UUID)`: Fetches comprehensive playlist details. [I]
        - `get_artist_stats(p_artist_id uuid)`: Retrieves comprehensive stats for an artist, likely using `artist_stats` view. [I]
        - `get_trending_songs(limit_count integer, time_period_hours integer)`: Fetches trending songs based on recent activity (e.g., from `song_user_views`). [I]
        - `get_recommended_songs(p_user_id uuid, limit_count integer)`: Generates personalized song recommendations. [I]
        - `search_songs_and_artists(search_query text, p_user_id uuid)`: Full-text search. [I]
        - `toggle_like(user_id, resource_id, resource_type)`: Generic like/unlike RPC. [C] (Currently, likes are often managed by direct table inserts/deletes or specific toggle functions for follows. A unified RPC could be beneficial.)
    - **Summary Tables / Materialized Views:** Pre-calculate and store aggregated data for faster querying. [C] (e.g., `creator_monthly_summary_mv`)
    - **Indexing:** Proper indexing on frequently queried columns and foreign keys. [I]

### 2.2. Optimisation de la Collecte et de l'Agrégation

Plusieurs stratégies sont utilisées pour optimiser la collecte et l'agrégation des données. Statut : [I] = Implémenté, [P] = Partiellement Implémenté, [C] = Conceptuel.

1.  **Triggers PostgreSQL :** [I]
    *   Mise à jour automatique des compteurs sur les tables principales lors d'insertions/suppressions dans les tables d'interaction.
    *   Exemples implémentés :
        *   Mise à jour de `like_count` sur `songs` lors d'une insertion/suppression dans `likes`.
        *   Mise à jour de `follower_count` sur `profiles` et `bands` lors d'une insertion/suppression dans `profile_followers` et `band_followers`.

2.  **RPCs (Database Functions) :** [I]
    *   Encapsulent la logique métier complexe et assurent l'atomicité des opérations.
    *   Fonctions implémentées :
        *   `increment_song_plays(song_id_param uuid, p_user_id uuid)` : Incrémente le compteur de plays et enregistre dans `song_user_views`.
        *   `get_artist_stats(p_artist_id uuid)` : Récupère les statistiques agrégées pour un artiste via la vue `artist_stats`.
        *   `get_trending_songs(limit_count integer, time_period_hours integer)` : Récupère les morceaux tendance basé sur l'activité récente.
        *   `get_recommended_songs(p_user_id uuid, limit_count integer)` : Génère des recommandations personnalisées.
        *   `toggle_profile_follow`, `toggle_band_follow` : Gèrent les suivis et mettent à jour les compteurs.
    *   Fonctions conceptuelles :
        *   `toggle_like(user_id, resource_id, resource_type)` : RPC générique pour les likes/unlikes. [C]

3.  **Vues SQL et Vues Matérialisées :**
    *   Vues SQL implémentées : [I]
        *   `resource_like_dislike_stats` : Agrégation des statistiques d'interaction.
        *   `artist_stats` : Statistiques agrégées pour les artistes.
        *   `user_playlist_details` : Informations agrégées sur les playlists.
        *   `playlist_songs_view` : Vue dénormalisée des morceaux dans les playlists.
    *   Vues matérialisées conceptuelles : [C]
        *   `creator_monthly_summary_mv` : Résumé mensuel des statistiques par créateur.
        *   `song_trending_score_mv` : Scores de tendance des morceaux.

4.  **Indexation :** [I]
    *   Index implémentés :
        *   Index sur les clés primaires et étrangères de toutes les tables.
        *   Index spécifiques sur `plays` : `plays_song_id_idx`, `plays_user_id_idx`, `plays_created_at_idx`.
        *   Index sur `audio_analysis` : `audio_analysis_song_id_idx`.
        *   Index sur `audience_demographics` : `audience_demographics_artist_id_idx`.

5.  **Optimisation des Requêtes :** [P]
    *   Implémenté :
        *   Utilisation de la vue `resource_like_dislike_stats` pour éviter les jointures complexes.
        *   Vues dénormalisées pour les cas d'utilisation fréquents.
    *   À implémenter :
        *   Pagination côté serveur pour les grandes listes.
        *   Mise en cache des résultats fréquemment accédés.
        *   Optimisation des requêtes de tendance et de recommandation.

### 3.3. Dashboard Utilisateur (`/dashboard`) et Page de Statistiques (`/stats`)

**Vision & Objectifs du Dashboard (`/dashboard`)**

Le dashboard utilisateur a pour objectif principal de fournir une **vue d'ensemble centralisée et personnalisée** de l'activité pertinente pour le créateur. Il doit permettre à l'utilisateur de :

1.  **Visualiser rapidement ses performances clés** : Nombre total d'écoutes, de vues, de likes, de followers, et évolution récente.
2.  **Suivre l'activité récente liée à son contenu et à sa communauté** : Nouveaux commentaires, morceaux récemment joués par d'autres, ajouts à des playlists, nouveaux followers.
3.  **Identifier les contenus qui performent bien ou nécessitent attention** : Mise en avant des morceaux populaires ou des projets en cours.
4.  **Découvrir des opportunités** : Suggestions de collaboration, de genres à explorer, ou de morceaux à mettre en avant.
5.  **Accéder facilement aux outils de création et de gestion** : Liens rapides vers le téléversement, l'édition de profil, la gestion des projets.

Il sert de **point d'entrée dynamique** dans l'écosystème de l'application, encourageant l'engagement et fournissant des informations exploitables sans submerger l'utilisateur.

Cette section détaille les fonctionnalités et composants du dashboard utilisateur (`app/(authenticated)/dashboard/page.tsx`) et de la page de statistiques dédiée (`app/(authenticated)/stats/page.tsx`). L'accès aux détails varie selon le plan d'abonnement (`analytics_level` via `hooks/use-plan-limits.ts` et table `plan_limits`).

**Structure Générale du Dashboard (`/dashboard`)**

Le dashboard (`app/(authenticated)/dashboard/page.tsx`) est pensé comme une page modulaire, composée de "cartes" ou "widgets" thématiques. Chaque carte se concentre sur un aspect spécifique de l'activité ou des performances de l'utilisateur.

*   **Composants UI Clés du Dashboard (Existant et Prévus) :**
    *   `DashboardProfileCard.tsx` (Existant, à améliorer en `EnhancedProfileCard.tsx`): Affiche les informations de base du profil et les statistiques agrégées (écoutes, followers, etc.).
    *   `RecentlyWorkedOnCard.tsx` (Existant): Liste les projets et morceaux récemment modifiés par l'utilisateur.
    *   `RecentPlaysOfMySongsCard.tsx` (Existant): Affiche les écoutes récentes des morceaux de l'utilisateur par d'autres.
    *   `MySongsInPlaylistsCard.tsx` (Existant): Montre les morceaux de l'utilisateur récemment ajoutés à des playlists.
    *   `RecentFollowersCard.tsx` (Existant): Liste les nouveaux followers.
    *   `HighlightSongSuggestionCard.tsx` (Conceptuel): Suggère un morceau à mettre en avant.
    *   `CollaborationSuggestionCard.tsx` (Conceptuel): Suggère des collaborations potentielles.
    *   `GenreFocusSuggestionCard.tsx` (Conceptuel): Suggère des genres sur lesquels se concentrer.
    *   `MultiMetricTimeline.tsx` (Conceptuel): Graphique d'évolution temporelle de plusieurs métriques clés (écoutes, vues, likes, followers).
    *   `PerformanceSummary.tsx` (Conceptuel): Résumé des performances globales.
    *   `LatestCommentsCard.tsx` (Conceptuel): Affiche les derniers commentaires reçus.
    *   `QuickActionsCard.tsx` (Conceptuel): Raccourcis vers les actions fréquentes (uploader, nouveau projet).

**Page de Statistiques (`/stats`)**

La page `/stats` (`app/(authenticated)/stats/page.tsx`) est la principale interface où les utilisateurs peuvent visualiser les statistiques détaillées relatives à leur propre contenu et profil. Elle est alimentée par des appels RPC Supabase (ex: `get_user_overview_stats`, `get_activity_timeline`) et des requêtes directes pour agréger et afficher les données. La fonction `calculateCombinedDailyData` (côté client) peut être utilisée pour fusionner des données journalières de différentes sources.

La page `/stats` est la principale interface où les utilisateurs peuvent visualiser les statistiques relatives à leur propre contenu et profil. Elle est alimentée par des appels RPC Supabase (ex: `get_user_overview_stats`, `get_activity_timeline`) et des requêtes directes pour agréger et afficher les données. La fonction `calculateCombinedDailyData` (côté client) peut être utilisée pour fusionner des données journalières de différentes sources.

**Niveaux de Statistiques et Fonctionnalités (Page `/stats`) :**

*   **Niveau Basique (Gratuit/Par Défaut)** :
    *   Nombre total d'écoutes, de vues, de likes.
    *   Nombre de followers.
    *   Top 3 des morceaux/vidéos les plus performants (derniers 30 jours).
    *   Flux d'activité de base (derniers 7 jours).
*   **Niveau Étendu (Abonnement Intermédiaire)** :
    *   Toutes les fonctionnalités du Niveau Basique.
    *   Analyse détaillée des performances par morceau/vidéo (écoutes, vues, likes, commentaires, partages au fil du temps).
    *   Aperçu démographique de l'audience (pays, tranche d'âge - agrégé et anonymisé).
    *   Sélection de plages de dates personnalisées pour les analyses.
    *   Comparaison avec les périodes précédentes.
*   **Niveau Pro (Abonnement Supérieur)** :
    *   Toutes les fonctionnalités du Niveau Étendu.
    *   Informations avancées sur l'audience (ex: rétention des auditeurs, heures de pointe d'écoute, types d'appareils).
    *   Rapports téléchargeables (CSV).
    *   Potentiellement, intégration avec des plateformes d'analyse externes.
    *   Informations sur les tests A/B pour les variations de contenu (conceptuel).

Les données spécifiques sont récupérées via des fonctions RPC dédiées comme `getUserOverviewStats`, `getUserActivityTimeline`, `getUserTopContent`, `getUserGenrePerformance`, `getUserSongCharacteristics`, `getUserAudienceDemographics`, et `getUserAudienceActivity`, dont la granularité et l'accès peuvent dépendre du niveau d'abonnement.

Le dashboard utilisateur (`/dashboard`) pourrait être structuré en sections thématiques pour une meilleure lisibilité :

Cette section détaille les fonctionnalités et composants du dashboard utilisateur (`app/(authenticated)/dashboard/page.tsx`) et de la page de statistiques dédiée (`app/(authenticated)/stats/page.tsx`). L'accès aux détails varie selon le plan d'abonnement (`analytics_level` via `hooks/use-plan-limits.ts` et table `plan_limits`).

**Structure Générale du Dashboard (`/dashboard`)**

Le dashboard (`app/(authenticated)/dashboard/page.tsx`) est pensé comme une page modulaire, composée de "cartes" ou "widgets" thématiques. Chaque carte se concentre sur un aspect spécifique de l'activité ou des performances de l'utilisateur.

*   **Composants UI Clés du Dashboard (Existant et Prévus) :**
    *   `DashboardProfileCard.tsx` (Existant, à améliorer en `EnhancedProfileCard.tsx`): Affiche les informations de base du profil et les statistiques agrégées (écoutes, followers, etc.).
    *   `RecentlyWorkedOnCard.tsx` (Existant): Liste les projets et morceaux récemment modifiés par l'utilisateur.
    *   `RecentPlaysOfMySongsCard.tsx` (Existant): Affiche les écoutes récentes des morceaux de l'utilisateur par d'autres.
    *   `MySongsInPlaylistsCard.tsx` (Existant): Montre les morceaux de l'utilisateur récemment ajoutés à des playlists.
    *   `RecentFollowersCard.tsx` (Existant): Liste les nouveaux followers.
    *   `HighlightSongSuggestionCard.tsx` (Conceptuel): Suggère un morceau à mettre en avant.
    *   `CollaborationSuggestionCard.tsx` (Conceptuel): Suggère des collaborations potentielles.
    *   `GenreFocusSuggestionCard.tsx` (Conceptuel): Suggère des genres sur lesquels se concentrer.
    *   `MultiMetricTimeline.tsx` (Conceptuel): Graphique d'évolution temporelle de plusieurs métriques clés (écoutes, vues, likes, followers).
    *   `PerformanceSummary.tsx` (Conceptuel): Résumé des performances globales.
    *   `LatestCommentsCard.tsx` (Conceptuel): Affiche les derniers commentaires reçus.
    *   `QuickActionsCard.tsx` (Conceptuel): Raccourcis vers les actions fréquentes (uploader, nouveau projet).

**Page de Statistiques (`/stats`)**

La page `/stats` (`app/(authenticated)/stats/page.tsx`) est la principale interface où les utilisateurs peuvent visualiser les statistiques détaillées relatives à leur propre contenu et profil. Elle est alimentée par des appels RPC Supabase (ex: `get_user_overview_stats`, `get_activity_timeline`) et des requêtes directes pour agréger et afficher les données. La fonction `calculateCombinedDailyData` (côté client) peut être utilisée pour fusionner des données journalières de différentes sources. Statut : [I] = Implémenté, [P] = Partiellement Implémenté, [C] = Conceptuel.

Le dashboard utilisateur pourrait être structuré en sections thématiques pour une meilleure lisibilité :

**3.3.1. Vue d'Ensemble (Dashboard & Stats Overview)**
   - **Objectif :** Fournir un aperçu rapide des métriques clés et de l'activité récente. [P]
   - **Composants UI Clés (Principalement sur `/dashboard`, certains éléments peuvent être sur `/stats`) :**
     - `EnhancedProfileCard.tsx`: [P] (Amélioration de `DashboardProfileCard.tsx`)
     - `MultiMetricTimeline.tsx`: [C] (Utilise `get_activity_timeline` RPC [C])
     - `PerformanceSummary.tsx`: [C]
     - `RecentlyWorkedOnCard.tsx`: [P] (Basé sur `songs.updated_at`, `albums.updated_at` [I])
     - `RecentPlaysOfMySongsCard.tsx`: [C] (Table `plays` [I], `song_user_views` [I])
     - `MySongsInPlaylistsCard.tsx`: [P] (Table `playlist_songs` [I])
     - `RecentFollowersCard.tsx`: [P] (Table `profile_followers` [I])
     - `HighlightSongSuggestionCard.tsx`: [C]
   - **Niveau 'Basic' (accessible à tous) :** [P]
        *   Total des écoutes des morceaux (via `resource_like_dislike_stats.play_count` [I] et `songs.user_id` [I]).
        *   Total des vues de contenu (via `resource_like_dislike_stats.view_count` [I] et `songs.user_id` / `albums.user_id` [I]).
        *   Total des likes reçus (via `resource_like_dislike_stats.like_count` [I] et `songs.user_id` [I]).
        *   Total des followers du profil (via `resource_like_dislike_stats.follower_count` [I] et `profiles.id` [I]).
        *   Total des followers des groupes (via `resource_like_dislike_stats.follower_count` [I] et `bands.id` [I], `band_members.user_id` [I]).
        *   Liste des 5 morceaux les plus joués (requête sur `songs` triée par `plays DESC` [I], filtré par `user_id` [I]).
   - **RPCs Associés :**
        *   `get_user_overview_stats(p_user_id UUID, p_time_range TEXT)`: [C] (Conceptuellement défini, mais pas listé dans `database-schema.md` comme existant).
        *   `get_activity_timeline(p_user_id UUID, p_metrics TEXT[], p_interval TEXT, p_days INTEGER)`: [C] (Conceptuellement défini).
        *   `get_dashboard_data(p_user_id UUID)`: [C] (Fonction centrale à créer).
        *   `get_artist_stats(p_artist_id uuid)`: [I] (Peut servir de base pour les stats d'overview).

**3.3.2. Analyse d'Audience (Niveaux Extended/Pro)**
   - **Objectif :** Comprendre qui écoute le contenu et comment. [C]
   - **Composants UI Clés (Principalement sur `/stats`) :**
     - `GeoHeatMap.tsx`: [C] (Utilise `audience_demographics` [I - table existe] ou `plays` [I])
     - `DemographicCharts.tsx`: [C] (Utilise `audience_demographics` [I - table existe])
     - `ListeningSources.tsx`: [C]
     - `ListeningBehavior.tsx`: [C] (Utilise `plays` [I] ou `song_user_views` [I] agrégées)
     - `FollowerTrendChart.tsx`: [P] (Utilise `profile_followers` [I] agrégée par date)
   - **Niveau 'Extended' (plan payant) :** [C]
        *   Graphiques d'évolution temporelle (écoutes, likes, vues, followers) pour morceaux/albums spécifiques.
        *   Tendances pour des périodes spécifiques (semaine, mois, année).
   - **Niveau 'Pro' (plan supérieur) :** [C]
        *   Analyses avancées : démographie de l'audience (table `audience_demographics` [I - table existe], peuplée via `get_playlist_details_for_view` [I] ou similaire, agrégée par artiste).
   - **RPCs Associés :**
        *   `get_audience_demographics(p_user_id UUID)`: [P] (Table `audience_demographics` [I] existe, RPC pour la requêter à affiner pour l'artiste).
        *   `get_geographic_distribution(p_user_id UUID)`: [C].
        *   `get_follower_trend(p_user_id UUID, p_time_range TEXT)`: [C].

**3.3.3. Analyse de Contenu (Niveaux Extended/Pro)**
   - **Objectif :** Analyser la performance des différents contenus musicaux. [P]
   - **Composants UI Clés (Principalement sur `/stats`) :**
     - `TopContentTable.tsx`: [P]
     - `GenrePerformance.tsx` (ou `TopSongsPlaysDistributionChart.tsx`): [C]
     - `MusicAttributesRadar.tsx`: [P] (Utilise `audio_analysis` [I - table existe], `get_song_characteristics` RPC [C])
     - `CompletionRateChart.tsx`: [C] (Nécessite tracking détaillé dans `plays` [I])
     - `ContentPerformanceComparisonChart.tsx`: [C]
   - **Niveau 'Extended' (plan payant) :** [P]
        *   Analyse des tags/genres les plus performants pour le contenu de l'utilisateur.
        *   Statistiques d'engagement détaillées par morceau/album (écoutes, vues, likes, taux de complétion) en aperçu sur le dashboard ou dans les listes.
   - **Niveau 'Pro' (plan supérieur) :** [C]
        *   Outils de comparaison.
        *   Export de données.
   - **RPCs Associés :**
        *   `get_genre_performance(p_user_id UUID)`: [C].
        *   `get_song_characteristics(p_song_id UUID)`: [C] (Pourrait utiliser `audio_analysis` [I]).
        *   `get_top_content(p_user_id UUID, p_time_range TEXT, p_content_type TEXT, p_limit INTEGER)`: [C] (Peut utiliser `get_trending_songs` [I] comme base).
        *   `get_content_performance_analysis(p_user_id UUID)`: [C].
        *   `get_content_trend_stats(p_resource_type TEXT, p_resource_id UUID, p_period TEXT)`: [C].
        *   `get_user_content_performance_by_taxonomy(p_user_id UUID, p_taxonomy_type TEXT)`: [C].
        *   `get_content_comparison_data(p_resource_type TEXT, p_resource_id UUID)`: [C].
        *   `get_trending_songs(limit_count integer, time_period_hours integer)`: [I] (Peut être utilisé pour le top contenu).
        *   `get_playlist_details_for_view(p_playlist_id UUID, p_requesting_user_id UUID)`: [I] (Pour stats de playlist).

**3.3.4. Engagement Social (Niveaux Extended/Pro)**
   - **Objectif :** Suivre les interactions sociales autour du contenu et du profil. [C]
   - **Composants UI Clés (Principalement sur `/stats` ou sections dédiées du dashboard) :**
     - `CommentActivity.tsx`: [C] (Tables `song_comments` [I], `project_comments` [I])
     - `LikeAnalysis.tsx`: [C] (Table `likes` [I])
     - `InfluenceNetwork.tsx`: [C]
     - `SharingMetrics.tsx`: [C]
     - Amélioration du composant existant pour les commentaires récents sur le dashboard. [P]
   - **RPCs Associés :**
        *   `get_engagement_metrics(p_user_id UUID)`: [C].

**3.3.6. Bibliothèques de Visualisation et Composants UI**

*   **Bibliothèques de Visualisation Suggérées:**
    *   **Recharts** (déjà utilisé) : Pour graphiques de base (lignes, barres, aires, camemberts).
    *   **Nivo** : Pour visualisations avancées (cartes de chaleur, graphiques réseau, radar).
    *   **react-simple-maps** : Pour visualisations géographiques.
    *   **d3.js** : Pour visualisations personnalisées complexes.
*   **Composants UI (`components/stats/` et `components/dashboard/`)** : (Liste détaillée fournie précédemment, incluant `AnalyticsOverview.tsx`, `ActivityTimeline.tsx`, `GenreAnalysis.tsx`, `TopContent.tsx`, etc.)
**3.3.5. Composants UI Communs pour Statistiques (`components/stats/`)**

Les composants suivants, situés dans `components/stats/`, sont utilisés pour construire les interfaces de statistiques, tant pour la page `/stats` utilisateur que potentiellement pour le dashboard admin. Statut : [I] = Implémenté, [P] = Partiellement Implémenté, [C] = Conceptuel.

*   `AnalyticsOverview.tsx`: [P] (Affiche les métriques clés. Utilisé sur `/stats`.)
*   `ActivityTimeline.tsx`: [P] (Graphique d'évolution de l'activité. Utilisé sur `/stats`.)
*   `GenreAnalysis.tsx`: [P] (Analyse des performances par genre. Utilisé sur `/stats`.)
*   `MusicMoodVisualization.tsx`: [C] (Visualisation interactive des morceaux par humeur.)
*   `AudienceAnalysis.tsx`: [P] (Analyse démographique de l'audience. Utilisé sur `/stats`.)
*   `MusicCharacteristics.tsx`: [P] (Analyse des caractéristiques musicales. Utilisé sur `/stats`.)
*   `TopContent.tsx`: [P] (Affichage du contenu le plus populaire. Utilisé sur `/stats`.)
*   `ArtistComparison.tsx`: [C] (Comparaison avec d'autres artistes.)
*   `PlayCounter.tsx`: [I] (Compteur simple d'écoutes, ex: sur page chanson)
*   `ViewCounter.tsx`: [I] (Compteur simple de vues, ex: sur page chanson)
*   `ResourceViewTracker.tsx`: [I] (Composant pour enregistrer les vues de ressources)
*   `ViewRecorder.tsx`: [I] (Logique pour enregistrer les vues, potentiellement un HOC ou un hook)
*   `stats-definitions.ts`: [I] (Définitions TypeScript pour les types de données statistiques)

### 3.4. Statistiques Administrateur (Page Admin)

Ces statistiques fournissent une vue globale de l'activité sur toute la plateforme et sont accessibles via un onglet dédié dans la page d'administration.

*   **Indicateurs Clés de la Plateforme (via `getPlatformKeyMetrics` [C] ou requêtes directes)**:
    *   Nombre total de morceaux publiés.
    *   Nombre total de playlists publiques.
    *   Nombre total d'albums publiés.
    *   Écoutes totales sur la plateforme.
    *   Vues totales sur la plateforme.
    *   Likes totaux sur la plateforme.
    *   Nombre total d'utilisateurs inscrits.
*   **Autres agrégats et moyennes pertinents pour l'administration.**

**Composants UI Potentiels pour la Page Admin (inspiré de `components/stats/`)**

Les composants développés pour la page `/stats` utilisateur pourraient être réutilisés ou adaptés pour la section admin :
*   `AnalyticsOverview.tsx` (adapté pour les KPIs plateforme)
*   `ActivityTimeline.tsx` (pour l'activité globale)
*   `GenreAnalysis.tsx` (pour les tendances de genre à l'échelle de la plateforme)
*   `TopContent.tsx` (pour identifier le contenu le plus populaire sur toute la plateforme)

### 3.6. Plan de Développement / TODO pour les Statistiques et le Dashboard ("Câbler ce qui manque")

Cette section consolide les tâches de développement nécessaires, en s'inspirant fortement de `DASHBOARD_ENHANCEMENTS_TODO.md` (pour le dashboard) et des besoins identifiés pour les statistiques et la page `/stats`.

**Objectifs de Développement du Dashboard (inspiré de `docs/dashboard_analytics.md`)**

1.  **Centraliser la récupération des données** via une ou plusieurs fonctions RPC Supabase pour améliorer les performances et simplifier le code client.
2.  **Améliorer l'UX** avec des états de chargement clairs, une gestion des erreurs gracieuse par section, et une interface plus réactive.
3.  **Enrichir les informations affichées** avec de nouvelles cartes et des visualisations de données plus parlantes (ex: graphiques de tendance).
4.  **Intégrer des suggestions proactives et personnalisées** pour aider les créateurs.
5.  **Refactoriser le code existant** pour une meilleure maintenabilité et testabilité (extraction de sous-composants).
6.  **Assurer la cohérence des données** affichées avec les statistiques disponibles sur la page `/stats`.

**IV. Refactoring et Optimisations Techniques (Priorité Haute)**
1.  **Centralisation de la Récupération des Données du Dashboard**
    *   **Action :** Créer/améliorer la fonction RPC Supabase `get_dashboard_data(p_user_id UUID)` qui retourne un JSON contenant toutes les données agrégées nécessaires pour le dashboard principal (statistiques globales, listes de contenu récent, données pour les graphiques de base).
    *   **Bénéfice :** Simplifie le code des pages React (`DashboardPage`, `StatsPage`), réduit le nombre d'appels `await` individuels, et améliore les performances.
    *   **Statut :** [C] PLANIFIÉ.
2.  **Extraction de Sous-Composants**
    *   **Action :** Décomposer `DashboardPage` et `StatsPage` en plus petits composants serveur ou client (ex: `StatsSummaryCardGroup`, `RecentActivityLists`, `PerformanceChartsSection`, `SuggestionsCarousel`).
    *   **Bénéfice :** Améliore la lisibilité, la maintenabilité et la testabilité. Permet un chargement potentiellement plus granulaire.
    *   **Statut :** [P] EN COURS / À CONTINUER.
3.  **Optimisation des Requêtes Lourdes**
    *   **Action :** Pour les statistiques nécessitant de scanner de grandes quantités de données, utiliser des tables de résumé (materialized views ou tables mises à jour par triggers/cron jobs) comme `daily_stats_summary`.
    *   **Bénéfice :** Améliore les temps de chargement.
    *   **Statut :** [C] À ÉTUDIER / PLANIFIÉ pour stats avancées.
4.  **Gestion des États de Chargement et d'Erreur par Section**
    *   **Action :** Chaque section majeure du dashboard/stats devrait avoir son propre indicateur de chargement et une gestion des erreurs gracieuse.
    *   **Bénéfice :** Améliore l'UX.
    *   **Statut :** [P] À IMPLÉMENTER SYSTÉMATIQUEMENT.

**Base de Données & Backend (RPCs & Fonctions PostgreSQL) :**
1.  **Vérifier/Implémenter la Table `views` (maintenant `song_user_views`) :** [I]
    *   Structure confirmée (`song_id`, `user_id`, `viewed_at`, `duration_viewed`).
    *   Peuplage via trigger `trg_increment_song_view_count_from_song_user_views` sur `song_user_views` [I].
2.  **Implémenter les Triggers pour Compteurs Agrégés :** [I]
    *   `public.plays.plays_count` sur `public.songs` via `trg_increment_song_plays` sur `plays` [I].
    *   `public.songs.view_count` via `trg_increment_song_view_count_from_song_user_views` sur `song_user_views` [I].
    *   `public.songs.like_count`, `public.songs.dislike_count` via `trg_update_song_likes_dislikes` sur `likes` [I].
    *   `public.profiles.follower_count` via `trg_update_profile_follower_counts` sur `profile_followers` [I].
    *   `public.bands.follower_count` via `trg_update_band_follower_counts` sur `band_followers` [I].
3.  **Développer/Finaliser les RPCs (fonctions PostgreSQL) pour le Dashboard et les Statistiques :**
    *   **RPCs Existants à Confirmer/Adapter/Utiliser (pour Dashboard et/ou Stats) :**
        *   `get_user_overview_stats(p_user_id UUID, p_time_range TEXT)` : [C] (Concept défini, à implémenter pour Vue d'Ensemble Dashboard & Stats. `get_artist_stats` [I] peut servir de base).
        *   `get_activity_timeline(p_user_id UUID, p_metrics TEXT[], p_interval TEXT, p_days INTEGER)` : [C] (Concept défini, pour `MultiMetricTimeline.tsx` sur Dashboard & `ActivityTimeline.tsx` sur Stats).
        *   `get_genre_performance(p_user_id UUID)` : [C] (Concept défini, pour `GenrePerformance.tsx` sur Stats).
        *   `get_song_characteristics(p_song_id UUID)` : [C] (Utiliser `audio_analysis` [I], pour `MusicAttributesRadar.tsx` sur Stats).
        *   `get_top_content(p_user_id UUID, p_time_range TEXT, p_content_type TEXT, p_limit INTEGER)` : [C] (Peut s'inspirer de `get_trending_songs` [I] pour `TopContentTable.tsx` sur Stats et potentiellement une carte Dashboard).
        *   `get_audience_demographics(p_user_id UUID)` : [P] (Table `audience_demographics` [I] existe, RPC à affiner pour artiste, utiliser `get_playlist_details_for_view` [I] comme source potentielle, pour Stats).
        *   `get_trending_songs(limit_count integer, time_period_hours integer)`: [I] (Utilisable pour top contenu sur Stats/Dashboard).
        *   `get_playlist_details_for_view(p_playlist_id UUID, p_requesting_user_id UUID)`: [I] (Source pour stats de playlist et potentiellement démographie sur Stats).
        *   `get_artist_stats(artist_user_id uuid)`: [I] (Fournit des stats agrégées pour un artiste, base pour `get_user_overview_stats`).
    *   **Nouveaux RPCs à Créer (inspiré de `DASHBOARD_ENHANCEMENTS_TODO.md` et besoins des stats) :**
        *   **`get_dashboard_data(p_user_id UUID)`**: [C] (PRIORITÉ HAUTE) Fonction centrale pour agréger les données du dashboard (KPIs, listes récentes, données pour graphiques de base). Pourrait appeler d'autres RPCs plus granulaires ou faire ses propres agrégations.
        *   `get_follower_trend(p_user_id UUID, p_time_range TEXT)`: [C] (Pour `FollowerTrendChart.tsx` sur Stats, potentiellement un résumé sur Dashboard).
        *   `get_recent_activity_summary(p_user_id UUID)`: [C] (Pourrait regrouper les données pour `RecentlyWorkedOnCard`, `RecentPlaysOfMySongsCard`, `MySongsInPlaylistsCard`, `RecentFollowersCard`, `LatestCommentsCard` sur le Dashboard).
        *   `get_content_performance_analysis(p_user_id UUID)`: [C] (Pour analyse de contenu détaillée sur Stats).
        *   `get_engagement_metrics(p_user_id UUID)`: [C] (Pour section engagement social sur Stats, utiliser `song_comments` [I], `project_comments` [I], `likes` [I]).
        *   `get_geographic_distribution(p_user_id UUID)`: [C] (Pour `GeoHeatMap.tsx` sur Stats, utiliser `audience_demographics` [I]).
        *   `get_content_trend_stats(p_resource_type TEXT, p_resource_id UUID, p_period TEXT)`: [C] (Pour Stats).
        *   `get_user_content_performance_by_taxonomy(p_user_id UUID, p_taxonomy_type TEXT)`: [C] (Pour Stats).
        *   `get_content_comparison_data(p_resource_type TEXT, p_resource_id UUID)`: [C] (Pour Stats).
        *   RPCs pour admin stats: `get_platform_key_metrics()` [C], `get_platform_activity_summary(period)` [C].
        *   RPC pour suggestions: `get_dashboard_suggestions(p_user_id UUID)` [C] (pourrait retourner des données pour `HighlightSongSuggestionCard`, `CollaborationSuggestionCard`, `GenreFocusSuggestionCard`).

**Frontend (Pages `/dashboard` & `/stats` - `app/(authenticated)/dashboard/page.tsx`, `app/(authenticated)/stats/page.tsx`) :**
1.  **Dashboard (`/dashboard`):** [P]
    *   Câbler `EnhancedProfileCard` (ou `DashboardProfileCard`) et KPIs principaux à `get_dashboard_data` [C] ou `get_user_overview_stats` [C] / `get_artist_stats` [I].
    *   Câbler `MultiMetricTimeline.tsx` [P] à `get_activity_timeline` [C].
    *   Câbler `RecentlyWorkedOnCard` [P], `RecentPlaysOfMySongsCard` [P], `MySongsInPlaylistsCard` [P], `RecentFollowersCard` [P] aux RPCs correspondants (potentiellement `get_recent_activity_summary` [C]).
    *   Câbler `HighlightSongSuggestionCard` [C] à une logique de suggestion (RPC [C] ou client-side pour commencer).
2.  **Page Statistiques (`/stats`):** [P]
    *   Câbler `AnalyticsOverview.tsx` [P] (si différent du dashboard) à `get_user_overview_stats` [C] / `get_artist_stats` [I].
    *   Câbler `ActivityTimeline.tsx` [P] (si différent du dashboard) à `get_activity_timeline` [C].
    *   Câbler `TopContentTable.tsx` [P] à `get_top_content` [C] (ou `get_trending_songs` [I]).
    *   Câbler `GenrePerformance.tsx` [C] à `get_genre_performance` [C].
    *   Câbler `MusicAttributesRadar.tsx` [P] à `get_song_characteristics` [C] (utilisant `audio_analysis` [I]).
    *   Implémenter et câbler progressivement les autres composants (`GeoHeatMap.tsx` [C], `DemographicCharts.tsx` [C], `CompletionRateChart.tsx` [C], `ContentPerformanceComparisonChart.tsx` [C], `FollowerTrendChart.tsx` [C], etc.) avec les RPCs correspondants.
3.  Assurer la gestion robuste des différents niveaux d'accès (`analytics_level`) pour afficher/masquer les composants/données sur les deux pages. [P]
4.  Remplacer toutes les données simulées restantes par des appels réels. [P]

**Suggestions Proactives et Personnalisées (Principalement sur Dashboard) :** [C]
1.  **"Votre Morceau en Or" (`HighlightSongSuggestionCard`)** [C]
    *   **Logique :** Algorithme simple (peut être RPC [C] ou client-side initialement) pour calculer un score d'engagement (likes/écoute, commentaires/écoute) ou une croissance rapide récente (utiliser `songs.plays`, `songs.like_count`, `song_comments` [I]).
    *   **Statut :** [C] À IMPLÉMENTER.
2.  **"Opportunité de Collaboration" (`CollaborationSuggestionCard`)** [C]
    *   **Données :** `profiles` [I] (besoin de champs pour compétences, instruments, recherche collaboration), `songs` [I] (genres).
    *   **Logique :** Matching simple.
    *   **Statut :** [C] CONCEPTUEL / FUTUR.
3.  **"Genre Tendance pour Vous" (`GenreFocusSuggestionCard`)** [C]
    *   **Données :** `songs` [I] (genres de l'utilisateur), données de tendance globales (pourrait utiliser `get_trending_songs` [I] comme base).
    *   **Statut :** [C] CONCEPTUEL / FUTUR.
*   **Base de Données & Backend (RPCs & Fonctions PostgreSQL) :**
    1.  **Table `song_user_views` :** [I] (Structure et peuplement via trigger confirmés).
    2.  **Triggers pour Compteurs Agrégés :** [I] (Pour `songs.plays`, `songs.view_count`, `songs.like_count`, `songs.dislike_count`, `profiles.follower_count`, `bands.follower_count` sont implémentés).
    3.  **Développer les RPCs (fonctions PostgreSQL) :**
        *   **`get_user_overview_stats(p_user_id UUID, p_time_range TEXT)` :** [C] (Concept défini, utiliser `get_artist_stats` [I] comme base).
        *   **`get_activity_timeline(p_user_id UUID, p_metrics TEXT[], p_interval TEXT, p_days INTEGER)` :** [C] (Concept défini).
        *   **`get_top_user_content(p_user_id UUID, p_content_type TEXT, p_limit INT)` :** [C] (Utiliser `songs.plays` [I] ou `resource_like_dislike_stats` [I], s'inspirer de `get_trending_songs` [I]).
        *   Développer progressivement les RPCs pour les niveaux 'Extended' et 'Pro' (ex: `get_content_trend_stats` [C], `get_user_content_performance_by_taxonomy` [C], `get_audience_demographics` [P - table existe, RPC à affiner], `get_content_performance_analysis` [C], `get_engagement_metrics` [C], `get_geographic_distribution` [C - table `audience_demographics` existe], `get_content_comparison_data` [C]).
        *   Développer les RPCs pour les statistiques administrateur (`get_platform_key_metrics` [C], `get_platform_activity_summary` [C]).
*   **Frontend (Page `/stats` - `app/(authenticated)/stats/page.tsx`) :** [P]
    1.  Câbler `AnalyticsOverview.tsx` [P] à l'appel RPC `get_user_overview_stats` [C] ou `get_artist_stats` [I].
    2.  Câbler `ActivityTimeline.tsx` [P] à `get_activity_timeline` [C].
    3.  Câbler `TopContent.tsx` [P] à `get_top_user_content` [C] (ou `get_trending_songs` [I]).
    4.  Implémenter et câbler progressivement les autres composants (`GenreAnalysis.tsx` [C], `MusicMoodVisualization.tsx` [C], etc.) avec les RPCs correspondants une fois ceux-ci créés.
    5.  Assurer la gestion robuste des différents niveaux d'accès (`analytics_level`) pour afficher/masquer les composants/données. [P]
    6.  Remplacer toutes les données simulées restantes par des appels réels. [P]

## 4. Module de Suggestions (Intégré au Dashboard et autres sections) [C]
Améliorer l'expérience utilisateur via des recommandations personnalisées.

### 4.1. Types de Suggestions [C]
*   **Découverte de Contenu :** Morceaux (`songs` [I]), albums (`albums` [I]), artistes (`profiles` [I]), playlists (`playlists` [I]) basés sur l'historique d'écoute (`plays` [I]), likes (`likes` [I]), artistes suivis (`profile_followers` [I], `band_followers` [I]), genres/moods préférés. Contenu tendance (voir `get_trending_songs` [I]).
*   **Collaboration :** Utilisateurs pour collaboration (compétences complémentaires, genres partagés, flags "cherche collaboration" dans les `profiles` [I]).
*   **Amélioration de Contenu (Assistée par IA) :** Suggestions structurelles, harmoniques, thématiques (cf. panel IA dans `SongForm` [P]).

### 4.2. Approche d'Implémentation (Conceptuelle) [C]
*   **Sources de Données :** Activité utilisateur (`plays` [I], `likes` [I], `profile_followers` [I], `band_followers` [I], données de `profiles` [I]), métadonnées de contenu (`songs.genre`, `songs.mood`, `tags` [I]), préférences explicites.
*   **Algorithmes (Initiaux) :**
    *   Filtrage Basé sur le Contenu : Similitude de genre/mood.
    *   Popularité : Tendances globales ou par genre (utiliser `get_trending_songs` [I]).
*   **Algorithmes (Avancés - Futur) :**
    *   Filtrage Collaboratif : "Les utilisateurs qui ont aimé X ont aussi aimé Y". Nécessite des algorithmes plus complexes (factorisation de matrices, plus proches voisins).
    *   Embeddings Vectoriels : Représentation vectorielle des utilisateurs et des contenus (morceaux, artistes) dans un espace latent pour trouver des similarités. Pourraient être générés par un LLM ou d'autres modèles ML.
*   **Intégration LLM :** Prompter un LLM avec le profil de goût d'un utilisateur (dérivé de son activité et de ses données de profil) et un catalogue de contenu disponible (potentiellement via RAG depuis une base de données vectorielle de métadonnées de contenu).
*   **Intégration UI :** Sections "Recommandé pour vous" sur le tableau de bord ou la page de découverte. Suggestions sur les pages artiste/morceau/album ("Artistes similaires", "Les auditeurs ont aussi aimé"). "Hub de collaboration" dédié ou suggestions dans les pages de groupe/profil.

## 5. Système de Promotion (Conceptuel - Futur) [C]
Permet aux utilisateurs d'augmenter la visibilité de leur contenu en utilisant des "coins" ou des crédits (liés aux plans d'abonnement ou à des packs achetables).

*   **Mécanisme :** Les utilisateurs dépensent des coins pour "booster" un morceau, un album ou un profil pendant une certaine durée. Le contenu boosté obtient un meilleur placement dans les flux de découverte, les résultats de recherche ou les listes de suggestions.
*   **Base de Données :** Table `promoted_content` [C] pour suivre les promotions actives. Colonne `profiles.coins_balance` [I - existe, via `user_details.coins_balance`] pour gérer la monnaie promotionnelle de l'utilisateur. La table `creation_costs` [I] existe et pourrait être étendue ou servir d'inspiration pour les coûts de promotion.
*   **UI :** Interface permettant aux utilisateurs de sélectionner le contenu à promouvoir et de choisir un pack/une durée de promotion. Indication claire du contenu promu dans l'interface utilisateur.

## 6. Système de Suggestions pour Créateurs (Conceptuel)

Ce module vise à fournir des informations et des outils exploitables aux créateurs pour améliorer leur contenu et leur portée.

### 6.1. Suggestions de Découverte de Contenu

*   Suggérer des mots-clés ou des tags tendance à ajouter au contenu.
*   Identifier des artistes ou du contenu similaires pour une collaboration potentielle ou une promotion croisée.
*   Mettre en évidence des niches ou des genres mal desservis avec un fort engagement.

### 6.2. Suggestions de Collaboration

*   Mettre en relation des artistes aux styles ou aux bases de fans complémentaires.
*   Faciliter les demandes de featuring ou les opportunités de remix.

### 6.3. Amélioration de Contenu Assistée par IA

*   Analyser les morceaux téléversés pour la qualité de production (ex: volume sonore, clarté) et suggérer des améliorations.
*   Fournir des retours sur la structure ou l'arrangement des morceaux basés sur des modèles à succès.
*   (Futur) Générer des mélodies alternatives, des harmonies ou des idées de paroles.

### 6.4. Approches d'Implémentation

*   **Sources de Données**: Données d'interaction utilisateur, métadonnées de contenu, fonctionnalités d'analyse audio, données de tendances externes.
*   **Algorithmes**:
    *   Systèmes simples basés sur des règles pour les suggestions de base.
    *   Clustering et analyse de similarité pour la collaboration/découverte.
    *   Modèles d'apprentissage automatique pour des informations prédictives (ex: prédire l'augmentation de l'engagement suite à l'ajout de certains tags).
    *   Intégration de LLM pour générer des retours textuels ou des idées créatives.

## 7. Système de Promotion (Conceptuel)

Un système pour permettre aux créateurs de promouvoir leur contenu au sein de la plateforme.

### 7.1. Mécanisme

*   Les créateurs gagnent/achètent des "Mouvik Coins" ou des "Crédits de Promotion".
*   Dépenser des crédits pour augmenter la visibilité des morceaux/albums/profils :
    *   Emplacements mis en avant sur les pages de découverte.
    *   Meilleur classement dans les résultats de recherche pour une durée limitée.
    *   Placements de type publicitaire ciblés vers des démographies d'utilisateurs spécifiques (si éthique et respectueux de la vie privée).

### 7.2. Obtention des Crédits

*   Engagement sur la plateforme (ex: téléversements réguliers, fort engagement sur le contenu).
*   Achèvement de tutoriels ou de défis guidés par la plateforme.
*   Achat direct.

### 7.3. Considérations

*   Équilibrer la promotion payante avec la découverte organique pour éviter un sentiment de "payer pour gagner".
*   Transparence dans la manière dont le contenu promu est affiché.
*   Prévention des abus du système.

## 8. Modèles de Données et Métriques Clés (Synthèse)

*   **Métriques Utilisateur Clés:**
    *   Total Écoutes, Auditeurs Uniques, Likes, Commentaires, Followers.
    *   Taux d'Engagement (Likes + Commentaires / Écoutes ou Vues).
    *   Performance par Genre/Humeur.
    *   Démographie de l'Audience (Âge, Genre, Pays).
*   **Métriques Contenu Clés:**
    *   Écoutes, Vues, Likes, Commentaires par morceau/album.
    *   Taux de Complétion des Écoutes.
    *   Caractéristiques Audio (Énergie, Dansabilité, Valence).
*   **Tables Impliquées (principales):**
    *   `plays`, `song_user_views`, `likes`, `comments`, `profile_followers`, `band_followers` (Interactions).
    *   `songs`, `albums`, `playlists` (Métadonnées Contenu).
    *   `audio_analysis` (Caractéristiques Audio).
    *   `audience_demographics` (Démographie Agrégée).
    *   `profiles`, `bands` (Entités Créateur).

## 9. Intégration avec d'Autres Composants Applicatifs

*   **Profils Utilisateur/Artiste**: Affichage de statistiques de base directement sur les pages de profil.
*   **Pages Morceau/Album**: Affichage des compteurs d'écoutes/likes, potentiellement des mini-graphiques de tendance.
*   **Système de Notifications**: Alertes pour les créateurs sur les nouveaux jalons statistiques ou les interactions significatives.
*   **Moteur de Recherche**: Utilisation des données de popularité pour pondérer les résultats de recherche.

## 10. Évolutions Futures Envisagées (Issues de `analytics_system.md` et autres)

Au-delà du plan de développement immédiat, les évolutions suivantes pourraient être considérées pour enrichir le système d'analyse et de recommandations :

*   **Tableaux de Bord Personnalisables** : Permettre aux utilisateurs de choisir les métriques et les visualisations qu'ils souhaitent voir.
*   **Alertes et Notifications Proactives** : Informer les utilisateurs des changements significatifs ou des opportunités.
*   **Tests A/B pour le Contenu** : Permettre aux créateurs de tester différentes versions de leurs morceaux ou de leurs métadonnées.
*   **Analyses Comparatives Approfondies** : Se comparer à des artistes similaires ou à des moyennes de la plateforme de manière anonymisée.
*   **Intégration d'Objectifs et Suivi de Progression** : Permettre aux créateurs de définir des objectifs et de suivre leur progression.
*   **Export de Données Amélioré** : Plus d'options pour exporter les données brutes et les rapports.
*   **Analyse de Sentiment sur les Commentaires** : Utiliser le NLP pour comprendre le ton général des retours utilisateurs.

1.  **Analyse Prédictive** : Utilisation de modèles pour prédire les tendances de popularité, le potentiel de croissance d'un contenu ou d'un artiste.
2.  **Segmentation d'Audience Avancée** : Analyse plus fine des différents groupes d'auditeurs et de leurs préférences.
3.  **Analyse Comparative Détaillée** : Outils permettant aux artistes de se comparer de manière anonymisée à des artistes similaires ou à des moyennes de la plateforme.
4.  **Analyse de Sentiment** : Traitement automatique du langage naturel (NLP) sur les commentaires pour évaluer la réception du contenu.
5.  **Intégration avec des Services Externes** : Possibilité de connecter des données depuis d'autres plateformes (ex: Spotify for Artists, Apple Music for Artists) pour une vue consolidée (si techniquement faisable et autorisé).
6.  **Rapports Automatisés et Personnalisés** : Génération de rapports périodiques (hebdomadaires, mensuels) envoyés par email aux créateurs.
7.  **Alertes Personnalisées** : Notifications basées sur des seuils ou des événements significatifs définis par l'utilisateur (ex: "Votre morceau X a atteint 1000 écoutes").
8.  **Gamification des Statistiques** : Introduction d'éléments ludiques, de badges ou de challenges basés sur l'atteinte de certains paliers statistiques.

## 11. Prochaines Étapes Générales du Module
Développement itératif, avec une priorisation possible comme suit (statuts mis à jour) :

**Priorité Haute (À implémenter en premier) :**
1.  **Centralisation de la Récupération des Données :** Amélioration des fonctions RPC existantes (ex: `get_user_overview_stats` ou équivalent) pour une récupération efficace des données de base.
2.  **Amélioration des Informations d'Activité Récente sur le Dashboard Principal :** Derniers commentaires, projets modifiés, nouveaux followers.
3.  **Graphique d'Activité Multi-Métrique (pour `/stats`) :** Remplacer/améliorer les graphiques actuels par une visualisation plus riche via `MultiMetricTimeline.tsx` et `get_activity_timeline_data`.
4.  Solidifier les statistiques de base pour les utilisateurs (Niveau 'Basic' sur la page `/stats`) en implémentant les RPCs et en câblant le frontend.

**Priorité Moyenne :**
1.  **Statistiques d'Engagement Détaillées :** Ajout de mini-stats aux listes de morceaux/albums récents.
2.  **Top Morceaux avec Métriques Détaillées (pour `/stats`) :** `TopContentTable.tsx`.
3.  Développer les statistiques de base pour l'administration.
4.  Implémenter les premiers algorithmes de suggestion simples (ex: basé sur la popularité par genre).

**Priorité Basse (Fonctionnalités avancées) :**
1.  **Analyse d'Audience (pour `/stats` niveaux supérieurs) :** `GeoHeatMap.tsx`, `DemographicCharts.tsx`.
2.  Explorer les fonctionnalités de promotion si jugé prioritaire.
3.  Itérer sur les statistiques avancées (Niveau 'Pro') et les suggestions plus complexes (filtrage collaboratif, LLM).
