import { createSupabaseServerClient } from "@/lib/supabase/server"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Plus, Calendar, MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"

export default async function TodosPage() {
  const supabase = createSupabaseServerClient()

  const { data: todos } = await supabase.from("todos").select("*").order("created_at", { ascending: false })

  const formatDate = (date: string | null) => {
    if (!date) return "Aucune date"
    return format(new Date(date), "dd MMMM yyyy", { locale: fr })
  }

  // Filter out error objects and only render valid todos
  const validTodos = Array.isArray(todos) ? todos.filter(todo => todo && typeof todo === 'object' && 'id' in todo && 'title' in todo) : []

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Ma Todo List</h1>
          <p className="text-muted-foreground">Gérez vos tâches et projets à venir</p>
        </div>
        <Button
          size="lg"
          className="bg-primary text-white rounded-xl font-semibold shadow-md hover:bg-primary/90 transition-colors w-full max-w-xs"
        >
          <Plus className="mr-2 h-5 w-5" />
          Créer une tâche
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Toutes les tâches</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {validTodos.map(todo => (
              <div key={todo.id} className="flex items-center justify-between rounded-lg border p-4">
                <div className="flex items-center gap-3">
                  <Checkbox checked={todo.is_completed} />
                  <div>
                    <div className={`font-medium ${todo.is_completed ? "line-through text-muted-foreground" : ""}`}>
                      {todo.title}
                    </div>
                    {todo.description && <div className="text-sm text-muted-foreground">{todo.description}</div>}
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  {todo.due_date && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(todo.due_date)}</span>
                    </div>
                  )}
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
            {(!validTodos || validTodos.length === 0) && (
              <div className="py-6 text-center text-muted-foreground">
                Aucune tâche trouvée. Créez votre première tâche !
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
