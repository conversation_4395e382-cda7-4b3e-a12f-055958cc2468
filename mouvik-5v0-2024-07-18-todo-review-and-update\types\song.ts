import { SupabaseClient } from '@supabase/supabase-js';

export type AIContentOrigin = '100%_ia' | 'hybrid' | 'full_human';
export type VisibilityType = 'public' | 'private' | 'unlisted';

export interface SongFormValues {
  // Identifiants
  id?: string;
  creator_user_id: string;
  band_id?: string | null;
  
  // Métadonnées de base
  title: string;
  description: string;
  
  // Classification
  genres: string[];
  moods: string[];
  tags: string[];
  
  // Fichiers média
  cover_art_url: string | null;
  audio_file_path: string;
  audio_url?: string; // URL générée à partir de audio_file_path
  
  // Métadonnées musicales
  duration_ms: number;
  bpm: number | null;
  musical_key: string;
  time_signature: string;
  
  // Contenu
  lyrics: string;
  chords: string;
  chords_diagrams?: Record<string, unknown>; // JSONB pour les diagrammes d'accords
  structure: string;
  lyrics_language?: string;
  themes?: string[];
  instruments?: string[];
  isrc_code?: string | null;
  release_date?: string; // Will be validated as date by Zod
  artist_name?: string;
  album_id?: string | null;
  is_favorite?: boolean;
  is_incomplete?: boolean;
  is_cover?: boolean;
  is_instrumental?: boolean;
  copyright_notice?: string | null;
  publisher_name?: string | null;
  licensing_info?: string | null;
  language?: string; // General song language
  parental_advisory?: string; // e.g., 'none', 'explicit', 'clean'
  recording_date?: string | null; // ISO Date string
  mixing_engineer?: string | null;
  mastering_engineer?: string | null;
  artwork_credits?: string | null;
  tablature?: string | null;
  performance_notes?: string | null;
  
  // Visibilité et statut
  is_public: boolean;
  is_explicit: boolean;
  visibility: VisibilityType;
  status: 'draft' | 'published' | 'archived';
  is_archived: boolean;
  
  // Métadonnées IA
  ai_content_origin?: AIContentOrigin;
  ai_assist_percentage?: number; // 0-100
  
  // Informations studio
  studio_name?: string;
  
  // Métadonnées système
  created_at: string;
  updated_at: string;
  
  // Champs hérités (à nettoyer éventuellement)
  editor_data?: Record<string, unknown>;
  song_versions?: any[]; // Placeholder, define specific type later if possible
  audio_file_versions?: any[]; // Placeholder, define specific type later if possible
  external_links?: Array<{ type: string; url: string }>;
  custom_fields?: Record<string, any>; // For user-defined fields
  lyrics_sync_data?: any | null; // For LRC or other sync formats
  spotify_url?: string;
  youtube_url?: string;
  version_notes?: string;
  is_major_version?: boolean;
  allow_comments?: boolean;
  allow_downloads?: boolean;
  // Ensure these are here, they are in schema
  // themes?: string[]; // Already present
  // instruments?: string[]; // Already present
  privacy_settings?: { 
    allow_embedding: boolean;
    allow_download: boolean;
  };
  collaborators?: any[]; // Array of collaborators, specific structure TBD
  split_sheet?: any | null; // Placeholder for split sheet data structure or link
  last_played_at?: string | null; // ISO date string
  play_count?: number;
  rating_average?: number;
  rating_count?: number;
  comments_count?: number;
  shares_count?: number;
  downloads_count?: number;
  stream_sources?: any[];
  purchase_links?: any[];
  related_songs?: string[];
  song_story?: string | null;
  production_notes?: string | null;
  gear_used?: string[];
  sample_credits?: any[];
  remix_info?: string | null;
  version_of_song_id?: string | null;
}

export interface Album {
  id: string;
  title: string;
  cover_art_url?: string | null;
  description?: string;
  release_date?: string;
  is_public: boolean;
  creator_user_id: string;
  created_at: string;
  updated_at: string;
}

export interface SongFormProps {
  mode: 'create' | 'edit';
  initialValues?: Partial<SongFormValues>;
  onFormSubmit: (data: SongFormValues, status?: 'draft' | 'published') => Promise<void>;
  isSubmitting: boolean;
  albums: Album[];
  isLoadingAlbums: boolean;
  supabaseClient: SupabaseClient;
  currentUserId: string; // Maintenant requis car essentiel
  id?: string;
  title?: string;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export interface SongFormHandle {
  submitForm: () => Promise<boolean>;
  resetForm: () => void;
  getValues: () => SongFormValues;
  triggerValidation: () => Promise<boolean>;
}

export interface LocalChord {
  id: string;
  chord: string;
  position: number;
  section: string;
  instrument?: string;
  diagram?: string;
}

export interface AudioAnalysis {
  duration: number;
  bpm?: number;
  key?: string;
  loudness?: number;
  tempo_confidence?: number;
  time_signature?: number;
  analysis_url?: string;
}
