# Dossier de Conception – Nouvelles Rubriques

Ce dossier regroupe toute la documentation professionnelle, les bonnes méthodes et les TODOs pour la mise en place des deux nouvelles rubriques majeures :
- **Découvrir & Playlists (multi-plateforme)**
- **Communauté (mur, groupes, hashtags, discussions)**

---

## 1. Découvrir & Playlists (multi-plateforme)

### Objectif Détaillé
Créer un hub musical centralisé permettant aux utilisateurs d'explorer, d'écouter, d'importer via URL (Spotify, Suno, Udio, Mouvik, YouTube, SoundCloud...) et de créer/partager des playlists mixtes (morceaux AI Composer internes + liens externes). L'objectif est de fusionner l'écosystème créatif interne avec la découverte musicale externe, tout en assurant une gestion cohérente des métadonnées et des tags.

### Fonctions Clés Enrichies
- **Ajout/Import Multi-Source :**
  - Via URL : Détection automatique de la plateforme (Spotify, Suno, etc.).
  - Via création interne : Utilisation des morceaux existants (`my_songs`).
- **Extraction Automatique & Validation :**
  - Récupération des métadonnées externes (titre, artiste, cover, genre, durée via API/scraping).
  - Affichage d'une preview à l'utilisateur pour validation.
- **Mapping Intelligent Tags/Genres :**
  - Suggestion de tags basés sur les métadonnées extraites.
  - Mapping vers la taxonomie interne (`tags` table) avec auto-complétion et validation utilisateur pour garantir la cohérence.
  - Ajout automatique du tag `platform:[nom_plateforme]`.
- **Gestion Playlists Mixtes :**
  - Création/édition/suppression/partage de playlists personnelles ou de groupe (`bands`).
  - Possibilité d'ajouter des `music_resources` internes (`my_songs` via leur ID) ou externes (liens).
- **Feed Unifié & Filtrable :**
  - Affichage de toutes les `music_resources` (internes/externes).
  - Filtres avancés : plateforme, tags, genre, style, humeur, utilisateur (`profiles.id`), groupe (`bands.id`), date.
  - Recherche textuelle (titre, artiste, tags).
- **Badges & UI Adaptée :**
  - Affichage clair de la provenance (logo/badge AI Composer, Spotify, Suno...).
  - Player adapté (embed Spotify/YouTube, player interne pour AI Composer).
- **Rattachement Contextuel :**
  - Lier une `music_resource` à un ou plusieurs albums (`albums.id`), groupes (`bands.id`), ou profils (`profiles.id` via `added_by`).
- **Génération Activité :**
  - Chaque ajout/import/partage crée une entrée dans la table `activity` visible sur le mur.

### Tables Principales & Relations
- **`music_resources` :**
  - `id` (PK)
  - `type`: 'ai_composer', 'spotify', 'suno', ... (Indexé)
  - `internal_song_id`: FK vers `my_songs.id` (si type='ai_composer', NULL sinon)
  - `external_url`: TEXT (si type!='ai_composer', NULL sinon, indexé/unique)
  - `title`: TEXT
  - `artist`: TEXT (optionnel)
  - `cover_url`: TEXT (optionnel)
  - `duration_ms`: INTEGER (optionnel)
  - `platform_data`: JSONB (métadonnées brutes spécifiques, ex: `spotify_track_id`)
  - `added_by`: FK vers `profiles.id` (NOT NULL)
  - `created_at`, `updated_at`: TIMESTAMPTZ
  - `is_public`: BOOLEAN (défaut: true)
- **`tags` :**
  - `id` (PK)
  - `label`: TEXT (unique, normalisé)
  - `type`: 'genre', 'style', 'mood', 'platform', 'custom'
- **`resource_tags` :** (Table de liaison Many-to-Many)
  - `resource_id`: FK vers `music_resources.id`
  - `tag_id`: FK vers `tags.id`
- **`playlists` :**
  - `id` (PK)
  - `name`: TEXT
  - `description`: TEXT (optionnel)
  - `owner_id`: FK vers `profiles.id`
  - `band_id`: FK vers `bands.id` (optionnel, pour playlists de groupe)
  - `is_public`: BOOLEAN
  - `created_at`, `updated_at`
- **`playlist_resources` :** (Table de liaison Many-to-Many)
  - `playlist_id`: FK vers `playlists.id`
  - `resource_id`: FK vers `music_resources.id`
  - `order`: INTEGER (position dans la playlist)
- **`activity` :** (Voir section Communauté)

### Workflow d'Ajout/Import Détaillé
1.  **Saisie :** L'utilisateur colle une URL OU sélectionne un morceau depuis `my_songs`.
2.  **Analyse URL (si externe) :** Détection plateforme, appel API/service d'extraction.
3.  **Pré-remplissage Formulaire :** Affichage titre, artiste, cover extraits. Champ URL pré-rempli.
4.  **Suggestion Tags :** Analyse métadonnées -> suggestion tags internes (ex: "Pop", "Chill"). Ajout auto tag `platform:xxx`.
5.  **Validation Utilisateur :**
    - Confirme/modifie titre, artiste.
    - Valide/ajoute/supprime tags via auto-complétion sur la table `tags`.
    - (Optionnel) Rattache à un album (`albums.id`), un groupe (`bands.id`).
    - Définit `is_public`.
6.  **Création BDD :**
    - Vérification unicité URL externe.
    - Insertion dans `music_resources` (`internal_song_id` ou `external_url` renseigné).
    - Insertion des liens dans `resource_tags`.
7.  **Génération Activité :** Création d'une entrée dans `activity` (`type='add_resource'`, `resource_id`, `user_id`).

### TODOs Priorisés
- [ ] **(P0)** Modéliser/Créer tables `music_resources`, `tags`, `resource_tags`, `playlists`, `playlist_resources`.
- [ ] **(P0)** Définir taxonomie `tags` initiale (genres, styles, plateformes).
- [ ] **(P1)** Développer service d'extraction pour Spotify (API).
- [ ] **(P1)** UI formulaire d'ajout/import (détection URL, preview, auto-complétion tags).
- [ ] **(P1)** API backend pour `POST /resources` (logique d'import/validation).
- [ ] **(P2)** Feed `Découvrir` basique (affichage `music_resources`).
- [ ] **(P2)** Développer service d'extraction pour Suno/Udio (scraping/API si dispo).
- [ ] **(P2)** Badges de provenance et player interne/embed simple.
- [ ] **(P3)** Filtres avancés & recherche sur le feed.
- [ ] **(P3)** Gestion complète des playlists (UI, API).
- [ ] **(P3)** Système de modération/suggestion pour les nouveaux `tags`.
- [ ] **(P3)** Tests unitaires/intégration (extraction, mapping, API).

---

## 2. Communauté (mur, groupes, hashtags, discussions)

### Objectif Détaillé
Fédérer les utilisateurs autour de leurs créations et de leurs centres d'intérêt musicaux. Fournir un espace dynamique pour le partage (morceaux AI Composer, ressources externes importées), la discussion (commentaires, débats), la collaboration (groupes/bands), et la découverte via hashtags et activité des membres suivis.

### Fonctions Clés Enrichies
- **Mur d'Activité Centralisé :**
  - Agrégation des actions : ajout `music_resource`, création `album`/`band`, posts directs, commentaires, likes, partages.
  - Filtrable par : type d'activité, utilisateur (`profiles.id`), groupe (`bands.id`), hashtag.
  - Affichage contextuel (preview ressource, texte du post, auteur, date).
- **Groupes/Bands Collaboratifs :**
  - Hérite de la structure `bands` existante (`id`, `name`, `description`, `owner_id`, `avatar_url`, `cover_url`).
  - Gestion des membres (`band_members` : `user_id`, `role`, `status`).
  - Permissions basées sur le rôle (`admin`, `member`).
  - Espace de discussion propre au groupe (posts filtrés par `band_id`).
  - Rattachement de `music_resources` et `albums` au groupe.
- **Hashtags & Trending Topics :**
  - Possibilité d'ajouter des #hashtags dans les posts, descriptions (`music_resources`, `albums`, `bands`).
  - Page dédiée par hashtag listant tous les contenus associés.
  - Calcul potentiel de trending topics.
- **Discussions & Interactions :**
  - Commentaires sous les posts `activity`.
  - Système de likes sur les posts et commentaires.
  - Partage d'une activité/ressource sur le mur ou en externe.
- **Notifications & Modération :**
  - Notifications pour mentions, commentaires, likes, invitations de groupe.
  - Outils de modération (signalement, masquage, suppression) pour posts/commentaires.

### Tables Principales & Relations
- **`activity` :**
  - `id` (PK)
  - `type`: 'add_resource', 'create_album', 'create_band', 'post_text', 'comment', 'like', 'share', ... (Indexé)
  - `user_id`: FK vers `profiles.id` (auteur de l'action)
  - `resource_id`: FK vers `music_resources.id` (optionnel, si lié à une ressource)
  - `album_id`: FK vers `albums.id` (optionnel)
  - `band_id`: FK vers `bands.id` (optionnel, contexte du groupe ou action de groupe)
  - `parent_activity_id`: FK vers `activity.id` (pour les commentaires/réponses)
  - `content`: TEXT (contenu du post textuel ou commentaire)
  - `created_at`: TIMESTAMPTZ
- **`bands` :** (Table existante)
  - `id`, `name`, `description`, `owner_id` (FK `profiles.id`), `avatar_url`, `cover_url`, `is_public`, `invitation_code`, `created_at`, `updated_at`.
- **`band_members` :** (Table existante)
  - `id`, `band_id` (FK `bands.id`), `user_id` (FK `profiles.id`), `role` ('admin', 'member', 'invited'), `status` ('active', 'pending', 'banned'), `joined_at`.
- **`hashtags` :**
  - `id` (PK)
  - `label`: TEXT (unique, normalisé, sans '#')
- **`activity_hashtags` :** (Table de liaison Many-to-Many)
  - `activity_id`: FK vers `activity.id`
  - `hashtag_id`: FK vers `hashtags.id`
- **(Optionnel) `entity_hashtags` :** (Pour tagger albums, bands directement)
  - `entity_type`: 'album', 'band'
  - `entity_id`: UUID
  - `hashtag_id`: FK vers `hashtags.id`
- **`likes` :**
  - `activity_id`: FK vers `activity.id`
  - `user_id`: FK vers `profiles.id`
  - `created_at`
  - (PK composite : `activity_id`, `user_id`)

### Workflow d'Activité Typique (Post Texte)
1.  **Création :** L'utilisateur rédige un post sur le mur général ou celui d'un groupe.
2.  **Analyse Contenu :** Détection des #hashtags.
3.  **Insertion BDD :**
    - Création entrée dans `activity` (`type='post_text'`, `user_id`, `content`, `band_id` si post de groupe).
    - Si hashtags détectés : recherche/création dans `hashtags`, insertion liens dans `activity_hashtags`.
4.  **Affichage :** Le post apparaît sur les murs concernés (général, groupe, page hashtag).
5.  **Interaction :** D'autres utilisateurs ajoutent des `likes` ou des `activity` de `type='comment'` avec `parent_activity_id` pointant vers le post initial.

### TODOs Priorisés
- [ ] **(P0)** Finaliser/Valider modèles BDD `activity`, `hashtags`, `likes`, et liaisons.
- [ ] **(P1)** API backend pour `POST /activity` (création post texte, détection hashtag).
- [ ] **(P1)** UI Mur d'activité basique (affichage posts, auteur, date).
- [ ] **(P1)** Intégrer la génération d'activité pour `add_resource`.
- [ ] **(P2)** API backend pour commentaires (`POST /activity` avec `parent_activity_id`).
- [ ] **(P2)** API backend pour likes (`POST /likes`, `DELETE /likes`).
- [ ] **(P2)** UI Affichage commentaires & likes.
- [ ] **(P2)** Système de hashtags (création liens, page par hashtag).
- [ ] **(P3)** Filtres sur le mur d'activité.
- [ ] **(P3)** Gestion complète des `bands` (UI création/gestion membres).
- [ ] **(P3)** Système de notifications basique.
- [ ] **(P3)** Outils de modération simples.

---

## 3. Bonnes pratiques chef de projet (Maintenues)
- Centraliser la taxonomie (tags, genres, styles, hashtags).
- Toujours prévoir l’extensibilité (nouvelles plateformes, types d'activité).
- Automatiser extraction/mapping, mais toujours valider côté utilisateur (pour tags/infos clés).
- Badge de provenance systématique pour les ressources.
- Historique d’activité clair et traçable.
- Documentation BDD/API/Workflows à jour.
- Process de validation/modération pour tags, posts, groupes.

---

**Ce dossier est collaboratif : ajoutez vos idées, questions, schémas, et TODOs !**

---

## Fichiers recommandés à ajouter dans ce dossier (Maintenus)
- `/MODELES.md` : schémas BDD finaux, exemples JSON
- `/WORKFLOWS.md` : diagrammes de flux (import, post, commentaire)
- `/API.md` : endpoints détaillés (requêtes, réponses)
- `/UI_WIREFRAMES.md` : maquettes des écrans clés
- `/TODO.md` : backlog JIRA/Trello, priorités affinées

---

> **À partager à toute l’équipe pour itération et enrichissement.**
