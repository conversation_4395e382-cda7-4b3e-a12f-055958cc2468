export const genreOptions = [
  { value: "acoustic", label: "Acoustique" },
  { value: "alternative-rock", label: "Rock Alternatif" },
  { value: "ambient", label: "Ambient" },
  { value: "blues", label: "Blues" },
  { value: "classical", label: "Classique" },
  { value: "country", label: "Country" },
  { value: "dance", label: "Dance" },
  { value: "electronic", label: "Électronique" },
  { value: "experimental", label: "Expérimental" },
  { value: "folk", label: "Folk" },
  { value: "funk", label: "Funk" },
  { value: "hip-hop", label: "Hip-Hop/Rap" },
  { value: "house", label: "House" },
  { value: "indie-pop", label: "Indie Pop" },
  { value: "indie-rock", label: "Indie Rock" },
  { value: "jazz", label: "Jazz" },
  { value: "latin", label: "Latino" },
  { value: "metal", label: "Metal" },
  { value: "pop", label: "Pop" },
  { value: "punk", label: "Punk" },
  { value: "rnb", label: "R&B/Soul" },
  { value: "reggae", label: "Reggae" },
  { value: "rock", label: "Rock" },
  { value: "soundtrack", label: "Bande Originale" },
  { value: "techno", label: "Techno" },
  { value: "world", label: "Musique du Monde" },
  { value: "other", label: "Autre" },
];

export const moodOptions = [
  { value: "acoustic", label: "Acoustique" },
  { value: "aggressive", label: "Agressif" },
  { value: "ambient", label: "Ambient" },
  { value: "angry", label: "En colère" },
  { value: "anthemic", label: "Hymne" },
  { value: "atmospheric", label: "Atmosphérique" },
  { value: "beautiful", label: "Beau" },
  { value: "bittersweet", label: "Doux-amer" },
  { value: "bouncy", label: "Rebondissant" },
  { value: "bright", label: "Lumineux" },
  { value: "calm", label: "Calme" },
  { value: "carefree", label: "Insouciant" },
  { value: "celebratory", label: "Festif" },
  { value: "chill", label: "Détendu" },
  { value: "cinematic", label: "Cinématographique" },
  { value: "cold", label: "Froid" },
  { value: "comedic", label: "Comique" },
  { value: "confident", label: "Confiant" },
  { value: "confused", label: "Confus" },
  { value: "cool", label: "Cool" },
  { value: "corporate", label: "Corporate" },
  { value: "creepy", label: "Effrayant" },
  { value: "cruising", label: "Balade" },
  { value: "curious", label: "Curieux" },
  { value: "dark", label: "Sombre" },
  { value: "delicate", label: "Délicat" },
  { value: "determined", label: "Déterminé" },
  { value: "dirty", label: "Sale" },
  { value: "disturbing", label: "Perturbant" },
  { value: "dramatic", label: "Dramatique" },
  { value: "dreamy", label: "Rêveur" },
  { value: "driving", label: "Entraînant" },
  { value: "eerie", label: "Étrange" },
  { value: "elegant", label: "Élégant" },
  { value: "emotional", label: "Émotionnel" },
  { value: "empowering", label: "Motivant" },
  { value: "energetic", label: "Énergique" },
  { value: "epic", label: "Épique" },
  { value: "euphoric", label: "Euphorique" },
  { value: "experimental", label: "Expérimental" },
  { value: "fancy", label: "Chic" },
  { value: "fearful", label: "Craintif" },
  { value: "fierce", label: "Féroce" },
  { value: "floating", label: "Flottant" },
  { value: "fun", label: "Amusant" },
  { value: "funky", label: "Funky" },
  { value: "futuristic", label: "Futuriste" },
  { value: "gentle", label: "Doux" },
  { value: "glamorous", label: "Glamour" },
  { value: "gleeful", label: "Joyeux" },
  { value: "gloomy", label: "Morose" },
  { value: "graceful", label: "Gracieux" },
  { value: "grateful", label: "Reconnaissant" },
  { value: "groovy", label: "Groovy" },
  { value: "happy", label: "Heureux" },
  { value: "haunting", label: "Obsédant" },
  { value: "heavy", label: "Lourd" },
  { value: "hopeful", label: "Plein d'espoir" },
  { value: "humorous", label: "Humoristique" },
  { value: "hypnotic", label: "Hypnotique" },
  { value: "industrial", label: "Industriel" },
  { value: "inspirational", label: "Inspirant" },
  { value: "intense", label: "Intense" },
  { value: "intimate", label: "Intime" },
  { value: "introspective", label: "Introspectif" },
  { value: "joyful", label: "Jovial" },
  { value: "laid back", label: "Décontracté" },
  { value: "light", label: "Léger" },
  { value: "lively", label: "Vif" },
  { value: "lonely", label: "Solitaire" },
  { value: "longing", label: "Nostalgique" },
  { value: "loud", label: "Fort" },
  { value: "love", label: "Amour" },
  { value: "luxurious", label: "Luxueux" },
  { value: "meditative", label: "Méditatif" },
  { value: "melancholic", label: "Mélancolique" },
  { value: "mellow", label: "Doux" },
  { value: "minimal", label: "Minimaliste" },
  { value: "mischievous", label: "Malicieux" },
  { value: "mysterious", label: "Mystérieux" },
  { value: "nostalgic", label: "Nostalgique" },
  { value: "ominous", label: "Menacant" },
  { value: "optimistic", label: "Optimiste" },
  { value: "passionate", label: "Passionné" },
  { value: "pastoral", label: "Pastoral" },
  { value: "peaceful", label: "Paisible" },
  { value: "playful", label: "Ludique" },
  { value: "positive", label: "Positif" },
  { value: "powerful", label: "Puissant" },
  { value: "pulsing", label: "Pulsant" },
  { value: "quirky", label: "Excentrique" },
  { value: "rebellious", label: "Rebelle" },
  { value: "reflective", label: "Réfléchi" },
  { value: "relaxed", label: "Relaxé" },
  { value: "retro", label: "Rétro" },
  { value: "reverent", label: "Révérencieux" },
  { value: "romantic", label: "Romantique" },
  { value: "sad", label: "Triste" },
  { value: "sassy", label: "Impertinent" },
  { value: "scary", label: "Effrayant" },
  { value: "searching", label: "En quête" },
  { value: "serious", label: "Sérieux" },
  { value: "sexy", label: "Sexy" },
  { value: "smooth", label: "Doux" },
  { value: "solemn", label: "Solennel" },
  { value: "sophisticated", label: "Sophistiqué" },
  { value: "spacey", label: "Spatial" },
  { value: "spiritual", label: "Spirituel" },
  { value: "strange", label: "Étrange" },
  { value: "strong", label: "Fort" },
  { value: "summer", label: "Estival" },
  { value: "suspenseful", label: "À suspense" },
  { value: "sweet", label: "Doux" },
  { value: "tense", label: "Tendu" },
  { value: "thoughtful", label: "Pensif" },
  { value: "touching", label: "Touchant" },
  { value: "trippy", label: "Psychédélique" },
  { value: "triumphant", label: "Triomphant" },
  { value: "troubled", label: "Troublé" },
  { value: "uplifting", label: "Édifiant" },
  { value: "warm", label: "Chaleureux" },
  { value: "whimsical", label: "Fantasque" },
  { value: "wistful", label: "Mélancolique" },
  { value: "worship", label: "Adoration" },
  { value: "yearning", label: "Désirant" },
];

export const tagOptions = [
  { value: "8-bit", label: "8-bit" },
  { value: "acoustic-guitar", label: "Guitare acoustique" },
  { value: "action", label: "Action" },
  { value: "adventure", label: "Aventure" },
  { value: "advertising", label: "Publicité" },
  { value: "aggressive", label: "Agressif" },
  { value: "alternative", label: "Alternatif" },
  { value: "ambient", label: "Ambient" },
  { value: "americana", label: "Americana" },
  { value: "analog-synth", label: "Synthé analogique" },
  { value: "animals", label: "Animaux" },
  { value: "arabic", label: "Arabe" },
  { value: "asian", label: "Asiatique" },
  { value: "background", label: "Fond sonore" },
  { value: "ballad", label: "Ballade" },
  { value: "bass", label: "Basse" },
  { value: "beat", label: "Beat" },
  { value: "beautiful", label: "Beau" },
  { value: "bed", label: "Nappe sonore" },
  { value: "big-beat", label: "Big Beat" },
  { value: "bluegrass", label: "Bluegrass" },
  { value: "blues", label: "Blues" },
  { value: "brazil", label: "Brésil" },
  { value: "breakbeat", label: "Breakbeat" },
  { value: "british", label: "Britannique" },
  { value: "build-up", label: "Montée en puissance" },
  { value: "calm", label: "Calme" },
  { value: "celtic", label: "Celtique" },
  { value: "children", label: "Enfants" },
  { value: "chill-out", label: "Chill Out" },
  { value: "christmas", label: "Noël" },
  { value: "cinematic", label: "Cinématographique" },
  { value: "classical", label: "Classique" },
  { value: "club", label: "Club" },
  { value: "comedy", label: "Comédie" },
  { value: "cool", label: "Cool" },
  { value: "country", label: "Country" },
  { value: "creepy", label: "Effrayant" },
  { value: "crime", label: "Crime" },
  { value: "dance", label: "Dance" },
  { value: "dark", label: "Sombre" },
  { value: "deep-house", label: "Deep House" },
  { value: "disco", label: "Disco" },
  { value: "documentary", label: "Documentaire" },
  { value: "drama", label: "Drame" },
  { value: "dream-pop", label: "Dream Pop" },
  { value: "drone", label: "Drone" },
  { value: "drum-and-bass", label: "Drum and Bass" },
  { value: "drums", label: "Batterie" },
  { value: "dub", label: "Dub" },
  { value: "dubstep", label: "Dubstep" },
  { value: "easy-listening", label: "Easy Listening" },
  { value: "edm", label: "EDM" },
  { value: "electro", label: "Electro" },
  { value: "electronic", label: "Électronique" },
  { value: "energetic", label: "Énergique" },
  { value: "epic", label: "Épique" },
  { value: "ethnic", label: "Ethnique" },
  { value: "experimental", label: "Expérimental" },
  { value: "fantasy", label: "Fantaisie" },
  { value: "fast", label: "Rapide" },
  { value: "female-vocals", label: "Voix féminine" },
  { value: "film", label: "Film" },
  { value: "folk", label: "Folk" },
  { value: "funk", label: "Funk" },
  { value: "funky", label: "Funky" },
  { value: "future-bass", label: "Future Bass" },
  { value: "game", label: "Jeu vidéo" },
  { value: "garage", label: "Garage" },
  { value: "glitch", label: "Glitch" },
  { value: "gospel", label: "Gospel" },
  { value: "gothic", label: "Gothique" },
  { value: "groove", label: "Groove" },
  { value: "guitar", label: "Guitare" },
  { value: "happy", label: "Heureux" },
  { value: "hardcore", label: "Hardcore" },
  { value: "heavy-metal", label: "Heavy Metal" },
  { value: "hip-hop", label: "Hip-Hop" },
  { value: "holiday", label: "Vacances" },
  { value: "hopeful", label: "Plein d'espoir" },
  { value: "house", label: "House" },
  { value: "indian", label: "Indien" },
  { value: "indie", label: "Indie" },
  { value: "industrial", label: "Industriel" },
  { value: "inspirational", label: "Inspirant" },
  { value: "instrumental", label: "Instrumental" },
  { value: "intro", label: "Intro" },
  { value: "jazz", label: "Jazz" },
  { value: "kids", label: "Enfants" },
  { value: "latin", label: "Latino" },
  { value: "lounge", label: "Lounge" },
  { value: "love", label: "Amour" },
  { value: "male-vocals", label: "Voix masculine" },
  { value: "medieval", label: "Médiéval" },
  { value: "meditation", label: "Méditation" },
  { value: "melancholic", label: "Mélancolique" },
  { value: "metal", label: "Metal" },
  { value: "minimal", label: "Minimal" },
  { value: "motivational", label: "Motivant" },
  { value: "movie", label: "Film" },
  { value: "nature", label: "Nature" },
  { value: "new-age", label: "New Age" },
  { value: "news", label: "Actualités" },
  { value: "orchestral", label: "Orchestral" },
  { value: "outro", label: "Outro" },
  { value: "party", label: "Fête" },
  { value: "peaceful", label: "Paisible" },
  { value: "piano", label: "Piano" },
  { value: "pop", label: "Pop" },
  { value: "positive", label: "Positif" },
  { value: "presentation", label: "Présentation" },
  { value: "psychedelic", label: "Psychédélique" },
  { value: "punk", label: "Punk" },
  { value: "rap", label: "Rap" },
  { value: "reggae", label: "Reggae" },
  { value: "retro", label: "Rétro" },
  { value: "rock", label: "Rock" },
  { value: "romantic", label: "Romantique" },
  { value: "sad", label: "Triste" },
  { value: "score", label: "Musique de film" },
  { value: "sexy", label: "Sexy" },
  { value: "slow", label: "Lent" },
  { value: "soul", label: "Soul" },
  { value: "soundscape", label: "Paysage sonore" },
  { value: "sport", label: "Sport" },
  { value: "strings", label: "Cordes" },
  { value: "summer", label: "Été" },
  { value: "suspense", label: "Suspense" },
  { value: "synth", label: "Synthé" },
  { value: "synth-pop", label: "Synth Pop" },
  { value: "tech-house", label: "Tech House" },
  { value: "techno", label: "Techno" },
  { value: "trailer", label: "Bande-annonce" },
  { value: "trance", label: "Trance" },
  { value: "trap", label: "Trap" },
  { value: "travel", label: "Voyage" },
  { value: "tribal", label: "Tribal" },
  { value: "trip-hop", label: "Trip Hop" },
  { value: "tutorial", label: "Tutoriel" },
  { value: "ukulele", label: "Ukulélé" },
  { value: "upbeat", label: "Entraînant" },
  { value: "uplifting", label: "Édifiant" },
  { value: "urban", label: "Urbain" },
  { value: "vintage", label: "Vintage" },
  { value: "vocal", label: "Vocal" },
  { value: "wedding", label: "Mariage" },
  { value: "western", label: "Western" },
  { value: "world", label: "Musique du Monde" },
];

export const instrumentationOptions = [
  { value: "accordion", label: "Accordéon" },
  { value: "acoustic-guitar", label: "Guitare acoustique" },
  { value: "bagpipes", label: "Cornemuse" },
  { value: "banjo", label: "Banjo" },
  { value: "bass-guitar", label: "Guitare basse" },
  { value: "bells", label: "Cloches" },
  { value: "bongo", label: "Bongo" },
  { value: "brass", label: "Cuivres" },
  { value: "cello", label: "Violoncelle" },
  { value: "choir", label: "Chœur" },
  { value: "clarinet", label: "Clarinette" },
  { value: "classical-guitar", label: "Guitare classique" },
  { value: "clavinet", label: "Clavinet" },
  { value: "conga", label: "Conga" },
  { value: "contrabass", label: "Contrebasse" },
  { value: "cymbal", label: "Cymbale" },
  { value: "didgeridoo", label: "Didgeridoo" },
  { value: "double-bass", label: "Contrebasse" },
  { value: "drum-machine", label: "Boîte à rythmes" },
  { value: "drums", label: "Batterie" },
  { value: "electric-guitar", label: "Guitare électrique" },
  { value: "electronic-drums", label: "Batterie électronique" },
  { value: "english-horn", label: "Cor anglais" },
  { value: "fiddle", label: "Violon (folk)" },
  { value: "flute", label: "Flûte" },
  { value: "french-horn", label: "Cor d'harmonie" },
  { value: "glockenspiel", label: "Glockenspiel" },
  { value: "gong", label: "Gong" },
  { value: "guitar", label: "Guitare" },
  { value: "harmonica", label: "Harmonica" },
  { value: "harp", label: "Harpe" },
  { value: "harpsichord", label: "Clavecin" },
  { value: "horn", label: "Cor" },
  { value: "kalimba", label: "Kalimba" },
  { value: "keyboard", label: "Clavier" },
  { value: "lute", label: "Luth" },
  { value: "mandolin", label: "Mandoline" },
  { value: "marimba", label: "Marimba" },
  { value: "mellotron", label: "Mellotron" },
  { value: "oboe", label: "Hautbois" },
  { value: "organ", label: "Orgue" },
  { value: "oud", label: "Oud" },
  { value: "pads", label: "Nappes (Synthé)" },
  { value: "percussion", label: "Percussion" },
  { value: "piano", label: "Piano" },
  { value: "piccolo", label: "Piccolo" },
  { value: "recorder", label: "Flûte à bec" },
  { value: "rhodes", label: "Piano Rhodes" },
  { value: "sampler", label: "Sampleur" },
  { value: "saxophone", label: "Saxophone" },
  { value: "sitar", label: "Sitar" },
  { value: "strings", label: "Cordes (ensemble)" },
  { value: "synthesizer", label: "Synthétiseur" },
  { value: "tabla", label: "Tabla" },
  { value: "tambourine", label: "Tambourin" },
  { value: "timpani", label: "Timbales" },
  { value: "triangle", label: "Triangle" },
  { value: "trombone", label: "Trombone" },
  { value: "trumpet", label: "Trompette" },
  { value: "tuba", label: "Tuba" },
  { value: "turntables", label: "Platines (DJ)" },
  { value: "ukulele", label: "Ukulélé" },
  { value: "upright-bass", label: "Contrebasse" },
  { value: "vibraphone", label: "Vibraphone" },
  { value: "viola", label: "Alto (instrument)" },
  { value: "violin", label: "Violon" },
  { value: "vocals", label: "Voix" },
  { value: "whistle", label: "Sifflet" },
  { value: "woodwinds", label: "Bois (instruments)" },
  { value: "xylophone", label: "Xylophone" },
  { value: "zither", label: "Cithare" },
];

// Add languageOptions (example)
export const themeOptions = [
  { value: "amour", label: "Amour" },
  { value: "voyage", label: "Voyage" },
  { value: "nature", label: "Nature" },
  { value: "fete", label: "Fête" },
  { value: "melancolie", label: "Mélancolie" },
  { value: "espoir", label: "Espoir" },
  { value: "reve", label: "Rêve" },
  { value: "combat", label: "Combat" },
  { value: "urbain", label: "Urbain" },
  { value: "spirituel", label: "Spirituel" },
  { value: "nostalgie", label: "Nostalgie" },
  { value: "science-fiction", label: "Science-Fiction" },
  { value: "histoire", label: "Histoire" },
  { value: "humour", label: "Humour" },
  { value: "autre", label: "Autre" }
];

export const languageOptions = [
  { value: "fr", label: "Français" },
  { value: "en", label: "Anglais" },
  { value: "es", label: "Espagnol" },
  { value: "de", label: "Allemand" },
  { value: "it", label: "Italien" },
  { value: "pt", label: "Portugais" },
  { value: "ja", label: "Japonais" },
  { value: "ko", label: "Coréen" },
  { value: "zh", label: "Chinois" },
  { value: "ar", label: "Arabe" },
  { value: "ru", label: "Russe" },
  { value: "other", label: "Autre" },
];

// Options for Profile Page
export const rolePrimaryOptions = [
  { value: "musician_pro", label: "Musicien(ne) Professionnel(le)" },
  { value: "musician_amateur", label: "Musicien(ne) Amateur(e)" },
  { value: "producer_pro", label: "Producteur(trice) Professionnel(le)" },
  { value: "producer_amateur", label: "Producteur(trice) Amateur(e)" },
  { value: "songwriter", label: "Auteur(e)-Compositeur(trice)" },
  { value: "audio_engineer", label: "Ingénieur(e) du Son" },
  { value: "dj", label: "DJ" },
  { value: "vocalist", label: "Chanteur(se)" },
  { value: "instrumentalist", label: "Instrumentiste" },
  { value: "band_member", label: "Membre de Groupe" },
  { value: "label_manager", label: "Manager de Label / A&R" },
  { value: "music_teacher", label: "Professeur de Musique / Coach" },
  { value: "music_journalist", label: "Journaliste Musical(e) / Critique" },
  { value: "event_organizer", label: "Organisateur(trice) d'Événements Musicaux" },
  { value: "music_enthusiast", label: "Mélomane / Passionné(e)" },
  { value: "listener_active", label: "Auditeur(trice) Actif(ve)" },
  { value: "other_pro", label: "Autre Professionnel de la Musique" },
  { value: "other", label: "Autre" },
];

export const roleSecondaryOptions = [
  { value: "session_musician", label: "Musicien(ne) de Session" },
  { value: "remixer", label: "Remixeur(se)" },
  { value: "sound_designer", label: "Sound Designer" },
  { value: "arranger", label: "Arrangeur(se)" },
  { value: "orchestrator", label: "Orchestrateur(rice)" },
  { value: "lyricist", label: "Parolier(ère)" },
  { value: "beatmaker", label: "Beatmaker" },
  { value: "promoter", label: "Promoteur(trice)" },
  { value: "blogger", label: "Blogueur(se) Musical(e)" },
  { value: "vj", label: "VJ (Visual Jockey)" },
  { value: "composer_film_game", label: "Compositeur(trice) (Film/Jeu)" },
  { value: "music_therapist", label: "Musicothérapeute" },
  { value: "instrument_technician", label: "Technicien(ne) d'instruments" },
  { value: "curator_playlist", label: "Curateur(trice) de Playlists" },
  // Can also include options from rolePrimaryOptions if they can be secondary
];

export const dawOptions = [
  { value: "Ableton Live", label: "Ableton Live" },
  { value: "FL Studio", label: "FL Studio" },
  { value: "Logic Pro X", label: "Logic Pro X" },
  { value: "Pro Tools", label: "Pro Tools" },
  { value: "Cubase", label: "Cubase" },
  { value: "Studio One", label: "Studio One" },
  { value: "Reaper", label: "Reaper" },
  { value: "Bitwig Studio", label: "Bitwig Studio" },
  { value: "GarageBand", label: "GarageBand" },
  { value: "Reason", label: "Reason" },
  { value: "MPC Software", label: "MPC Software / Akai Force" },
  { value: "Digital Performer", label: "Digital Performer (MOTU)" },
  { value: "Samplitude", label: "Samplitude Pro X" },
  { value: "Cakewalk by BandLab", label: "Cakewalk by BandLab" },
  { value: "Ardour", label: "Ardour" },
  { value: "LMMS", label: "LMMS" },
  { value: "Online DAW (e.g. Soundtrap, BandLab)", label: "DAW en ligne (Soundtrap, BandLab, etc.)" },
  { value: "Mobile DAW (e.g. GarageBand iOS, FL Studio Mobile)", label: "DAW Mobile (GarageBand iOS, FLSM, etc.)" },
  { value: "None", label: "Aucun / Pas applicable" },
  { value: "Other", label: "Autre" },
];

export const osOptions = [
  { value: "windows", label: "Windows" },
  { value: "macos", label: "macOS" },
  { value: "linux", label: "Linux" },
  { value: "ios", label: "iOS (pour apps musicales)" },
  { value: "android", label: "Android (pour apps musicales)" },
  { value: "other", label: "Autre / Multi-plateforme" },
];

export const influenceOptions = [
  // From EditProfilePage
  { value: "daft_punk", label: "Daft Punk" }, { value: "bjork", label: "Björk" },
  { value: "radiohead", label: "Radiohead" }, { value: "kendrick_lamar", label: "Kendrick Lamar" },
  { value: "bob_marley", label: "Bob Marley" }, { value: "stromae", label: "Stromae" },
  { value: "aphex_twin", label: "Aphex Twin" }, { value: "justice", label: "Justice" },
  { value: "rosalia", label: "Rosalía" }, { value: "sza", label: "SZA" },
  { value: "sun_ra", label: "Sun Ra" }, { value: "sunn_o", label: "Sunn O)))" },
  { value: "udio_ai", label: "Udio AI" }, { value: "suno_ai", label: "Suno AI" },
  // Add more
  { value: "kraftwerk", label: "Kraftwerk" }, { value: "massive_attack", label: "Massive Attack" },
  { value: "brian_eno", label: "Brian Eno" }, { value: "jean_michel_jarre", label: "Jean-Michel Jarre" },
  { value: "serge_gainsbourg", label: "Serge Gainsbourg" }, { value: "air", label: "Air" },
  { value: "boards_of_canada", label: "Boards of Canada"}, { value: "four_tet", label: "Four Tet"},
  { value: "james_blake", label: "James Blake"}, { value: "fka_twigs", label: "FKA Twigs"},
  { value: "pink_floyd", label: "Pink Floyd"}, { value: "led_zeppelin", label: "Led Zeppelin"},
  { value: "the_beatles", label: "The Beatles"}, { value: "miles_davis", label: "Miles Davis"},
  { value: "john_coltrane", label: "John Coltrane"}, { value: "joni_mitchell", label: "Joni Mitchell"},
  { value: "kate_bush", label: "Kate Bush"}, { value: "prince", label: "Prince"},
  { value: "david_bowie", label: "David Bowie"}, { value: "ryuichi_sakamoto", label: "Ryuichi Sakamoto"},
  { value: "hans_zimmer", label: "Hans Zimmer"}, { value: "ennio_morricone", label: "Ennio Morricone"},
];

export const countryOptions = [
  { value: "AF", label: "Afghanistan" },
  { value: "AL", label: "Albanie" },
  { value: "DZ", label: "Algérie" },
  { value: "AD", label: "Andorre" },
  { value: "AO", label: "Angola" },
  { value: "AG", label: "Antigua-et-Barbuda" },
  { value: "AR", label: "Argentine" },
  { value: "AM", label: "Arménie" },
  { value: "AU", label: "Australie" },
  { value: "AT", label: "Autriche" },
  { value: "AZ", label: "Azerbaïdjan" },
  { value: "BS", label: "Bahamas" },
  { value: "BH", label: "Bahreïn" },
  { value: "BD", label: "Bangladesh" },
  { value: "BB", label: "Barbade" },
  { value: "BY", label: "Biélorussie" },
  { value: "BE", label: "Belgique" },
  { value: "BZ", label: "Belize" },
  { value: "BJ", label: "Bénin" },
  { value: "BT", label: "Bhoutan" },
  { value: "BO", label: "Bolivie" },
  { value: "BA", label: "Bosnie-Herzégovine" },
  { value: "BW", label: "Botswana" },
  { value: "BR", label: "Brésil" },
  { value: "BN", label: "Brunéi Darussalam" },
  { value: "BG", label: "Bulgarie" },
  { value: "BF", label: "Burkina Faso" },
  { value: "BI", label: "Burundi" },
  { value: "CV", label: "Cabo Verde" },
  { value: "KH", label: "Cambodge" },
  { value: "CM", label: "Cameroun" },
  { value: "CA", label: "Canada" },
  { value: "CF", label: "République centrafricaine" },
  { value: "TD", label: "Tchad" },
  { value: "CL", label: "Chili" },
  { value: "CN", label: "Chine" },
  { value: "CO", label: "Colombie" },
  { value: "KM", label: "Comores" },
  { value: "CG", label: "Congo (Brazzaville)" },
  { value: "CD", label: "Congo (Kinshasa)" },
  { value: "CR", label: "Costa Rica" },
  { value: "CI", label: "Côte d'Ivoire" },
  { value: "HR", label: "Croatie" },
  { value: "CU", label: "Cuba" },
  { value: "CY", label: "Chypre" },
  { value: "CZ", label: "Tchéquie" },
  { value: "DK", label: "Danemark" },
  { value: "DJ", label: "Djibouti" },
  { value: "DM", label: "Dominique" },
  { value: "DO", label: "République dominicaine" },
  { value: "EC", label: "Équateur" },
  { value: "EG", label: "Égypte" },
  { value: "SV", label: "El Salvador" },
  { value: "GQ", label: "Guinée équatoriale" },
  { value: "ER", label: "Érythrée" },
  { value: "EE", label: "Estonie" },
  { value: "SZ", label: "Eswatini" },
  { value: "ET", label: "Éthiopie" },
  { value: "FJ", label: "Fidji" },
  { value: "FI", label: "Finlande" },
  { value: "FR", label: "France" },
  { value: "GA", label: "Gabon" },
  { value: "GM", label: "Gambie" },
  { value: "GE", label: "Géorgie" },
  { value: "DE", label: "Allemagne" },
  { value: "GH", label: "Ghana" },
  { value: "GR", label: "Grèce" },
  { value: "GD", label: "Grenade" },
  { value: "GT", label: "Guatemala" },
  { value: "GN", label: "Guinée" },
  { value: "GW", label: "Guinée-Bissau" },
  { value: "GY", label: "Guyana" },
  { value: "HT", label: "Haïti" },
  { value: "HN", label: "Honduras" },
  { value: "HU", label: "Hongrie" },
  { value: "IS", label: "Islande" },
  { value: "IN", label: "Inde" },
  { value: "ID", label: "Indonésie" },
  { value: "IR", label: "Iran" },
  { value: "IQ", label: "Irak" },
  { value: "IE", label: "Irlande" },
  { value: "IL", label: "Israël" },
  { value: "IT", label: "Italie" },
  { value: "JM", label: "Jamaïque" },
  { value: "JP", label: "Japon" },
  { value: "JO", label: "Jordanie" },
  { value: "KZ", label: "Kazakhstan" },
  { value: "KE", label: "Kenya" },
  { value: "KI", label: "Kiribati" },
  { value: "KW", label: "Koweït" },
  { value: "KG", label: "Kirghizistan" },
  { value: "LA", label: "Laos" },
  { value: "LV", label: "Lettonie" },
  { value: "LB", label: "Liban" },
  { value: "LS", label: "Lesotho" },
  { value: "LR", label: "Libéria" },
  { value: "LY", label: "Libye" },
  { value: "LI", label: "Liechtenstein" },
  { value: "LT", label: "Lituanie" },
  { value: "LU", label: "Luxembourg" },
  { value: "MG", label: "Madagascar" },
  { value: "MW", label: "Malawi" },
  { value: "MY", label: "Malaisie" },
  { value: "MV", label: "Maldives" },
  { value: "ML", label: "Mali" },
  { value: "MT", label: "Malte" },
  { value: "MH", label: "Îles Marshall" },
  { value: "MR", label: "Mauritanie" },
  { value: "MU", label: "Maurice" },
  { value: "MX", label: "Mexique" },
  { value: "FM", label: "Micronésie" },
  { value: "MD", label: "Moldavie" },
  { value: "MC", label: "Monaco" },
  { value: "MN", label: "Mongolie" },
  { value: "ME", label: "Monténégro" },
  { value: "MA", label: "Maroc" },
  { value: "MZ", label: "Mozambique" },
  { value: "MM", label: "Myanmar (Birmanie)" },
  { value: "NA", label: "Namibie" },
  { value: "NR", label: "Nauru" },
  { value: "NP", label: "Népal" },
  { value: "NL", label: "Pays-Bas" },
  { value: "NZ", label: "Nouvelle-Zélande" },
  { value: "NI", label: "Nicaragua" },
  { value: "NE", label: "Niger" },
  { value: "NG", label: "Nigéria" },
  { value: "KP", label: "Corée du Nord" },
  { value: "MK", label: "Macédoine du Nord" },
  { value: "NO", label: "Norvège" },
  { value: "OM", label: "Oman" },
  { value: "PK", label: "Pakistan" },
  { value: "PW", label: "Palaos" },
  { value: "PA", label: "Panama" },
  { value: "PG", label: "Papouasie-Nouvelle-Guinée" },
  { value: "PY", label: "Paraguay" },
  { value: "PE", label: "Pérou" },
  { value: "PH", label: "Philippines" },
  { value: "PL", label: "Pologne" },
  { value: "PT", label: "Portugal" },
  { value: "QA", label: "Qatar" },
  { value: "RO", label: "Roumanie" },
  { value: "RU", label: "Russie" },
  { value: "RW", label: "Rwanda" },
  { value: "KN", label: "Saint-Kitts-et-Nevis" },
  { value: "LC", label: "Sainte-Lucie" },
  { value: "VC", label: "Saint-Vincent-et-les Grenadines" },
  { value: "WS", label: "Samoa" },
  { value: "SM", label: "Saint-Marin" },
  { value: "ST", label: "Sao Tomé-et-Principe" },
  { value: "SA", label: "Arabie saoudite" },
  { value: "SN", label: "Sénégal" },
  { value: "RS", label: "Serbie" },
  { value: "SC", label: "Seychelles" },
  { value: "SL", label: "Sierra Leone" },
  { value: "SG", label: "Singapour" },
  { value: "SK", label: "Slovaquie" },
  { value: "SI", label: "Slovénie" },
  { value: "SB", label: "Îles Salomon" },
  { value: "SO", label: "Somalie" },
  { value: "ZA", label: "Afrique du Sud" },
  { value: "KR", label: "Corée du Sud" },
  { value: "SS", label: "Soudan du Sud" },
  { value: "ES", label: "Espagne" },
  { value: "LK", label: "Sri Lanka" },
  { value: "SD", label: "Soudan" },
  { value: "SR", label: "Suriname" },
  { value: "SE", label: "Suède" },
  { value: "CH", label: "Suisse" },
  { value: "SY", label: "Syrie" },
  { value: "TJ", label: "Tadjikistan" },
  { value: "TZ", label: "Tanzanie" },
  { value: "TH", label: "Thaïlande" },
  { value: "TL", label: "Timor oriental" },
  { value: "TG", label: "Togo" },
  { value: "TO", label: "Tonga" },
  { value: "TT", label: "Trinité-et-Tobago" },
  { value: "TN", label: "Tunisie" },
  { value: "TR", label: "Turquie" },
  { value: "TM", label: "Turkménistan" },
  { value: "TV", label: "Tuvalu" },
  { value: "UG", label: "Ouganda" },
  { value: "UA", label: "Ukraine" },
  { value: "AE", label: "Émirats arabes unis" },
  { value: "GB", label: "Royaume-Uni" },
  { value: "US", label: "États-Unis" },
  { value: "UY", label: "Uruguay" },
  { value: "UZ", label: "Ouzbékistan" },
  { value: "VU", label: "Vanuatu" },
  { value: "VE", label: "Venezuela" },
  { value: "VN", label: "Viêt Nam" },
  { value: "YE", label: "Yémen" },
  { value: "ZM", label: "Zambie" },
  { value: "ZW", label: "Zimbabwe" },
  { value: "OTHER", label: "Autre/Non spécifié" }
];

export const albumTypeOptions = [
  { value: "album", label: "Album" },
  { value: "ep", label: "EP" },
  { value: "single", label: "Single" },
  { value: "compilation", label: "Compilation" },
  { value: "soundtrack", label: "Bande Originale (Soundtrack)" },
  { value: "live_album", label: "Album Live" },
  { value: "remix_album", label: "Album de Remixes" },
  { value: "mixtape", label: "Mixtape" },
  { value: "demo", label: "Démo" },
  { value: "other", label: "Autre" },
];

export const timeSignatureOptions = [
  { value: "2/4", label: "2/4" },
  { value: "3/4", label: "3/4" },
  { value: "4/4", label: "4/4" },
  { value: "5/4", label: "5/4" },
  { value: "6/8", label: "6/8" },
  { value: "7/8", label: "7/8" },
  { value: "9/8", label: "9/8" },
  { value: "12/8", label: "12/8" },
  { value: "other", label: "Autre" },
];

// Retaining existing options like genreOptions, moodOptions, tagOptions, instrumentationOptions
// Ensure they are comprehensive enough or allow user-defined values in MultiSelect.
// The genreOptions and languageOptions in this file are already quite good.
// The tagOptions and instrumentationOptions are also very extensive.
