-- Création de la table des likes
CREATE TABLE IF NOT EXISTS likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content_type TEXT NOT NULL,
  content_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, content_type, content_id)
);

-- Index pour les recherches rapides de likes
CREATE INDEX IF NOT EXISTS likes_content_idx ON likes(content_type, content_id);
CREATE INDEX IF NOT EXISTS likes_user_idx ON likes(user_id);
