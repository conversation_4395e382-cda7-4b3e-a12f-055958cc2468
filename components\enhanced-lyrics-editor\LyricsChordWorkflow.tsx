/**
 * 🎼 LYRICS CHORD WORKFLOW - Workflow Complet Paroles + Accords
 * 
 * Composant wrapper intégrant l'éditeur amélioré avec suggestions IA
 * Remplacement de LyricsEditorWithAI avec fonctionnalités étendues
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { 
  Music, Sparkles, Save, Settings, Eye, EyeOff,
  ChevronDown, ChevronUp, History, Download, Upload
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { toast } from '@/components/ui/use-toast';
import { EnhancedLyricsEditor, type ChordPlacement, type DisplayMode } from './EnhancedLyricsEditor';
import { AiChordSuggestions, type ChordSuggestion, type SuggestionContext } from './AiChordSuggestions';
import { ChordSystemProvider } from '@/components/chord-system/providers/ChordSystemProvider';
import type { UnifiedChordPosition } from '@/components/chord-system/types/chord-system';
import type { Control } from 'react-hook-form';

// ============================================================================
// TYPES POUR LE WORKFLOW
// ============================================================================

export interface AiHistoryItem {
  id: string;
  userPrompt: string;
  assistantResponse: string;
  timestamp: string;
  type: 'lyrics' | 'chords' | 'suggestions';
}

export interface AiConfig {
  provider: string;
  model: string;
  temperature: number;
}

export interface SongFormValues {
  lyrics: string;
  // Autres champs du formulaire...
}

export interface LyricsChordWorkflowProps {
  // Props héritées de LyricsEditorWithAI
  lyricsContent: string;
  handleLyricsChange: (newContent: string) => void;
  quillRef: React.RefObject<any>;
  formControl: Control<SongFormValues>;
  
  // Props IA
  aiConfig: AiConfig;
  aiGeneralPrompt: string;
  addAiHistory: (userPrompt: string, assistantResponse: string) => void;
  aiHistory: AiHistoryItem[];
  showAiHistory: boolean;
  setShowAiHistory: (show: boolean) => void;
  
  // Props pour les accords (nouvelles)
  chords?: ChordPlacement[];
  onChordsChange?: (chords: ChordPlacement[]) => void;
  
  // Props pour la sauvegarde
  onSave?: (lyrics: string, chords: ChordPlacement[]) => void;
  autoSave?: boolean;
  
  // Métadonnées du morceau
  songMetadata?: {
    genre?: string;
    key?: string;
    tempo?: number;
    mood?: string;
    title?: string;
  };
  
  // Configuration
  className?: string;
}

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const LyricsChordWorkflow: React.FC<LyricsChordWorkflowProps> = ({
  lyricsContent,
  handleLyricsChange,
  quillRef,
  formControl,
  aiConfig,
  aiGeneralPrompt,
  addAiHistory,
  aiHistory,
  showAiHistory,
  setShowAiHistory,
  chords = [],
  onChordsChange,
  onSave,
  autoSave = true,
  songMetadata,
  className = ''
}) => {
  // État local
  const [currentChords, setCurrentChords] = useState<ChordPlacement[]>(chords);
  const [displayMode, setDisplayMode] = useState<DisplayMode>('hybrid');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isAiLoading, setIsAiLoading] = useState(false);
  const [currentSelection, setCurrentSelection] = useState<any>(null);

  // ============================================================================
  // HANDLERS POUR LES ACCORDS
  // ============================================================================

  const handleChordsChange = useCallback((newChords: ChordPlacement[]) => {
    setCurrentChords(newChords);
    onChordsChange?.(newChords);
  }, [onChordsChange]);

  const handleSave = useCallback((lyrics: string, chords: ChordPlacement[]) => {
    onSave?.(lyrics, chords);
    
    // Sauvegarder dans ai_composer_data
    const composerData = {
      lyrics,
      chords: chords.map(c => ({
        id: c.id,
        chord: c.chord.chord,
        position: c.textPosition,
        timestamp: c.timestamp,
        metadata: c.metadata
      })),
      lastUpdated: new Date().toISOString()
    };
    
    // TODO: Intégrer avec Supabase ai_composer_data
    console.log('Sauvegarde ai_composer_data:', composerData);
    
    toast({
      title: "Sauvegardé !",
      description: `Paroles et ${chords.length} accords sauvegardés.`
    });
  }, [onSave]);

  // ============================================================================
  // HANDLERS POUR L'IA
  // ============================================================================

  const handleRequestChordSuggestions = useCallback(async (
    context: string, 
    position: number
  ): Promise<UnifiedChordPosition[]> => {
    setIsAiLoading(true);
    
    try {
      // Construire le prompt pour l'IA
      const existingChordsText = currentChords.map(c => c.chord.chord).join(', ');
      const prompt = `
        ${aiGeneralPrompt}
        
        Analyse ces paroles et suggère des accords appropriés :
        "${context}"
        
        Contexte musical :
        - Genre : ${songMetadata?.genre || 'Non spécifié'}
        - Tonalité : ${songMetadata?.key || 'Non spécifiée'}
        - Tempo : ${songMetadata?.tempo || 'Non spécifié'} BPM
        - Ambiance : ${songMetadata?.mood || 'Non spécifiée'}
        
        Accords existants : ${existingChordsText || 'Aucun'}
        Position dans le texte : ${position}
        
        Retourne une liste d'accords avec justification harmonique.
        Format : [Accord] - [Raison] - [Théorie musicale]
      `;

      // TODO: Remplacer par l'appel IA réel
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulation de réponse IA
      const mockSuggestions = [
        { chord: 'C', reason: 'Tonique principale', theory: 'I degré en Do majeur' },
        { chord: 'Am', reason: 'Relative mineure', theory: 'vi degré, transition douce' },
        { chord: 'F', reason: 'Sous-dominante', theory: 'IV degré, progression classique' },
        { chord: 'G', reason: 'Dominante', theory: 'V degré, tension vers la tonique' }
      ];

      const aiResponse = mockSuggestions.map(s => `${s.chord} - ${s.reason} - ${s.theory}`).join('\n');
      
      // Ajouter à l'historique IA
      addAiHistory(prompt, aiResponse);
      
      // Convertir en UnifiedChordPosition (simulation)
      const suggestions: UnifiedChordPosition[] = mockSuggestions.map((s, index) => ({
        id: `suggestion-${index}`,
        chord: s.chord,
        instrument: 'guitar',
        tuning: 'standard',
        frets: [0, 0, 0, 0, 0, 0], // Positions simplifiées
        fingers: [0, 0, 0, 0, 0, 0],
        difficulty: 'beginner' as const,
        category: 'major' as const
      }));

      return suggestions;
      
    } catch (error) {
      console.error('Erreur suggestions IA:', error);
      toast({
        title: "Erreur IA",
        description: "Impossible de générer des suggestions d'accords.",
        variant: "destructive"
      });
      return [];
    } finally {
      setIsAiLoading(false);
    }
  }, [aiGeneralPrompt, currentChords, songMetadata, addAiHistory]);

  const handleAiSuggestions = useCallback(async (): Promise<ChordSuggestion[]> => {
    const context: SuggestionContext = {
      lyrics: lyricsContent,
      existingChords: currentChords,
      currentPosition: currentSelection?.index || 0,
      songMetadata
    };

    try {
      const suggestions = await handleRequestChordSuggestions(context.lyrics, context.currentPosition);
      
      // Convertir en ChordSuggestion
      return suggestions.map((chord, index) => ({
        chord,
        confidence: Math.floor(Math.random() * 40) + 60, // 60-100%
        reason: `Accord suggéré pour la position ${context.currentPosition}`,
        musicTheory: `Analyse harmonique basée sur le contexte`,
        position: context.currentPosition,
        category: index === 0 ? 'progression' : index === 1 ? 'substitution' : 'transition'
      }));
      
    } catch (error) {
      console.error('Erreur génération suggestions:', error);
      return [];
    }
  }, [lyricsContent, currentChords, currentSelection, songMetadata, handleRequestChordSuggestions]);

  // ============================================================================
  // HANDLERS POUR L'INTERFACE
  // ============================================================================

  const handleSelectionChange = useCallback((range: any, source: any, editor: any) => {
    setCurrentSelection(range);
  }, []);

  const handleChordDrop = useCallback((chord: UnifiedChordPosition, position: number) => {
    const newChordPlacement: ChordPlacement = {
      id: crypto.randomUUID(),
      chord,
      textPosition: position,
      lineNumber: 0, // TODO: Calculer
      wordIndex: 0, // TODO: Calculer
      timestamp: new Date().toISOString(),
      metadata: {
        emphasis: 'medium'
      }
    };

    const updatedChords = [...currentChords, newChordPlacement];
    handleChordsChange(updatedChords);
  }, [currentChords, handleChordsChange]);

  const handleToggleSuggestions = useCallback(() => {
    setShowSuggestions(!showSuggestions);
  }, [showSuggestions]);

  // ============================================================================
  // EFFETS
  // ============================================================================

  // Synchroniser les accords avec les props
  useEffect(() => {
    setCurrentChords(chords);
  }, [chords]);

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <ChordSystemProvider>
      <div className={`lyrics-chord-workflow space-y-4 ${className}`}>
        {/* En-tête du workflow */}
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200 rounded-lg">
          <div className="flex items-center">
            <Music className="w-6 h-6 text-blue-600 mr-3" />
            <div>
              <h3 className="font-semibold text-gray-900">Éditeur Paroles & Accords</h3>
              <p className="text-sm text-gray-600">
                {currentChords.length} accord{currentChords.length !== 1 ? 's' : ''} • 
                Mode {displayMode === 'hybrid' ? 'Hybride' : displayMode === 'text-only' ? 'Texte' : 'Accords'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleSuggestions}
              className="flex items-center"
            >
              <Sparkles className="w-4 h-4 mr-1" />
              {showSuggestions ? 'Masquer' : 'Suggestions'} IA
            </Button>
          </div>
        </div>

        {/* Éditeur principal */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Éditeur de texte et accords */}
          <div className={showSuggestions ? 'lg:col-span-2' : 'lg:col-span-3'}>
            <EnhancedLyricsEditor
              value={lyricsContent}
              onChange={handleLyricsChange}
              placeholder="Commencez à écrire vos paroles ici..."
              quillRef={quillRef}
              onSelectionChange={handleSelectionChange}
              chords={currentChords}
              onChordsChange={handleChordsChange}
              displayMode={displayMode}
              onDisplayModeChange={setDisplayMode}
              onRequestChordSuggestions={handleRequestChordSuggestions}
              onChordDrop={handleChordDrop}
              onSave={handleSave}
              autoSave={autoSave}
              className="border border-gray-200 rounded-lg overflow-hidden"
            />
          </div>

          {/* Panneau de suggestions IA */}
          {showSuggestions && (
            <div className="lg:col-span-1">
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <AiChordSuggestions
                  context={{
                    lyrics: lyricsContent,
                    existingChords: currentChords,
                    currentPosition: currentSelection?.index || 0,
                    songMetadata
                  }}
                  onRequestSuggestions={handleAiSuggestions}
                  onChordSelect={(chord, position) => {
                    handleChordDrop(chord, position || currentSelection?.index || 0);
                  }}
                  aiConfig={aiConfig}
                  isLoading={isAiLoading}
                />
              </div>
            </div>
          )}
        </div>

        {/* Historique IA (collapsible) */}
        <Collapsible open={showAiHistory} onOpenChange={setShowAiHistory}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="w-full flex items-center justify-between">
              <div className="flex items-center">
                <History className="w-4 h-4 mr-2" />
                Historique IA ({aiHistory.length})
              </div>
              {showAiHistory ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="space-y-2 mt-2">
            {aiHistory.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                Aucun historique IA pour le moment
              </div>
            ) : (
              <div className="max-h-60 overflow-y-auto space-y-2">
                {aiHistory.slice(-5).reverse().map((item) => (
                  <div key={item.id} className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900 mb-1">
                        Prompt: {item.userPrompt.slice(0, 100)}...
                      </div>
                      <div className="text-gray-700">
                        Réponse: {item.assistantResponse.slice(0, 200)}...
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(item.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Actions rapides */}
        <div className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600">
            Dernière modification: {new Date().toLocaleTimeString()}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSave(lyricsContent, currentChords)}
              className="flex items-center"
            >
              <Save className="w-4 h-4 mr-1" />
              Sauvegarder
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // TODO: Export functionality
                toast({
                  title: "Export",
                  description: "Fonctionnalité d'export en cours de développement"
                });
              }}
              className="flex items-center"
            >
              <Download className="w-4 h-4 mr-1" />
              Exporter
            </Button>
          </div>
        </div>
      </div>
    </ChordSystemProvider>
  );
};
