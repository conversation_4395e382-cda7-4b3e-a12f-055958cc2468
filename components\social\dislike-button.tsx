"use client";

import { useState, useEffect } from 'react';
import { Button, ButtonProps } from "@/components/ui/button";
import { ThumbsDown } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { cn } from '@/lib/utils';
import { useResourceInteractionStore, getResourceInteractionStoreState, type ResourceInteractionStoreState, type ToggleDislikeResult, type ResourceType, DEFAULT_RESOURCE_STATE, getResourceKey } from "@/lib/stores/resource-interaction-store";
import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';

interface DislikeButtonProps extends Omit<ButtonProps, 'onClick'> {
  resourceId: string;
  resourceType: ResourceType;
  initialDislikes?: number;
  initialIsDisliked?: boolean;
  userId?: string;
  onDislikeToggle?: (disliked: boolean, newDislikeCount: number, newLikeCount?: number) => void;
  size?: ButtonProps['size'];
  variant?: ButtonProps['variant'];
  className?: string;
}

export function DislikeButton({ 
  resourceId, 
  resourceType, 
  initialDislikes,
  initialIsDisliked,
  userId,
  onDislikeToggle,  // Ajouté
  size = "default", 
  className, 
  variant = "ghost",
  ...rest 
}: DislikeButtonProps) {
  if (resourceType !== 'song') {
    // Optionally, render nothing or a disabled button if not a song
    console.warn('DislikeButton used for non-song resource:', resourceType);
    return null; 
  }
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const { user: currentUser } = useUser();

  const storeActions = getResourceInteractionStoreState();
  const { 
    isDisliked: displayIsDisliked, 
    dislikeCount: displayDislikeCount 
  } = useResourceInteractionStore(
    (state: ResourceInteractionStoreState) => state.resourceStates[getResourceKey(resourceType, resourceId)] || DEFAULT_RESOURCE_STATE
  );

  const handleDislike = async () => {
    if (!currentUser) {
      toast({ title: "Connexion requise", description: "Vous devez être connecté pour disliker.", variant: "default" });
      return;
    }
    if (isLoading) return;
    setIsLoading(true);

    const actualApiCall = async (): Promise<ToggleDislikeResult> => {
      if (!currentUser) throw new Error('User not authenticated for API call'); 
      const supabase = getSupabaseClient();
      const { data, error } = await supabase.rpc('toggle_dislike', { p_resource_id: resourceId, p_resource_type: resourceType, p_user_id: currentUser.id });
      if (error) {
        console.error('Error toggling dislike:', error);
        throw error;
      }
      // Map snake_case from RPC to camelCase for LikeDislikeApiResult
      return {
        newDislikeCount: data.new_dislike_count,
        newIsDisliked: data.new_is_disliked,
        newLikeCount: data.new_like_count, // RPC for toggle_dislike should also return like status
        newIsLiked: data.new_is_liked,
      };
    };

    try {
      await storeActions.toggleDislike(resourceType, resourceId, actualApiCall);
    } catch (error: any) {
      if (!error.message?.includes("Supabase RPC Error")) { 
        toast({
          title: "Erreur",
          description: error.message || `Une erreur est survenue en dislikant ${resourceType}.`,
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant} 
      size={size}
      onClick={handleDislike}
      disabled={isLoading || !currentUser}
      className={`transition-colors ${displayIsDisliked ? 'text-blue-500 border-blue-500 hover:bg-blue-500/10' : ''} ${className || ''}`}
      aria-pressed={displayIsDisliked}
      {...rest}
    >
      <ThumbsDown className={cn("h-4 w-4", displayIsDisliked && "fill-blue-500")} />
      <span className="text-xs font-normal">
        {displayDislikeCount}
      </span>
    </Button>
  );
}
