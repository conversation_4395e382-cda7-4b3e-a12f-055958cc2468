# Architecture Technique - MOUVIK

## Vue d'Ensemble

MOUVIK est construit sur une architecture moderne basée sur Next.js avec l'App Router et Supabase comme backend. Cette architecture permet une expérience utilisateur fluide tout en maintenant une séparation claire entre le frontend et le backend.

## Diagramme d'Architecture

```mermaid
%% TODO: Générer un diagramme d'architecture Mermaid détaillé
%% Ce diagramme devrait illustrer les principaux composants :
%% - Client (Next.js Frontend, React, App Router)
%% - Serveur Next.js (API Routes, Server Actions)
%% - Supabase (PostgreSQL, Auth, Storage, Functions, Edge Functions)
%% - Interactions entre ces composants (API calls, DB queries, etc.)
%% - Flux de données clés (authentification, lecture audio, création de contenu, interactions sociales)

graph TD
    A[Client: Next.js Frontend] -->|API Calls / Server Actions| B(Serveur: Next.js Backend)
    B -->|Database Queries / RPC| C{Supabase}
    C -->|Auth| D[Authentication]
    C -->|PostgreSQL| E[Database]
    C -->|Storage| F[File Storage]
    C -->|Functions| G[Edge Functions]
```

**Note:** Ce diagramme est une représentation simplifiée. Un diagramme plus complet sera généré pour illustrer les interactions spécifiques et les flux de données plus en détail.

## Couches Applicatives

### 1. Couche Présentation (Frontend)

- **Technologie**: React, Next.js App Router
- **Responsabilités**:
  - Rendu des interfaces utilisateur
  - Gestion des états locaux avec React Hooks
  - Routing côté client
  - Interactions utilisateur
  - Lecture et manipulation audio côté client

### 2. Couche Serveur (Backend)

- **Technologie**: Next.js API Routes, Server Actions
- **Responsabilités**:
  - Traitement des requêtes API
  - Validation des données
  - Logique métier
  - Communication avec Supabase
  - Authentification et autorisation

### 3. Couche Données

- **Technologie**: Supabase (PostgreSQL)
- **Responsabilités**:
  - Stockage persistant des données
  - Relations entre entités
  - Indexation et recherche
  - Sécurité au niveau de la base de données

## Modèle de Données

### Principales Entités

1. **Users**
   - Informations d'authentification
   - Rôles et permissions

2. **Profiles**
   - Informations personnelles
   - Préférences utilisateur
   - Relations sociales

3. **Tracks**
   - Métadonnées des pistes audio
   - Références aux fichiers audio
   - Statistiques d'écoute

4. **Albums**
   - Collections de pistes
   - Métadonnées (titre, date, artwork)
   - Relations avec les artistes

5. **Playlists**
   - Collections personnalisées de pistes
   - Métadonnées (titre, description)
   - Visibilité et partage

6. **Comments**
   - Commentaires sur les pistes/albums
   - Relations avec les utilisateurs
   - Horodatage

7. **Subscriptions**
   - Informations d'abonnement
   - Historique de paiement
   - Niveau d'accès

## Flux de Données

### Authentification

1. L'utilisateur soumet ses identifiants via le formulaire de connexion
2. Next.js transmet les identifiants à Supabase Auth
3. Supabase valide les identifiants et génère un JWT
4. Le JWT est stocké côté client pour les requêtes authentifiées
5. Les routes protégées vérifient la validité du JWT

### Lecture Audio

1. L'utilisateur sélectionne une piste à lire
2. Le client demande l'URL du fichier audio via une API Route
3. L'API Route vérifie les permissions et génère une URL signée
4. Le client charge et lit le fichier audio
5. Les événements de lecture sont enregistrés pour les statistiques

### Création de Contenu

1. L'utilisateur téléverse un fichier audio
2. Le fichier est envoyé à Supabase Storage via une API Route
3. Les métadonnées sont extraites et stockées dans la base de données
4. Le traitement audio (waveform, normalisation) est effectué
5. La piste devient disponible dans la bibliothèque de l'utilisateur

## Sécurité

### Authentification et Autorisation

- Authentification basée sur JWT avec Supabase Auth
- Politiques RLS (Row Level Security) dans PostgreSQL
- Vérification des rôles pour les fonctionnalités administratives
- Protection CSRF pour les formulaires et API Routes

### Sécurité des Données

- Chiffrement des données sensibles
- URLs signées pour les ressources protégées
- Validation des entrées utilisateur
- Sanitization des sorties HTML

## Performances

### Stratégies d'Optimisation

- Rendu côté serveur (SSR) pour le chargement initial rapide
- Streaming audio adaptatif
- Mise en cache des ressources statiques
- Chargement paresseux des composants et médias
- Optimisation des images et ressources audio

## Extensibilité

L'architecture est conçue pour permettre l'ajout facile de nouvelles fonctionnalités :

- Système modulaire de composants React
- API Routes indépendantes pour chaque domaine fonctionnel
- Modèle de données extensible avec relations flexibles
- Hooks personnalisés pour la logique réutilisable

## Intégration des Fonctionnalités Clés

Cette section détaille l'intégration des modules principaux tels que "Découvrir & Playlists" et "Communauté", en s'appuyant sur l'architecture globale.

### Modules Fonctionnels

- **Découverte & Playlists**: Permet aux utilisateurs d'importer des ressources musicales externes (ex: Spotify, Suno), de les taguer, de les organiser en playlists et de les partager.
    - **Flux d'Importation**:
        1. Saisie URL (Frontend).
        2. Prévisualisation via API (extraction de métadonnées, suggestion de tags).
        3. Validation utilisateur et soumission.
        4. Création de la ressource dans Supabase (`music_resources`, `tags`, `resource_tags`) et enregistrement de l'activité.
    - **Composants Clés**: `ResourceCard`, `ImportForm`, `PlaylistManager`, `TagInput`.
    - **Tables Supabase associées**: `music_resources`, `tags`, `resource_tags`, `playlists`, `playlist_resources` (se référer à `database-schema.md` pour les détails).
    - **Endpoints API principaux**: `/api/discover/resources` (CRUD), `/api/discover/preview`, `/api/discover/playlists` (CRUD).

- **Communauté**: Facilite les interactions sociales autour de la musique, incluant les posts, commentaires, likes, et la gestion des groupes (bands).
    - **Flux de Publication**:
        1. Création de post (Frontend).
        2. Traitement via Server Action ou API (validation, détection de hashtags, création dans `activity` et `activity_hashtags`).
        3. Affichage dans le flux d'activité.
    - **Composants Clés**: `ActivityFeed`, `ActivityCard`, `PostInput`, `CommentSection`, `LikeButton`, `BandManager`.
    - **Tables Supabase associées**: `activity`, `hashtags`, `activity_hashtags`, `likes`, `comments`, `bands`, `band_members` (se référer à `database-schema.md` pour les détails).
    - **Endpoints API principaux**: `/api/community/activity` (CRUD), `/api/community/activity/{id}/like`, `/api/hashtags`.

### Points d'Intégration et Considérations Techniques

- **Services Partagés (`/lib`)**: Fonctions utilitaires pour la logique métier réutilisable (ex: création d'activité, extraction de métadonnées).
- **Gestion Centralisée des Tags**: Un système de tags unifié (table `tags`, endpoint `/api/tags`) pour la catégorisation et la recherche.
- **Notifications**: (À développer) Système pour informer les utilisateurs des interactions pertinentes.
- **Modération**: (À développer) Mécanismes pour signaler et gérer le contenu.
- **Provenance des Données**: Indication claire de l'origine des ressources (MOUVIK, Spotify, etc.) avec UI adaptée.
- **Relations Inter-Modules**:
    - L'ajout de ressources (Découvrir) génère des entrées dans le flux d'activité (Communauté).
    - Les tags sont partagés entre la découverte et la navigation communautaire (hashtags).
    - Les playlists peuvent être partagées et commentées.

### Schéma Relationnel Simplifié (Illustratif)

```mermaid
erDiagram
    profiles ||--o{ music_resources : "possède"
    profiles ||--o{ playlists : "crée"
    profiles ||--o{ activity : "génère"
    profiles ||--o{ comments : "écrit"
    profiles ||--o{ likes : "donne"

    bands ||--o{ band_members : "a"
    profiles ||--o{ band_members : "est"
    bands ||--o{ activity : "génère (via membres)"

    music_resources ||--o{ resource_tags : "est tagué avec"
    tags ||--o{ resource_tags : "taggue"
    music_resources ||--o{ playlist_resources : "appartient à"
    playlists ||--o{ playlist_resources : "contient"
    music_resources ||--o{ comments : "a des"
    music_resources ||--o{ likes : "reçoit des"

    playlists ||--o{ comments : "a des"
    playlists ||--o{ likes : "reçoit des"

    activity ||--o{ activity_hashtags : "utilise"
    hashtags ||--o{ activity_hashtags : "est utilisé dans"
    activity ||--o{ comments : "a des"
    activity ||--o{ likes : "reçoit des (ex: post, ressource)" }

    %% Types d'activité exemples: post_text, add_resource, new_comment
    %% Note: Ce diagramme est une simplification.
    %% Se référer à database-schema.md pour les détails complets.
```

### Considérations de Sécurité, Performance et Extensibilité

- **Validation des Données**: Stricte validation des entrées à la fois côté client et serveur.
- **Gestion des API Externes**: Sécurisation des clés API et gestion des quotas.
- **Modularité**: Conception pour faciliter l'ajout de nouvelles sources de données ou fonctionnalités.
- **Tests**: Couverture des flux critiques par des tests.
- **Conformité RGPD**: Prise en compte des droits des utilisateurs sur leurs données.

---
Pour toute modification, se référer systématiquement à la roadmap, aux guidelines et à la liste des tâches pour garantir la cohérence globale du projet.
