// components/hook-form/rhf-switch.tsx
import React from 'react';
import { Control, FieldValues, FieldPath, useFormContext } from 'react-hook-form';
import { Switch } from '@/components/ui/switch';
import type { ComponentProps, ComponentPropsWithoutRef } from 'react';
import { FormField, FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form'; // Assuming FormMessage might be needed for some edge cases

interface RHFSwitchProps<TFieldValues extends FieldValues = FieldValues> extends Omit<React.ComponentPropsWithoutRef<typeof Switch>, 'name' | 'checked' | 'onCheckedChange'> {
  control?: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
  label: string;
  description?: string;
  className?: string;
  labelClassName?: string;
}

export const RHFSwitch = <TFieldValues extends FieldValues = FieldValues>({
  control: controlProp,
  name,
  label,
  description,
  className,
  labelClassName,
  ...rest
}: RHFSwitchProps<TFieldValues>) => {
  const context = useFormContext<TFieldValues>();
  const control = controlProp || context?.control;

  const { control: contextControl } = useFormContext<TFieldValues>() || {};
  const finalControl = controlProp || contextControl;

  if (!finalControl) {
    console.error('RHFSwitch requires control prop or to be used within a FormProvider.');
    return <FormItem className={className}><FormLabel>{label}</FormLabel><FormMessage>Control not found or FormProvider is missing.</FormMessage></FormItem>;
  }

  return (
    <FormField
      control={finalControl}
      name={name}
      render={({ field, fieldState: { error } }) => (
        <FormItem className={`flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm ${className || ''}`}>
          <div className="space-y-0.5">
            <FormLabel className={labelClassName}>{label}</FormLabel>
            {description && <FormDescription>{description}</FormDescription>}
          </div>
          <FormControl>
            <Switch
              checked={field.value as boolean | undefined}
              onCheckedChange={field.onChange}
              {...rest}
            />
          </FormControl>
          {/* FormMessage might be useful if there's a scenario where a switch can have an error directly associated with it */}
          {error && <FormMessage className="col-span-full">{error.message}</FormMessage>}
        </FormItem>
      )}
    />
  );
};