import { useState, useCallback } from 'react';

// Types pour la gestion des fichiers (états locaux)
export interface LocalFileState {
  file: File | null;
  previewUrl: string | null;
  uploadProgress: number;
  error: string | null;
  isUploading: boolean;
}

export interface UseLocalFileManagementProps {
  initialCoverArtUrl?: string | null;
  initialAudioUrl?: string | null;
  onCoverArtChange?: (file: File | null, previewUrl: string | null) => void;
  onAudioFileChange?: (file: File | null, previewUrl: string | null) => void;
}

export interface UseLocalFileManagementReturn {
  localCoverArtFile: LocalFileState;
  localAudioFile: LocalFileState;
  handleCoverArtSelect: (fileOrEvent: File[] | File | null, initialUrl?: string | null) => void;
  handleClearCoverArt: (initialUrl?: string | null) => void;
  handleAudioFileSelect: (fileOrEvent: File[] | File | null, initialUrl?: string | null) => void;
  handleClearAudio: (initialUrl?: string | null) => void;
  setLocalCoverArtFile: React.Dispatch<React.SetStateAction<LocalFileState>>;
  setLocalAudioFile: React.Dispatch<React.SetStateAction<LocalFileState>>;
}

const useLocalFileManagement = ({
  initialCoverArtUrl = null,
  initialAudioUrl = null,
  onCoverArtChange,
  onAudioFileChange,
}: UseLocalFileManagementProps): UseLocalFileManagementReturn => {
  const [localCoverArtFile, setLocalCoverArtFile] = useState<LocalFileState>({
    file: null,
    previewUrl: initialCoverArtUrl,
    uploadProgress: 0,
    error: null,
    isUploading: false,
  });

  const [localAudioFile, setLocalAudioFile] = useState<LocalFileState>({
    file: null,
    previewUrl: initialAudioUrl, // For audio, preview might be just the name or a waveform component might handle the file object
    uploadProgress: 0,
    error: null,
    isUploading: false,
  });

  const handleCoverArtSelect = useCallback((fileOrEvent: File[] | File | null, initialUrl?: string | null) => {
    // Revoke previous blob URL if it exists
    if (localCoverArtFile.previewUrl && localCoverArtFile.previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(localCoverArtFile.previewUrl);
    }
    let file: File | null = null;
    let previewUrl: string | null = null;

    if (Array.isArray(fileOrEvent) && fileOrEvent.length > 0) {
      file = fileOrEvent[0];
      previewUrl = URL.createObjectURL(file);
      console.log('Cover art selected from event:', file.name);
    } else if (fileOrEvent instanceof File) {
      file = fileOrEvent;
      previewUrl = URL.createObjectURL(file);
      console.log('Cover art set directly with file:', file.name);
    } else if (initialUrl) {
      previewUrl = initialUrl;
      console.log('Cover art set with initial URL:', initialUrl);
    }

    setLocalCoverArtFile({ file, previewUrl, uploadProgress: 0, error: null, isUploading: false });
    if (onCoverArtChange) onCoverArtChange(file, previewUrl);
  }, [localCoverArtFile.previewUrl, onCoverArtChange]);

  const handleClearCoverArt = useCallback((initialUrl?: string | null) => {
    if (localCoverArtFile.previewUrl && localCoverArtFile.previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(localCoverArtFile.previewUrl);
    }
    setLocalCoverArtFile({ file: null, previewUrl: initialUrl || null, uploadProgress: 0, error: null, isUploading: false });
    if (onCoverArtChange) onCoverArtChange(null, initialUrl || null);
    console.log(initialUrl ? `Cover art cleared and set to ${initialUrl}` : 'Cover art cleared');
  }, [localCoverArtFile.previewUrl, onCoverArtChange]);

  const handleAudioFileSelect = useCallback((fileOrEvent: File[] | File | null, initialUrl?: string | null) => {
    const oldPreviewUrl = localAudioFile.previewUrl;

    let file: File | null = null;
    let previewUrl: string | null = null;

    if (Array.isArray(fileOrEvent) && fileOrEvent.length > 0) {
      file = fileOrEvent[0];
      previewUrl = URL.createObjectURL(file);
      console.log('Audio file selected from event:', file.name);
    } else if (fileOrEvent instanceof File) {
      file = fileOrEvent;
      previewUrl = URL.createObjectURL(file);
      console.log('Audio file set directly with file:', file.name);
    } else if (initialUrl) {
      previewUrl = initialUrl;
      console.log('Audio file set with initial URL:', initialUrl);
    }
    
    setLocalAudioFile({ file, previewUrl, uploadProgress: 0, error: null, isUploading: false });
    if (onAudioFileChange) onAudioFileChange(file, previewUrl);

    // Revoke the OLD blob URL after a short delay
    // This gives React time to re-render with the new URL and for the new component instance to pick it up
    if (typeof oldPreviewUrl === 'string' && oldPreviewUrl.startsWith('blob:')) {
      setTimeout(() => {
        URL.revokeObjectURL(oldPreviewUrl);
        console.log('Old audio blob URL revoked:', oldPreviewUrl);
      }, 100); // 100ms delay, adjust if necessary
    }
  }, [localAudioFile.previewUrl, onAudioFileChange]);

  const handleClearAudio = useCallback((initialUrl?: string | null) => {
    if (localAudioFile.previewUrl && localAudioFile.previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(localAudioFile.previewUrl);
    }
    setLocalAudioFile({ file: null, previewUrl: initialUrl || null, uploadProgress: 0, error: null, isUploading: false });
    if (onAudioFileChange) onAudioFileChange(null, initialUrl || null);
    console.log(initialUrl ? `Audio file cleared and set to ${initialUrl}` : 'Audio file cleared');
  }, [localAudioFile.previewUrl, onAudioFileChange]);

  return {
    localCoverArtFile,
    localAudioFile,
    handleCoverArtSelect,
    handleClearCoverArt,
    handleAudioFileSelect,
    handleClearAudio,
    setLocalCoverArtFile, 
    setLocalAudioFile,
  };
};

export default useLocalFileManagement;