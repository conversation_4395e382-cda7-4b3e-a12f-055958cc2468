"use client"; 

import { useState, useEffect } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client'; 
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Music2, UserCircle, Disc, Clock, AlignLeft, Info, Mic2, GitFork, Palette, Guitar, Share2, Edit3, Heart } from 'lucide-react'; 
import { Badge } from '@/components/ui/badge';
import { formatDuration } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button'; 
import { PlayButton } from '@/components/audio/play-button'; 
import { LikeButton } from '@/components/social/like-button';
import { DislikeButton } from '@/components/social/dislike-button'; // Added DislikeButton import
import { ResourceViewTracker } from '@/components/stats/resource-view-tracker';
import { ArtistCardDisplay } from '@/components/artists/artist-card-display';
import { CommentSection } from '@/components/comments/comment-section';
import { SimilarSongs } from '@/components/songs/similar-songs'; 
import { useUser } from '@/contexts/user-context'; 
import type { Song } from '@/types'; 
import { SharePopover } from '@/components/shared/share-popover'; 
import { ResourceHeaderBanner } from '@/components/shared/resource-header-banner';
import { ResourceStatsDisplay } from '@/components/shared/ResourceStatsDisplay'; // Added

interface PublicSongData extends Omit<Song, 'are_comments_public'> { 
  profiles: { 
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
    id: string; 
  } | null;
  albums: { 
    id: string;
    title: string;
    cover_url: string | null;
    slug?: string | null; 
  } | null;
  like_count?: number; 
  view_count?: number;
  plays?: number; // Added for stats display
  dislike_count?: number; // Added for stats display
  // allow_comments is part of Song type
}

async function getSongBySlug(slug: string, supabaseClient: any): Promise<PublicSongData | null> {
  const supabase = supabaseClient;

  const { data: songData, error } = await supabase
    .from('songs')
    .select(`
      *, 
      profiles:user_id (id, username, display_name, avatar_url, bio, website, location_city, location_country),
      albums:album_id (id, title, cover_url, slug)
    `) // Removed are_comments_public from select
    .eq('slug', slug)
    .eq('is_public', true) 
    .single(); 

  if (error || !songData) {
    console.error('Error fetching public song by slug:', slug, error);
    return null;
  }
  
  let likeCount = 0;
  let viewCount = 0;

  const { data: likeCountData } = await supabase
    .rpc('get_like_count', { resource_id_param: songData.id, resource_type_param: 'song' });
  if (typeof likeCountData === 'number') likeCount = likeCountData;

  const { data: viewCountData } = await supabase
    .rpc('get_view_count', { resource_id_param: songData.id, resource_type_param: 'song' });
  if (typeof viewCountData === 'number') viewCount = viewCountData;

  const profilesData = Array.isArray(songData.profiles) 
    ? (songData.profiles[0] as PublicSongData['profiles'] || null) 
    : (songData.profiles as PublicSongData['profiles'] || null);

  const albumData = Array.isArray(songData.albums)
    ? (songData.albums[0] as PublicSongData['albums'] || null)
    : (songData.albums as PublicSongData['albums'] || null);

  // Adapt genre (TEXT) to genres (string[]) if needed
  let processedGenres: string[] | null = null;
  if (songData.genres && Array.isArray(songData.genres)) {
    processedGenres = songData.genres;
  } else if (typeof songData.genre === 'string' && songData.genre.trim() !== '') {
    processedGenres = songData.genre.split(',').map((g: string) => g.trim()).filter((g: string) => g !== '');
  }

  // Assuming songData from DB might have plays and dislike_count directly
  return {
    ...songData,
    genres: processedGenres, // Use the processed genres
    profiles: profilesData,
    albums: albumData,
    like_count: likeCount,
    view_count: viewCount,
    plays: songData.plays || 0, 
    dislike_count: songData.dislike_count || 0 
  } as PublicSongData;
}

export default function PublicSongPage({ params }: { params: { slug: string } }) {
  const supabase = getSupabaseClient();
  const { user: currentUser } = useUser();
  const [song, setSong] = useState<PublicSongData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLikedByCurrentUser, setIsLikedByCurrentUser] = useState(false); 
  const [isDislikedByCurrentUser, setIsDislikedByCurrentUser] = useState(false); // Added state for dislike

  useEffect(() => {
    const fetchSongData = async () => {
      setIsLoading(true);
      const fetchedSong = await getSongBySlug(params.slug, supabase);
      if (!fetchedSong) {
        notFound();
      } else {
        setSong(fetchedSong);
        if (currentUser && fetchedSong.id) {
          // Fetch like status
          const { data: likeData } = await supabase
            .from('likes')
            .select('id')
            .eq('resource_id', fetchedSong.id)
            .eq('resource_type', 'song')
            .eq('user_id', currentUser.id)
            .maybeSingle();
          setIsLikedByCurrentUser(!!likeData);

          // Fetch dislike status
          const { data: dislikeData } = await supabase
            .from('dislikes')
            .select('id')
            .eq('resource_id', fetchedSong.id)
            .eq('resource_type', 'song')
            .eq('user_id', currentUser.id)
            .maybeSingle();
          setIsDislikedByCurrentUser(!!dislikeData);
        } else {
          setIsLikedByCurrentUser(false);
          setIsDislikedByCurrentUser(false);
        }
      }
      setIsLoading(false);
    };
    fetchSongData();
  }, [params.slug, supabase, currentUser]);

  if (isLoading) {
    return <div className="container mx-auto max-w-5xl py-8 px-4 text-center">Chargement de la chanson...</div>;
  }

  if (!song) {
    notFound();
  }

  const artistDisplayName = song.profiles?.display_name || song.profiles?.username || 'Artiste inconnu';
  const coverArt = song.cover_url || song.albums?.cover_url;

  const musicalInfoBadges = (
    <>
      {song.genres && Array.isArray(song.genres) && song.genres.length > 0 && (
        song.genres.map((genre: string) => (
          <Badge key={`genre-${genre}`} variant="outline" className="text-xs">
            {genre}
          </Badge>
        ))
      )}
      {song.key && <Badge variant="outline" className="text-xs">Tonalité: {song.key}</Badge>}
      {song.bpm && <Badge variant="outline" className="text-xs">BPM: {song.bpm}</Badge>}
      {song.time_signature && <Badge variant="outline" className="text-xs">Rythme: {song.time_signature}</Badge>}
      {typeof song.capo === 'number' && <Badge variant="outline" className="text-xs">Capo: {song.capo}</Badge>}
    </>
  );

  // song object for PlayButton should be compatible with Song type from '@/types'
  // Ensure all necessary fields are present or defaulted.
  const songForPlayer: Song = {
    id: song.id,
    title: song.title,
    audio_url: song.audio_url || null,
    cover_art_url: coverArt || null, // Updated from cover_url
    duration_ms: song.duration || 0, // Updated from duration, assuming song.duration is in ms
    creator_user_id: song.user_id, // Updated from user_id
    artist_name: artistDisplayName,
    is_public: song.is_public ?? true,
    slug: song.slug ?? null,
    genres: song.genres ?? [],
    moods: song.moods ?? [],
    instrumentation: song.instrumentation ?? [],
    lyrics: song.lyrics ?? null,
    bpm: song.bpm ?? null,
    key: song.key ?? null,
    time_signature: song.time_signature ?? null, // Added for completeness from Song type
    capo: typeof song.capo === 'number' ? song.capo : null, // Added for completeness
    release_date: song.release_date ?? null, // Added for completeness
    ai_content_origin: song.ai_content_origin, 
    is_explicit: song.is_explicit ?? false, 
    status: song.status ?? 'published', 
    created_at: song.created_at || new Date().toISOString(), 
    updated_at: song.updated_at || new Date().toISOString(), 
    album_id: song.albums?.id || null,
    allow_comments: song.allow_comments ?? true, // from Song type
    // Ensure all other required fields from Song type are present or defaulted if necessary
  };
  
  const handleSongStatsUpdate = (newIsLiked: boolean, newLikeCount: number, newDislikeCount?: number) => {
    setSong(prev => {
      if (!prev) return null;
      return { 
        ...prev, 
        like_count: newLikeCount,
        dislike_count: newDislikeCount !== undefined ? newDislikeCount : prev.dislike_count 
      };
    });
    setIsLikedByCurrentUser(newIsLiked);
    if (newDislikeCount !== undefined) setIsDislikedByCurrentUser(false); // If liked, cannot be disliked
  };

  const handleSongDislikeUpdate = (newIsDisliked: boolean, newDislikeCount: number, newLikeCount?: number) => {
    setSong(prev => {
      if (!prev) return null;
      return { 
        ...prev, 
        dislike_count: newDislikeCount,
        like_count: newLikeCount !== undefined ? newLikeCount : prev.like_count
      };
    });
    setIsDislikedByCurrentUser(newIsDisliked);
    if (newLikeCount !== undefined) setIsLikedByCurrentUser(false); // If disliked, cannot be liked
  };


  return (
    <div className="pb-8"> 
      <ResourceViewTracker resourceId={song.id} resourceType="song" />
      
      <ResourceHeaderBanner
        coverUrl={coverArt}
        defaultIcon={<Music2 className="w-20 h-20 text-muted-foreground" />}
        musicalInfoLine={musicalInfoBadges}
        title={song.title}
        artistName={artistDisplayName}
        artistLink={song.profiles?.username ? `/artists/${song.profiles.username}` : (song.user_id ? `/profile/${song.user_id}` : undefined)}
        description={song.description}
        details={
          <>
            {song.duration && <span className="flex items-center"><Clock className="w-4 h-4 mr-1"/> {formatDuration(song.duration)}</span>}
            {song.release_date && <span>• Sorti le {format(new Date(song.release_date), 'd MMM yyyy', { locale: fr })}</span>}
            {song.albums && (
              <span>
                • De l'album <Link href={song.albums.slug ? `/album/${song.albums.slug}` : `/albums/${song.albums.id}`} className="hover:underline">{song.albums.title}</Link>
              </span>
            )}
          </>
        }
        actionButtons={
          <>
            {song.audio_url && <PlayButton song={songForPlayer} size="lg" />}
            {currentUser && (
              <LikeButton 
                resourceId={song.id} 
                resourceType="song" 
                initialLikes={song.like_count || 0} 
                initialIsLiked={isLikedByCurrentUser} 
                userId={currentUser.id}
                onLikeToggle={handleSongStatsUpdate}
                size="lg"
              />
            )}
            {currentUser && (
              <DislikeButton
                resourceId={song.id}
                resourceType="song"
                initialDislikes={song.dislike_count || 0}
                initialIsDisliked={isDislikedByCurrentUser}
                userId={currentUser.id}
                onDislikeToggle={handleSongDislikeUpdate} 
                size="lg"
              />
            )}
            <SharePopover
              resourceType="song"
              resourceSlug={song.slug || song.id}
              resourceTitle={song.title}
              triggerButton={
                <Button variant="outline" size="lg" title="Partager">
                  <Share2 className="mr-2 h-4 w-4" /> Partager
                </Button>
              }
            />
            {currentUser?.id === song.user_id && (
              <Button variant="outline" asChild title="Modifier le morceau" className="px-3">
                <Link href={`/songs/${song.id}/edit`}><Edit3 className="mr-2 h-4 w-4" /> Éditer</Link>
              </Button>
            )}
          </>
        }
        stats={
          <ResourceStatsDisplay
            resourceType="song"
            likeCount={song.like_count}
            dislikeCount={song.dislike_count}
            viewCount={song.view_count}
            playCount={song.plays}
          />
        }
      />

      <div className="lg:grid lg:grid-cols-3 lg:gap-x-8 px-4 sm:px-6 lg:px-8">
        <div className="lg:col-span-2 mb-8 lg:mb-0">
          <Tabs defaultValue="lyrics" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="lyrics">Paroles</TabsTrigger>
              <TabsTrigger value="details">Détails</TabsTrigger>
            </TabsList>
            <TabsContent value="lyrics">
              <Card>
                <CardHeader><CardTitle className="flex items-center"><AlignLeft className="mr-2"/>Paroles</CardTitle></CardHeader>
                <CardContent>
                  {song.lyrics ? (
                    <div className="whitespace-pre-wrap text-muted-foreground">{song.lyrics}</div>
                  ) : (
                    <p className="text-muted-foreground">Paroles non disponibles.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="details">
              <Card>
                <CardHeader><CardTitle className="flex items-center"><Info className="mr-2"/>Détails du morceau</CardTitle></CardHeader>
                <CardContent className="space-y-3 text-sm">
                  {song.description && <p className="text-muted-foreground whitespace-pre-wrap mb-4">{song.description}</p>}
                  <div className="flex flex-wrap gap-2">
                    {song.genres?.map((g: string) => <Badge key={g} variant="secondary">{g}</Badge>)}
                    {song.moods?.map((m: string) => <Badge key={m} variant="outline">{m}</Badge>)}
                  </div>
                  {song.instrumentation && song.instrumentation.length > 0 && (
                    <p><strong className="font-medium">Instrumentation:</strong> {song.instrumentation.join(', ')}</p>
                  )}
                  {song.key && <p><strong className="font-medium">Tonalité:</strong> {song.key}</p>}
                  {song.bpm && <p><strong className="font-medium">BPM:</strong> {song.bpm}</p>}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          <CommentSection 
            resourceId={song.id} 
            resourceType="song" 
            resourceCreatorId={song.user_id}
            isModeratorView={currentUser?.id === song.user_id}
            areCommentsPublic={song.allow_comments ?? true} // Use song.allow_comments for public visibility
          />
        </div>

        <aside className="lg:col-span-1 space-y-8">
          {song.profiles && <ArtistCardDisplay artist={song.profiles} />}

          {song.albums && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Fait partie de l'album</CardTitle>
              </CardHeader>
              <CardContent>
                <Link href={song.albums.slug ? `/album/${song.albums.slug}` : `/albums/${song.albums.id}`} className="flex items-center gap-3 group">
                  {song.albums.cover_url ? (
                    <Image 
                      src={song.albums.cover_url} 
                      alt={`Pochette de ${song.albums.title}`} 
                      width={64} 
                      height={64} 
                      className="rounded-md aspect-square object-cover" 
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-md bg-muted flex items-center justify-center">
                      <Disc className="w-8 h-8 text-muted-foreground" />
                    </div>
                  )}
                  <div>
                    <p className="font-semibold group-hover:underline">{song.albums.title}</p>
                    <p className="text-xs text-muted-foreground">Album par {artistDisplayName}</p>
                  </div>
                </Link>
              </CardContent>
            </Card>
          )}

          <SimilarSongs 
            currentSongId={song.id} 
            currentSongGenres={song.genres} 
            currentSongArtistId={song.user_id} 
          />
        </aside>
      </div>
    </div>
  );
}
