"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { getSupabaseClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { CheckCircle2, XCircle } from 'lucide-react';

interface InvitationCardActionsProps {
  invitationId: string;
  bandId: string;
  userId: string; // Current user's ID
  offeredRole: string | null;
  // onActionComplete: () => void; // Callback to refresh parent list - will use router.refresh() instead
}

export function InvitationCardActions({
  invitationId,
  bandId,
  userId,
  offeredRole,
  // onActionComplete,
}: InvitationCardActionsProps) {
  const supabase = getSupabaseClient();
  const router = useRouter(); // For router.refresh()
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleAccept = async () => {
    setIsLoading(true);
    try {
      // 1. Add user to band_members
      const { error: memberError } = await supabase.from("band_members").insert({
        band_id: bandId,
        user_id: userId,
        role: offeredRole || "member", // Default to 'member' if role is null
        is_admin: false, // New members are not admins by default
      });
      if (memberError) throw memberError;

      // 2. Update invitation status
      const { error: updateError } = await supabase
        .from("band_invitations")
        .update({ status: "accepted" })
        .eq("id", invitationId);
      if (updateError) throw updateError;

      toast({ title: "Invitation acceptée", description: "Vous avez rejoint le groupe !" });
      router.refresh(); // Refresh page data
    } catch (error: any) {
      toast({ title: "Erreur", description: `Impossible d'accepter l'invitation: ${error.message}`, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleReject = async () => {
    setIsLoading(true);
    try {
      const { error: updateError } = await supabase
        .from("band_invitations")
        .update({ status: "rejected" })
        .eq("id", invitationId);
      if (updateError) throw updateError;

      toast({ title: "Invitation refusée" });
      router.refresh(); // Refresh page data
    } catch (error: any) {
      toast({ title: "Erreur", description: `Impossible de refuser l'invitation: ${error.message}`, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex gap-2 pt-2">
      <Button size="sm" className="flex-1" onClick={handleAccept} disabled={isLoading}>
        <CheckCircle2 className="mr-2 h-4 w-4" />
        {isLoading ? "Acceptation..." : "Accepter"}
      </Button>
      <Button size="sm" variant="outline" className="flex-1" onClick={handleReject} disabled={isLoading}>
        <XCircle className="mr-2 h-4 w-4" />
        {isLoading ? "Refus..." : "Refuser"}
      </Button>
    </div>
  );
}
