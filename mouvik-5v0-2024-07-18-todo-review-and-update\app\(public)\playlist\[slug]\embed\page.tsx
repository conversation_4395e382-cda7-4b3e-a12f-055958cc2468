// Server-side imports and data fetching logic remain at the top level (Server Component context)
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { notFound } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { ListMusic, Music, Play, Clock } from "lucide-react";
import { formatDuration as formatDurationUtil } from '@/lib/utils';
import type { Song } from "@/types";

// Interfaces remain accessible to both Server and Client parts as needed
interface PlaylistSongForEmbed {
  id: string;
  title: string;
  duration: number | null;
  cover_url: string | null;
  audio_url?: string | null;
  artist_name?: string;
  profiles: {
    username: string | null;
    display_name: string | null;
  } | null;
  slug?: string | null;
}

interface PublicPlaylistEmbedDetails {
  id: string;
  name: string;
  description: string | null;
  cover_url: string | null;
  user_id: string;
  profiles: {
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
  } | null;
  playlist_songs: { songs: (PlaylistSongForEmbed & { slug?: string | null; audio_url?: string | null; user_id?: string; artist_name?: string; }) | null }[];
}

// Data fetching function - remains a server-side utility
async function getPlaylistDataForEmbed(slug: string, supabaseClient: any): Promise<PublicPlaylistEmbedDetails | null> {
  const { data: playlistDataResult, error: playlistError } = await supabaseClient
    .from("playlists")
    .select(`
      id, name, description, cover_url, user_id,
      profiles:user_id (username, display_name, avatar_url),
      playlist_songs:playlist_songs_view(
        song_id,
        songs:songs (id, title, duration, cover_url, audio_url, user_id, slug, profiles:user_id (username, display_name))
      )
    `)
    .eq("slug", slug)
    .eq("is_public", true)
    .single();

  if (playlistError || !playlistDataResult) {
    console.error("Error fetching public playlist for embed by slug:", slug, playlistError);
    return null;
  }
  return playlistDataResult as PublicPlaylistEmbedDetails;
}

import PlaylistEmbedView from "./PlaylistEmbedView";

// Server Component: Fetches data and passes it to the Client Component for rendering
export default async function PlaylistEmbedPage({ params }: { params: { slug: string } }) {
  const supabase = createSupabaseServerClient();
  const playlistData = await getPlaylistDataForEmbed(params.slug, supabase);

  if (!playlistData) {
    notFound();
  }

  return <PlaylistEmbedView playlistData={playlistData} params={params} />;
}
