'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Play, Pause, Square, SkipBack, SkipForward } from 'lucide-react';

interface StudioControlsProps {
  isPlaying: boolean;
  onPlayPause: () => void;
  onStop: () => void;
  onPrevious: () => void;
  onNext: () => void;
  currentTime: number;
  duration: number;
}

export const StudioControls: React.FC<StudioControlsProps> = ({
  isPlaying,
  onPlayPause,
  onStop,
  onPrevious,
  onNext,
  currentTime,
  duration
}) => {
  
  return (
    <div className="flex items-center gap-3">
      <Button variant="ghost" size="sm" onClick={onPrevious}>
        <SkipBack className="h-4 w-4" />
      </Button>
      <Button 
        variant={isPlaying ? "default" : "outline"} 
        size="sm" 
        onClick={onPlayPause}
      >
        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
      </Button>
      <Button variant="ghost" size="sm" onClick={onStop}>
        <Square className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="sm" onClick={onNext}>
        <SkipForward className="h-4 w-4" />
      </Button>
    </div>
  );
};
