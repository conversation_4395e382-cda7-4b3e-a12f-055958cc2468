"use client";

import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, Edit3, Trash2, PlayCircle, Music2, UserCircle, Disc, ListPlus, Heart, ThumbsUp, Copy, Loader2, ThumbsDown, Eye, Play as PlayIconSmall // Added ThumbsDown, Eye, PlayIconSmall, ThumbsUp
} from 'lucide-react';
import type { Song, UserProfile, Album } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useResourceInteractionStore, getResourceInteractionStoreState, type ResourceInteractionStoreState, getResourceKey, DEFAULT_RESOURCE_STATE } from '@/lib/stores/resource-interaction-store';
import { AddToPlaylistModal } from '@/components/playlists/add-to-playlist-modal';
import { useUser } from '@/contexts/user-context';
import { useAudioPlayerStore } from '@/lib/stores/audioPlayerStore'; // For Play and Add to Queue
import { PlayButton } from '@/components/audio/play-button'; // Re-using PlayButton component
import { AddToPlaylistButton } from '@/components/playlists/add-to-playlist-button'; // Import AddToPlaylistButton
import { LikeButton } from '@/components/social/like-button'; // Import LikeButton
import { DislikeButton } from '@/components/social/dislike-button'; // Import DislikeButton
import { getSupabaseClient } from '@/lib/supabase/client';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/lib/utils'; // Assuming a duration formatting util

// Extend Song type for card display
interface SongForCard extends Song { // is_public and slug are now part of base Song type
  profiles?: UserProfile | null; 
  albums?: Pick<Album, 'id' | 'title' | 'cover_url'> | null; 
  duration_ms: number | null; // Use duration_ms from schema
  plays?: number | null; // Use plays from schema, now optional
  // cover_art_url is expected to be part of the base Song type
}

interface SongCardProps {
  song: SongForCard;
  onDelete?: (songId: string) => void;
  onUpdateStatus?: (songId: string, newStatus: { is_public: boolean; slug: string | null }) => void;
  onStatsChange?: (songId: string, newStats: { 
    like_count: number; 
    dislike_count: number; 
    is_liked_by_user: boolean; 
    is_disliked_by_user: boolean; 
  }) => void; // This prop might be re-evaluated as store centralizes state
}

export function SongCard({ song, onDelete, onUpdateStatus, onStatsChange }: SongCardProps) {
  console.log(`SongCard received song: ${song.title}, cover_art_url: ${song.cover_art_url}, album_cover_url: ${song.albums?.cover_url}`);
  const initialImageSrc = song.cover_art_url || song.albums?.cover_url;
  const fallbackImageSrc = '/images/covers/mouvk.png'; // Ensure this image exists in public/images/covers
  const [currentImageSrc, setCurrentImageSrc] = useState(initialImageSrc || fallbackImageSrc);
  const [hasLoadError, setHasLoadError] = useState(false);

  useEffect(() => {
    setCurrentImageSrc(initialImageSrc || fallbackImageSrc);
    setHasLoadError(false); // Reset error state when song prop changes
  }, [initialImageSrc, fallbackImageSrc]);

  const handleImageError = () => {
    if (!hasLoadError) { // Prevent infinite loop if fallback also fails
      setCurrentImageSrc(fallbackImageSrc);
      setHasLoadError(true);
    }
  };
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const playSpecificSong = useAudioPlayerStore(state => state.playSong);
  const currentSong = useAudioPlayerStore(state => state.currentSong);
  const isPlaying = useAudioPlayerStore(state => state.isPlaying);
  // const addToQueue = useAudioPlayerStore(state => state.addToQueue); // TODO: Implement addToQueue in store
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);
  const [isAddToPlaylistModalOpen, setIsAddToPlaylistModalOpen] = useState(false);
  

  useEffect(() => {
    if (song && song.id) {
      getResourceInteractionStoreState().setResourceStatus('song', song.id, {
        isLiked: song.is_liked_by_current_user ?? false,
        likeCount: song.like_count ?? 0,
        isDisliked: song.is_disliked_by_current_user ?? false,
        dislikeCount: song.dislike_count ?? 0,
        // followerCount and isFollowed will use defaults from DEFAULT_RESOURCE_STATE or existing values in store
      });
    }
  }, [song]);

  const { 
    likeCount: currentLikeCount,
    dislikeCount: currentDislikeCount
  } = useResourceInteractionStore(
    (state: ResourceInteractionStoreState) => 
      state.resourceStates[getResourceKey('song', song.id)] || { 
        likeCount: song.like_count ?? 0, 
        dislikeCount: song.dislike_count ?? 0 
      }
  );

  const viewPageUrl = song.slug ? `/songs/${song.slug}` : `/songs/${song.id}`;
const durationMs: number | null = song.duration_ms ?? null; // Use duration_ms from schema, default to null

  const handleDelete = () => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le morceau "${song.title}" ?`)) {
      if (onDelete) onDelete(song.id);
      else toast({ title: "Suppression (Placeholder)"});
    }
  };

  // Connect Play button
  const handlePlay = () => {
    // The PlayButton component handles play logic itself using usePlaySong hook
    // If we use a custom button, we'd call:
    // playSpecificSong({ ...song, artist: artistDisplay }); 
    // For now, we will replace the placeholder Button with the actual PlayButton component.
  };

  const handleAddToQueue = () => {
    playSpecificSong({ ...song, artist_name: artistDisplay }); // Play it. Queue logic to be added to store.
    toast({ title: "Lecture demandée", description: `"${song.title}" devrait commencer à jouer.` });
  };
  
  // handleLike will be managed by LikeButton component
  
  const handleAddToPlaylist = () => {
    if (!user) {
      toast({ title: "Connexion requise", description: "Vous devez être connecté pour ajouter à une playlist.", variant: "destructive"});
      return;
    }
    setIsAddToPlaylistModalOpen(true);
  };
  const handleDuplicate = () => toast({ title: "Duplication (Placeholder)"});

  const togglePublicStatus = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!user || song.creator_user_id !== user.id || isTogglingStatus || song.is_public === undefined) return;

    setIsTogglingStatus(true);
    try {
      const { data, error } = await supabase.rpc('toggle_song_public_status', {
         p_song_id: song.id,
         p_user_id: user.id 
      });
      if (error) throw error;
      
      if (data && data.length > 0) {
        const { new_is_public, new_slug } = data[0];
        toast({ 
          title: "Statut du morceau mis à jour", 
          description: `Le morceau "${song.title}" est maintenant ${new_is_public ? 'public' : 'privé'}.`
        });
        if (onUpdateStatus) {
          onUpdateStatus(song.id, { is_public: new_is_public, slug: new_slug });
        }
      } else {
        throw new Error("Réponse invalide de la fonction RPC toggle_song_public_status.");
      }
    } catch (err: any) {
      console.error("Error toggling song status:", err);
      toast({ title: "Erreur", description: err.message || "Impossible de changer le statut du morceau.", variant: "destructive" });
    } finally {
      setIsTogglingStatus(false);
    }
  };
  
  const artistDisplay = song.profiles?.display_name || song.profiles?.username || song.artist_name || "Artiste inconnu";

  return (
    <Card className="overflow-hidden flex flex-col h-full group/card">
      <CardHeader className="p-0 relative">
        {song.is_public !== undefined && (
            <div 
                title={song.is_public ? "Public (cliquer pour changer)" : "Privé (cliquer pour changer)"}
                onClick={togglePublicStatus}
                className={cn(
                    "absolute top-2 left-2 z-10 w-4 h-4 rounded-full cursor-pointer flex items-center justify-center transition-all",
                    "ring-2 ring-offset-2 ring-offset-card", // Base ring for halo effect
                    isTogglingStatus 
                      ? "bg-yellow-500 ring-yellow-500/50 animate-pulse" // Yellow for loading, with halo
                      : song.is_public 
                        ? "bg-green-500 ring-green-500/50 hover:bg-green-400" // Green filled, green halo
                        : "bg-red-500 ring-red-500/50 hover:bg-red-400" // Red filled, red halo
                )}
            >
                {isTogglingStatus && <Loader2 className="h-2.5 w-2.5 animate-spin text-white" />} 
                {/* Ensure loader icon is visible on yellow, green, red */}
            </div>
        )}
        <Link href={viewPageUrl} className="block aspect-square relative group">
          <Image 
            src={currentImageSrc} 
            alt={song.title || 'Song cover'} 
            width={300} 
            height={300} 
            className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
            onError={handleImageError}
          />
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <PlayCircle className="w-12 h-12 text-white" />
          </div>
        </Link>
      </CardHeader>
      <CardContent className="p-3 flex-grow"> {/* Reduced padding slightly */}
        <Link href={viewPageUrl}>
          <CardTitle className="text-md font-semibold hover:underline mb-0.5 truncate" title={song.title}>
            {song.title}
          </CardTitle>
        </Link>
        <div className="text-xs text-muted-foreground mb-1.5 flex items-center flex-wrap">
          <Link href={`/artists/${song.profiles?.username || song.creator_user_id}`} className="hover:underline flex items-center gap-1 mr-1.5">
            {song.profiles?.avatar_url ? (
                <Image src={song.profiles.avatar_url} alt={artistDisplay} width={16} height={16} className="rounded-full" />
            ) : (
                <UserCircle className="w-3 h-3" />
            )}
            <span className="truncate" title={artistDisplay}>{artistDisplay}</span>
          </Link>
          {song.albums && (
            <>
              <span className="mx-1">•</span>
              <Link href={`/albums/${song.albums.id}`} className="hover:underline truncate" title={song.albums.title}>
                {song.albums.title}
              </Link>
            </>
          )}
        </div>
         {/* TODO: Add genre/mood tags if available on SongForCard */}
         {/* Stats Display */}
        <div className="mt-2 flex items-center gap-x-3 gap-y-1 flex-wrap text-xs text-muted-foreground">
          {song.plays !== undefined && (
            <span className="flex items-center gap-1" title="Écoutes"><PlayIconSmall className="w-3 h-3" /> {song.plays}</span>
          )}
          {/* Assuming view_count is available on song object, if not, it needs to be added to FetchedSongForPage */}
          {(song as any).view_count !== undefined && ( 
            <span className="flex items-center gap-1" title="Vues"><Eye className="w-3 h-3" /> {(song as any).view_count}</span>
          )}
          <span title={`${currentLikeCount ?? 0} likes`} className="flex items-center mr-3"><ThumbsUp className="w-3.5 h-3.5 mr-1" /> {currentLikeCount ?? 0}</span>
          <span title={`${currentDislikeCount ?? 0} dislikes`} className="flex items-center mr-3"><ThumbsDown className="w-3.5 h-3.5 mr-1" /> {currentDislikeCount ?? 0}</span>
        </div>
      </CardContent>
      <CardFooter className="p-3 pt-1 flex justify-between items-center">
        <div className="flex items-center gap-0.5"> {/* Reduced gap for more compact buttons */}
          <PlayButton song={{ ...song, artist_name: artistDisplay }} size="sm" variant="ghost" className="h-7 w-7" />
          
          <Button onClick={handleAddToQueue} size="icon" variant="ghost" className="h-7 w-7" title="Ajouter à la file d'attente">
            <ListPlus className="h-4 w-4" />
          </Button>

          {/* Direct AddToPlaylistButton - this component handles its own modal logic */}
          {user && <AddToPlaylistButton songId={song.id} />} 
          <div className="flex items-center space-x-1">
            <LikeButton resourceId={song.id} resourceType="song" size="sm" variant="ghost" className="h-7 w-7" />
            <span className="text-xs text-muted-foreground tabular-nums w-5 text-center min-w-[1.25rem]">{currentLikeCount ?? 0}</span>
          </div>
          <div className="flex items-center space-x-1">
            <DislikeButton resourceId={song.id} resourceType="song" size="sm" variant="ghost" className="h-7 w-7" />
            <span className="text-xs text-muted-foreground tabular-nums w-5 text-center min-w-[1.25rem]">{currentDislikeCount ?? 0}</span>
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-7 w-7">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Options</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleAddToPlaylist}><ListPlus className="mr-2 h-4 w-4" /> Ajouter à une playlist</DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push(`/manage-songs/${song.id}/edit`)}><Edit3 className="mr-2 h-4 w-4" /> Modifier</DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}><Copy className="mr-2 h-4 w-4" /> Dupliquer</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10">
              <Trash2 className="mr-2 h-4 w-4" /> Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>

      {isAddToPlaylistModalOpen && user && (
        <AddToPlaylistModal
          songId={song.id}
          isOpen={isAddToPlaylistModalOpen}
          onClose={() => setIsAddToPlaylistModalOpen(false)}
        />
      )}
    </Card>
  );
}
