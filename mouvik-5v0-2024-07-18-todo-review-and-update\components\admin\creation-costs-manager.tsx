"use client";

import { useState, useEffect } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';
import { Loader2, Save } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

interface CreationCost {
  resource_type: string;
  cost: number;
  updated_at?: string;
}

export function CreationCostsManager() {
  const supabase = getSupabaseClient();
  const [costs, setCosts] = useState<CreationCost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editableCosts, setEditableCosts] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchCosts = async () => {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('creation_costs')
        .select('resource_type, cost, updated_at')
        .order('resource_type');

      if (error) {
        toast({ title: "Erreur de chargement", description: "Impossible de charger les coûts de création.", variant: "destructive" });
        console.error("Error fetching creation costs:", error);
      } else {
        setCosts(data || []);
        const initialEditableCosts: Record<string, string> = {};
        (data || []).forEach(item => {
          initialEditableCosts[item.resource_type] = item.cost.toString();
        });
        setEditableCosts(initialEditableCosts);
      }
      setIsLoading(false);
    };
    fetchCosts();
  }, [supabase]);

  const handleCostChange = (resourceType: string, value: string) => {
    setEditableCosts(prev => ({ ...prev, [resourceType]: value }));
  };

  const handleSaveChanges = async () => {
    setIsSaving(true);
    const updates = costs.map(async (item) => {
      const newCostStr = editableCosts[item.resource_type];
      const newCost = parseInt(newCostStr, 10);

      if (isNaN(newCost) || newCost < 0) {
        toast({ title: "Valeur invalide", description: `Le coût pour ${item.resource_type} doit être un nombre positif.`, variant: "destructive" });
        return Promise.reject(new Error("Invalid cost value"));
      }

      if (newCost !== item.cost) {
        const { error } = await supabase
          .from('creation_costs')
          .update({ cost: newCost, updated_at: new Date().toISOString() })
          .eq('resource_type', item.resource_type);
        
        if (error) {
          toast({ title: "Erreur de sauvegarde", description: `Impossible de sauvegarder le coût pour ${item.resource_type}.`, variant: "destructive" });
          console.error(`Error updating cost for ${item.resource_type}:`, error);
          return Promise.reject(error);
        }
      }
      return Promise.resolve();
    });

    try {
      await Promise.all(updates);
      toast({ title: "Succès", description: "Coûts de création sauvegardés." });
      // Refetch costs to show updated_at and ensure consistency
      const { data, error } = await supabase.from('creation_costs').select('resource_type, cost, updated_at').order('resource_type');
      if (!error && data) {
        setCosts(data);
        const refreshedEditableCosts: Record<string, string> = {};
        data.forEach(i => { refreshedEditableCosts[i.resource_type] = i.cost.toString(); });
        setEditableCosts(refreshedEditableCosts);
      }
    } catch (error) {
      // Errors are toasted individually
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center py-8"><Loader2 className="h-6 w-6 animate-spin" /></div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Gestion des Coûts de Création</CardTitle>
        <CardDescription>Définissez le coût en pièces pour la création de différents types de contenu.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {costs.map((item) => (
          <div key={item.resource_type} className="flex items-center justify-between gap-4 p-2 border rounded-md">
            <Label htmlFor={`cost-${item.resource_type}`} className="capitalize flex-1">
              {item.resource_type}
              {item.updated_at && (
                <span className="block text-xs text-muted-foreground">
                  Mis à jour: {new Date(item.updated_at).toLocaleString()}
                </span>
              )}
            </Label>
            <Input
              id={`cost-${item.resource_type}`}
              type="number"
              min="0"
              value={editableCosts[item.resource_type] || '0'}
              onChange={(e) => handleCostChange(item.resource_type, e.target.value)}
              className="w-24"
              disabled={isSaving}
            />
          </div>
        ))}
        {costs.length === 0 && <p className="text-sm text-muted-foreground">Aucun coût de création trouvé. Les valeurs par défaut (0) seront utilisées.</p>}
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveChanges} disabled={isSaving || isLoading}>
          {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Sauvegarder les Coûts
        </Button>
      </CardFooter>
    </Card>
  );
}
