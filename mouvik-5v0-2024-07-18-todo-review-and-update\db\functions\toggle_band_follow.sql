CREATE OR REPLACE FUNCTION toggle_band_follow(
    p_band_id UUID,
    p_user_id UUID
)
RETURNS TABLE (is_following BOOLEAN, new_follower_count INTEGER)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_is_following BOOLEAN;
    v_new_follower_count INTEGER;
BEGIN
    -- Check if the user is already following
    SELECT EXISTS (
        SELECT 1
        FROM public.band_followers
        WHERE user_id = p_user_id AND band_id = p_band_id
    ) INTO v_is_following;

    IF v_is_following THEN
        -- Unfollow: Delete the record and decrement count
        DELETE FROM public.band_followers
        WHERE user_id = p_user_id AND band_id = p_band_id;

        UPDATE public.bands
        SET follower_count = GREATEST(0, follower_count - 1)
        WHERE id = p_band_id
        RETURNING follower_count INTO v_new_follower_count;
        
        v_is_following := FALSE;
    ELSE
        -- Follow: Insert the record and increment count
        INSERT INTO public.band_followers (user_id, band_id)
        VALUES (p_user_id, p_band_id);

        UPDATE public.bands
        SET follower_count = follower_count + 1
        WHERE id = p_band_id
        RETURNING follower_count INTO v_new_follower_count;
        
        v_is_following := TRUE;
    END IF;

    RETURN QUERY SELECT v_is_following, v_new_follower_count;
END;
$$;
