"use client";

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useF<PERSON>, SubmitH<PERSON>ler, Controller } from 'react-hook-form'; // Added Controller
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/hooks/use-toast';
import { Loader2, Save, ArrowLeft, ListMusic, ImageUp, Palette, Music2, Guitar, Eye, Trash2 } from 'lucide-react'; // Added Trash2
import Link from 'next/link';
import Image from 'next/image'; 
import { Card } from '@/components/ui/card'; 
import { ImageUploader } from "@/components/ui/image-uploader"; 
import { MultiSelect } from "@/components/ui/multi-select"; 
import { genreOptions, moodOptions, instrumentationOptions } from '@/lib/constants/song-options'; 
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd'; 
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from "@/components/ui/dialog"; // Added Dialog
import { Checkbox } from "@/components/ui/checkbox"; // Added Checkbox
import { Plus } from 'lucide-react'; // Added Plus icon

// Schema for editing a playlist
const playlistEditFormSchema = z.object({
  name: z.string().min(1, "Le nom est requis.").max(100, "Le nom ne doit pas dépasser 100 caractères."),
  description: z.string().max(500, "La description ne doit pas dépasser 500 caractères.").optional().nullable(),
  is_public: z.boolean().default(true),
  cover_url: z.string().url("URL de pochette invalide.").optional().nullable(),
  banner_url: z.string().url("URL de bannière invalide.").optional().nullable(),
  genres: z.array(z.string()).optional(),
  moods: z.array(z.string()).optional(),
  instrumentation: z.array(z.string()).optional(),
  are_comments_public: z.boolean().default(false).optional(), // Added for comment visibility
});

type PlaylistEditFormData = z.infer<typeof playlistEditFormSchema>;

interface PlaylistData {
  id: string;
  name: string;
  description: string | null;
  is_public: boolean;
  user_id: string;
  slug?: string | null;
  cover_url?: string | null; // Added
  banner_url?: string | null; // Added
  genres?: string[] | null; 
  moods?: string[] | null; 
  instrumentation?: string[] | null; 
  are_comments_public?: boolean; // Added for comment visibility
}

// Type for songs within the playlist for editing
interface PlaylistSongEditItem {
  playlist_song_id: string; // id from playlist_songs table
  song_id: string;
  title: string;
  artist_name: string | null; // Or from song.profiles.display_name
  cover_url: string | null;
  duration: number | null;
  track_number: number | null;
}

export default function EditPlaylistPage() {
  const supabase = getSupabaseClient();
  const { user } = useUser();
  const router = useRouter();
  const params = useParams();
  const playlistId = params.id as string;

  const [playlist, setPlaylist] = useState<PlaylistData | null>(null);
  const [playlistSongs, setPlaylistSongs] = useState<PlaylistSongEditItem[]>([]); 
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [initialSongOrder, setInitialSongOrder] = useState<PlaylistSongEditItem[]>([]); 

  const [availableSongs, setAvailableSongs] = useState<any[]>([]); // User's songs
  const [isAddSongModalOpen, setIsAddSongModalOpen] = useState(false);
  const [songsToAddInModal, setSongsToAddInModal] = useState<Set<string>>(new Set());


  const { register, handleSubmit, formState: { errors }, reset, control, watch, setValue } = useForm<PlaylistEditFormData>({ 
    resolver: zodResolver(playlistEditFormSchema),
  });

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
    if (!playlistId) {
        toast({title: "Erreur", description: "ID de playlist manquant.", variant: "destructive"});
        router.push('/playlists');
        return;
    }

    const fetchPlaylistAndSongs = async () => {
      setIsLoading(true);
      
      // Fetch playlist details
      const { data: playlistDetails, error: playlistError } = await supabase
        .from('playlists')
        .select('id, name, description, is_public, user_id, slug, cover_url, banner_url, genres, moods, instrumentation, are_comments_public') // Added are_comments_public
        .eq('id', playlistId)
        .single();

      if (playlistError || !playlistDetails) {
        toast({ title: "Erreur", description: "Playlist non trouvée ou accès non autorisé.", variant: "destructive" });
        console.error("Error fetching playlist for edit:", playlistError);
        router.push('/playlists');
        return;
      }
      if (playlistDetails.user_id !== user.id) {
        toast({ title: "Accès refusé", description: "Vous ne pouvez pas modifier cette playlist.", variant: "destructive" });
        router.push('/playlists');
        return;
      }
      const playlistData = playlistDetails as PlaylistData;
      setPlaylist(playlistData);
      reset({
        name: playlistData.name,
        description: playlistData.description,
        is_public: playlistData.is_public,
        cover_url: playlistData.cover_url || '',
        banner_url: playlistData.banner_url || '',
        genres: playlistData.genres || [],
        moods: playlistData.moods || [],
        instrumentation: playlistData.instrumentation || [],
        are_comments_public: playlistData.are_comments_public ?? false, // Populate are_comments_public
      });

      // Fetch playlist songs
      const { data: songsData, error: songsError } = await supabase
        .from('playlist_songs')
        .select(`
          id, 
          track_number,
          songs (id, title, cover_url, duration, profiles (display_name, username))
        `)
        .eq('playlist_id', playlistId)
        .order('track_number', { ascending: true, nullsFirst: false }); // Ensure consistent order

      if (songsError) {
        toast({ title: "Erreur", description: "Impossible de charger les morceaux de la playlist.", variant: "destructive" });
        console.error("Error fetching playlist songs for edit:", songsError);
      } else {
        const mappedSongs: PlaylistSongEditItem[] = (songsData || [])
          .map(item => {
            // Ensure item.songs is treated as an object, not an array, and handle if it's null
            const songDetails = item.songs as any; // Cast to any to bypass strict typing if Supabase types are complex for joins
            if (!songDetails) return null; // Skip if song data is missing

            return {
              playlist_song_id: item.id, 
              song_id: songDetails.id,
              title: songDetails.title,
              artist_name: songDetails.profiles?.display_name || songDetails.profiles?.username || "Inconnu",
              cover_url: songDetails.cover_url,
              duration: songDetails.duration,
              track_number: item.track_number,
            };
          })
          .filter((song): song is PlaylistSongEditItem => song !== null); 
        setPlaylistSongs(mappedSongs);
        setInitialSongOrder(JSON.parse(JSON.stringify(mappedSongs))); // Deep copy for comparison
      }
      setIsLoading(false);
    };

    fetchPlaylistAndSongs();
    loadAvailableSongs(); // Load user's songs for the modal
  }, [user, supabase, router, playlistId, reset]);

  const loadAvailableSongs = async () => {
    if (!user) return;
    try {
      const { data, error } = await supabase
        .from("songs")
        .select("id, title, cover_url, duration, artist_name, profiles(display_name, username)") // Fetch needed fields
        .eq("user_id", user.id)
        .eq("status", "published") // Only published songs
        .order("title", { ascending: true });

      if (error) throw error;
      setAvailableSongs(data || []);
    } catch (error) {
      console.error("Error loading available songs:", error);
      toast({ title: "Erreur", description: "Impossible de charger vos morceaux disponibles.", variant: "destructive" });
    }
  };

  const handleConfirmAddSongsFromModal = () => {
    const newSongDetails = availableSongs
      .filter(s => songsToAddInModal.has(s.id) && !playlistSongs.some(ps => ps.song_id === s.id))
      .map((s, index) => ({
        playlist_song_id: `new-${s.id}-${Date.now()}`, // Temporary ID for new items
        song_id: s.id,
        title: s.title,
        artist_name: s.profiles?.display_name || s.profiles?.username || s.artist_name || "Inconnu",
        cover_url: s.cover_url,
        duration: s.duration,
        track_number: playlistSongs.length + index + 1, // Append to end
      }));

    setPlaylistSongs(prev => [...prev, ...newSongDetails]);
    setIsAddSongModalOpen(false);
    setSongsToAddInModal(new Set());
  };

  const handleDeleteSongFromPlaylist = async (playlistSongId: string, songTitle: string) => {
    if (!user || !playlist) return;

    if (!window.confirm(`Êtes-vous sûr de vouloir retirer le morceau "${songTitle}" de cette playlist ?`)) {
      return;
    }

    // Optimistically update UI or show loading state if preferred
    // For now, just call delete and then refetch or filter
    
    const { error } = await supabase
      .from('playlist_songs')
      .delete()
      .eq('id', playlistSongId)
      .eq('playlist_id', playlist.id); // Ensure it's for the current playlist

    if (error) {
      toast({ title: "Erreur de suppression", description: `Impossible de retirer le morceau: ${error.message}`, variant: "destructive" });
      console.error("Error deleting song from playlist:", error);
    } else {
      toast({ title: "Morceau retiré", description: `"${songTitle}" a été retiré de la playlist.` });
      setPlaylistSongs(prevSongs => prevSongs.filter(s => s.playlist_song_id !== playlistSongId));
      // Optionally, could re-fetch song count for the main playlist object if displayed, or update it.
    }
  };

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) return;
    const items = Array.from(playlistSongs);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update track_number based on new order
    const updatedItems = items.map((item, index) => ({
      ...item,
      track_number: index + 1,
    }));
    setPlaylistSongs(updatedItems);
  };

  const onSubmit: SubmitHandler<PlaylistEditFormData> = async (data) => {
    if (!playlist) return;
    setIsSubmitting(true);
    let slugToSave: string | null = playlist.slug ?? null; 

    // Check if slug needs to be updated:
    // 1. If is_public is true AND (name changed OR (is_public changed from false to true AND no slug exists))
    // 2. If is_public is false, slug should be null
    const nameChanged = data.name !== playlist.name;
    const visibilityChangedToPublic = data.is_public && !playlist.is_public;
    const needsNewSlug = data.is_public && (nameChanged || visibilityChangedToPublic || !playlist.slug);

    if (!data.is_public) {
      slugToSave = null;
    } else if (needsNewSlug && data.name) {
      const { data: slugData, error: slugError } = await supabase.rpc('slugify', { value: data.name });
      if (slugError || !slugData) {
        console.error("Error generating slug:", slugError);
        toast({ title: "Erreur de slug", description: "Impossible de générer un slug.", variant: "destructive" });
        setIsSubmitting(false);
        return;
      }
      slugToSave = slugData;
      // Basic uniqueness handling (client-side retry with suffix)
      let isUnique = false;
      let attempt = 0;
      let tempSlug = slugToSave;
      while (!isUnique && attempt < 5) {
        // Check if slug exists for a *different* playlist
        const { data: existing, error: checkError } = await supabase.from('playlists').select('id').eq('slug', tempSlug).neq('id', playlist.id).maybeSingle();
        if (checkError) { break; }
        if (!existing) {
          isUnique = true;
          slugToSave = tempSlug;
        } else {
          attempt++;
          tempSlug = `${slugData}-${Math.random().toString(36).substring(2, 7)}`;
        }
      }
      if (!isUnique) {
        toast({ title: "Erreur de slug", description: "Impossible de générer un slug unique. Essayez un nom différent.", variant: "destructive" });
        setIsSubmitting(false);
        return;
      }
    }
    
    try {
      const updatePayload = {
        name: data.name,
        description: data.description,
        is_public: data.is_public,
        slug: slugToSave,
        cover_url: data.cover_url || null,
        banner_url: data.banner_url || null,
        genres: data.genres && data.genres.length > 0 ? data.genres : null,
        moods: data.moods && data.moods.length > 0 ? data.moods : null,
        instrumentation: data.instrumentation && data.instrumentation.length > 0 ? data.instrumentation : null,
        are_comments_public: data.are_comments_public, // Save comment visibility
        updated_at: new Date().toISOString(),
      };

      const { error: playlistUpdateError } = await supabase
        .from('playlists')
        .update(updatePayload)
        .eq('id', playlist.id);

      if (playlistUpdateError) throw playlistUpdateError;

      // Check if song order has changed
      const orderChanged = JSON.stringify(playlistSongs.map(s => s.playlist_song_id)) !== JSON.stringify(initialSongOrder.map(s => s.playlist_song_id));
      
      if (playlistUpdateError) throw playlistUpdateError;

      // Handle song list changes (new songs and reordering)
      // 1. Identify new songs (those without a persisted playlist_song_id or where playlist_song_id starts with 'new-')
      const newSongsToInsert = playlistSongs
        .filter(s => s.playlist_song_id.startsWith('new-'))
        .map((song, index) => ({
          playlist_id: playlist.id,
          song_id: song.song_id,
          track_number: index + 1, // This assumes playlistSongs is already correctly ordered
          user_id: user?.id // Add user_id if your playlist_songs table requires it
        }));

      // 2. Identify existing songs whose order might have changed
      const existingSongsToUpdateOrder = playlistSongs
        .filter(s => !s.playlist_song_id.startsWith('new-'))
        .map((song, index) => ({
          id: song.playlist_song_id,
          track_number: index + 1,
        }));
      
      // Perform DB operations for songs
      if (newSongsToInsert.length > 0) {
        const { error: insertError } = await supabase.from('playlist_songs').insert(newSongsToInsert);
        if (insertError) {
          console.error("Error inserting new songs to playlist:", insertError);
          toast({ title: "Erreur partielle", description: "Certains nouveaux morceaux n'ont pas pu être ajoutés.", variant: "destructive"});
        }
      }

      // Update order for all songs (new and existing) based on current playlistSongs order
      // This simplifies logic: delete all, then re-insert with correct order.
      // More optimized: update existing, insert new, delete removed (if any - not handled here yet)
      const { error: deleteError } = await supabase.from('playlist_songs').delete().eq('playlist_id', playlist.id);
      if (deleteError) {
         console.error("Error clearing old playlist songs:", deleteError);
         // Potentially stop or warn user
      }
      
      const finalPlaylistSongsToSave = playlistSongs.map((song, index) => ({
        playlist_id: playlist.id,
        song_id: song.song_id,
        track_number: index + 1,
        user_id: user?.id 
      }));

      if (finalPlaylistSongsToSave.length > 0) {
        const { error: finalInsertError } = await supabase.from('playlist_songs').insert(finalPlaylistSongsToSave);
        if (finalInsertError) {
          console.error("Error saving final playlist songs order:", finalInsertError);
          toast({ title: "Erreur partielle", description: "L'ordre des morceaux ou les nouveaux ajouts n'ont pas pu être sauvegardés.", variant: "destructive"});
        }
      }
      
      setInitialSongOrder(JSON.parse(JSON.stringify(playlistSongs))); 

      toast({ title: "Succès", description: `Playlist "${data.name}" mise à jour.` });
      router.push(`/playlists/${playlist.id}`); 
    } catch (error: any) {
      console.error("Error updating playlist:", error);
      if (error.code === '23505' && error.message.includes('playlists_slug_idx')) {
        toast({ title: "Erreur de slug", description: "Ce nom de playlist génère un slug déjà utilisé. Essayez un nom légèrement différent.", variant: "destructive" });
      } else {
        toast({ title: "Erreur de mise à jour", description: error.message, variant: "destructive" });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <div className="container py-8 flex justify-center items-center min-h-[calc(100vh-200px)]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }
  if (!playlist) {
    // This case should be handled by redirect in useEffect, but as a fallback:
    return <div className="container py-8 text-center">Playlist non trouvée.</div>;
  }

  return (
    <div className="container max-w-2xl py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold flex items-center">
          <ListMusic className="mr-3 h-8 w-8 text-primary" />
          Modifier la Playlist
        </h1>
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/playlists/${playlistId}`}><ArrowLeft className="mr-2 h-4 w-4" />Annuler</Link>
        </Button>
      </div>
      
      <p className="text-lg font-semibold mb-1">{playlist.name}</p>
      <p className="text-sm text-muted-foreground mb-6">ID: {playlist.id}</p>


      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <Label htmlFor="name">Nom de la playlist</Label>
          <Input id="name" {...register('name')} className="mt-1" />
          {errors.name && <p className="text-sm text-destructive mt-1">{errors.name.message}</p>}
        </div>

        <div>
          <Label htmlFor="description">Description (optionnel)</Label>
          <Textarea id="description" {...register('description')} className="mt-1" rows={4} />
          {errors.description && <p className="text-sm text-destructive mt-1">{errors.description.message}</p>}
        </div>

        <div className="flex items-center space-x-2">
          <Controller
            name="is_public"
            control={control}
            render={({ field }) => (
              <Switch
                id="is_public"
                checked={field.value}
                onCheckedChange={field.onChange}
                aria-label="Rendre la playlist publique"
              />
            )}
          />
          <Label htmlFor="is_public">Rendre la playlist publique</Label>
        </div>
        {errors.is_public && <p className="text-sm text-destructive mt-1">{errors.is_public.message}</p>}

        <div className="space-y-2">
          <Label htmlFor="cover_url" className="flex items-center"><ImageUp className="mr-2 h-4 w-4 text-muted-foreground" />Pochette</Label>
          <ImageUploader 
            onImageUploaded={(url) => setValue('cover_url', url, { shouldValidate: true })} 
            existingImageUrl={watch('cover_url') || undefined}
            bucketName="playlist-covers" // Changed underscore to hyphen
            // aspectRatio="square" // Removed, defaults to "free"
            maxWidth={1200} maxHeight={1200} 
          />
          <p className="text-xs text-muted-foreground mt-1">Max 1200x1200px, 5MB. Ratio libre.</p>
          {errors.cover_url && <p className="text-sm text-destructive mt-1">{errors.cover_url.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="banner_url" className="flex items-center"><ImageUp className="mr-2 h-4 w-4 text-muted-foreground" />Bannière</Label>
          <ImageUploader 
            onImageUploaded={(url) => setValue('banner_url', url, { shouldValidate: true })} 
            existingImageUrl={watch('banner_url') || undefined}
            bucketName="playlist-banners" 
            // aspectRatio="landscape" // Removed, defaults to "free"
            maxWidth={1600} maxHeight={900}
          />
          <p className="text-xs text-muted-foreground mt-1">Max 1600x900px, 5MB. Ratio libre.</p>
          {errors.banner_url && <p className="text-sm text-destructive mt-1">{errors.banner_url.message}</p>}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="genres" className="flex items-center"><Music2 className="mr-2 h-4 w-4 text-muted-foreground" />Genres</Label>
            <MultiSelect options={genreOptions} selected={watch('genres') || []} onChange={(selected) => setValue('genres', selected)} placeholder="Sélectionnez des genres" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="moods" className="flex items-center"><Palette className="mr-2 h-4 w-4 text-muted-foreground" />Ambiances</Label>
            <MultiSelect options={moodOptions} selected={watch('moods') || []} onChange={(selected) => setValue('moods', selected)} placeholder="Sélectionnez des ambiances" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="instrumentation" className="flex items-center"><Guitar className="mr-2 h-4 w-4 text-muted-foreground" />Instrumentation</Label>
            <MultiSelect options={instrumentationOptions} selected={watch('instrumentation') || []} onChange={(selected) => setValue('instrumentation', selected)} placeholder="Sélectionnez des instruments" />
          </div>
        </div>

        <div className="flex items-center space-x-2 pt-2">
          <Controller
            name="are_comments_public"
            control={control}
            render={({ field }) => (
              <Switch
                id="are_comments_public"
                checked={field.value}
                onCheckedChange={field.onChange}
                aria-label="Rendre les commentaires publics"
              />
            )}
          />
          <Label htmlFor="are_comments_public">Rendre les commentaires publics ?</Label>
        </div>
        {errors.are_comments_public && <p className="text-sm text-destructive mt-1">{errors.are_comments_public.message}</p>}
        
        {/* Placeholder for managing songs in playlist */}
        <div className="pt-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-semibold">Morceaux dans la playlist</h2>
              <Dialog open={isAddSongModalOpen} onOpenChange={setIsAddSongModalOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline"><Plus className="mr-2 h-4 w-4" />Ajouter des morceaux</Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[625px]">
                  <DialogHeader><DialogTitle>Ajouter des morceaux</DialogTitle><DialogDescription>Sélectionnez des morceaux de votre bibliothèque à ajouter à cette playlist.</DialogDescription></DialogHeader>
                  <div className="max-h-[60vh] overflow-y-auto p-1 space-y-1">
                    {availableSongs.filter(s => !playlistSongs.some(ps => ps.song_id === s.id)).length > 0 ?
                      availableSongs.filter(s => !playlistSongs.some(ps => ps.song_id === s.id)).map(song => (
                        <div key={song.id} className="flex items-center justify-between p-2 hover:bg-muted/50 rounded-md border">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id={`song-modal-${song.id}`}
                              checked={songsToAddInModal.has(song.id)}
                              onCheckedChange={(checked) => {
                                setSongsToAddInModal(prev => {
                                  const newSet = new Set(prev);
                                  if (checked) newSet.add(song.id);
                                  else newSet.delete(song.id);
                                  return newSet;
                                });
                              }}
                            />
                            {song.cover_url && <Image src={song.cover_url} alt={song.title} width={40} height={40} className="rounded-md object-cover" />}
                            {!song.cover_url && <div className="w-10 h-10 rounded-md bg-muted flex items-center justify-center"><Music2 className="w-5 h-5 text-muted-foreground"/></div>}
                            <div>
                              <Label htmlFor={`song-modal-${song.id}`} className="font-medium cursor-pointer">{song.title}</Label>
                              <p className="text-xs text-muted-foreground">{song.artist_name || song.profiles?.display_name || "Artiste inconnu"}</p>
                            </div>
                          </div>
                        </div>
                      ))
                      : <p className="text-sm text-muted-foreground text-center py-4">Tous vos morceaux publiés sont déjà dans cette playlist ou vous n'avez pas d'autres morceaux publiés.</p>
                    }
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => {setIsAddSongModalOpen(false); setSongsToAddInModal(new Set());}}>Annuler</Button>
                    <Button type="button" onClick={handleConfirmAddSongsFromModal} disabled={songsToAddInModal.size === 0}>Ajouter ({songsToAddInModal.size})</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
            {playlistSongs.length > 0 ? (
              <DragDropContext onDragEnd={onDragEnd}>
                <Droppable droppableId="playlistSongs">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                      {playlistSongs.map((song, index) => (
                        <Draggable key={song.playlist_song_id} draggableId={song.playlist_song_id} index={index}>
                          {(providedDraggable) => (
                            <Card
                              ref={providedDraggable.innerRef}
                              {...providedDraggable.draggableProps}
                              {...providedDraggable.dragHandleProps}
                              className="flex items-center p-3 gap-3 bg-card hover:bg-muted/80 transition-shadow"
                            >
                              <span className="text-sm text-muted-foreground w-6 text-center cursor-grab">{index + 1}</span>
                              {song.cover_url ? (
                                <Image src={song.cover_url} alt={song.title} width={40} height={40} className="rounded-md aspect-square object-cover" />
                              ) : (
                                <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center">
                                  <Music2 className="h-5 w-5 text-muted-foreground" />
                                </div>
                              )}
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate" title={song.title}>{song.title}</p>
                                <p className="text-xs text-muted-foreground truncate" title={song.artist_name || undefined}>{song.artist_name || "Artiste inconnu"}</p>
                              </div>
                              <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  onClick={() => handleDeleteSongFromPlaylist(song.playlist_song_id, song.title)} 
                                  title="Supprimer de la playlist"
                                  className="h-8 w-8"
                              >
                                  <Trash2 className="h-4 w-4 text-destructive"/>
                              </Button>
                            </Card>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">Cette playlist est vide. Ajoutez des morceaux depuis leurs pages respectives.</p>
            )}
        </div>


        <div className="flex justify-end gap-4 pt-8 items-center"> {/* Increased top padding */}
          {playlist && (
            <Link 
              href={playlist.is_public && playlist.slug ? `/playlist/${playlist.slug}` : `/playlists/${playlist.id}`} 
              passHref 
              target="_blank" 
              rel="noopener noreferrer"
            >
              <Button type="button" variant="outline" disabled={isSubmitting}>
                <Eye className="mr-2 h-4 w-4" />
                Prévisualiser
              </Button>
            </Link>
          )}
          <Button type="button" variant="outline" onClick={() => router.push(`/playlists/${playlistId}`)} disabled={isSubmitting}>
            Annuler
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Sauvegarder les modifications
          </Button>
        </div>
      </form>
    </div>
  );
}
