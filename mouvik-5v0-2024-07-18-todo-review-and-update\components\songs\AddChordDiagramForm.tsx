import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { ChordInstrument, ChordDiagramProps } from './ChordDiagram'; // Assurez-vous que ChordDiagramProps est exporté

interface AddChordDiagramFormProps {
  onAdd: (diagram: Omit<ChordDiagramProps, 'key'>) => void; // Omit 'key' si elle est présente
}

const AddChordDiagramForm: React.FC<AddChordDiagramFormProps> = ({ onAdd }) => {
  // Define a type for the structure of the chord JSON data for better type safety
  type ChordJsonData = {
    main: {
      strings: number;
      fretsOnChord: number;
      name: string;
      numberOfChords: number;
    };
    tunings: {
      [tuningName: string]: string[];
    };
    keys: string[];
    suffixes: string[];
    chords: {
      [key: string]: Array<{
        key: string;
        suffix: string;
        positions: Array<{
          frets: (number | string)[]; // Can be number or 'x' for muted
          fingers: (number | string)[];
          baseFret: number;
          barres: number[];
          capo?: boolean;
        }>;
      }>;
    };
  };

  const [instrument, setInstrument] = useState<ChordInstrument>('guitar');
  const [key, setKey] = useState('C');
  const [suffix, setSuffix] = useState('major');
  const [variantIdx, setVariantIdx] = useState<number>(0);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [suffixes, setSuffixes] = useState<string[]>([]);
  const [variants, setVariants] = useState<any[]>([]);
  const [chordsDb, setChordsDb] = useState<ChordJsonData | null>(null);

  // Charger dynamiquement la base d'accords selon l'instrument
  useEffect(() => {
    let url = '';
    if (instrument === 'guitar') url = '/chords/guitar.json';
    else if (instrument === 'ukulele') url = '/chords/ukulele.json';
    else if (instrument === 'mandolin') url = '/chords/mandolin.json';
    // Ajoute d'autres instruments ici si besoin
    else url = '';
    if (!url) {
      setChordsDb(null);
      setSuggestions([]);
      setSuffixes([]);
      setVariants([]);
      setVariantIdx(0);
      return;
    }
    fetch(url)
      .then(res => res.json() as Promise<ChordJsonData>)
      .then(data => {
        setChordsDb(data);
        setSuggestions(data.keys || []);
        setSuffixes(data.suffixes || []);
        setVariants([]);
        setVariantIdx(0);
      });
  }, [instrument]);

  // Quand la tonalité ou le type change, chercher les variantes
  useEffect(() => {
    if (!key || !suffix || !chordsDb) return;
    const chordsForInstrument = chordsDb.chords;
    if (chordsForInstrument && chordsForInstrument[key]) {
      const foundChords = chordsForInstrument[key].filter((c: any) => c.suffix === suffix);
      if (foundChords.length > 0 && foundChords[0].positions && foundChords[0].positions.length > 0) {
        setVariants(foundChords[0].positions);
        setVariantIdx(0);
      } else {
        setVariants([]);
      }
    } else {
      setVariants([]);
    }
  }, [key, suffix, chordsDb]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!key || !suffix || !chordsDb || variants.length === 0) {
      console.warn('Form not fully submitted or no variants available.');
      // Optionally, show a toast message to the user
      return;
    }

    const selectedVariantPosition = variants[variantIdx];

    if (!selectedVariantPosition || !selectedVariantPosition.frets) {
      console.warn(`Selected variant for ${key}${suffix} at index ${variantIdx} is invalid or missing frets.`);
      // Optionally, show a toast message to the user
      onAdd({
        instrument,
        chord: `${key}${suffix} (Variante invalide)`,
        positions: [],
      });
      return;
    }

    // Construct the chord name for display (e.g., Cmajor, Amin, G7)
    // Suffixes like 'major' or 'minor' are often shortened or implied in chord names.
    // This is a simple concatenation; you might want more sophisticated naming.
    let displayChordName = key;
    if (suffix !== 'major') { // Often 'major' is implied, e.g., 'C' instead of 'Cmajor'
        // You might want to map suffixes to common abbreviations, e.g., 'minor' to 'm', '7' to '7'
        displayChordName += suffix.replace(/^major$/i, '').replace(/^minor$/i, 'm');
    }

    const numStrings = chordsDb.main.strings;
    // Assuming 'standard' tuning for now, this could be made selectable if JSON supports multiple tunings per file
    const actualTuning = chordsDb.tunings.standard; 

    const transformedPositions: Array<[number, number | 'x', (string | number)?]> = selectedVariantPosition.frets.map((fret, index) => {
      const finger = selectedVariantPosition.fingers[index];
      // In JSON, -1 means not played. Convert to 'x'.
      // Fret numbers are 0-indexed from baseFret in VexFlow, but here frets are absolute or relative to baseFret if baseFret > 1
      // The ChordDiagram component expects fret numbers directly.
      // The 'frets' array in JSON is from low E to high e for guitar.
      // Our 'positions' prop is [string_index_from_low_0_based, fret, finger_label?]
      let displayFret: number | 'x' = typeof fret === 'number' && fret === -1 ? 'x' : (fret as number);
      
      // If baseFret is > 1, the fret numbers in the JSON are relative to baseFret-1
      // However, Vexchords in ChordDiagram.tsx expects absolute fret numbers for its 'chord' array.
      // The JSON 'frets' seem to be absolute if baseFret=1, or relative if baseFret > 1 (need to confirm this convention from library)
      // For now, assuming frets in JSON are absolute if baseFret=1, or need baseFret added if not 'x'
      // This part might need adjustment based on how 'frets' and 'baseFret' are defined by the chord-json-library
      if (typeof displayFret === 'number' && selectedVariantPosition.baseFret > 1) {
         // This logic is tricky: if 'frets' are 0 for open string when baseFret > 1, it's an issue.
         // Assuming 'frets' in JSON are the actual fret numbers to press, or -1 for mute.
         // If a barre is applied at baseFret, and a fret is marked '1', it means baseFret itself.
         // The Vexchords `draw` function with `baseFret` handles this, but we are passing absolute positions.
         // For now, let's assume the frets in JSON are absolute for simplicity, or that ChordDiagram's Vexchords handles baseFret if we pass it.
         // The current ChordDiagram does not explicitly use baseFret for Vexchords drawing, it expects absolute fret numbers.
      }

      return [index, displayFret, finger === 0 ? undefined : String(finger)];
    });

    const newDiagram: Omit<ChordDiagramProps, 'key'> = {
      instrument,
      chord: displayChordName,
      positions: transformedPositions,
      actualTuning: actualTuning, // Pass the actual tuning notes
      numStrings: numStrings,     // Pass the number of strings
      // baseFret and barres could be passed if ChordDiagram is enhanced to use them for Vexchords
    };

    onAdd(newDiagram);
    // Optionally reset parts of the form, or leave as is for quick successive additions
    // For example, you might want to keep the instrument and key, but reset suffix and variant.
    // setSuffix('major'); // Reset to default suffix
    // setVariantIdx(0); // Reset to first variant
  };

  return (
    <div className="p-4 border rounded-md space-y-3 bg-muted/20">
      <h4 className="text-sm font-medium">Ajouter un diagramme d'accord</h4>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        <div>
          <Label htmlFor="instrument-select">Instrument</Label>
          <Select value={instrument} onValueChange={(value) => setInstrument(value as ChordInstrument)}>
            <SelectTrigger id="instrument-select">
              <SelectValue placeholder="Choisir instrument" />
            </SelectTrigger>
            <SelectContent className="bg-background border shadow-md">
              <SelectItem value="guitar">Guitare</SelectItem>
              <SelectItem value="piano">Piano</SelectItem>
              <SelectItem value="ukulele">Ukulélé</SelectItem>
              <SelectItem value="mandolin">Mandoline</SelectItem>
              <SelectItem value="banjo">Banjo</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="key-select">Tonalité</Label>
          <Select value={key} onValueChange={setKey}>
            <SelectTrigger id="key-select">
            <SelectValue placeholder="Tonalité" />
          </SelectTrigger>
          <SelectContent className="bg-background border shadow-md">
            {suggestions.map((k, idx) => (
              <SelectItem key={idx} value={k}>{k}</SelectItem>
            ))}
          </SelectContent>
          </Select>
          <Label htmlFor="suffix-select" className="mt-2">Type</Label>
          <Select value={suffix} onValueChange={setSuffix}>
          <SelectTrigger id="suffix-select">
            <SelectValue placeholder="Type" />
            </SelectTrigger>
        <SelectContent className="bg-background border shadow-md">
          {suffixes.map((s, idx) => (
            <SelectItem key={idx} value={s}>{s}</SelectItem>
          ))}
        </SelectContent>
      </Select>
        </div>
      </div>
      {/* Sélecteur de variante si plusieurs positions */}
      {variants.length > 1 && (
        <div className="mt-2">
          <Label>Variante</Label>
          <Select value={String(variantIdx)} onValueChange={v => setVariantIdx(Number(v))}>
            <SelectTrigger><SelectValue placeholder="Variante" /></SelectTrigger>
            <SelectContent>
              {variants.map((v, idx) => (
                <SelectItem key={idx} value={String(idx)}>Variante {idx + 1}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
      <Button type="button" onClick={handleSubmit} size="sm">Ajouter le diagramme</Button>
    </div>
  );
};

export default AddChordDiagramForm;
