"use client"
import { useState } from "react"
import type React from "react"

import { useRouter } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// Added MultiSelect and options
import { MultiSelect } from "@/components/ui/multi-select" 
import { genreOptions, moodOptions, instrumentationOptions, countryOptions } from '@/lib/constants/song-options'; // Added countryOptions
import { useToast } from "@/hooks/use-toast"
import { ImageUploader } from "@/components/ui/image-uploader"
import { slugify } from "@/lib/utils"; 
// import { revalidatePath } from "next/cache"; // No longer directly used here
import { Switch } from "@/components/ui/switch"; // Added Switch import
import { revalidateBandPaths } from "@/lib/actions/band-actions"; // Import the server action
// Label is already imported on line 8

export default function CreateBandPage() {
  const router = useRouter()
  const { toast } = useToast()
  const supabase = getSupabaseClient()

  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    genres: [] as string[], // Changed from genre: ""
    moods: [] as string[],  // Added
    instrumentation: [] as string[], // Added
    countries: [] as string[], // Added countries
    location: "",
    avatar_url: "",
    cover_url: "",
    are_comments_public: false, // New state for comment visibility, default private
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  // handleSelectChange is no longer needed for single genre select
  // MultiSelect will use inline updates: (selected) => setFormData(prev => ({ ...prev, fieldName: selected }))

  const handleImageUpload = (type: "avatar" | "cover", url: string) => {
    setFormData((prev) => ({
      ...prev,
      [type === "avatar" ? "avatar_url" : "cover_url"]: url,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const { data: userData, error: userError } = await supabase.auth.getUser()

      if (userError || !userData.user) { // Ensure user is available
        toast({ title: "Erreur", description: "Utilisateur non authentifié.", variant: "destructive" });
        setIsLoading(false);
        return;
      }
      
      let bandSlug = slugify(formData.name); // Generate initial slug

      // Handle slug uniqueness
      let isSlugUnique = false;
      let attempt = 0;
      const baseSlug = bandSlug; // Keep the original slug for suffixing

      while (!isSlugUnique && attempt < 10) { // Try up to 10 times
        const { data: existingBand, error: checkError } = await supabase
          .from("bands")
          .select("id")
          .eq("slug", bandSlug)
          .maybeSingle();

        if (checkError) {
          console.error("Error checking slug uniqueness:", checkError);
          toast({ title: "Erreur", description: "Erreur lors de la vérification du slug.", variant: "destructive" });
          setIsLoading(false);
          return;
        }

        if (!existingBand) {
          isSlugUnique = true;
        } else {
          attempt++;
          // Generate a new slug with a random suffix (e.g., my-band-a2b3c)
          bandSlug = `${baseSlug}-${Math.random().toString(36).substring(2, 7)}`;
        }
      }

      if (!isSlugUnique) {
        toast({ title: "Erreur de Slug", description: "Impossible de générer un slug unique pour ce nom de groupe. Veuillez essayer un nom légèrement différent.", variant: "destructive" });
        setIsLoading(false);
        return;
      }

      // Create the band
      const { data: bandData, error: bandError } = await supabase
        .from("bands")
        .insert({
          name: formData.name,
          description: formData.description,
          genres: formData.genres.length > 0 ? formData.genres : null,
          moods: formData.moods.length > 0 ? formData.moods : null,
          instrumentation: formData.instrumentation.length > 0 ? formData.instrumentation : null,
          countries: formData.countries.length > 0 ? formData.countries : null,
          location: formData.location,
          avatar_url: formData.avatar_url,
          cover_url: formData.cover_url,
          creator_id: userData.user.id,
          slug: bandSlug,
          are_comments_public: formData.are_comments_public // Re-added after adding column to DB
        })
        .select("id, slug") 
        .single()

      if (bandError) throw bandError
      if (!bandData) throw new Error("La création du groupe a échoué, aucune donnée retournée.");


      // Add the creator as the owner
      const { error: memberError } = await supabase.from("band_members").insert({
        band_id: bandData.id,
        user_id: userData.user.id,
        role: "owner",
        permissions: ["manage_members", "manage_projects", "edit_band", "delete_band"],
      })

      if (memberError) throw memberError

      toast({
        title: "Groupe créé avec succès",
        description: `Le groupe "${formData.name}" a été créé.`,
      })

      // Redirect to the new band's public page using slug if available, otherwise ID.
      // Assuming public band page will be at /bands/[slug] or /bands/id/[id] if no slug page yet
      // For now, let's assume the authenticated page is the target, but we need to fix the 404.
      // The user wants a "prettier URL", so slug is preferred.
      // For now, redirect to the authenticated page by ID.
      // The 404 on this page needs to be fixed first. // This comment is now outdated as 404 should be fixed.
      
      // Revalidate paths using the server action
      const revalidationResult = await revalidateBandPaths(bandData.id, bandData.slug);
      if (!revalidationResult.success) {
        // Optionally handle revalidation error, e.g., log it or show a subtle toast
        console.warn("Revalidation failed after band creation:", revalidationResult.error);
      }

      // Redirect to the new public band page using slug if available
      if (bandData.slug) {
        router.push(`/bands/${bandData.slug}`);
      } else {
        // Fallback to ID-based authenticated page if slug somehow wasn't generated or saved.
        // Using /manage-bands/[id] as the standard authenticated page for a band
        router.push(`/manage-bands/${bandData.id}`); 
      }
    } catch (error) {
      console.error("Error creating band:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la création du groupe.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="container max-w-3xl py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Créer un nouveau groupe</h1>
        <p className="text-muted-foreground">Créez un groupe pour collaborer avec d'autres artistes</p>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informations du groupe</CardTitle>
            <CardDescription>Entrez les informations de base de votre groupe</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Nom du groupe</Label>
              <Input
                id="name"
                name="name"
                placeholder="Entrez le nom du groupe"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Décrivez votre groupe et son style musical"
                value={formData.description}
                onChange={handleChange}
                rows={4}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="genres">Genres</Label>
                <MultiSelect
                  options={genreOptions}
                  selected={formData.genres}
                  onChange={(selected) => setFormData((prev) => ({ ...prev, genres: selected }))}
                  placeholder="Sélectionnez des genres"
                  className="w-full"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="moods">Ambiances / Moods</Label>
                <MultiSelect
                  options={moodOptions}
                  selected={formData.moods}
                  onChange={(selected) => setFormData((prev) => ({ ...prev, moods: selected }))}
                  placeholder="Sélectionnez des ambiances"
                  className="w-full"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="instrumentation">Instrumentation</Label>
                <MultiSelect
                  options={instrumentationOptions}
                  selected={formData.instrumentation}
                  onChange={(selected) => setFormData((prev) => ({ ...prev, instrumentation: selected }))}
                  placeholder="Sélectionnez des instruments"
                  className="w-full"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="countries">Pays</Label>
                <MultiSelect
                  options={countryOptions}
                  selected={formData.countries}
                  onChange={(selected) => setFormData((prev) => ({ ...prev, countries: selected }))}
                  placeholder="Sélectionnez des pays"
                  className="w-full"
                />
              </div>
            </div>
            
            <div className="space-y-2"> {/* Location can remain as a free text field for city/region */}
              <Label htmlFor="location">Localisation (Ville/Région)</Label>
              <Input
                id="location"
                name="location"
                placeholder="Ex: Paris, Île-de-France"
                value={formData.location}
                onChange={handleChange}
              />
            </div>

            <div className="space-y-2">
              <Label>Avatar du groupe</Label>
              <ImageUploader
                onImageUploaded={(url) => handleImageUpload("avatar", url)}
                existingImageUrl={formData.avatar_url}
                aspectRatio="free" // Changed from "square" to "free"
                maxWidth={300}
                maxHeight={300} // Added maxHeight for clarity with free aspect ratio
                bucketName="band-avatars"
              />
              <p className="text-xs text-muted-foreground">Recommandé: image carrée, minimum 300x300px</p>
            </div>

            <div className="space-y-2">
              <Label>Image de couverture</Label>
              <ImageUploader
                onImageUploaded={(url) => handleImageUpload("cover", url)}
                existingImageUrl={formData.cover_url}
                aspectRatio="free" // Changed from "landscape" to "free"
                maxWidth={1200}
                maxHeight={1200} // Added maxHeight for clarity, adjust if a specific landscape-ish limit is still desired
                bucketName="band-covers"
              />
              <p className="text-xs text-muted-foreground">Recommandé: image au format 16:9, minimum 1200x675px</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="are_comments_public"
                  checked={formData.are_comments_public}
                  onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, are_comments_public: checked }))}
                />
                <Label htmlFor="are_comments_public" className="cursor-pointer">
                  Rendre les commentaires publics ?
                </Label>
              </div>
              <p className="text-xs text-muted-foreground">
                Si coché, les commentaires seront visibles par tous. Sinon, ils seront privés (visibles uniquement par les membres du groupe).
              </p>
            </div>

          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Création en cours..." : "Créer le groupe"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
