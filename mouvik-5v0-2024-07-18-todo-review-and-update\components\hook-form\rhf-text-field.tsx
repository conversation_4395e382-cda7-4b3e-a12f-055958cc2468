// components/hook-form/rhf-text-field.tsx
import React from 'react';
import { Control, FieldValues, FieldPath, useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import type { ComponentProps } from 'react';
import { FormField, FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

interface RHFTextFieldProps<TFieldValues extends FieldValues = FieldValues> extends Omit<ComponentProps<'input'>, 'name' | 'value' | 'onChange' | 'onBlur'> {
  control?: Control<TFieldValues>; // Made control optional
  name: FieldPath<TFieldValues>;
  label: string;
  description?: string;
  className?: string;
}

export const RHFTextField = <TFieldValues extends FieldValues = FieldValues>({
  control: controlProp,
  name,
  label,
  description,
  className,
  ...rest
}: RHFTextFieldProps<TFieldValues>) => {
  const context = useFormContext<TFieldValues>();
  const control = controlProp || context?.control;

  // The control prop is now primarily for explicit passing, 
  // but FormField will use context if control is not provided to it.
  // We rely on FormProvider to make control available to FormField.
  const { control: contextControl } = useFormContext<TFieldValues>() || {};
  const finalControl = controlProp || contextControl;

  if (!finalControl) {
    console.error('RHFTextField requires control prop or to be used within a FormProvider.');
    return <FormItem className={className}><FormLabel>{label}</FormLabel><FormMessage>Control not found or FormProvider is missing.</FormMessage></FormItem>;
  }

  return (
    <FormField
      control={finalControl}
      name={name}
      render={({ field, fieldState: { error } }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input {...field} value={field.value === null ? '' : field.value} {...rest} />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormItem>
      )}
    />
  );
};