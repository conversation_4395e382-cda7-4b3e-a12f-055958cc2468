export const LANGUAGE_OPTIONS = [
  { value: 'fr', label: 'Français' },
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Español' },
  { value: 'de', label: '<PERSON><PERSON><PERSON>' },
  { value: 'it', label: 'Italiano' },
  { value: 'pt', label: 'Português' },
  { value: 'other', label: 'Autre' },
];

export const SONG_STATUS_OPTIONS = [
  { value: 'draft', label: 'Brouillon' },
  { value: 'in_progress', label: 'En cours' },
  { value: 'completed', label: 'Terminé' },
  { value: 'released', label: 'Sorti / Publié' },
  { value: 'archived_status', label: 'Archivé (Statut)' }, 
];

export const ATTRIBUTION_TYPE_OPTIONS = [
  { value: 'all_rights_reserved', label: 'Tous droits réservés' },
  { value: 'cc_by', label: 'Creative Commons - Attribution (CC BY)' },
  { value: 'cc_by_sa', label: 'Creative Commons - Attribution-ShareAlike (CC BY-SA)' },
  { value: 'cc_by_nd', label: 'Creative Commons - Attribution-NoDerivs (CC BY-ND)' },
  { value: 'cc_by_nc', label: 'Creative Commons - Attribution-NonCommercial (CC BY-NC)' },
  { value: 'cc_by_nc_sa', label: 'Creative Commons - Attribution-NonCommercial-ShareAlike (CC BY-NC-SA)' },
  { value: 'cc_by_nc_nd', label: 'Creative Commons - Attribution-NonCommercial-NoDerivs (CC BY-NC-ND)' },
  { value: 'public_domain', label: 'Domaine Public' },
  { value: 'other', label: 'Autre (préciser)' },
];

export const GENRES_OPTIONS = [
  { value: 'acoustic', label: 'Acoustique' },
  { value: 'alternative_rock', label: 'Rock Alternatif' },
  { value: 'ambient', label: 'Ambient' },
  { value: 'blues', label: 'Blues' },
  { value: 'classical', label: 'Classique' },
  { value: 'country', label: 'Country' },
  { value: 'dance', label: 'Dance' },
  { value: 'electronic', label: 'Électronique' },
  { value: 'experimental', label: 'Expérimental' },
  { value: 'folk', label: 'Folk' },
  { value: 'funk', label: 'Funk' },
  { value: 'hip_hop', label: 'Hip Hop' },
  { value: 'indie_pop', label: 'Indie Pop' },
  { value: 'jazz', label: 'Jazz' },
  { value: 'latin', label: 'Latino' },
  { value: 'metal', label: 'Metal' },
  { value: 'pop', label: 'Pop' },
  { value: 'punk', label: 'Punk' },
  { value: 'rnb', label: 'R&B' },
  { value: 'reggae', label: 'Reggae' },
  { value: 'rock', label: 'Rock' },
  { value: 'soul', label: 'Soul' },
  { value: 'world_music', label: 'Musique du Monde' },
  { value: 'other', label: 'Autre' },
];

export const SUBGENRES_OPTIONS = [
  // Rock
  { value: 'hard_rock', label: 'Hard Rock', group: 'Rock' },
  { value: 'prog_rock', label: 'Rock Progressif', group: 'Rock' },
  { value: 'psychedelic_rock', label: 'Rock Psychédélique', group: 'Rock' },
  // Electronic
  { value: 'techno', label: 'Techno', group: 'Électronique' },
  { value: 'house', label: 'House', group: 'Électronique' },
  { value: 'trance', label: 'Trance', group: 'Électronique' },
  { value: 'drum_and_bass', label: 'Drum and Bass', group: 'Électronique' },
  // Hip Hop
  { value: 'trap', label: 'Trap', group: 'Hip Hop' },
  { value: 'boom_bap', label: 'Boom Bap', group: 'Hip Hop' },
  // Metal
  { value: 'heavy_metal', label: 'Heavy Metal', group: 'Metal' },
  { value: 'death_metal', label: 'Death Metal', group: 'Metal' },
  { value: 'black_metal', label: 'Black Metal', group: 'Metal' },
  // Jazz
  { value: 'swing', label: 'Swing', group: 'Jazz' },
  { value: 'bebop', label: 'Bebop', group: 'Jazz' },
  { value: 'fusion', label: 'Fusion', group: 'Jazz' },
  { value: 'other', label: 'Autre' },
];

export const MOODS_OPTIONS = [
  { value: 'aggressive', label: 'Agressif' },
  { value: 'calm', label: 'Calme' },
  { value: 'dark', label: 'Sombre' },
  { value: 'dreamy', label: 'Rêveur' },
  { value: 'energetic', label: 'Énergique' },
  { value: 'epic', label: 'Épique' },
  { value: 'happy', label: 'Joyeux' },
  { value: 'introspective', label: 'Introspectif' },
  { value: 'melancholic', label: 'Mélancolique' },
  { value: 'peaceful', label: 'Paisible' },
  { value: 'romantic', label: 'Romantique' },
  { value: 'sad', label: 'Triste' },
  { value: 'uplifting', label: 'Entraînant' },
  { value: 'other', label: 'Autre' },
];

export const THEMES_OPTIONS = [
  { value: 'love', label: 'Amour' },
  { value: 'breakup', label: 'Rupture' },
  { value: 'nature', label: 'Nature' },
  { value: 'party', label: 'Fête' },
  { value: 'protest', label: 'Contestation' },
  { value: 'spirituality', label: 'Spiritualité' },
  { value: 'storytelling', label: 'Narration' },
  { value: 'travel', label: 'Voyage' },
  { value: 'war', label: 'Guerre' },
  { value: 'work', label: 'Travail' },
  { value: 'other', label: 'Autre' },
];

export const INSTRUMENTS_OPTIONS = [
  { value: 'guitar_acoustic', label: 'Guitare acoustique' },
  { value: 'guitar_electric', label: 'Guitare électrique' },
  { value: 'bass', label: 'Basse' },
  { value: 'drums', label: 'Batterie' },
  { value: 'piano', label: 'Piano' },
  { value: 'keyboard', label: 'Clavier' },
  { value: 'synthesizer', label: 'Synthétiseur' },
  { value: 'violin', label: 'Violon' },
  { value: 'cello', label: 'Violoncelle' },
  { value: 'saxophone', label: 'Saxophone' },
  { value: 'trumpet', label: 'Trompette' },
  { value: 'vocals', label: 'Voix' },
  { value: 'other', label: 'Autre' },
];

export const MUSICAL_KEY_OPTIONS = [
  { value: 'C', label: 'C' },
  { value: 'C#', label: 'C#' },
  { value: 'Db', label: 'Db' },
  { value: 'D', label: 'D' },
  { value: 'D#', label: 'D#' },
  { value: 'Eb', label: 'Eb' },
  { value: 'E', label: 'E' },
  { value: 'F', label: 'F' },
  { value: 'F#', label: 'F#' },
  { value: 'Gb', label: 'Gb' },
  { value: 'G', label: 'G' },
  { value: 'G#', label: 'G#' },
  { value: 'Ab', label: 'Ab' },
  { value: 'A', label: 'A' },
  { value: 'A#', label: 'A#' },
  { value: 'Bb', label: 'Bb' },
  { value: 'B', label: 'B' },
  { value: 'Cm', label: 'Cm' },
  { value: 'C#m', label: 'C#m' },
  { value: 'Dbm', label: 'Dbm' },
  { value: 'Dm', label: 'Dm' },
  { value: 'D#m', label: 'D#m' },
  { value: 'Ebm', label: 'Ebm' },
  { value: 'Em', label: 'Em' },
  { value: 'Fm', label: 'Fm' },
  { value: 'F#m', label: 'F#m' },
  { value: 'Gbm', label: 'Gbm' },
  { value: 'Gm', label: 'Gm' },
  { value: 'G#m', label: 'G#m' },
  { value: 'Abm', label: 'Abm' },
  { value: 'Am', label: 'Am' },
  { value: 'A#m', label: 'A#m' },
  { value: 'Bbm', label: 'Bbm' },
  { value: 'Bm', label: 'Bm' },
  { value: 'Other', label: 'Autre' },
];

export const VISIBILITY_OPTIONS = [
  { value: 'public', label: 'Public' },
  { value: 'private', label: 'Privé' },
  { value: 'unlisted', label: 'Non listé' },
];

export const STATUS_OPTIONS = [
  { value: 'idea', label: 'Idée' },
  { value: 'wip', label: 'En cours (WIP)' },
  { value: 'demo', label: 'Démo' },
  { value: 'completed', label: 'Terminé' },
  { value: 'released', label: 'Sorti' },
];

export const CREATION_PROCESS_OPTIONS = [
  { value: 'human_only', label: '100% Humain' },
  { value: 'hybrid', label: 'Hybride (Humain + IA)' },
  { value: 'ai_assisted', label: 'Assisté par IA' },
  { value: 'ai_generated', label: 'Généré par IA (expérimental)' },
];

export const PROGRESS_STATUS_OPTIONS = [
  { value: 'not_started', label: 'Pas commencé' },
  { value: 'in_progress', label: 'En cours' },
  { value: 'basic_done', label: 'Structure de base / Démo' },
  { value: 'detailed', label: 'Détaillé / Avancé' },
  { value: 'completed', label: 'Terminé' },
  { value: 'on_hold', label: 'En pause' },
  { value: 'cancelled', label: 'Annulé' },
];

export const TIME_SIGNATURE_OPTIONS = [
  { value: '2/4', label: '2/4' },
  { value: '3/4', label: '3/4' },
  { value: '4/4', label: '4/4' },
  { value: '5/4', label: '5/4' },
  { value: '6/8', label: '6/8' },
  { value: '7/8', label: '7/8' },
  { value: '9/8', label: '9/8' },
  { value: '12/8', label: '12/8' },
  { value: 'Other', label: 'Autre' },
];