'use client'

import React, { useState, useEffect, useMemo } from 'react';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress'; // Using Progress for the global bar

// Define the sections with their properties
// Icons are emojis, labels are French as per user spec
// Colors are placeholders, can be refined to soft neons
const songSectionsConfig = [
  { id: 'melody', label: 'Mélodie principale', icon: '🎵', color: 'hsl(190, 70%, 60%)' },
  { id: 'lyrics', label: 'Paroles', icon: '✍️', color: 'hsl(210, 70%, 60%)' },
  { id: 'harmony', label: 'Harmonie / accords', icon: '🎹', color: 'hsl(260, 70%, 65%)' },
  { id: 'structure', label: 'Structure du morceau', icon: '🧱', color: 'hsl(30, 90%, 60%)' },
  { id: 'performance', label: 'Interprétation / voix', icon: '🧑‍🎤', color: 'hsl(320, 70%, 65%)' },
  { id: 'mixing', label: 'Mixage', icon: '🎧', color: 'hsl(120, 60%, 60%)' },
  { id: 'mastering', label: 'Mastering', icon: '🪄', color: 'hsl(50, 80%, 60%)' },
];

export type SongProgressData = {
  [key: string]: number; // e.g., { melody: 50, lyrics: 75 }
};

interface SongProgressTrackerProps {
  value: SongProgressData;
  onChange: (newProgressData: SongProgressData) => void;
}

const SongProgressTracker: React.FC<SongProgressTrackerProps> = ({ value, onChange }) => {
  const [currentProgress, setCurrentProgress] = useState<SongProgressData>(value || {});

  useEffect(() => {
    // Ensure all sections are initialized in the state if not present in initial value
    const initialisedProgress: SongProgressData = {};
    let changed = false;
    songSectionsConfig.forEach(section => {
      if (value && value[section.id] !== undefined) {
        initialisedProgress[section.id] = value[section.id];
      } else {
        initialisedProgress[section.id] = 0; // Default to 0 if not provided
        if (!value || value[section.id] === undefined) changed = true;
      }
    });
    setCurrentProgress(initialisedProgress);
    if (changed) { // If we added default 0s, notify parent
        onChange(initialisedProgress);
    }
  }, [value, onChange]);

  const handleSliderChange = (sectionId: string, newValue: number) => {
    const newProgress = {
      ...currentProgress,
      [sectionId]: newValue,
    };
    setCurrentProgress(newProgress);
    onChange(newProgress);
  };

  const overallAverage = useMemo(() => {
    const progresses = Object.values(currentProgress);
    if (progresses.length === 0) return 0;
    const sum = progresses.reduce((acc, p) => acc + p, 0);
    return Math.round(sum / progresses.length);
  }, [currentProgress]);

  return (
    <div className="p-4 bg-card-foreground/5 dark:bg-card-foreground/10 rounded-lg shadow space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-1">Avancement Global</h3>
        <div className="flex items-center gap-3">
          {/* Placeholder for segmented global progress bar */}
          {/* For now, using a simple Progress component */}
          <Progress value={overallAverage} className="w-full h-4 [&>div]:bg-gradient-to-r [&>div]:from-pink-500 [&>div]:via-purple-500 [&>div]:to-indigo-500" />
          <span className="text-lg font-bold tabular-nums">{overallAverage}%</span>
        </div>
        <p className="text-xs text-muted-foreground mt-1">Moyenne de toutes les sections.</p>
      </div>

      <div className="space-y-4">
        {songSectionsConfig.map((section) => (
          <div key={section.id} className="space-y-1.5">
            <div className="flex justify-between items-center">
              <Label htmlFor={`slider-${section.id}`} className="text-sm font-medium">
                <span role="img" aria-label={section.label} className="mr-2">{section.icon}</span>
                {section.label}
              </Label>
              <span className="text-sm font-semibold tabular-nums" style={{ color: section.color }}>
                {currentProgress[section.id] || 0}%
              </span>
            </div>
            <input
              type="range"
              id={`slider-${section.id}`}
              min="0"
              max="100"
              value={currentProgress[section.id] || 0}
              onChange={(e) => handleSliderChange(section.id, parseInt(e.target.value, 10))}
              className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer accent-primary"
              // Style the thumb and track using accent color (can be customized further)
              // For specific track color based on section.color, would need more complex styling or a custom slider component
              style={{ accentColor: section.color }}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default SongProgressTracker;
