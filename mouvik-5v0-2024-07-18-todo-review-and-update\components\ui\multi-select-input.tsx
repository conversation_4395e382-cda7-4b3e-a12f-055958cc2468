"use client"

import * as React from "react";
import { X } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
  CommandInput,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";

export interface Option {
  value: string;
  label: string;
  disable?: boolean;
  fixed?: boolean;
}

interface MultiSelectProps {
  options: Option[];
  selectedValues: string[];
  onValuesChange: (newValues: string[]) => void;
  placeholder?: string;
  className?: string;
  id?: string;
  creatable?: boolean;
}

export function MultiSelectInput({
  options,
  selectedValues,
  onValuesChange,
  placeholder = "Select options...",
  className,
  id,
  creatable = false,
}: MultiSelectProps) {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");

  const handleSelect = (option: Option) => {
    if (!selectedValues.includes(option.value)) {
      onValuesChange([...selectedValues, option.value]);
    }
    setInputValue("");
    setOpen(false);
  };

  const handleRemove = (valueToRemove: string) => {
    const option = options.find(opt => opt.value === valueToRemove);
    if (option && option.fixed) return; // Do not remove fixed options
    onValuesChange(selectedValues.filter((value) => value !== valueToRemove));
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (inputRef.current && event.key === "Enter" && inputValue.trim()) {
      event.preventDefault();
      const trimmedValue = inputValue.trim();
      if (creatable) {
        if (!selectedValues.includes(trimmedValue) && !options.some(opt => opt.value === trimmedValue || opt.label === trimmedValue)) {
          onValuesChange([...selectedValues, trimmedValue]);
        }
        setInputValue("");
        setOpen(false);
      } else {
        const firstMatchingOption = filteredOptions.find(opt => opt.label.toLowerCase() === trimmedValue.toLowerCase());
        if (firstMatchingOption && !selectedValues.includes(firstMatchingOption.value)) {
          handleSelect(firstMatchingOption);
        }
      }
    } else if (inputRef.current && event.key === "Backspace" && !inputValue && selectedValues.length > 0) {
      const lastSelected = selectedValues[selectedValues.length - 1];
      const option = options.find(opt => opt.value === lastSelected);
      if (!option || !option.fixed) {
         handleRemove(lastSelected);
      }
    }
  };

  const filteredOptions = options.filter(
    (option) =>
      !selectedValues.includes(option.value) &&
      (option.label.toLowerCase().includes(inputValue.toLowerCase()) || 
       option.value.toLowerCase().includes(inputValue.toLowerCase()))
  );

  const getDisplayLabel = (value: string) => {
    const option = options.find((opt) => opt.value === value);
    return option ? option.label : value;
  };

  return (
    <Command onKeyDown={handleKeyDown} className={cn("overflow-visible bg-transparent", className)}>
      <div className="group rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2">
        <div className="flex flex-wrap gap-1">
          {selectedValues.map((value) => {
            const option = options.find(opt => opt.value === value);
            return (
              <Badge key={value} variant={option?.fixed ? "default" : "secondary"}>
                {getDisplayLabel(value)}
                {!option?.fixed && (
                  <button
                    type="button"
                    onClick={() => handleRemove(value)}
                    className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    aria-label={`Remove ${getDisplayLabel(value)}`}
                  >
                    <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                  </button>
                )}
              </Badge>
            );
          })}
          <CommandInput
            ref={inputRef}
            id={id}
            value={inputValue}
            onValueChange={setInputValue} 
            onBlur={() => setOpen(false)}
            onFocus={() => setOpen(true)}
            placeholder={selectedValues.length > 0 ? "" : placeholder}
            className="ml-2 flex-1 bg-transparent outline-none placeholder:text-muted-foreground p-0 h-auto" 
          />
        </div>
      </div>
      <div className="relative mt-2">
        {open && (filteredOptions.length > 0 || (creatable && inputValue.trim())) ? (
          <div className="absolute top-0 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
            <CommandList>
              <CommandGroup>
                {inputValue.trim() && creatable && !options.some(opt => opt.label.toLowerCase() === inputValue.trim().toLowerCase() || opt.value.toLowerCase() === inputValue.trim().toLowerCase()) && (
                  <CommandItem
                    onSelect={() => {
                      if (!selectedValues.includes(inputValue.trim())) {
                        onValuesChange([...selectedValues, inputValue.trim()]);
                      }
                      setInputValue("");
                      setOpen(false);
                    }}
                    value={`create-${inputValue.trim()}`}
                  >
                    Créer "{inputValue.trim()}"
                  </CommandItem>
                )}
                {filteredOptions.map((option) => (
                  <CommandItem
                    key={option.value}
                    onSelect={() => handleSelect(option)}
                    disabled={option.disable}
                    value={option.value} 
                  >
                    {option.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </div>
        ) : null}
      </div>
    </Command>
  );
}
