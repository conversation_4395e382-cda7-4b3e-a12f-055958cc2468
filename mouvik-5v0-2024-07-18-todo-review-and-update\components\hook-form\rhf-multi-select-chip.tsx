// components/hook-form/rhf-multi-select-chip.tsx
import React from 'react';
import { Control, FieldValues, FieldPath, useFormContext } from 'react-hook-form';
import { MultiSelect, type Option, type MultiSelectProps } from '@/components/ui/multi-select';
import type { MultiSelectProps as InternalMultiSelectProps } from '@/components/ui/multi-select'; // Import with an alias if needed or define locally
import { FormField, FormItem, FormLabel, FormMessage, FormControl, FormDescription } from '@/components/ui/form';

// If MultiSelectProps is not directly available, you might need to define a relevant subset of props
interface RHFMultiSelectChipProps<TFieldValues extends FieldValues = FieldValues> 
  extends Omit<MultiSelectProps, 'selected' | 'onChange' | 'options'> { // Omit props handled by RHF, MultiSelect uses 'selected' and 'onChange'
  control?: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
  label: string;
  options: Option[]; // Ensure Option type is correctly imported or defined
  placeholder?: string;
  description?: string;
  className?: string;
}

export const RHFMultiSelectChip = <TFieldValues extends FieldValues = FieldValues>({
  control: controlProp,
  name,
  label,
  options,
  placeholder,
  description,
  className,
  ...rest
}: RHFMultiSelectChipProps<TFieldValues>) => {
  const context = useFormContext<TFieldValues>();
  const control = controlProp || context?.control;

  const { control: contextControl } = useFormContext<TFieldValues>() || {};
  const finalControl = controlProp || contextControl;

  if (!finalControl) {
    console.error('RHFMultiSelectChip requires control prop or to be used within a FormProvider.');
    return <FormItem className={className}><FormLabel>{label}</FormLabel><FormMessage>Control not found or FormProvider is missing.</FormMessage></FormItem>;
  }

  return (
    <FormField
      control={finalControl}
      name={name}
      render={({ field, fieldState: { error } }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <MultiSelect
              {...rest} // Spread other MultiSelect specific props
              options={options}
              selected={field.value as string[] || []} // MultiSelect expects 'selected' prop
              onChange={field.onChange} // MultiSelect expects 'onChange' prop
              placeholder={placeholder}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormItem>
      )}
    />
  );
};