'use client'

import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON>2, Wand2, <PERSON><PERSON>, <PERSON>hare2, <PERSON><PERSON><PERSON>2, <PERSON>, CheckCircle2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { AuthPopup } from '@/components/auth/auth-popup'
import React, { useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { getSupabaseClient } from '@/lib/supabase/client';

export default function HomePage() {
  const router = useRouter()
  const authRef = useRef<any>(null)

  const handleOpenAuth = () => {
    if (authRef.current && typeof authRef.current.openAuth === 'function') {
      authRef.current.openAuth()
    }
  }

  const [isAuthenticated, setIsAuthenticated] = React.useState(false);

  useEffect(() => {
    const supabase = getSupabaseClient();
    supabase.auth.getSession().then(({ data: { session } }) => {
      setIsAuthenticated(!!session);
      if (session) {
        router.replace('/dashboard');
      }
    });
    const { data: listener } = supabase.auth.onAuthStateChange((event, session) => {
      setIsAuthenticated(!!session);
      if (session) {
        router.replace('/dashboard');
      }
    });
    return () => {
      listener?.subscription.unsubscribe();
    };
  }, [router]);

  return (
    <div className="min-h-screen flex flex-col bg-[#0a1419] text-slate-50 items-center w-full">
      {/* Header premium, logo seul, sans cadre ni bouton compte */}
      <header className="flex h-24 items-center justify-between py-6 px-4 md:px-12 z-40 w-full bg-gradient-to-b from-[#101c24] to-[#112229] shadow-xl">
        <div className="flex items-center gap-6">
          <img
            src="/LOGO_Mouvik.png"
            alt="MOUVIK"
            className="h-14 md:h-16 w-auto drop-shadow-lg"
          />
        </div>
        <nav className="hidden gap-8 md:flex">
          <Link href="#features" className="text-base font-medium text-muted-foreground transition-colors hover:text-primary">Fonctionnalités</Link>
          <Link href="#pricing" className="text-base font-medium text-muted-foreground transition-colors hover:text-primary">Tarifs</Link>
          <Link href="#testimonials" className="text-base font-medium text-muted-foreground transition-colors hover:text-primary">Témoignages</Link>
          <Link href="#faq" className="text-base font-medium text-muted-foreground transition-colors hover:text-primary">FAQ</Link>
        </nav>
        <div className="flex items-center gap-3">
          {!isAuthenticated && <AuthPopup ref={authRef} />}
        </div>
      </header>

      {/* Hero Section - image only */}
      <section className="relative min-h-[420px] md:min-h-[520px] flex flex-col justify-end overflow-hidden bg-gradient-to-b from-[#0a1419] via-[#0f1a1f] to-[#112229] w-full">
        <div className="absolute inset-0 w-full h-full flex justify-center items-center pointer-events-none select-none z-0">
          <img src="/img_indexintro3.jpg" alt="Concert Hero" className="w-full h-full object-cover object-center opacity-90 mix-blend-screen" />
        </div>
      </section>

      {/* Section titre + sous-titre sous l'image */}
      <section className="bg-[#101c24] bg-gradient-to-b from-[#101c24] to-[#112229] py-12 md:py-16 flex flex-col items-center justify-center text-center w-full">
        <h1 className="text-5xl md:text-7xl font-extrabold mb-6 mt-2 text-center drop-shadow-xl tracking-tight text-white w-full max-w-4xl mx-auto">
          MAKE. MORPH. <span className="text-cyan-400">CREATE.</span>
        </h1>
        <p className="text-lg md:text-2xl text-slate-200 max-w-2xl mx-auto w-full">
          Transformez vos idées musicales en morceaux professionnels avec l'assistance de l'IA et collaborez avec des artistes du monde entier.
        </p>
      </section>

      {/* Pourquoi Mouvik ? */}
      <section id="why-mouvik" className="py-16 md:py-24 bg-[#112229]">
        <div className="container mx-auto px-6 md:px-12 lg:px-24">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-white">Pourquoi Mouvik ?</h2>
          <p className="text-lg text-muted-foreground text-center mb-12 max-w-2xl mx-auto">Découvrez ce qui rend Mouvik unique pour la création musicale collaborative et assistée par IA.</p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Card 1: IA */}
            <div className="bg-slate-800/50 p-6 rounded-xl shadow-xl hover:shadow-cyan-500/30 transition-shadow border border-slate-700/50 flex flex-col items-center text-center">
              <div className="flex items-center justify-center h-12 w-12 rounded-full bg-cyan-500/20 text-cyan-400 mb-4">
                 <Wand2 size={24}/>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-white">IA Intégrée</h3>
              <p className="text-muted-foreground text-sm">Suggestions intelligentes pour mélodies, accords et arrangements.</p>
            </div>
            {/* Card 2: Sécurité */}
            <div className="bg-slate-800/50 p-6 rounded-xl shadow-xl hover:shadow-cyan-500/30 transition-shadow border border-slate-700/50 flex flex-col items-center text-center">
              <div className="flex items-center justify-center h-12 w-12 rounded-full bg-cyan-500/20 text-cyan-400 mb-4">
                 <Layers size={24}/> {/* Placeholder, use appropriate icon like ShieldCheck */}
              </div>
              <h3 className="text-xl font-semibold mb-2 text-white">Sécurité Maximale</h3>
              <p className="text-muted-foreground text-sm">Vos créations sont protégées, vos données privées et chiffrées.</p>
            </div>
            {/* Card 3: Communauté */}
            <div className="bg-slate-800/50 p-6 rounded-xl shadow-xl hover:shadow-cyan-500/30 transition-shadow border border-slate-700/50 flex flex-col items-center text-center">
              <div className="flex items-center justify-center h-12 w-12 rounded-full bg-cyan-500/20 text-cyan-400 mb-4">
                 <Users size={24}/> 
              </div>
              <h3 className="text-xl font-semibold mb-2 text-white">Communauté Globale</h3>
              <p className="text-muted-foreground text-sm">Collaborez avec des artistes et producteurs du monde entier.</p>
            </div>
            {/* Card 4: Simplicité */}
            <div className="bg-slate-800/50 p-6 rounded-xl shadow-xl hover:shadow-cyan-500/30 transition-shadow border border-slate-700/50 flex flex-col items-center text-center">
              <div className="flex items-center justify-center h-12 w-12 rounded-full bg-cyan-500/20 text-cyan-400 mb-4">
                 <Share2 size={24}/> {/* Placeholder, use appropriate icon like Zap or Lightbulb */}
              </div>
              <h3 className="text-xl font-semibold mb-2 text-white">Simplicité & Puissance</h3>
              <p className="text-muted-foreground text-sm">Interface intuitive, outils professionnels, prise en main rapide.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Comment ça marche ? */}
      <section id="how-it-works" className="py-16 md:py-24 bg-[#0a1419]">
        <div className="container mx-auto px-6 md:px-12 lg:px-24">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-white">Comment ça marche ?</h2>
          <p className="text-lg text-muted-foreground text-center mb-12 max-w-2xl mx-auto">Commencez à créer en quelques étapes simples.</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Wand2 className="h-8 w-8 text-primary" />
              </div>
              <h3 className="font-bold text-lg text-white mb-2">1. Créez</h3>
              <p className="text-slate-300 text-sm text-center">Composez, enregistrez et structurez vos morceaux en quelques clics.</p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Users className="h-8 w-8 text-primary" />
              </div>
              <h3 className="font-bold text-lg text-white mb-2">2. Collaborez</h3>
              <p className="text-slate-300 text-sm text-center">Invitez d’autres artistes, échangez en temps réel, partagez vos idées.</p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Share2 className="h-8 w-8 text-primary" />
              </div>
              <h3 className="font-bold text-lg text-white mb-2">3. Publiez</h3>
              <p className="text-slate-300 text-sm text-center">Diffusez votre musique sur toutes les plateformes en un clic.</p>
            </div>
          </div>
          <div className="flex justify-center mt-10">
            <Button
              size="lg"
              className="bg-gradient-to-r from-cyan-500 to-cyan-300 text-white font-bold text-xl px-12 py-5 rounded-full shadow-xl hover:from-cyan-400 hover:to-cyan-200 transition-all border-2 border-cyan-400 focus:outline-none focus:ring-4 focus:ring-cyan-400/40"
              onClick={handleOpenAuth}
            >
              Je crée mon compte gratuitement
            </Button>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-16 md:py-24 bg-[#112229]">
        <div className="container mx-auto px-6 md:px-12 lg:px-24">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-white">Tarifs Simples et Transparents</h2>
          <p className="text-lg text-muted-foreground text-center mb-12 max-w-2xl mx-auto">Choisissez le plan qui correspond à vos ambitions musicales. Commencez gratuitement !</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto items-stretch">
            {/* Free Plan */}
            <div className="bg-[#0f1e25] p-8 rounded-xl shadow-xl border border-slate-700 flex flex-col transition-all duration-300 hover:shadow-slate-500/20 hover:border-slate-600">
              <h3 className="text-2xl font-bold text-white mb-2">Gratuit</h3>
              <p className="text-4xl font-extrabold text-white mb-1">0€<span className="text-sm font-normal text-muted-foreground ml-1">/mois</span></p>
              <p className="text-muted-foreground text-sm mb-6 min-h-[40px]">Parfait pour débuter et explorer.</p>
              <ul className="space-y-3 text-muted-foreground mb-8 flex-grow">
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>3 Projets</span></li>
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Assistance IA basique</span></li>
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Stockage cloud (1GB)</span></li>
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Accès communauté</span></li>
              </ul>
              <Button variant="outline" className="w-full mt-auto border-cyan-600 text-cyan-500 hover:bg-cyan-600 hover:text-black font-semibold py-3 text-base rounded-full transition-colors duration-150" onClick={handleOpenAuth}>
                Commencer Gratuitement
              </Button>
            </div>
            {/* Pro Plan */}
            <div className="relative flex flex-col">
              <div className="bg-[#142c36] p-8 rounded-xl shadow-2xl border-2 border-cyan-500 flex flex-col transition-all duration-300 hover:shadow-cyan-500/40 relative mt-6">
                <h3 className="text-2xl font-bold text-white mb-2 mt-2">Pro</h3>
                <p className="text-4xl font-extrabold text-white mb-1">19€<span className="text-sm font-normal text-muted-foreground ml-1">/mois</span></p>
                <p className="text-muted-foreground text-sm mb-6 min-h-[40px]">Pour les artistes et producteurs sérieux.</p>
                <ul className="space-y-3 text-muted-foreground mb-8 flex-grow">
                  <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Projets illimités</span></li>
                  <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Assistance IA avancée</span></li>
                  <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Stockage cloud (50GB)</span></li>
                  <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Collaboration prioritaire</span></li>
                  <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Distribution facilitée</span></li>
                </ul>
                <Button className="w-full mt-auto bg-cyan-500 hover:bg-cyan-400 text-white font-bold py-3 text-lg rounded-full shadow-lg transition-colors duration-150" onClick={handleOpenAuth}>
                  Choisir Pro
                </Button>
              </div>
            </div>
            {/* Studio Plan */}
            <div className="bg-[#0f1e25] p-8 rounded-xl shadow-xl border border-slate-700 flex flex-col transition-all duration-300 hover:shadow-slate-500/20 hover:border-slate-600">
              <h3 className="text-2xl font-bold text-white mb-2">Studio</h3>
              <p className="text-4xl font-extrabold text-white mb-1">49€<span className="text-sm font-normal text-muted-foreground ml-1">/mois</span></p>
              <p className="text-muted-foreground text-sm mb-6 min-h-[40px]">Solution complète pour les professionnels.</p>
              <ul className="space-y-3 text-muted-foreground mb-8 flex-grow">
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Tout du plan Pro</span></li>
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Mastering IA Premium</span></li>
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Stockage cloud (200GB)</span></li>
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Marque blanche</span></li>
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Tableau de bord analytique</span></li>
                <li className="flex items-center gap-3"><CheckCircle2 className="h-5 w-5 text-cyan-500 flex-shrink-0" /> <span>Support dédié</span></li>
              </ul>
              <Button variant="outline" className="w-full mt-auto border-cyan-600 text-cyan-500 hover:bg-cyan-600 hover:text-white font-semibold py-3 text-base rounded-full transition-colors duration-150" onClick={handleOpenAuth}> 
                 Contacter Ventes 
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-16 md:py-24 bg-[#0a1419]">
        <div className="container mx-auto px-6 md:px-12 lg:px-24">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-white">Ce que nos utilisateurs disent</h2>
          <p className="text-lg text-muted-foreground text-center mb-12 max-w-2xl mx-auto">Découvrez les expériences de créateurs comme vous.</p>
          <div className="text-center text-slate-400">
            {/* Placeholder for testimonials content */}
            <p>Les témoignages arriveront bientôt !</p>
          </div>
        </div>
      </section>

      {/* Bloc Unleash your creativity modernisé, centré et responsif */}
      <section className="py-24 md:py-32 bg-gradient-to-br from-[#101c24] to-[#1a2632] flex justify-center items-center w-full">
        <div className="flex flex-col md:flex-row md:items-center md:gap-16 max-w-6xl w-full px-6 md:px-12 lg:px-24 mx-auto items-center justify-center">
          <div className="flex-1 flex justify-center mb-8 md:mb-0 items-center">
            <img src="/mouvk.png" alt="Mouvik immersif" className="w-full max-w-xl h-[340px] md:h-[460px] object-cover object-center rounded-2xl shadow-2xl opacity-95 mix-blend-screen" />
          </div>
          <div className="flex-1 flex flex-col items-center justify-center text-center w-full">
            <h2 className="text-4xl md:text-5xl font-extrabold mb-6 text-white drop-shadow-xl w-full max-w-3xl mx-auto">Unleash your creativity</h2>
            <p className="text-xl text-slate-200 mb-8 max-w-xl w-full mx-auto">Libérez votre potentiel créatif avec Mouvik. Créez, collaborez et partagez vos morceaux dans une expérience immersive, assistée par l’IA.</p>
            <Button size="lg" className="bg-white text-cyan-700 font-bold text-2xl px-12 py-6 rounded-full shadow-2xl transition-transform duration-150 hover:bg-cyan-500 hover:text-white border-2 border-cyan-400 focus:outline-none focus:ring-4 focus:ring-cyan-400/40 w-full max-w-xs md:w-auto" onClick={handleOpenAuth}>
              Commencer à Créer <ArrowRight className="ml-3 h-7 w-7" />
            </Button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-16 md:py-24 bg-[#112229]">
        <div className="container mx-auto px-6 md:px-12 lg:px-24">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-white">Questions Fréquemment Posées</h2>
          <p className="text-lg text-muted-foreground text-center mb-12 max-w-2xl mx-auto">Trouvez des réponses à vos interrogations les plus courantes.</p>
          <div className="max-w-3xl mx-auto space-y-6">
            <div>
              <h3 className="font-bold text-white mb-2">Est-ce que Mouvik est vraiment gratuit ?</h3>
              <p className="text-slate-300 text-sm">Oui, une version gratuite puissante est disponible. Des options avancées sont proposées via les plans Pro et Studio.</p>
            </div>
            <div>
              <h3 className="font-bold text-white mb-2">Puis-je collaborer en temps réel ?</h3>
              <p className="text-slate-300 text-sm">Absolument, la collaboration en temps réel est au cœur de la plateforme.</p>
            </div>
            <div>
              <h3 className="font-bold text-white mb-2">Mes morceaux m’appartiennent-ils ?</h3>
              <p className="text-slate-300 text-sm">Oui, vous gardez 100% de vos droits sur vos créations.</p>
            </div>
            <div>
              <h3 className="font-bold text-white mb-2">L’IA va-t-elle remplacer les artistes ?</h3>
              <p className="text-slate-300 text-sm">Non, l’IA est un outil d’inspiration et d’assistance, jamais un substitut à la créativité humaine.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-10 bg-[#0a1419] border-t border-slate-700/50 w-full">
        <div className="container mx-auto px-6 md:px-12 lg:px-24 w-full">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8 text-sm w-full">
            <div className="flex items-center gap-3">
              <img src="/LOGO_Mouvik.png" alt="Logo Mouvik" className="h-8 w-auto" />
              <span className="text-slate-400 text-sm"> 2023 Mouvik. Tous droits réservés.</span>
            </div>
            <div className="flex gap-6 text-slate-400 text-sm col-span-1 md:col-span-3 lg:col-span-4 justify-end items-center w-full">
              <Link href="/legal" className="hover:text-primary transition-colors">Mentions légales</Link>
              <Link href="/privacy" className="hover:text-primary transition-colors">Confidentialité</Link>
              <a href="mailto:<EMAIL>" className="hover:text-primary transition-colors">Contact</a>
              <a href="https://twitter.com/mouvik" target="_blank" rel="noopener noreferrer" className="hover:text-primary transition-colors">Twitter</a>
              <a href="https://instagram.com/mouvik" target="_blank" rel="noopener noreferrer" className="hover:text-primary transition-colors">Instagram</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
