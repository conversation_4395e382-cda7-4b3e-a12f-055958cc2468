/**
 * 🎼 CHORD WORKFLOW INTEGRATION - Intégration AI Composer
 * 
 * Composant wrapper pour intégrer le système d'accords
 * dans le workflow AI Composer existant
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Music, FileText, Grid3X3, Clock, Sparkles, X, Minimize2, Maximize2 } from 'lucide-react';
import { ChordSystemProvider } from '../providers/ChordSystemProvider';
import { ChordLibraryBrowser } from '../components/ChordLibraryBrowser';
import { ChordProgressionBuilder } from '../components/ChordProgressionBuilder';
import { ChordPickerModal } from '../components/ChordPickerModal';
import { 
  ChordIntegrationManager, 
  createIntegrationManager, 
  detectOptimalIntegrationMode,
  createDefaultIntegrationConfig
} from './ChordIntegrationManager';
import type { 
  UnifiedChordPosition, 
  ChordProgression,
  ChordGridSection 
} from '../types/chord-system';
import type { 
  IntegrationMode, 
  IntegrationConfig, 
  IntegrationCallbacks,
  SongSectionType 
} from './ChordIntegrationManager';

// ============================================================================
// TYPES POUR LE COMPOSANT
// ============================================================================

interface ChordWorkflowIntegrationProps {
  /** Mode d'intégration forcé (optionnel, sinon auto-détection) */
  forcedMode?: IntegrationMode;
  /** Configuration d'intégration personnalisée */
  integrationConfig?: Partial<IntegrationConfig>;
  /** Callbacks pour l'intégration avec AI Composer */
  aiComposerCallbacks?: Partial<IntegrationCallbacks>;
  /** Affichage initial (minimisé ou maximisé) */
  initialDisplay?: 'minimized' | 'maximized' | 'modal';
  /** Position du widget (pour mode minimisé) */
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  /** Classe CSS personnalisée */
  className?: string;
}

interface WorkflowState {
  isVisible: boolean;
  displayMode: 'minimized' | 'maximized' | 'modal';
  activeTab: 'browser' | 'progression' | 'picker';
  integrationMode: IntegrationMode;
  selectedChords: UnifiedChordPosition[];
  currentProgression: ChordProgression | null;
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Widget minimisé flottant
 */
const MinimizedWidget: React.FC<{
  onMaximize: () => void;
  onOpenPicker: () => void;
  position: string;
  chordCount: number;
}> = ({ onMaximize, onOpenPicker, position, chordCount }) => {
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };

  return (
    <div className={`fixed ${positionClasses[position as keyof typeof positionClasses]} z-50`}>
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-3">
        <div className="flex items-center space-x-2">
          <Music className="w-5 h-5 text-blue-600" />
          <span className="text-sm font-medium text-gray-900">Accords</span>
          {chordCount > 0 && (
            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
              {chordCount}
            </span>
          )}
        </div>
        
        <div className="flex items-center mt-2 space-x-1">
          <button
            onClick={onOpenPicker}
            className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
            title="Sélectionner un accord"
          >
            +
          </button>
          <button
            onClick={onMaximize}
            className="p-1 text-gray-600 hover:text-gray-800 rounded"
            title="Ouvrir l'interface complète"
          >
            <Maximize2 className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Interface maximisée
 */
const MaximizedInterface: React.FC<{
  activeTab: string;
  onTabChange: (tab: string) => void;
  onMinimize: () => void;
  onClose: () => void;
  integrationMode: IntegrationMode;
  integrationManager: ChordIntegrationManager | null;
  currentProgression: ChordProgression | null;
  onProgressionChange: (progression: ChordProgression) => void;
}> = ({ 
  activeTab, onTabChange, onMinimize, onClose, 
  integrationMode, integrationManager, currentProgression, onProgressionChange 
}) => {
  return (
    <div className="fixed inset-4 z-50 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col">
      {/* En-tête */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <Music className="w-6 h-6 text-blue-600" />
          <h2 className="text-lg font-semibold text-gray-900">Système d'Accords</h2>
          
          {/* Indicateur de mode d'intégration */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Mode:</span>
            <span className={`
              px-2 py-1 text-xs rounded-full font-medium
              ${integrationMode === 'text-editor' ? 'bg-green-100 text-green-800' :
                integrationMode === 'song-structure' ? 'bg-blue-100 text-blue-800' :
                integrationMode === 'timeline' ? 'bg-purple-100 text-purple-800' :
                'bg-gray-100 text-gray-800'
              }
            `}>
              {integrationMode === 'text-editor' && <><FileText className="w-3 h-3 inline mr-1" />Éditeur</>}
              {integrationMode === 'song-structure' && <><Grid3X3 className="w-3 h-3 inline mr-1" />Structure</>}
              {integrationMode === 'timeline' && <><Clock className="w-3 h-3 inline mr-1" />Timeline</>}
              {integrationMode === 'ai-suggestions' && <><Sparkles className="w-3 h-3 inline mr-1" />IA</>}
              {integrationMode === 'standalone' && <>Autonome</>}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={onMinimize}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
            title="Minimiser"
          >
            <Minimize2 className="w-4 h-4" />
          </button>
          <button
            onClick={onClose}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded"
            title="Fermer"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Onglets */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => onTabChange('browser')}
          className={`
            px-4 py-2 text-sm font-medium border-b-2 transition-colors
            ${activeTab === 'browser' 
              ? 'border-blue-500 text-blue-600 bg-blue-50' 
              : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }
          `}
        >
          Bibliothèque
        </button>
        <button
          onClick={() => onTabChange('progression')}
          className={`
            px-4 py-2 text-sm font-medium border-b-2 transition-colors
            ${activeTab === 'progression' 
              ? 'border-blue-500 text-blue-600 bg-blue-50' 
              : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
            }
          `}
        >
          Progression
        </button>
      </div>

      {/* Contenu */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'browser' && (
          <div className="h-full overflow-y-auto p-4">
            <ChordLibraryBrowser
              showAudioControls={true}
              showAdvancedFilters={true}
              onChordSelect={(chord) => {
                if (integrationManager) {
                  integrationManager.insertChordToTextEditor(chord);
                }
              }}
              onAddToProgression={(chord) => {
                // Ajouter à la progression actuelle
                console.log('Ajouter à progression:', chord);
              }}
            />
          </div>
        )}

        {activeTab === 'progression' && (
          <div className="h-full overflow-y-auto p-4">
            <ChordProgressionBuilder
              initialProgression={currentProgression || undefined}
              showIntegrationControls={true}
              showAISuggestions={true}
              integrationMode={integrationMode}
              onProgressionChange={onProgressionChange}
              onInsertToTextEditor={(text) => {
                if (integrationManager) {
                  // Utiliser le gestionnaire d'intégration
                  console.log('Insérer dans éditeur:', text);
                }
              }}
              onAddToSongStructure={(progression, sectionType) => {
                if (integrationManager) {
                  integrationManager.addProgressionToSongStructure(progression, sectionType as any);
                }
              }}
              onSyncWithTimeline={(progression) => {
                if (integrationManager) {
                  integrationManager.syncProgressionWithTimeline(progression);
                }
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const ChordWorkflowIntegration: React.FC<ChordWorkflowIntegrationProps> = ({
  forcedMode,
  integrationConfig = {},
  aiComposerCallbacks = {},
  initialDisplay = 'minimized',
  position = 'bottom-right',
  className = ''
}) => {
  // État du workflow
  const [workflowState, setWorkflowState] = useState<WorkflowState>(() => ({
    isVisible: true,
    displayMode: initialDisplay,
    activeTab: 'browser',
    integrationMode: forcedMode || detectOptimalIntegrationMode(),
    selectedChords: [],
    currentProgression: null
  }));

  const [showPickerModal, setShowPickerModal] = useState(false);
  const [integrationManager, setIntegrationManager] = useState<ChordIntegrationManager | null>(null);

  // ============================================================================
  // CONFIGURATION D'INTÉGRATION
  // ============================================================================

  const finalIntegrationConfig = useMemo(() => {
    const defaultConfig = createDefaultIntegrationConfig(workflowState.integrationMode);
    return { ...defaultConfig, ...integrationConfig };
  }, [workflowState.integrationMode, integrationConfig]);

  const integrationCallbacks = useMemo<IntegrationCallbacks>(() => ({
    // Callbacks par défaut
    onInsertToTextEditor: (text, position) => {
      console.log('Insérer dans éditeur de texte:', text, 'à la position:', position);
      // Ici on intégrerait avec l'éditeur de texte existant
    },
    
    onAddToSongStructure: (section, sectionType) => {
      console.log('Ajouter à la structure:', section, 'type:', sectionType);
      // Ici on intégrerait avec le module de structure de morceau
    },
    
    onSyncWithTimeline: (progression, config) => {
      console.log('Synchroniser avec timeline:', progression, 'config:', config);
      // Ici on intégrerait avec la timeline
    },
    
    onRequestAISuggestions: async (context) => {
      console.log('Demander suggestions IA:', context);
      // Ici on intégrerait avec le système de suggestions IA
      return [];
    },
    
    // Fusionner avec les callbacks fournis
    ...aiComposerCallbacks
  }), [aiComposerCallbacks]);

  // ============================================================================
  // EFFETS
  // ============================================================================

  // Initialiser le gestionnaire d'intégration
  useEffect(() => {
    const manager = createIntegrationManager(finalIntegrationConfig, integrationCallbacks);
    setIntegrationManager(manager);

    return () => {
      manager.dispose();
    };
  }, [finalIntegrationConfig, integrationCallbacks]);

  // Écouter les événements globaux
  useEffect(() => {
    const handleOpenPicker = () => setShowPickerModal(true);
    const handleOpenProgressionBuilder = () => {
      setWorkflowState(prev => ({
        ...prev,
        displayMode: 'maximized',
        activeTab: 'progression'
      }));
    };

    window.addEventListener('chord-system:open-picker', handleOpenPicker);
    window.addEventListener('chord-system:open-progression-builder', handleOpenProgressionBuilder);

    return () => {
      window.removeEventListener('chord-system:open-picker', handleOpenPicker);
      window.removeEventListener('chord-system:open-progression-builder', handleOpenProgressionBuilder);
    };
  }, []);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleChordSelect = useCallback((chord: UnifiedChordPosition) => {
    setWorkflowState(prev => ({
      ...prev,
      selectedChords: [...prev.selectedChords, chord]
    }));

    // Intégrer avec AI Composer selon le mode
    if (integrationManager) {
      integrationManager.insertChordToTextEditor(chord);
    }
  }, [integrationManager]);

  const handleProgressionChange = useCallback((progression: ChordProgression) => {
    setWorkflowState(prev => ({
      ...prev,
      currentProgression: progression
    }));
  }, []);

  const handleMinimize = useCallback(() => {
    setWorkflowState(prev => ({
      ...prev,
      displayMode: 'minimized'
    }));
  }, []);

  const handleMaximize = useCallback(() => {
    setWorkflowState(prev => ({
      ...prev,
      displayMode: 'maximized'
    }));
  }, []);

  const handleClose = useCallback(() => {
    setWorkflowState(prev => ({
      ...prev,
      isVisible: false
    }));
  }, []);

  const handleTabChange = useCallback((tab: string) => {
    setWorkflowState(prev => ({
      ...prev,
      activeTab: tab as any
    }));
  }, []);

  // ============================================================================
  // RENDU
  // ============================================================================

  if (!workflowState.isVisible) {
    return null;
  }

  return (
    <ChordSystemProvider>
      <div className={`chord-workflow-integration ${className}`}>
        {/* Widget minimisé */}
        {workflowState.displayMode === 'minimized' && (
          <MinimizedWidget
            onMaximize={handleMaximize}
            onOpenPicker={() => setShowPickerModal(true)}
            position={position}
            chordCount={workflowState.selectedChords.length}
          />
        )}

        {/* Interface maximisée */}
        {workflowState.displayMode === 'maximized' && (
          <MaximizedInterface
            activeTab={workflowState.activeTab}
            onTabChange={handleTabChange}
            onMinimize={handleMinimize}
            onClose={handleClose}
            integrationMode={workflowState.integrationMode}
            integrationManager={integrationManager}
            currentProgression={workflowState.currentProgression}
            onProgressionChange={handleProgressionChange}
          />
        )}

        {/* Modal de sélection rapide */}
        <ChordPickerModal
          isOpen={showPickerModal}
          onClose={() => setShowPickerModal(false)}
          onChordSelect={(chord) => {
            handleChordSelect(chord);
            setShowPickerModal(false);
          }}
          title="Sélection rapide d'accord"
        />
      </div>
    </ChordSystemProvider>
  );
};

// ============================================================================
// EXPORTS POUR L'INTÉGRATION
// ============================================================================

/**
 * Hook pour utiliser le système d'accords dans AI Composer
 */
export function useChordWorkflowIntegration(mode?: IntegrationMode) {
  const [isVisible, setIsVisible] = useState(false);
  const [integrationMode] = useState(mode || detectOptimalIntegrationMode());

  const openChordSystem = useCallback(() => {
    setIsVisible(true);
  }, []);

  const closeChordSystem = useCallback(() => {
    setIsVisible(false);
  }, []);

  const openChordPicker = useCallback(() => {
    window.dispatchEvent(new CustomEvent('chord-system:open-picker'));
  }, []);

  const openProgressionBuilder = useCallback(() => {
    window.dispatchEvent(new CustomEvent('chord-system:open-progression-builder'));
  }, []);

  return {
    isVisible,
    integrationMode,
    openChordSystem,
    closeChordSystem,
    openChordPicker,
    openProgressionBuilder
  };
}

/**
 * Composant bouton pour ouvrir le système d'accords
 */
export const ChordSystemTrigger: React.FC<{
  variant?: 'button' | 'icon' | 'floating';
  onClick?: () => void;
}> = ({ variant = 'button', onClick }) => {
  const handleClick = () => {
    onClick?.();
    window.dispatchEvent(new CustomEvent('chord-system:open-picker'));
  };

  if (variant === 'icon') {
    return (
      <button
        onClick={handleClick}
        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
        title="Ouvrir le système d'accords"
      >
        <Music className="w-5 h-5" />
      </button>
    );
  }

  if (variant === 'floating') {
    return (
      <button
        onClick={handleClick}
        className="fixed bottom-4 right-4 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40"
        title="Ouvrir le système d'accords"
      >
        <Music className="w-6 h-6" />
      </button>
    );
  }

  return (
    <button
      onClick={handleClick}
      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
    >
      <Music className="w-4 h-4 mr-2" />
      Accords
    </button>
  );
};
