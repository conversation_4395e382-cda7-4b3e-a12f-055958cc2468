-- Supprimer l'ancienne contrainte de clé étrangère si elle existe
ALTER TABLE public.comments
DROP CONSTRAINT IF EXISTS comments_project_id_fkey;

-- Renommer project_id en resource_id, seulement si project_id existe et resource_id n'existe pas encore
DO $$
BEGIN
  IF EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='comments' AND column_name='project_id') AND
     NOT EXISTS(SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='comments' AND column_name='resource_id') THEN
    ALTER TABLE public.comments RENAME COLUMN project_id TO resource_id;
  END IF;
END $$;

-- Ajouter la colonne resource_type
ALTER TABLE public.comments
ADD COLUMN IF NOT EXISTS resource_type TEXT;

-- Ajouter la colonne status pour la modération
ALTER TABLE public.comments
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'visible';

-- Ajouter des index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS idx_comments_resource ON public.comments (resource_id, resource_type);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments (user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON public.comments (parent_id);

-- Mettre à jour les commentaires existants (si certains étaient liés à des "projects")
-- Vous devrez peut-être ajuster cela en fonction de vos données existantes.
-- Par exemple, si tous les anciens commentaires étaient pour des "albums" et que resource_id correspondait à un album_id :
-- UPDATE public.comments
-- SET resource_type = 'album'
-- WHERE resource_type IS NULL; 

-- (Optionnel) Supprimer la colonne timestamp si elle n'est pas utilisée
-- ALTER TABLE public.comments
-- DROP COLUMN IF EXISTS timestamp;

COMMENT ON COLUMN public.comments.resource_id IS 'ID de la ressource commentée (album, chanson, playlist, etc.)';
COMMENT ON COLUMN public.comments.resource_type IS 'Type de la ressource (album, song, playlist, artist, band)';
COMMENT ON COLUMN public.comments.status IS 'Statut du commentaire (visible, hidden_by_moderator, deleted_by_user)';
COMMENT ON COLUMN public.comments.parent_id IS 'ID du commentaire parent pour les réponses';

-- Vérification (peut être exécutée dans l'éditeur SQL pour voir la nouvelle structure)
-- SELECT column_name, data_type, is_nullable, column_default
-- FROM information_schema.columns
-- WHERE table_schema = 'public' AND table_name = 'comments'
-- ORDER BY ordinal_position;
