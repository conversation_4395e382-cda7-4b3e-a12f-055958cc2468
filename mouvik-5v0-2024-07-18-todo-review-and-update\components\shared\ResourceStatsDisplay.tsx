"use client"; // May not be strictly necessary if only displaying data, but good for consistency
import React from "react";

import { Eye, Play, Heart, ThumbsDown, Rss as FollowIcon } from 'lucide-react'; // Using Rss for FollowIcon as in playlist page. Removed unused Users.

interface ResourceStatsDisplayProps {
  viewCount?: number | null;
  playCount?: number | null;
  likeCount?: number | null;
  dislikeCount?: number | null;
  followerCount?: number | null;
  // Add other stats like commentCount if needed in the future
  resourceType: 'album' | 'song' | 'playlist' | 'band' | 'profile'; // Added 'profile'
}

export function ResourceStatsDisplay({
  viewCount,
  playCount,
  likeCount,
  dislikeCount,
  followerCount,
  resourceType,
}: ResourceStatsDisplayProps) {
  const statsItems = [];

  // Likes
  if (likeCount !== undefined && likeCount !== null) {
    statsItems.push(
      <span key="likes" className="flex items-center gap-0.5 px-2 py-0.5 bg-muted/60 rounded-full border border-muted-foreground/10 shadow-sm" style={{ minWidth: 0 }}>
        <Heart className="w-3 h-3" />
        <span className="ml-0.5">{(likeCount || 0).toLocaleString()}</span>
      </span>
    );
  }

  // Dislikes
  if (dislikeCount !== undefined && dislikeCount !== null) {
    if (statsItems.length > 0) statsItems.push(<span key="sep-dislikes" className="mx-1 self-center">•</span>);
    statsItems.push(
      <span key="dislikes" className="flex items-center gap-0.5 px-2 py-0.5 bg-muted/60 rounded-full border border-muted-foreground/10 shadow-sm" style={{ minWidth: 0 }}>
        <ThumbsDown className="w-3 h-3" />
        <span className="ml-0.5">{(dislikeCount || 0).toLocaleString()}</span>
      </span>
    );
  }
  
  // Views
  if (viewCount !== undefined && viewCount !== null) {
    if (statsItems.length > 0) statsItems.push(<span key="sep-views" className="mx-1 self-center">•</span>);
    statsItems.push(
      <span key="views" className="flex items-center gap-0.5 px-2 py-0.5 bg-muted/60 rounded-full border border-muted-foreground/10 shadow-sm" style={{ minWidth: 0 }}>
        <Eye className="w-3 h-3" />
        <span className="ml-0.5">{(viewCount || 0).toLocaleString()} Vue(s)</span>
      </span>
    );
  }

  // Plays
  if (playCount !== undefined && playCount !== null) {
    if (statsItems.length > 0) statsItems.push(<span key="sep-plays" className="mx-1 self-center">•</span>);
    const playLabel = resourceType === 'song' ? 'Écoute(s)' : 'Lecture(s)';
    statsItems.push(
      <span key="plays" className="flex items-center gap-0.5 px-2 py-0.5 bg-muted/60 rounded-full border border-muted-foreground/10 shadow-sm" style={{ minWidth: 0 }}>
        <Play className="w-3 h-3" />
        <span className="ml-0.5">{(playCount || 0).toLocaleString()} {playLabel}</span>
      </span>
    );
  }

  // Followers
  if (followerCount !== undefined && followerCount !== null) {
    if (statsItems.length > 0) statsItems.push(<span key="sep-followers" className="mx-1 self-center">•</span>);
    statsItems.push(
      <span key="followers" className="flex items-center gap-0.5 px-2 py-0.5 bg-muted/60 rounded-full border border-muted-foreground/10 shadow-sm" style={{ minWidth: 0 }}>
        <FollowIcon className="w-3 h-3" />
        <span className="ml-0.5">{(followerCount || 0).toLocaleString()} Follower(s)</span>
      </span>
    );
  }

  // If no stats are provided at all (all props are undefined), then return null.
  // Otherwise, show the div, even if all counts are zero.
  if (
    likeCount === undefined &&
    dislikeCount === undefined &&
    viewCount === undefined &&
    playCount === undefined &&
    followerCount === undefined
  ) {
    return null;
  }

  return (
    <div className="flex flex-wrap justify-center items-center gap-x-1 gap-y-1 text-xs text-muted-foreground">
      {statsItems.map((item) => item /* Render items directly as they are now fully formed */)}
    </div>
  );
}
