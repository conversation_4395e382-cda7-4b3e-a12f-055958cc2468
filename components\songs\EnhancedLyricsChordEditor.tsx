"use client";

import React, { useState, useRef, useEffect } from "react";
import dynamic from "next/dynamic";
import { Control, UseFormWatch } from "react-hook-form";
import { SongFormValues } from "@/types/song";
import { AiConfig, AiHistoryItem } from "@/types/ai";
import { FormField, FormItem, FormControl, FormMessage } from "@/components/ui/form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/hooks/use-toast";
import {
  Palette, Music, Lightbulb, RefreshCcw, AlignLeft, Languages, History,
  ChevronUp, ChevronDown, Cog, Settings, Eye, Download, Upload,
  Type, Brush, Zap, Target, BarChart3, Wand2, Sparkles, Brain,
  Guitar, Piano, Drum, Mic, Volume2, Play, Pause, Square
} from "lucide-react";
import { ChordDiagramViewer } from "@/components/ui/ChordDiagramViewer";
import "react-quill/dist/quill.snow.css";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

// Types pour les accords
interface ChordDiagram {
  name: string;
  frets: number[];
  fingers: number[];
  instrument: 'guitar' | 'ukulele' | 'bass' | 'mandolin';
}

interface ChordLibrary {
  [key: string]: ChordDiagram[];
}

// Types pour la personnalisation
interface TextStyle {
  fontSize: number;
  fontFamily: string;
  textColor: string;
  backgroundColor: string;
  lineHeight: number;
  letterSpacing: number;
}

interface EditorTheme {
  name: string;
  background: string;
  textColor: string;
  accentColor: string;
  borderColor: string;
}

const DEFAULT_THEMES: EditorTheme[] = [
  { name: "Clair", background: "#ffffff", textColor: "#000000", accentColor: "#3b82f6", borderColor: "#e5e7eb" },
  { name: "Sombre", background: "#1f2937", textColor: "#f9fafb", accentColor: "#60a5fa", borderColor: "#374151" },
  { name: "Sépia", background: "#fef7ed", textColor: "#92400e", accentColor: "#d97706", borderColor: "#fed7aa" },
  { name: "Océan", background: "#ecfeff", textColor: "#164e63", accentColor: "#0891b2", borderColor: "#a5f3fc" },
  { name: "Forêt", background: "#f0fdf4", textColor: "#14532d", accentColor: "#16a34a", borderColor: "#bbf7d0" },
  { name: "Coucher", background: "#fef3c7", textColor: "#92400e", accentColor: "#f59e0b", borderColor: "#fde68a" }
];

// Bibliothèque d'accords maintenant gérée par ChordDiagramViewer
// const CHORD_LIBRARY = { ... }; // Déplacé vers ChordDiagramViewer

interface EnhancedLyricsChordEditorProps {
  lyricsContent: string;
  onLyricsChange: (content: string) => void;
  formControl: Control<SongFormValues>;
  watch: UseFormWatch<SongFormValues>;
  aiConfig: AiConfig;
  aiHistory: AiHistoryItem[];
  onAiHistoryAdd: (item: AiHistoryItem) => void;
}

export function EnhancedLyricsChordEditor({
  lyricsContent,
  onLyricsChange,
  formControl,
  watch,
  aiConfig,
  aiHistory,
  onAiHistoryAdd
}: EnhancedLyricsChordEditorProps) {
  // États pour l'éditeur
  const [activeTab, setActiveTab] = useState("editor");
  const [isStylePanelOpen, setIsStylePanelOpen] = useState(false);
  const [isChordPanelOpen, setIsChordPanelOpen] = useState(false);
  const [isAiPanelOpen, setIsAiPanelOpen] = useState(false);
  
  // États pour la personnalisation
  const [selectedTheme, setSelectedTheme] = useState<EditorTheme>(DEFAULT_THEMES[0]);
  const [customTextStyle, setCustomTextStyle] = useState<TextStyle>({
    fontSize: 14,
    fontFamily: "Inter",
    textColor: "#000000",
    backgroundColor: "#ffffff",
    lineHeight: 1.6,
    letterSpacing: 0
  });
  
  // États pour les accords (maintenant gérés par ChordDiagramViewer)
  // const [selectedInstrument, setSelectedInstrument] = useState<'guitar' | 'ukulele' | 'bass' | 'mandolin'>('guitar');
  // const [selectedChord, setSelectedChord] = useState<string>("");
  const [customChords, setCustomChords] = useState<ChordDiagram[]>([]);
  
  // États pour l'IA
  const [aiLoading, setAiLoading] = useState(false);
  const [aiResult, setAiResult] = useState<string>("");
  const [currentSelectionRange, setCurrentSelectionRange] = useState<any>(null);
  
  const quillRef = useRef<any>(null);
  
  // Fonction d'exécution des requêtes IA
  const executeAiRequest = async (prompt: string, actionName: string): Promise<string> => {
    setAiLoading(true);
    try {
      if (aiConfig.provider === 'Ollama') {
        const response = await fetch('http://localhost:11434/api/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            model: aiConfig.model || 'llama2',
            prompt: prompt,
            stream: false,
            options: {
              temperature: aiConfig.temperature || 0.7,
              num_predict: aiConfig.max_tokens || 500
            }
          })
        });
        
        if (!response.ok) throw new Error(`Erreur API: ${response.status}`);
        const data = await response.json();
        return data.response;
      } else {
        await new Promise(resolve => setTimeout(resolve, 1500));
        return `[Simulation IA pour ${actionName} avec ${aiConfig.provider}]`;
      }
    } catch (error: any) {
      toast({ title: "Erreur IA", description: error.message, variant: "destructive" });
      throw error;
    } finally {
      setAiLoading(false);
    }
  };
  
  // Gestionnaires d'analyse IA avancée
  const handleAdvancedLyricsAnalysis = async () => {
    const songData = watch();
    const prompt = `Analyse approfondie des paroles suivantes :

Titre: ${songData.title || 'Sans titre'}
Artiste: ${songData.artist || 'Inconnu'}
Genre: ${songData.genre || 'Non spécifié'}

Paroles:
${lyricsContent}

Fournis une analyse détaillée incluant :
1. Structure narrative et thématique
2. Techniques poétiques utilisées
3. Émotions véhiculées
4. Cohérence et flow
5. Suggestions d'amélioration
6. Points forts et faibles
7. Adaptation au genre musical`;
    
    try {
      const result = await executeAiRequest(prompt, "Analyse Avancée des Paroles");
      setAiResult(result);
      onAiHistoryAdd({ role: 'user', content: 'Analyse avancée des paroles', timestamp: Date.now() });
      onAiHistoryAdd({ role: 'assistant', content: result, timestamp: Date.now() });
    } catch (error) {
      console.error('Erreur analyse IA:', error);
    }
  };
  
  const handleChordProgressionAnalysis = async () => {
    const songData = watch();
    const prompt = `Analyse la progression d'accords suivante :

Titre: ${songData.title}
Tonalité: ${songData.key || 'Non spécifiée'}
Tempo: ${songData.tempo || 'Non spécifié'} BPM

Accords: ${songData.chords || 'Non spécifiés'}
Paroles avec accords:
${lyricsContent}

Fournis :
1. Analyse harmonique de la progression
2. Suggestions d'accords alternatifs
3. Modulations possibles
4. Techniques d'enrichissement harmonique
5. Adaptation au style musical
6. Conseils pour l'arrangement`;
    
    try {
      const result = await executeAiRequest(prompt, "Analyse Progression d'Accords");
      setAiResult(result);
      onAiHistoryAdd({ role: 'user', content: 'Analyse progression d\'accords', timestamp: Date.now() });
      onAiHistoryAdd({ role: 'assistant', content: result, timestamp: Date.now() });
    } catch (error) {
      console.error('Erreur analyse accords:', error);
    }
  };
  
  const handleRhythmicAnalysis = async () => {
    const songData = watch();
    const prompt = `Analyse rythmique de la chanson :

Titre: ${songData.title}
Tempo: ${songData.tempo} BPM
Signature rythmique: ${songData.time_signature || '4/4'}
Genre: ${songData.genre}

Paroles:
${lyricsContent}

Analyse :
1. Correspondance paroles-rythme
2. Placement des accents
3. Phrasé et respiration
4. Suggestions rythmiques
5. Variations possibles
6. Adaptation au groove`;
    
    try {
      const result = await executeAiRequest(prompt, "Analyse Rythmique");
      setAiResult(result);
      onAiHistoryAdd({ role: 'user', content: 'Analyse rythmique', timestamp: Date.now() });
      onAiHistoryAdd({ role: 'assistant', content: result, timestamp: Date.now() });
    } catch (error) {
      console.error('Erreur analyse rythmique:', error);
    }
  };
  
  const handleMelodyGeneration = async () => {
    const songData = watch();
    const selectedText = currentSelectionRange ? quillRef.current?.getEditor()?.getText(currentSelectionRange.index, currentSelectionRange.length) : "";
    const prompt = `Génère des suggestions mélodiques pour :

Titre: ${songData.title}
Tonalité: ${songData.key}
Tempo: ${songData.tempo} BPM
Genre: ${songData.genre}

${selectedText ? `Texte sélectionné: "${selectedText}"` : `Paroles complètes:
${lyricsContent}`}

Fournis :
1. Suggestions de contour mélodique
2. Notes recommandées pour les mots clés
3. Variations mélodiques
4. Techniques d'expression vocale
5. Adaptation au style musical`;
    
    try {
      const result = await executeAiRequest(prompt, "Génération Mélodique");
      setAiResult(result);
      onAiHistoryAdd({ role: 'user', content: 'Génération mélodique', timestamp: Date.now() });
      onAiHistoryAdd({ role: 'assistant', content: result, timestamp: Date.now() });
    } catch (error) {
      console.error('Erreur génération mélodie:', error);
    }
  };
  
  // Fonction pour insérer un accord
  const insertChord = (chordName: string) => {
    if (quillRef.current) {
      const editor = quillRef.current.getEditor();
      const range = editor.getSelection();
      if (range) {
        editor.insertText(range.index, `[${chordName}]`, 'bold', true);
        editor.setSelection(range.index + chordName.length + 2);
      }
    }
  };
  
  // Fonction pour appliquer un thème
  const applyTheme = (theme: EditorTheme) => {
    setSelectedTheme(theme);
    setCustomTextStyle(prev => ({
      ...prev,
      textColor: theme.textColor,
      backgroundColor: theme.background
    }));
  };
  
  // Fonction de rendu des diagrammes maintenant dans ChordDiagramViewer
  // const renderChordDiagram = (chord: ChordDiagram) => { ... }; // Déplacé vers ChordDiagramViewer
  
  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="editor">Éditeur</TabsTrigger>
          <TabsTrigger value="style">Style</TabsTrigger>
          <TabsTrigger value="chords">Accords</TabsTrigger>
          <TabsTrigger value="ai">IA Avancée</TabsTrigger>
        </TabsList>
        
        <TabsContent value="editor" className="space-y-4">
          {/* Barre d'outils rapide */}
          <div className="flex flex-wrap gap-2 p-2 bg-muted/50 rounded-lg">
            <Button size="sm" variant="outline" onClick={() => setIsStylePanelOpen(!isStylePanelOpen)}>
              <Brush className="w-4 h-4 mr-1" /> Style
            </Button>
            <Button size="sm" variant="outline" onClick={() => setIsChordPanelOpen(!isChordPanelOpen)}>
              <Guitar className="w-4 h-4 mr-1" /> Accords
            </Button>
            <Button size="sm" variant="outline" onClick={() => setIsAiPanelOpen(!isAiPanelOpen)}>
              <Brain className="w-4 h-4 mr-1" /> IA
            </Button>
            <Separator orientation="vertical" className="h-8" />
            <Button size="sm" variant="outline">
              <Play className="w-4 h-4 mr-1" /> Aperçu
            </Button>
            <Button size="sm" variant="outline">
              <Download className="w-4 h-4 mr-1" /> Export
            </Button>
          </div>
          
          {/* Éditeur principal */}
          <FormField
            control={formControl}
            name="lyrics"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div 
                    className="rounded-lg border"
                    style={{
                      backgroundColor: selectedTheme.background,
                      borderColor: selectedTheme.borderColor
                    }}
                  >
                    <ReactQuill
                      ref={quillRef}
                      theme="snow"
                      value={lyricsContent}
                      onChange={onLyricsChange}
                      onChangeSelection={(range, source, editor) => {
                        setCurrentSelectionRange(range);
                      }}
                      placeholder="Écrivez vos paroles ici... Utilisez [C], [G], [Am] pour les accords"
                      modules={{
                        toolbar: [
                          [{ header: [1, 2, 3, false] }],
                          [{ size: ['small', false, 'large', 'huge'] }],
                          ['bold', 'italic', 'underline', 'strike'],
                          [{ color: [] }, { background: [] }],
                          [{ align: [] }],
                          [{ list: 'ordered' }, { list: 'bullet' }],
                          ['blockquote', 'code-block'],
                          ['link'],
                          ['clean']
                        ]
                      }}
                      style={{
                        fontSize: `${customTextStyle.fontSize}px`,
                        fontFamily: customTextStyle.fontFamily,
                        color: customTextStyle.textColor,
                        lineHeight: customTextStyle.lineHeight,
                        letterSpacing: `${customTextStyle.letterSpacing}px`
                      }}
                      className="min-h-[400px]"
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </TabsContent>
        
        <TabsContent value="style" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette className="w-5 h-5 mr-2" />
                Personnalisation du Style
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Thèmes prédéfinis */}
              <div>
                <Label className="text-sm font-medium mb-3 block">Thèmes Prédéfinis</Label>
                <div className="grid grid-cols-3 gap-2">
                  {DEFAULT_THEMES.map((theme) => (
                    <Button
                      key={theme.name}
                      variant={selectedTheme.name === theme.name ? "default" : "outline"}
                      size="sm"
                      onClick={() => applyTheme(theme)}
                      className="h-12 flex flex-col"
                      style={{
                        backgroundColor: theme.background,
                        color: theme.textColor,
                        borderColor: theme.borderColor
                      }}
                    >
                      <span className="text-xs">{theme.name}</span>
                    </Button>
                  ))}
                </div>
              </div>
              
              {/* Personnalisation avancée */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm">Taille de police</Label>
                  <Slider
                    value={[customTextStyle.fontSize]}
                    onValueChange={([value]) => setCustomTextStyle(prev => ({ ...prev, fontSize: value }))}
                    min={10}
                    max={24}
                    step={1}
                    className="mt-2"
                  />
                  <span className="text-xs text-muted-foreground">{customTextStyle.fontSize}px</span>
                </div>
                
                <div>
                  <Label className="text-sm">Interligne</Label>
                  <Slider
                    value={[customTextStyle.lineHeight]}
                    onValueChange={([value]) => setCustomTextStyle(prev => ({ ...prev, lineHeight: value }))}
                    min={1}
                    max={3}
                    step={0.1}
                    className="mt-2"
                  />
                  <span className="text-xs text-muted-foreground">{customTextStyle.lineHeight}</span>
                </div>
                
                <div>
                  <Label className="text-sm">Couleur du texte</Label>
                  <Input
                    type="color"
                    value={customTextStyle.textColor}
                    onChange={(e) => setCustomTextStyle(prev => ({ ...prev, textColor: e.target.value }))}
                    className="mt-2 h-10"
                  />
                </div>
                
                <div>
                  <Label className="text-sm">Couleur de fond</Label>
                  <Input
                    type="color"
                    value={customTextStyle.backgroundColor}
                    onChange={(e) => setCustomTextStyle(prev => ({ ...prev, backgroundColor: e.target.value }))}
                    className="mt-2 h-10"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-sm">Police</Label>
                <Select value={customTextStyle.fontFamily} onValueChange={(value) => setCustomTextStyle(prev => ({ ...prev, fontFamily: value }))}>
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Inter">Inter</SelectItem>
                    <SelectItem value="Arial">Arial</SelectItem>
                    <SelectItem value="Georgia">Georgia</SelectItem>
                    <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                    <SelectItem value="Courier New">Courier New</SelectItem>
                    <SelectItem value="Helvetica">Helvetica</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="chords" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Guitar className="w-5 h-5 mr-2" />
                Outils d'Accords
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Chord Tools */}
              <ChordDiagramViewer onChordSelect={insertChord} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="ai" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="w-5 h-5 mr-2" />
                Analyses IA Avancées
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Boutons d'analyse */}
              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={handleAdvancedLyricsAnalysis}
                  disabled={aiLoading}
                  className="h-16 flex flex-col"
                >
                  <Sparkles className="w-5 h-5 mb-1" />
                  <span className="text-xs">Analyse Paroles</span>
                </Button>
                
                <Button
                  onClick={handleChordProgressionAnalysis}
                  disabled={aiLoading}
                  className="h-16 flex flex-col"
                >
                  <Music className="w-5 h-5 mb-1" />
                  <span className="text-xs">Analyse Accords</span>
                </Button>
                
                <Button
                  onClick={handleRhythmicAnalysis}
                  disabled={aiLoading}
                  className="h-16 flex flex-col"
                >
                  <Drum className="w-5 h-5 mb-1" />
                  <span className="text-xs">Analyse Rythmique</span>
                </Button>
                
                <Button
                  onClick={handleMelodyGeneration}
                  disabled={aiLoading}
                  className="h-16 flex flex-col"
                >
                  <Wand2 className="w-5 h-5 mb-1" />
                  <span className="text-xs">Génération Mélodie</span>
                </Button>
              </div>
              
              {/* Résultats IA */}
              {aiLoading && (
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-2">Analyse en cours...</span>
                </div>
              )}
              
              {aiResult && !aiLoading && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Résultat d'Analyse</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[300px]">
                      <pre className="whitespace-pre-wrap text-sm">{aiResult}</pre>
                    </ScrollArea>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}