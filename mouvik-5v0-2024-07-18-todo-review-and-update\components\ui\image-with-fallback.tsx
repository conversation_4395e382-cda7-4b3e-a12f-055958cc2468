"use client";

import Image, { ImageProps } from 'next/image';
import { useEffect, useState } from 'react';

interface ImageWithFallbackProps extends Omit<ImageProps, 'src' | 'alt'> {
  src: string | null | undefined;
  fallbackSrc: string;
  alt: string;
}

const ImageWithFallback: React.FC<ImageWithFallbackProps> = ({
  src,
  fallbackSrc,
  alt,
  ...props
}) => {
  const [error, setError] = useState<React.SyntheticEvent<HTMLImageElement, Event> | null>(null);
  const [currentSrc, setCurrentSrc] = useState(src);

  useEffect(() => {
    setCurrentSrc(src); // Reset src when the prop changes
    setError(null); // Reset error state when src prop changes
  }, [src]);

  const handleLoadingError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    if (currentSrc !== fallbackSrc) { // Prevent infinite loop if fallback also fails
        setCurrentSrc(fallbackSrc);
    }
    setError(e);
    if (props.onError) {
      props.onError(e);
    }
  };

  // Note: For Next/Image, onError is only available if 'unoptimized' is true or if using a custom loader.
  // If you want to use the standard <img> tag for simpler onError handling with external URLs:
  // return (
  //   <img 
  //     src={error ? fallbackSrc : (currentSrc || fallbackSrc)}
  //     alt={alt}
  //     onError={() => setCurrentSrc(fallbackSrc)} 
  //     {...props} // Spreading other props like className, width, height etc.
  //   />
  // );

  // Using standard <img> tag for simplicity with onError for external URLs
  // If you need Next/Image optimization, this approach needs adjustment or use unoptimized={true}
  return (
    <img
      src={currentSrc || fallbackSrc}
      alt={alt}
      onError={handleLoadingError}
      className={props.className}
      style={props.style} // Ensure style prop is passed if used
      // Pass width/height if they are part of props and you want to maintain them
      // width={props.width} 
      // height={props.height}
    />
  );
};

export default ImageWithFallback;
