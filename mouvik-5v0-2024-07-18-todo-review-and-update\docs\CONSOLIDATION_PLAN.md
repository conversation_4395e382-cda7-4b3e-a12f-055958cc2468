# Plan de Consolidation de la Documentation

## Documents liés aux Statistiques et à l'Analytique

Après analyse des différents documents liés aux statistiques et à l'analytique, voici le plan de consolidation détaillé :

### Document principal à conserver

**`SYSTEM_ANALYTICS_RECOMMENDATIONS.md`** - Ce document est le plus complet et à jour. Il se présente déjà comme la "source de vérité unique" pour le système intégré d'analyse, de statistiques, du dashboard utilisateur et des recommandations.

### Documents à fusionner dans le document principal

1. **`Statistics.md`**
   - **Contenu unique à intégrer :**
     - Description détaillée des objectifs du système de statistiques
     - Types de statistiques (utilisateur et admin)
     - Méthodes de collecte de données via tables dédiées (plays, views, likes, follows, comments)
     - Structure des composants UI dans `components/stats/`
     - TODOs spécifiques pour la page `/stats` utilisateur
   - **Section cible dans le document principal :** 
     - Ajouter ces informations dans les sections "Objectifs" et "Sources de Données" existantes
     - Compléter la section "UI Components" avec les détails des composants
     - Intégrer les TODOs dans la section "Development Plan/TODO List"

2. **`STATISTICS_AND_SUGGESTIONS.md`**
   - **Contenu unique à intégrer :**
     - Détails sur les différents niveaux d'accès aux statistiques (tiers d'abonnement)
     - Implémentation détaillée du module de suggestions
     - Concept du système de promotion avec mécanisme de pièces/crédits
   - **Section cible dans le document principal :**
     - Enrichir la section "User Dashboard and Statistics Pages" avec les informations sur les niveaux d'accès
     - Développer la section "Suggestions Module" avec les détails d'implémentation
     - Ajouter les informations sur le système de promotion dans la section "Promotion System"

3. **`analytics_system.md`**
   - **Contenu unique à intégrer :**
     - Architecture détaillée du système d'analyse (couches de données, API, présentation)
     - Fonctions SQL spécifiques (get_user_overview_stats, get_activity_timeline, etc.)
     - Composants frontend détaillés (AnalyticsOverview, ActivityTimeline, etc.)
     - Bibliothèques utilisées (Recharts, Lucide React, shadcn/ui)
     - Évolutions futures planifiées
   - **Section cible dans le document principal :**
     - Enrichir la section "General Architecture" avec les détails des trois couches
     - Compléter la section "Database RPCs" avec les fonctions SQL manquantes
     - Ajouter les composants frontend dans la section "UI Components"
     - Intégrer les bibliothèques recommandées dans la section appropriée
     - Compléter la section "Future Potential Evolutions"

4. **`dashboard_analytics.md`**
   - **Contenu unique à intégrer :**
     - Structure détaillée du dashboard amélioré (Vue d'ensemble, Analyse d'audience, Analyse de contenu, Engagement social)
     - Composants UI spécifiques à développer
     - Bibliothèques recommandées pour les visualisations avancées (Nivo, react-simple-maps, d3.js)
     - Plan d'implémentation en phases
     - Priorités de développement
   - **Section cible dans le document principal :**
     - Enrichir la section "User Dashboard and Statistics Pages" avec la structure détaillée
     - Compléter la section "UI Components" avec les nouveaux composants
     - Ajouter les bibliothèques recommandées dans la section appropriée
     - Intégrer le plan d'implémentation et les priorités dans la section "Development Plan/TODO List"

5. **`database_schema_analytics.md`**
   - **Contenu unique à intégrer :**
     - Schémas détaillés des tables (plays, audio_analysis, audience_demographics)
     - Fonctions SQL complètes avec implémentation
     - Déclencheurs (triggers) pour la mise à jour des compteurs et statistiques
   - **Section cible :**
     - Intégrer les schémas de tables dans `database-schema.md` pour tout ce qui concerne la structure de la base de données
     - Enrichir la section "Data Collection Sources" de `SYSTEM_ANALYTICS_RECOMMENDATIONS.md` avec les détails des tables
     - Compléter la section "Database RPCs" avec les fonctions SQL
     - Ajouter une sous-section sur les triggers dans la section "Optimization Strategies"

### Processus de consolidation

1. **Préparation du Document Principal**
   - Réviser la structure actuelle de SYSTEM_ANALYTICS_RECOMMENDATIONS.md
   - Identifier les sections à enrichir ou à ajouter
   - Créer un plan de structure final

2. **Migration des Informations (par ordre de priorité)**
   - Commencer par Statistics.md (informations fondamentales)
   - Poursuivre avec analytics_system.md (architecture)
   - Intégrer dashboard_analytics.md (améliorations UI)
   - Ajouter les détails techniques de database_schema_analytics.md
   - Finir avec STATISTICS_AND_SUGGESTIONS.md (concepts avancés)

3. **Révision et Harmonisation**
   - Assurer la cohérence terminologique
   - Éliminer les redondances
   - Vérifier les références croisées
   - Mettre à jour les liens internes

4. **Finalisation**
   - Ajouter une table des matières complète
   - Mettre à jour les sections d'introduction et de conclusion
   - Vérifier le formatage et la lisibilité

5. **Mise à jour des Références**
   - Mettre à jour les références dans le README.md du répertoire docs
   - Supprimer les documents fusionnés

## Autres documents à évaluer

Après avoir traité les documents liés aux statistiques et à l'analytique, il faudra évaluer d'autres documents potentiellement redondants :

- Vérifier la relation entre `API.md` et `api-documentation.md`
- Évaluer si d'autres documents peuvent être consolidés

## Standardisation

Pour tous les documents conservés, appliquer une standardisation :

- Format cohérent des titres
- Structure claire avec table des matières
- Indication de l'état d'implémentation des fonctionnalités ([I] = Implémenté, [P] = Partiellement Implémenté, [C] = Conceptuel)
- Liens entre documents lorsque pertinent