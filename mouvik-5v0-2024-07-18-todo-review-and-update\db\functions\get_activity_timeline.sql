-- db/functions/get_activity_timeline.sql

CREATE OR REPLACE FUNCTION public.get_activity_timeline(
    p_user_id uuid
)
RETURNS TABLE (
    id uuid,
    title text,
    created_at timestamp,
    play_count bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        s.id,
        s.title,
        s.created_at,
        COALESCE(COUNT(p.id), 0) AS play_count
    FROM
        public.songs s
    LEFT JOIN public.plays p ON p.song_id = s.id
    WHERE
        s.creator_user_id = p_user_id
    GROUP BY s.id, s.title, s.created_at
    ORDER BY s.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE;
