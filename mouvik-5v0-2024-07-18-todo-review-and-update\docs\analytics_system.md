# Système d'Analyse et de Statistiques

Ce document décrit l'architecture et les fonctionnalités du système d'analyse et de statistiques de la plateforme Mouvik.

## Table des matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture](#architecture)
3. [Fonctions SQL](#fonctions-sql)
4. [Composants Frontend](#composants-frontend)
5. [Modèles de données](#modèles-de-données)
6. [Intégration avec le reste de l'application](#intégration-avec-le-reste-de-lapplication)
7. [Évolutions futures](#évolutions-futures)

## Vue d'ensemble

Le système d'analyse et de statistiques permet aux utilisateurs de visualiser et d'analyser les performances de leur contenu musical sur la plateforme. Il offre des visualisations interactives, des métriques d'engagement et des recommandations personnalisées basées sur les données d'utilisation.

### Fonctionnalités principales

- **Vue d'ensemble des statistiques** : Métriques clés et tendances d'activité
- **Analyse d'audience** : Démographie, géographie et comportement des auditeurs
- **Analyse de contenu** : Performance par morceau, album, playlist, genre, mood et instrumentation
- **Insights et recommandations** : Suggestions basées sur les données pour améliorer la performance

## Architecture

Le système d'analyse est composé de trois couches principales :

1. **Couche de données** : Fonctions SQL dans Supabase pour l'agrégation et l'analyse des données
2. **Couche API** : Endpoints RPC Supabase pour exposer les données aux clients
3. **Couche présentation** : Composants React pour la visualisation des données

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Composants     │     │  API Supabase   │     │  Base de        │
│  React          │◄────┤  (RPC)          │◄────┤  données        │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Fonctions SQL

Les fonctions SQL suivantes sont implémentées dans Supabase pour alimenter les visualisations :

### 1. Vue d'ensemble des statistiques

```sql
CREATE OR REPLACE FUNCTION get_user_overview_stats(p_user_id UUID, p_time_range TEXT)
RETURNS JSON
```

Cette fonction calcule les statistiques générales d'un utilisateur pour une période donnée, y compris :
- Nombre total d'écoutes
- Nombre d'auditeurs uniques
- Nombre de commentaires
- Nombre de likes
- Taux d'engagement
- Variations par rapport à la période précédente

### 2. Timeline d'activité

```sql
CREATE OR REPLACE FUNCTION get_activity_timeline(p_user_id UUID, p_metrics TEXT[], p_interval TEXT, p_days INTEGER)
RETURNS JSON
```

Cette fonction génère des données chronologiques pour visualiser l'évolution des métriques (écoutes, likes, commentaires) au fil du temps.

### 3. Analyse par genre

```sql
CREATE OR REPLACE FUNCTION get_genre_performance(p_user_id UUID)
RETURNS JSON
```

Cette fonction analyse la performance des morceaux par genre musical, en calculant les écoutes, likes, et taux d'engagement pour chaque genre.

### 4. Caractéristiques musicales

```sql
CREATE OR REPLACE FUNCTION get_song_characteristics(p_song_id UUID)
RETURNS JSON
```

Cette fonction récupère les caractéristiques audio d'un morceau (énergie, dansabilité, valence, etc.) pour les visualisations radar.

### 5. Contenu populaire

```sql
CREATE OR REPLACE FUNCTION get_top_content(p_user_id UUID, p_time_range TEXT, p_content_type TEXT, p_limit INTEGER)
RETURNS JSON
```

Cette fonction identifie le contenu le plus populaire d'un utilisateur (morceaux, albums, playlists) en fonction des métriques d'engagement.

## Composants Frontend

Le système utilise plusieurs composants React pour visualiser les données :

### Composants principaux

- `AnalyticsOverview` : Vue d'ensemble des statistiques principales
- `ActivityTimeline` : Graphique d'évolution de l'activité
- `GenreAnalysis` : Analyse des performances par genre musical
- `MusicMoodVisualization` : Visualisation interactive des morceaux
- `AudienceAnalysis` : Analyse démographique de l'audience
- `MusicCharacteristics` : Analyse des caractéristiques musicales
- `TopContent` : Affichage du contenu le plus populaire
- `ArtistComparison` : Comparaison avec d'autres artistes

### Bibliothèques utilisées

- **Recharts** : Pour les visualisations de données (graphiques en barres, lignes, camemberts, etc.)
- **Lucide React** : Pour les icônes
- **shadcn/ui** : Pour les composants d'interface utilisateur

## Modèles de données

### Métriques principales

- **Écoutes (Plays)** : Nombre de fois qu'un morceau a été écouté
- **Likes** : Nombre de likes reçus sur un contenu
- **Commentaires** : Nombre de commentaires reçus sur un contenu
- **Vues** : Nombre de fois qu'un contenu a été consulté
- **Followers** : Nombre d'utilisateurs qui suivent un artiste

### Tables impliquées

- `songs` : Morceaux musicaux
- `plays` : Enregistrements des écoutes
- `likes` : Likes sur les contenus
- `comments` : Commentaires sur les contenus
- `follows` : Relations de suivi entre utilisateurs
- `albums` : Albums musicaux
- `playlists` : Playlists d'utilisateurs
- `audio_analysis` : Caractéristiques audio des morceaux (à implémenter)

## Intégration avec le reste de l'application

Le système d'analyse s'intègre avec les autres composants de l'application :

- **Système d'authentification** : Pour identifier l'utilisateur et ses contenus
- **Système de lecture** : Pour enregistrer les écoutes et les interactions
- **Système de recommandation** : Pour alimenter les recommandations basées sur les données d'analyse

## Évolutions futures

Voici les évolutions prévues pour le système d'analyse :

1. **Analyse prédictive** : Prédiction des tendances et du potentiel de croissance
2. **Segmentation d'audience** : Analyse plus fine des groupes d'auditeurs
3. **Analyse comparative** : Comparaison avec des artistes similaires
4. **Analyse de sentiment** : Analyse des commentaires pour déterminer la réception du contenu
5. **Intégration avec des services externes** : Connexion avec des plateformes comme Spotify, Apple Music, etc.
6. **Rapports automatisés** : Génération de rapports périodiques envoyés par email
7. **Alertes personnalisées** : Notifications basées sur des seuils définis par l'utilisateur
