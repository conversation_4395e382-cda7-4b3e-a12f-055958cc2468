"use client";

import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Guitar, Users, PlayCircle } from 'lucide-react';
import type { Band } from '@/types';
import { cn } from '@/lib/utils';

interface BandCardProps {
  band: Band;
  className?: string;
}

export function BandCard({ band, className }: BandCardProps) {
  const viewPageUrl = `/bands/${band.slug || band.id}`;

  return (
    <Card className={cn("overflow-hidden flex flex-col h-full group/card", className)}>
      <CardHeader className="p-0 relative">
        <Link href={viewPageUrl} className="block aspect-square relative group">
          {band.cover_url || band.avatar_url ? ( // Use cover_url first, then avatar_url
            <Image
              src={band.cover_url || band.avatar_url!}
              alt={band.name}
              width={300}
              height={300}
              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Guitar className="w-16 h-16 text-muted-foreground" />
            </div>
          )}
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <PlayCircle className="w-12 h-12 text-white" />
          </div>
        </Link>
      </CardHeader>
      <CardContent className="p-4 flex-grow">
        <Link href={viewPageUrl}>
          <CardTitle className="text-lg font-semibold hover:underline mb-1 truncate" title={band.name}>
            {band.name}
          </CardTitle>
        </Link>
        {band.genres && band.genres.length > 0 && (
          <div className="text-xs text-muted-foreground mb-2 flex flex-wrap gap-1">
            {band.genres.slice(0, 3).map((genre) => (
              <Badge key={genre} variant="secondary" className="capitalize">
                {genre}
              </Badge>
            ))}
          </div>
        )}
        {typeof band.follower_count === 'number' && band.follower_count > 0 && (
          <div className="text-xs text-muted-foreground flex items-center gap-1">
            <Users className="w-3 h-3" /> {band.follower_count} follower{band.follower_count === 1 ? '' : 's'}
          </div>
        )}
      </CardContent>
      {/* Footer can be added later if actions like 'Play All' or 'Follow' are needed */}
    </Card>
  );
}
