# API Reference

## Overview

MOUVIK uses Supabase as its backend service, which provides a RESTful API for interacting with the database. This document outlines the key API endpoints and how to use them.

## Authentication

### Sign Up

Create a new user account.

\`\`\`typescript
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'example-password',
})
\`\`\`

### Sign In

Sign in to an existing user account.

\`\`\`typescript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'example-password',
})
\`\`\`

### Sign Out

Sign out the current user.

\`\`\`typescript
const { error } = await supabase.auth.signOut()
\`\`\`

### Get Current User

Get the currently authenticated user.

\`\`\`typescript
const { data: { user } } = await supabase.auth.getUser()
\`\`\`

## Songs

### Create Song

Create a new song.

\`\`\`typescript
const { data, error } = await supabase
  .from('songs')
  .insert({
    title: 'Song Title',
    description: 'Song Description',
    genre: 'pop,electronic',
    key: 'C',
    bpm: 120,
    cover_url: 'https://example.com/cover.jpg',
    audio_url: 'https://example.com/audio.mp3',
    waveform_url: 'https://example.com/waveform.json',
    lyrics: 'Song lyrics...',
    status: 'published',
    is_explicit: false,
    duration: 180,
    user_id: 'user-uuid',
  })
  .select()
\`\`\`

### Get Song

Get a song by ID.

\`\`\`typescript
const { data, error } = await supabase
  .from('songs')
  .select('*')
  .eq('id', 'song-uuid')
  .single()
\`\`\`

### Update Song

Update an existing song.

\`\`\`typescript
const { data, error } = await supabase
  .from('songs')
  .update({
    title: 'Updated Song Title',
    description: 'Updated Song Description',
  })
  .eq('id', 'song-uuid')
  .select()
\`\`\`

### Delete Song

Delete a song.

\`\`\`typescript
const { error } = await supabase
  .from('songs')
  .delete()
  .eq('id', 'song-uuid')
\`\`\`

### List Songs

List songs with filtering and pagination.

\`\`\`typescript
const { data, error } = await supabase
  .from('songs')
  .select('*')
  .eq('user_id', 'user-uuid')
  .eq('status', 'published')
  .order('created_at', { ascending: false })
  .range(0, 9)
\`\`\`

## Albums

### Create Album

Create a new album.

\`\`\`typescript
const { data, error } = await supabase
  .from('albums')
  .insert({
    title: 'Album Title',
    description: 'Album Description',
    genre: 'rock,alternative',
    release_date: '2023-01-01',
    cover_url: 'https://example.com/album-cover.jpg',
    status: 'published',
    is_explicit: false,
    user_id: 'user-uuid',
  })
  .select()
\`\`\`

### Get Album

Get an album by ID.

\`\`\`typescript
const { data, error } = await supabase
  .from('albums')
  .select('*, songs(*)')
  .eq('id', 'album-uuid')
  .single()
\`\`\`

### Update Album

Update an existing album.

\`\`\`typescript
const { data, error } = await supabase
  .from('albums')
  .update({
    title: 'Updated Album Title',
    description: 'Updated Album Description',
  })
  .eq('id', 'album-uuid')
  .select()
\`\`\`

### Delete Album

Delete an album.

\`\`\`typescript
const { error } = await supabase
  .from('albums')
  .delete()
  .eq('id', 'album-uuid')
\`\`\`

### List Albums

List albums with filtering and pagination.

\`\`\`typescript
const { data, error } = await supabase
  .from('albums')
  .select('*, album_songs(count)')
  .eq('user_id', 'user-uuid')
  .eq('status', 'published')
  .order('created_at', { ascending: false })
  .range(0, 9)
\`\`\`

## Social Features

### Follow User

Follow another user.

\`\`\`typescript
const { data, error } = await supabase
  .from('follows')
  .insert({
    follower_id: 'current-user-uuid',
    following_id: 'user-to-follow-uuid',
  })
  .select()
\`\`\`

### Unfollow User

Unfollow a user.

\`\`\`typescript
const { error } = await supabase
  .from('follows')
  .delete()
  .eq('follower_id', 'current-user-uuid')
  .eq('following_id', 'user-to-unfollow-uuid')
\`\`\`

### Like Content

Like a song, album, or other content.

\`\`\`typescript
const { data, error } = await supabase
  .from('likes')
  .insert({
    user_id: 'current-user-uuid',
    resource_id: 'content-uuid',
    resource_type: 'song', // or 'album', 'playlist', etc.
  })
  .select()
\`\`\`

### Unlike Content

Remove a like from content.

\`\`\`typescript
const { error } = await supabase
  .from('likes')
  .delete()
  .eq('user_id', 'current-user-uuid')
  .eq('resource_id', 'content-uuid')
  .eq('resource_type', 'song')
\`\`\`

### Add Comment

Add a comment to content.

\`\`\`typescript
const { data, error } = await supabase
  .from('comments')
  .insert({
    user_id: 'current-user-uuid',
    resource_id: 'content-uuid',
    resource_type: 'song', // or 'album', 'playlist', etc.
    content: 'This is a comment',
    parent_id: null, // or parent comment UUID for replies
  })
  .select()
\`\`\`

### Get Comments

Get comments for content.

\`\`\`typescript
const { data, error } = await supabase
  .from('comments')
  .select('*, profiles(username, avatar_url)')
  .eq('resource_id', 'content-uuid')
  .eq('resource_type', 'song')
  .order('created_at', { ascending: false })
\`\`\`

## Analytics

### Record Play

Record a song play event.

\`\`\`typescript
const { data, error } = await supabase
  .from('plays')
  .insert({
    song_id: 'song-uuid',
    user_id: 'current-user-uuid', // optional, can be null for anonymous plays
  })
\`\`\`

### Record View

Record a content view event.

\`\`\`typescript
const { data, error } = await supabase
  .from('views')
  .insert({
    resource_id: 'content-uuid',
    resource_type: 'album', // or 'song', 'playlist', etc.
    user_id: 'current-user-uuid', // optional, can be null for anonymous views
  })
\`\`\`

### Get Play Count

Get the play count for a song.

\`\`\`typescript
const { count, error } = await supabase
  .from('plays')
  .select('*', { count: 'exact', head: true })
  .eq('song_id', 'song-uuid')
\`\`\`

### Get View Count

Get the view count for content.

\`\`\`typescript
const { count, error } = await supabase
  .from('views')
  .select('*', { count: 'exact', head: true })
  .eq('resource_id', 'content-uuid')
  .eq('resource_type', 'album')
\`\`\`

## File Storage

### Upload File

Upload a file to storage.

\`\`\`typescript
const { data, error } = await supabase.storage
  .from('bucket-name')
  .upload('file-path', file, {
    cacheControl: '3600',
    upsert: false,
  })
\`\`\`

### Get File URL

Get a public URL for a file.

\`\`\`typescript
const { data } = supabase.storage
  .from('bucket-name')
  .getPublicUrl('file-path')
\`\`\`

### Delete File

Delete a file from storage.

\`\`\`typescript
const { error } = await supabase.storage
  .from('bucket-name')
  .remove(['file-path'])
