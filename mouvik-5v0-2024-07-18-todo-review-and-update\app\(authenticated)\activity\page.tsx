import { createSupabaseServerClient } from "@/lib/supabase/server"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { ActivityIcon, Music2, Disc, MessageSquare, Plus, Heart } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default async function ActivityPage() {
  const supabase = createSupabaseServerClient()

  const { data: activities } = await supabase
    .from("activities")
    .select(`
      *,
      profiles:user_id (username, avatar_url)
    `)
    .order("created_at", { ascending: false })
    .limit(10)

  const formatDate = (date: string) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true, locale: fr })
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "song_created":
      case "song_updated":
        return <Music2 className="h-5 w-5" />
      case "album_created":
      case "album_updated":
        return <Disc className="h-5 w-5" />
      case "comment_added":
        return <MessageSquare className="h-5 w-5" />
      default:
        return <ActivityIcon className="h-5 w-5" />
    }
  }

  const getActivityTitle = (activity: any) => {
    const username = activity.profiles?.username || "Utilisateur"

    switch (activity.activity_type) {
      case "song_created":
        return `${username} a créé un nouveau morceau`
      case "song_updated":
        return `${username} a mis à jour un morceau`
      case "album_created":
        return `${username} a créé un nouvel album`
      case "album_updated":
        return `${username} a mis à jour un album`
      case "comment_added":
        return `${username} a commenté`
      case "playlist_created":
        return `${username} a créé une nouvelle playlist`
      case "playlist_updated":
        return `${username} a mis à jour une playlist`
      default:
        return `${username} a effectué une action`
    }
  }

  // Filter out error objects and only render valid activities
  const validActivities = Array.isArray(activities) ? activities.filter(activity => activity && typeof activity === 'object' && 'id' in activity) : []

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Activité</h1>
          <p className="text-muted-foreground">Suivez l'activité de votre réseau et de vos projets</p>
        </div>
        <Tabs defaultValue="all">
          <TabsList>
            <TabsTrigger value="all">Tout</TabsTrigger>
            <TabsTrigger value="following">Abonnements</TabsTrigger>
            <TabsTrigger value="mine">Mes activités</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          {validActivities.map(activity => (
            <Card key={activity.id}>
              <CardContent className="p-6">
                <div className="flex gap-4">
                  <Avatar>
                    <AvatarImage src={activity.profiles?.avatar_url || "/placeholder.svg"} />
                    <AvatarFallback>{activity.profiles?.username?.charAt(0).toUpperCase() || "U"}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{getActivityTitle(activity)}</h3>
                      <span className="text-xs text-muted-foreground">{formatDate(activity.created_at)}</span>
                    </div>
                    {activity.content && <p className="mt-2 text-sm">{activity.content}</p>}
                    <div className="mt-4 flex items-center gap-4">
                      <Button variant="ghost" size="sm" className="h-8 gap-1">
                        <Heart className="h-4 w-4" />
                        <span>J'aime</span>
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 gap-1">
                        <MessageSquare className="h-4 w-4" />
                        <span>Commenter</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          {(!validActivities || validActivities.length === 0) && (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">Aucune activité récente</p>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Créer un post</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <textarea
                  className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Partagez vos pensées..."
                />
                <Button className="w-full">
                  <Plus className="mr-2 h-4 w-4" />
                  Publier
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Suggestions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">Fonctionnalité à venir</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
