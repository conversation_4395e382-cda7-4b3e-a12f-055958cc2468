"use client";
import React from "react";

export function LanguageForm() {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold mb-2">Langue de l'interface</h2>
      <select className="border rounded p-2">
        <option value="fr">Français</option>
        <option value="en">English</option>
      </select>
      <p className="text-xs text-muted-foreground">Le changement de langue s'applique à toute l'interface (bientôt disponible).</p>
    </div>
  );
}
