export interface Album {
  id: string; // UUID
  title: string;
  artist_name?: string; // Or artist_id if you have an artists table
  cover_art_url?: string | null;
  release_date?: Date | string | null;
  user_id?: string; // If albums are user-specific
  band_id?: string | null; // If albums belong to bands
  // Add other relevant album properties here
  // e.g., genre, description, songs_count, etc.
}
