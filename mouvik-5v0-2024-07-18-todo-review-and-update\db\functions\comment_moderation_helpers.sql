-- Function to check if a user can moderate a specific comment.
-- A user can moderate a comment if they are the creator of the resource
-- to which the comment is attached.
CREATE OR REPLACE FUNCTION public.can_moderate_comment(
  p_comment_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER -- Important for <PERSON><PERSON> checks across tables
AS $$
DECLARE
  v_resource_type TEXT;
  v_resource_id UUID;
  v_resource_creator_id UUID;
  v_can_moderate BOOLEAN;
BEGIN
  RAISE NOTICE 'can_moderate_comment: p_comment_id=%, p_user_id=%', p_comment_id, p_user_id;

  -- Get the resource type and ID from the comment
  SELECT resource_type, resource_id
  INTO v_resource_type, v_resource_id
  FROM public.comments
  WHERE id = p_comment_id;

  IF NOT FOUND THEN
    RAISE NOTICE 'can_moderate_comment: Comment not found.';
    RETURN FALSE; -- Comment does not exist
  END IF;
  RAISE NOTICE 'can_moderate_comment: v_resource_type=%, v_resource_id=%', v_resource_type, v_resource_id;

  -- Get the creator_id of the resource
  IF v_resource_type = 'album' THEN
    SELECT user_id INTO v_resource_creator_id FROM public.albums WHERE id = v_resource_id;
  ELSIF v_resource_type = 'song' THEN
    SELECT user_id INTO v_resource_creator_id FROM public.songs WHERE id = v_resource_id;
  ELSIF v_resource_type = 'playlist' THEN
    SELECT user_id INTO v_resource_creator_id FROM public.playlists WHERE id = v_resource_id;
  ELSIF v_resource_type = 'artist' THEN 
    v_resource_creator_id := v_resource_id;
  ELSIF v_resource_type = 'band' THEN
    SELECT creator_id INTO v_resource_creator_id FROM public.bands WHERE id = v_resource_id;
  ELSE
    RAISE NOTICE 'can_moderate_comment: Unknown resource type.';
    RETURN FALSE; -- Unknown resource type
  END IF;

  IF NOT FOUND THEN
     RAISE NOTICE 'can_moderate_comment: Resource (e.g. album, song) not found by resource_id % OR its creator_id is NULL.', v_resource_id;
     RETURN FALSE; 
  END IF;
  
  IF v_resource_creator_id IS NULL THEN
     RAISE NOTICE 'can_moderate_comment: Resource creator_id is NULL for resource_id %.', v_resource_id;
     RETURN FALSE;
  END IF;
  RAISE NOTICE 'can_moderate_comment: v_resource_creator_id=%', v_resource_creator_id;

  -- Check if the provided user_id is the creator of the resource
  v_can_moderate := (v_resource_creator_id = p_user_id);
  RAISE NOTICE 'can_moderate_comment: Comparison result (v_resource_creator_id = p_user_id) is %', v_can_moderate;
  
  RETURN v_can_moderate;
END;
$$;

-- Policy: Allow resource creators to update (e.g., change status) comments on their resources
-- Ensure this is the primary or only UPDATE policy that should apply for this scenario.
-- If other UPDATE policies exist, review their conditions.
DROP POLICY IF EXISTS "Allow resource creators to update comments on their resources" ON public.comments;
CREATE POLICY "Allow resource creators to update comments on their resources"
ON public.comments
FOR UPDATE
TO authenticated
USING (public.can_moderate_comment(id, auth.uid()))
WITH CHECK (public.can_moderate_comment(id, auth.uid()));
