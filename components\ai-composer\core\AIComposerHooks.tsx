'use client';

import { useCallback, useMemo } from 'react';
import { toast } from '@/hooks/use-toast';
import type { LyricsSection, ChordPosition } from './AIComposerCore';

// Hook pour la gestion des accords
export const useChordManagement = (
  songSections: LyricsSection[],
  setSongSections: (sections: LyricsSection[]) => void,
  selectedSection: string
) => {
  
  const handleChordInsert = useCallback((chordData: {
    chord: string;
    position: 'above' | 'inline' | 'below';
    section?: string;
  }) => {
    const currentSection = songSections.find(s => s.id === selectedSection);
    if (!currentSection) return;
    
    // Insérer l'accord dans la section courante
    const updatedSections = songSections.map(section => {
      if (section.id === selectedSection) {
        const newChord: ChordPosition = {
          id: `chord-${Date.now()}`,
          position: section.content.length,
          chord: chordData.chord,
          instrument: 'guitar' // À adapter selon l'instrument sélectionné
        };
        
        return {
          ...section,
          chords: [...section.chords, newChord],
          content: chordData.position === 'above' 
            ? `[${chordData.chord}]\n${section.content}`
            : chordData.position === 'below'
            ? `${section.content}\n[${chordData.chord}]`
            : `${section.content} [${chordData.chord}]`
        };
      }
      return section;
    });
    
    setSongSections(updatedSections);
  }, [songSections, selectedSection, setSongSections]);

  const handleChordAdd = useCallback((sectionId: string, chord: ChordPosition) => {
    setSongSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, chords: [...section.chords, chord] }
        : section
    ));
  }, [setSongSections]);
  
  const handleChordRemove = useCallback((sectionId: string, chordId: string) => {
    setSongSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, chords: section.chords.filter(c => c.id !== chordId) }
        : section
    ));
  }, [setSongSections]);
  
  const handleChordUpdate = useCallback((sectionId: string, chordId: string, updates: Partial<ChordPosition>) => {
    setSongSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { 
            ...section, 
            chords: section.chords.map(chord => 
              chord.id === chordId ? { ...chord, ...updates } : chord
            )
          }
        : section
    ));
  }, [setSongSections]);

  const handlePlayChord = useCallback((chordMidi: number[]) => {
    console.log('Playing chord:', chordMidi);
    // Ici vous pouvez intégrer votre logique de lecture MIDI
  }, []);

  return {
    handleChordInsert,
    handleChordAdd,
    handleChordRemove,
    handleChordUpdate,
    handlePlayChord
  };
};

// Hook pour la gestion des sections
export const useSectionManagement = (
  songSections: LyricsSection[],
  setSongSections: (sections: LyricsSection[]) => void,
  selectedSection: string,
  setSelectedSection: (section: string) => void,
  lyricsContent: string,
  setLyricsContent: (content: string) => void
) => {

  const handleLyricsChange = useCallback((content: string) => {
    setLyricsContent(content);
    
    // Mettre à jour la section courante
    const updatedSections = songSections.map(section => {
      if (section.id === selectedSection) {
        return { ...section, content };
      }
      return section;
    });
    
    setSongSections(updatedSections);
  }, [songSections, selectedSection, setLyricsContent, setSongSections]);

  const handleSectionAdd = useCallback((type: LyricsSection['type']) => {
    const newSection: LyricsSection = {
      id: `section-${Date.now()}`,
      type,
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} ${songSections.length + 1}`,
      content: '',
      chords: []
    };
    
    setSongSections(prev => [...prev, newSection]);
    setSelectedSection(newSection.id);
  }, [songSections.length, setSongSections, setSelectedSection]);

  const handleStructureChange = useCallback((newStructure: Array<{
    id: string;
    type: LyricsSection['type'];
    title: string;
    duration: number;
    startTime: number;
    key?: string;
    tempo?: number;
    content?: string;
    lyrics?: string;
  }>) => {
    // Convertir la nouvelle structure en LyricsSection
    const convertedSections: LyricsSection[] = newStructure.map(section => ({
      id: section.id,
      type: section.type,
      title: section.title,
      content: section.content || '', // Assurer que content n'est jamais undefined
      chords: [],
      key: section.key,
      tempo: section.tempo,
      duration: section.duration,
      startTime: section.startTime,
      lyrics: section.lyrics ? [section.lyrics] : []
    }));
    setSongSections(convertedSections);
  }, [setSongSections]);

  // Fonction pour formater les paroles avec les accords au-dessus
  const formatLyricsWithChords = useCallback(() => {
    const currentSection = songSections.find(section => section.id === selectedSection);
    if (!currentSection) return '';
    
    const lines = currentSection.content.split('\n');
    let formattedOutput = '';
    
    lines.forEach((line, index) => {
      // Vérifier s'il y a des accords pour cette ligne
      const chordsForLine = currentSection.chords?.filter(chord => 
        chord.position !== undefined
      ) || [];
      
      if (chordsForLine.length > 0) {
        // Créer une ligne d'accords
        let chordLine = ' '.repeat(line.length);
        chordsForLine.forEach(chord => {
          const pos = chord.position || 0;
          if (pos < chordLine.length) {
            const chordText = chord.chord;
            chordLine = chordLine.substring(0, pos) + chordText + 
                       ' '.repeat(Math.max(0, chordLine.length - pos - chordText.length));
          }
        });
        formattedOutput += chordLine.trimEnd() + '\n';
      }
      
      formattedOutput += line + '\n';
    });
    
    return formattedOutput;
  }, [songSections, selectedSection]);

  return {
    handleLyricsChange,
    handleSectionAdd,
    handleStructureChange,
    formatLyricsWithChords
  };
};

// Hook pour la gestion des fonctions IA
export const useAIFunctions = (
  isConfigured: boolean,
  callAI: (prompt: string, systemPrompt: string) => Promise<string>,
  songSections: LyricsSection[],
  setLastAiResult: (result: string) => void,
  setAiHistory: (history: { role: string; content: string }[]) => void,
  aiHistory: { role: string; content: string }[]
) => {

  const handleAIGenerate = useCallback(async (prompt: string, type: string) => {
    if (!isConfigured) {
      toast({
        title: 'Configuration requise',
        description: 'Veuillez configurer votre provider IA.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const systemPrompt = `Vous êtes un assistant de composition musicale. Aidez avec ${type}.`;
      const result = await callAI(prompt, systemPrompt);
      setLastAiResult(result);
      setAiHistory(prev => [...prev, 
        { role: 'user', content: prompt },
        { role: 'assistant', content: result }
      ]);
      
      toast({
        title: 'Génération terminée',
        description: `${type} généré avec succès.`
      });
    } catch (error) {
      toast({
        title: 'Erreur IA',
        description: error instanceof Error ? error.message : 'Erreur lors de la génération',
        variant: 'destructive'
      });
    }
  }, [isConfigured, callAI, setLastAiResult, setAiHistory]);

  return {
    handleAIGenerate
  };
};

// Hook pour les instruments disponibles
export const useInstruments = () => {
  const AVAILABLE_INSTRUMENTS = useMemo(() => [
    {
      id: 'guitar',
      name: 'Guitare',
      tunings: [
        { id: 'standard', name: 'Standard', notes: ['E', 'A', 'D', 'G', 'B', 'E'] },
        { id: 'drop_d', name: 'Drop D', notes: ['D', 'A', 'D', 'G', 'B', 'E'] },
        { id: 'open_g', name: 'Open G', notes: ['D', 'G', 'D', 'G', 'B', 'D'] }
      ]
    },
    {
      id: 'ukulele',
      name: 'Ukulélé',
      tunings: [
        { id: 'standard', name: 'Standard', notes: ['G', 'C', 'E', 'A'] },
        { id: 'low_g', name: 'Low G', notes: ['G', 'C', 'E', 'A'] }
      ]
    },
    {
      id: 'piano',
      name: 'Piano',
      tunings: [
        { id: 'standard', name: 'Standard', notes: [] }
      ]
    }
  ], []);

  return { AVAILABLE_INSTRUMENTS };
};
