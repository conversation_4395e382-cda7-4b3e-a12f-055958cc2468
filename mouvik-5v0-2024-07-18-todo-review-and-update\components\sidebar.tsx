"use client"
import React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { BarChart3, Music, Disc, ListTodo, Activity, Users, Compass, Settings, LogOut, Plus, Radio, ShieldCheck, UserCog as UserCogIcon, Star, Gem, BarChartBig, CircleDollarSign, PanelLeft, ListMusic } from "lucide-react" // Added ListMusic
import { getSupabaseClient } from "@/lib/supabase/client"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"; 
// rolePrimaryOptions no longer needed here if we remove the role badge
// import { rolePrimaryOptions } from "@/lib/constants/song-options"; 
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuAction,
  // SidebarGroupLabel, // Not used in the original, keep it out unless needed
  // SidebarGroup,      // Not used in the original for navItems, but used once below
  // SidebarGroupContent, // Not used in the original for navItems
  useSidebar, 
} from "@/components/ui/sidebar" // Adjusted imports based on actual usage in original
import { useToast } from "@/hooks/use-toast"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Updated interface to include username
export interface UserProfileForSidebar { // Added export
  id: string;
  name?: string | null; // This is often used as a primary name
  display_name?: string | null; // Adding this for consistency with full UserProfile
  email?: string | null;
  avatar_url?: string | null;
  username?: string | null;
  role_primary?: string | null;
  subscription_tier?: string | null;
  user_role?: string | null;
  // Add custom quota fields for consistency with userObj from layout context
  custom_uploads_per_month?: number | null;
  custom_vault_space_gb?: number | null;
  custom_ia_credits_month?: number | null;
  custom_coins_month?: number | null;
  custom_max_playlists?: number | null;
  custom_max_friends?: number | null;
  custom_vault_max_files?: number | null; // Added custom_vault_max_files
  ia_credits?: number | null; 
  coins_balance?: number | null; 
}

interface SidebarProps {
  user: UserProfileForSidebar | null; 
}

// Define a type for nav items for better type safety
interface NavItemAction {
  href: string;
  icon: React.ElementType; // Lucide icons are typically React.ElementType
  tooltip: string;
}
interface NavItem {
  title: string;
  href: string;
  icon: React.ElementType;
  action?: NavItemAction; // Action is optional
}

export function AppSidebar({ user }: SidebarProps) {
  const pathname = usePathname()
  const { toast } = useToast()
  const { state: sidebarState, toggleSidebar } = useSidebar(); 
  const isUiCollapsed = sidebarState === 'collapsed'; 

  const isAdmin = user?.user_role === 'admin';

  const handleSignOut = async () => {
    const supabase = getSupabaseClient()
    const { error } = await supabase.auth.signOut()

    if (error) {
      toast({
        title: "Erreur",
        description: "Impossible de se déconnecter. Veuillez réessayer.",
        variant: "destructive",
      })
    } else {
      toast({
        title: "Déconnexion réussie",
        description: "Vous avez été déconnecté avec succès.",
      })
      window.location.href = "/"
    }
  }

  const navItems = [
    { title: "Vue d'ensemble", href: "/dashboard", icon: BarChart3 },
    { title: "Morceaux", href: "/manage-songs", icon: Music, action: { href: "/manage-songs/create", icon: Plus, tooltip: "Nouveau morceau" }},
    { title: "Albums", href: "/albums", icon: Disc, action: { href: "/albums/create", icon: Plus, tooltip: "Nouvel album" }},
    { title: "Playlists", href: "/playlists", icon: ListMusic, action: { href: "/playlists/create", icon: Plus, tooltip: "Nouvelle playlist" }}, 
    { title: "Groupes", href: "/manage-bands", icon: Users, action: { href: "/manage-bands/create", icon: Plus, tooltip: "Nouveau groupe" }}, // Updated links
    { title: "Statistiques", href: "/stats", icon: BarChartBig }, 
    { title: "Découvrir", href: "/discover", icon: Compass },
    { title: "Communauté", href: "/community", icon: Radio },
    { title: "Todolist", href: "/todos", icon: ListTodo },
    { title: "Activité", href: "/activity", icon: Activity },
  ] as NavItem[]; // Explicitly type navItems

  return (
    <Sidebar collapsible="icon" className="transition-all duration-300 bg-zinc-900 min-h-screen flex flex-col">
      {/* Adjust SidebarHeader for collapsed state: ensure logo and expand button fit and look good */}
      <SidebarHeader 
        className={`flex flex-col items-center p-2 border-b border-zinc-800 transition-all duration-300 
                    ${isUiCollapsed ? 'justify-center h-[calc(var(--sidebar-width-icon)_+2rem)] py-3' : 'justify-center gap-2 py-4'}`} // Centered items, increased height for collapsed
      >
        {/* Logo Section */}
        <div className={`w-full flex ${isUiCollapsed ? 'justify-center' : 'justify-start'} items-center`}>
          <Link href="/" className={`${isUiCollapsed ? 'flex justify-center w-full' : ''}`}>
            <img src="/LOGO_Mouvik.png" alt="Mouvik" className={`transition-all duration-300 object-contain ${isUiCollapsed ? 'h-9' : 'h-10'}`} /> 
          </Link>
        </div>

        {/* Toggle Button - common for both states, icon changes */}
        <button
            onClick={toggleSidebar} 
            className="rounded-md p-1.5 hover:bg-primary/10 transition-all duration-200 border border-zinc-700 focus:border-primary shadow-sm text-zinc-400 hover:text-primary"
            aria-label={isUiCollapsed ? "Agrandir la sidebar" : "Réduire la sidebar"}
            // Position button differently based on state
            // When expanded, it's next to the logo (handled by parent flex if not isUiCollapsed)
            // When collapsed, it's below the logo
            // This button is now the *only* toggle button in the header.
        >
          {isUiCollapsed ? 
            <PanelLeft className="h-4 w-4 transform rotate-180" /> // Icon for expanding (e.g., panel open to right)
            : 
            <PanelLeft className="h-4 w-4" /> // Icon for collapsing
          }
        </button>
        
        {/* This is the old structure for separate buttons, removing it.
           The button above now handles both states.
        {!isUiCollapsed && (
          // ... old collapse button ...
        )}
        {isUiCollapsed && (
          // ... old expand button ...
        )}
        */}
      </SidebarHeader>
      <SidebarContent className="bg-zinc-900 transition-all duration-300 flex-1 overflow-y-auto p-4"> {/* Changed pt-3 to p-4 */}
        <SidebarMenu>
          {/* Admin Link */}
          {isAdmin && (
            <SidebarMenuItem>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SidebarMenuButton asChild isActive={pathname.startsWith('/admin')} className="group transition-all duration-150 hover:bg-red-500/10 hover:scale-[1.04] focus:bg-red-500/20 text-red-400 hover:text-red-300 data-[active=true]:bg-red-500/20 data-[active=true]:text-red-300">
                      <Link href="/admin">
                        <ShieldCheck className={`h-5 w-5 ${isUiCollapsed ? 'mx-auto' : ''}`} />
                        {!isUiCollapsed && <span className="transition-all duration-200">Administration</span>}
                      </Link>
                    </SidebarMenuButton>
                  </TooltipTrigger>
                  {isUiCollapsed && <TooltipContent side="right">Administration</TooltipContent>}
                </Tooltip>
              </TooltipProvider>
            </SidebarMenuItem>
          )}

          {/* Regular Nav Items */}
          {navItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SidebarMenuButton asChild isActive={pathname === item.href} className="group transition-all duration-150 hover:bg-primary/10 hover:scale-[1.04] focus:bg-primary/20 text-slate-300 hover:text-slate-50 data-[active=true]:bg-primary/10 data-[active=true]:text-primary">
                      <Link href={item.href}>
                        <item.icon className={`h-5 w-5 ${isUiCollapsed ? 'mx-auto' : ''}`} />
                        {!isUiCollapsed && <span className="transition-all duration-200">{item.title}</span>}
                      </Link>
                    </SidebarMenuButton>
                  </TooltipTrigger>
                  {isUiCollapsed && <TooltipContent side="right">{item.title}</TooltipContent>}
                </Tooltip>
              </TooltipProvider>
              {!isUiCollapsed && item.action && (
                 <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuAction asChild showOnHover>
                          <Link href={item.action.href}>
                            <item.action.icon className="h-4 w-4" />
                            <span className="sr-only">{item.action.tooltip}</span>
                          </Link>
                        </SidebarMenuAction>
                      </TooltipTrigger>
                      <TooltipContent side="right" className="ml-2">{item.action.tooltip}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
              )}
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="border-t border-zinc-800 p-3"> {/* Removed mb-[70px] */}
        <div className="flex flex-col gap-2"> {/* Reduced gap */}
          {user ? (
            <div className={`${isUiCollapsed ? 'space-y-2' : 'space-y-2'}`}>
              <div className={`flex items-center ${isUiCollapsed ? 'justify-center' : 'gap-2'}`}>
                <Link href={user.username ? `/artists/${user.username}` : "/profile/edit"} passHref>
                  <Avatar className={`cursor-pointer border-2 border-primary transition-transform hover:scale-110 ${isUiCollapsed ? 'h-9 w-9' : 'h-10 w-10'}`}>
                    <AvatarImage src={user.avatar_url || "/placeholder.svg"} alt={user.name || user.email || 'User avatar'} />
                    <AvatarFallback className={`bg-slate-700 text-slate-300 ${isUiCollapsed ? 'text-sm' : 'text-base'}`}>
                      {user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                </Link>
                {!isUiCollapsed && (
                  <div className="flex flex-col overflow-hidden flex-1">
                    <Link href={user.username ? `/artists/${user.username}` : "/profile/edit"} passHref className="hover:underline">
                      <span className="text-xs font-semibold text-slate-100 truncate" title={user.name || user.email || undefined}>
                        {user.name || user.email}
                      </span>
                    </Link>
                    {/* Display Coins Balance instead of Primary Role */}
                    <div className="text-xs text-amber-400 flex items-center gap-1 mt-0.5" title="Vos MOUVIKS">
                      <CircleDollarSign className="h-3.5 w-3.5" />
                      <span>{user.coins_balance?.toLocaleString('fr-FR') || 0} MOUVIKS</span>
                    </div>
                    {/* Display Account Status/Tier */}
                    <div className="text-xs text-primary flex items-center gap-1 mt-0.5">
                      {user.user_role === 'admin' && <ShieldCheck className="h-3.5 w-3.5" />}
                      {user.user_role === 'moderator' && <UserCogIcon className="h-3.5 w-3.5" />}
                      {user.subscription_tier === 'pro' && user.user_role !== 'admin' && user.user_role !== 'moderator' && <Star className="h-3.5 w-3.5" />}
                      {user.subscription_tier === 'studio' && user.user_role !== 'admin' && user.user_role !== 'moderator' && <Gem className="h-3.5 w-3.5" />}
                      
                      <span className="capitalize">
                        {user.user_role === 'admin' ? 'Admin' :
                         user.user_role === 'moderator' ? 'Modérateur' :
                         user.subscription_tier === 'pro' ? 'Pro' :
                         user.subscription_tier === 'studio' ? 'Studio' :
                         'Free'} {/* Default to Free if not a special role or paid tier */}
                      </span>
                    </div>
                  </div>
                )}
              </div>
              <div className={`flex ${isUiCollapsed ? 'flex-col items-center space-y-1 mt-1' : 'gap-1 mt-1.5'}`}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
<Link
  href="/preferences"
  className={`flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-xs hover:bg-slate-700/70 hover:text-slate-100 text-slate-300 ${isUiCollapsed ? 'justify-center' : ''}`}
  aria-label="Préférences"
>
  <Settings className="h-3.5 w-3.5" />
  {!isUiCollapsed && <span>Préférences</span>}
</Link>
                    </TooltipTrigger>
                    {isUiCollapsed && <TooltipContent side="right">Préférences</TooltipContent>}
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={handleSignOut}
                        className={`flex w-full items-center gap-2 rounded-md px-2 py-1.5 text-xs hover:bg-slate-700/70 hover:text-slate-100 text-slate-300 ${isUiCollapsed ? 'justify-center' : ''}`}
                        aria-label="Déconnexion"
                      >
                        <LogOut className="h-3.5 w-3.5" />
                        {!isUiCollapsed && <span>Déconnexion</span>}
                      </button>
                    </TooltipTrigger>
                    {isUiCollapsed && <TooltipContent side="right">Déconnexion</TooltipContent>}
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          ) : (
            <div className={`flex ${isUiCollapsed ? 'justify-center' : ''}`}>
              <Link
                href="/login"
                className={`rounded-md bg-primary px-3 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90 ${isUiCollapsed ? 'p-2' : ''}`}
              >
                {isUiCollapsed ? <LogOut className="h-5 w-5 transform rotate-180" /> : "Se connecter"}
              </Link>
            </div>
          )}
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
