import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import ImageWithFallback from '@/components/ui/image-with-fallback';
import { ChevronLeft, BarChart3, Settings } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Mes Insights Musicaux',
  description: 'Analysez vos préférences musicales et découvrez des statistiques personnalisées.',
};

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { Badge } from '@/components/ui/badge';

async function getUserFavoriteGenres(userId: string): Promise<string[]> {
  const supabase = createSupabaseServerClient();

  // 1. Fetch liked song_ids
  const { data: likedSongsData, error: likedSongsError } = await supabase
    .from('likes')
    .select('resource_id') // This is the song_id
    .eq('user_id', userId)
    .eq('resource_type', 'song');

  if (likedSongsError) {
    console.error('Error fetching liked songs for insights:', likedSongsError);
    return [];
  }

  if (!likedSongsData || likedSongsData.length === 0) {
    console.log('No liked songs found for user for insights:', userId);
    return []; // No liked songs, so no genres to derive
  }

  const songIds = likedSongsData.map(like => like.resource_id);

  // 2. Fetch genres for these song_ids from the 'songs' table
  // The 'genre' column in 'songs' is TEXT, e.g., "Pop, Rock", "Electronic"
  const { data: songsData, error: songsError } = await supabase
    .from('songs')
    .select('id, genre') // Select 'genre' which is TEXT
    .in('id', songIds);

  if (songsError) {
    console.error('Error fetching song genres for insights:', songsError);
    return [];
  }

  if (!songsData || songsData.length === 0) {
    console.log('No song details found for liked song IDs for insights:', songIds);
    return [];
  }

  // 3. Process genres and count frequencies
  const genreCounts: { [key: string]: number } = {};

  songsData.forEach(song => {
    if (song.genre && typeof song.genre === 'string') {
      let potentialGenres: string[] = [];
      const rawGenreString = song.genre;

      try {
        // Attempt 1: song.genre is a valid JSON string array like "[\"Pop\", \"Rock\"]"
        // Or it might be a JSON string that itself contains a stringified array: "[\"[\\\"Ambient\\\"]\"]"
        let parsed = JSON.parse(rawGenreString);
        
        // Handle cases where parsing results in a string that might be another JSON array
        if (typeof parsed === 'string') {
          try {
            const reparsed = JSON.parse(parsed);
            if (Array.isArray(reparsed)) parsed = reparsed;
          } catch (reparseError) {
            // If reparsing fails, 'parsed' remains the string from the first parse.
          }
        }

        if (Array.isArray(parsed)) {
          potentialGenres = parsed.flat(Infinity).filter(item => typeof item === 'string');
        } else if (typeof parsed === 'string') {
          // Attempt 2: song.genre was like "\"Pop\"", which JSON.parse converts to "Pop"
          // Or it could be a comma-separated list within a single JSON string: "\"Pop, Rock\""
          potentialGenres = parsed.split(',').map(s => s.trim()).filter(s => s);
        }
      } catch (e) {
        // Attempt 3: song.genre is plain text, possibly comma-separated, possibly with brackets
        // e.g., "Pop, Rock", or "[Pop, Rock]", or "Pop"
        let cleanedGenreString = rawGenreString.trim();
        // Remove leading/trailing brackets if present (e.g., from "[Pop, Rock]")
        if (cleanedGenreString.startsWith('[') && cleanedGenreString.endsWith(']')) {
          cleanedGenreString = cleanedGenreString.substring(1, cleanedGenreString.length - 1);
        }
        potentialGenres = cleanedGenreString.split(',').map(s => s.trim()).filter(s => s);
      }

      potentialGenres.forEach(gStr => {
        let processedString = gStr.trim();

        // If the string itself is a JSON array representation, e.g., "[\"ambient\"]" or "[\"Pop\", \"Rock\"]"
        if (processedString.startsWith('[') && processedString.endsWith(']')) {
            try {
                const items = JSON.parse(processedString);
                if (Array.isArray(items)) {
                    items.forEach(item => {
                        if (typeof item === 'string') {
                            let normalizedItem = item.toLowerCase().trim();
                            // Remove potential quotes around the actual genre name, e.g. "\"Ambient\"" -> "ambient"
                            if ((normalizedItem.startsWith('"') && normalizedItem.endsWith('"')) || (normalizedItem.startsWith('\'') && normalizedItem.endsWith('\''))) {
                                normalizedItem = normalizedItem.substring(1, normalizedItem.length - 1);
                            }
                            if (normalizedItem && normalizedItem !== 'null' && normalizedItem !== 'undefined' && normalizedItem !== '') {
                                genreCounts[normalizedItem] = (genreCounts[normalizedItem] || 0) + 1;
                            }
                        }
                    });
                    return; // Done with this gStr, it was processed as an array string
                }
            } catch (e) {
                // Not a valid JSON array string, proceed to treat processedString as a single genre below
            }
        }
        
        // Treat as a single genre string, potentially quoted (e.g., "\"Pop\"")
        let normalized = processedString.toLowerCase();
        if ((normalized.startsWith('"') && normalized.endsWith('"')) || (normalized.startsWith('\'') && normalized.endsWith('\''))) {
            normalized = normalized.substring(1, normalized.length - 1);
        }
        
        if (normalized && normalized !== 'null' && normalized !== 'undefined' && normalized !== '') {
            genreCounts[normalized] = (genreCounts[normalized] || 0) + 1;
        }
      });
    }
  });

  // 4. Sort genres by frequency and return top N (e.g., top 10)
  const sortedGenres = Object.entries(genreCounts)
    .sort(([, countA], [, countB]) => countB - countA)
    .map(([genre]) => genre);

  console.log(`Derived top ${sortedGenres.slice(0, 10).length} genres for user ${userId}:`, sortedGenres.slice(0, 10));
  return sortedGenres.slice(0, 10); // Return top 10 or fewer
}

async function getUserFavoriteArtists(userId: string): Promise<{ id: string; display_name: string | null; username: string | null; avatar_url: string | null; }[]> {
  const supabase = createSupabaseServerClient();

  const { data: likedSongsData, error: likedSongsError } = await supabase
    .from('likes')
    .select('resource_id') // song_id
    .eq('user_id', userId)
    .eq('resource_type', 'song');

  if (likedSongsError || !likedSongsData || likedSongsData.length === 0) {
    console.error('Error fetching liked songs for artists insights:', likedSongsError);
    return [];
  }

  const songIds = likedSongsData.map(like => like.resource_id);

  const { data: songsData, error: songsError } = await supabase
    .from('songs')
    .select('creator_user_id')
    .in('id', songIds);

  if (songsError || !songsData || songsData.length === 0) {
    console.error('Error fetching songs for artists insights:', songsError);
    return [];
  }

  const artistCounts: { [key: string]: number } = {};
  songsData.forEach(song => {
    if (song.creator_user_id) {
      artistCounts[song.creator_user_id] = (artistCounts[song.creator_user_id] || 0) + 1;
    }
  });

  const sortedArtistIds = Object.entries(artistCounts)
    .sort(([, countA], [, countB]) => countB - countA)
    .map(([artistId]) => artistId)
    .slice(0, 5); // Top 5 artists

  if (sortedArtistIds.length === 0) return [];

  const { data: artistsData, error: artistsError } = await supabase
    .from('profiles')
    .select('id, display_name, username, avatar_url')
    .in('id', sortedArtistIds)
    .eq('is_artist', true);
  
  if (artistsError) {
    console.error('Error fetching favorite artists profiles:', artistsError);
    return [];
  }
  // Ensure the order is preserved
  return sortedArtistIds.map(id => artistsData?.find(a => a.id === id)).filter(Boolean) as any;
}

async function getUserMostLikedSongs(userId: string): Promise<any[]> { // Define a proper type later
  const supabase = createSupabaseServerClient();

  const { data: likedSongsData, error: likedSongsError } = await supabase
    .from('likes')
    .select('resource_id') // song_id
    .eq('user_id', userId)
    .eq('resource_type', 'song')
    .order('created_at', { ascending: false })
    .limit(5); // Get 5 most recently liked songs as a proxy for 'most liked'

  if (likedSongsError || !likedSongsData || likedSongsData.length === 0) {
    console.error('Error fetching liked songs for most liked insights:', likedSongsError);
    return [];
  }

  const songIds = likedSongsData.map(like => like.resource_id);

  const { data: songsData, error: songsError } = await supabase
    .from('songs')
    .select('*, profiles!songs_creator_user_id_fkey(id, display_name, username), albums(id, title, cover_url)')
    .in('id', songIds);
  
  if (songsError) {
    console.error('Error fetching song details for most liked insights:', songsError);
    return [];
  }
  // Ensure the order is preserved from likedSongsData
  return songIds.map(id => songsData?.find(s => s.id === id)).filter(Boolean);
}

export default async function InsightsPage() {
  const supabase = createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    // Handle case where user is not authenticated, though route protection should prevent this
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <p>Veuillez vous connecter pour voir vos insights.</p>
        <Button asChild className="mt-4">
          <Link href="/login">Se connecter</Link>
        </Button>
      </div>
    );
  }

  const [favoriteGenres, favoriteArtists, mostLikedSongs] = await Promise.all([
    getUserFavoriteGenres(user.id),
    getUserFavoriteArtists(user.id),
    getUserMostLikedSongs(user.id)
  ]);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex items-center">
        <Button variant="ghost" size="icon" asChild className="mr-2">
          <Link href="/discover">
            <ChevronLeft className="h-6 w-6" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold text-center flex-grow">
          <BarChart3 className="inline-block w-8 h-8 mr-2 text-primary" />
          Vos Insights Musicaux
        </h1>
        <Button variant="ghost" size="icon" asChild>
          <Link href="/insights/configure"> {/* Placeholder link for future config page */}
            <Settings className="w-6 h-6" />
            <span className="sr-only">Configurer les Insights</span>
          </Link>
        </Button>
      </div>
      
      {favoriteGenres.length > 0 ? (
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-center">Vos Genres de Prédilection</h2>
          <div className="flex flex-wrap gap-3 justify-center">
            {favoriteGenres.map((genre: string) => (
              <Link key={genre} href={`/genres/${encodeURIComponent(genre.toLowerCase().replace(/\s+/g, '-'))}`} passHref>
                <Badge 
                  variant="secondary"
                  className="px-4 py-2 text-md cursor-pointer hover:bg-primary/20 transition-colors duration-150 ease-in-out transform hover:scale-105 active:scale-95"
                >
                  {genre}
                </Badge>
              </Link>
            ))}
          </div>
          <p className="text-center mt-6 text-muted-foreground">
            Cliquez sur un genre pour explorer plus de contenu.
          </p>
        </section>
      ) : (
        <div className="text-center py-10">
          <p className="text-xl text-muted-foreground mb-4">
            Nous n'avons pas encore assez d'informations pour afficher vos genres de prédilection.
          </p>
          <p className="mb-8">
            Interagissez davantage avec l'application pour des insights personnalisés !
          </p>
        </div>
      )}

      {/* Favorite Artists Section */}
      {favoriteArtists.length > 0 && (
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-center">Vos Artistes Favoris</h2>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 justify-center">
            {favoriteArtists.map((artist: any) => (
              <Link key={artist.id} href={`/artists/${artist.username || artist.id}`} className="block group">
                <div className="flex flex-col items-center p-3 rounded-lg hover:bg-muted/50 transition-colors">
                  <ImageWithFallback 
                    src={artist.avatar_url}
                    fallbackSrc='/images/placeholder-song.svg'
                    alt={artist.display_name || artist.username || 'Artiste'} 
                    className="w-24 h-24 rounded-full object-cover mb-2 shadow-md group-hover:scale-105 transition-transform"
                  />
                  <p className="text-sm font-medium text-center truncate w-full group-hover:text-primary">{artist.display_name || artist.username}</p>
                </div>
              </Link>
            ))}
          </div>
        </section>
      )}

      {/* Most Liked Songs Section */}
      {mostLikedSongs.length > 0 && (
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-center">Vos Morceaux Récemment Aimés</h2>
          <div className="space-y-4">
            {mostLikedSongs.map((song: any, index: number) => (
              <Link key={song.id} href={`/songs/${song.slug || song.id}`} className="block group">
                <div className="flex items-center p-3 rounded-lg hover:bg-muted/50 transition-colors border">
                  <span className="text-lg font-medium mr-4 text-muted-foreground">{index + 1}.</span>
                  <ImageWithFallback 
                    src={song.cover_art_url || song.albums?.cover_url}
                    fallbackSrc='/images/placeholder-song.svg'
                    alt={song.title || 'Morceau'} 
                    className="w-12 h-12 rounded-md object-cover mr-4 shadow-sm group-hover:opacity-80 transition-opacity"
                  />
                  <div className="flex-grow">
                    <p className="font-semibold truncate group-hover:text-primary">{song.title}</p>
                    <p className="text-sm text-muted-foreground truncate">{song.profiles?.display_name || song.profiles?.username || 'Artiste inconnu'}</p>
                  </div>
                  {/* Optional: Add play count or like date if available */}
                </div>
              </Link>
            ))}
          </div>
        </section>
      )}

      {/* Fallback if no insights at all */}
      {favoriteGenres.length === 0 && favoriteArtists.length === 0 && mostLikedSongs.length === 0 && (
        <div className="text-center py-10">
          <p className="text-xl text-muted-foreground mb-4">
            Commencez à explorer et à interagir avec la musique pour découvrir vos insights personnalisés ici !
          </p>
          <Button asChild className="mt-4">
            <Link href="/discover">Découvrir de la musique</Link>
          </Button>
        </div>
      )}

      {/* Original fallback for no genres, keep for now or integrate into the one above */}
      {favoriteGenres.length > 0 ? (
        <section className="mb-12">
          {/* This section is already defined above for genres, this is a placeholder to ensure the replacement target is unique and correctly placed */}
        </section>
      ) : (
        <div className="text-center py-10">
          <p className="text-xl text-muted-foreground mb-4">
            Il semble que nous n'ayons pas encore assez d'informations pour afficher vos genres de prédilection.
          </p>
          <p className="mb-8">
            Écoutez plus de musique et interagissez avec l'application pour que nous puissions personnaliser vos insights !
          </p>
        </div>
      )}

      <div className="text-center py-10 border-t mt-12 pt-12">
        {(favoriteGenres.length > 0 || favoriteArtists.length > 0 || mostLikedSongs.length > 0) && (
          <p className="text-muted-foreground mb-6">Ceci n'est qu'un début, continuez d'explorer pour affiner vos insights !</p>
        )}

        <p className="text-xl text-muted-foreground mb-4">
          Plus d'analyses à venir !
        </p>
        <p className="mb-8">
          Nous travaillons à vous apporter des statistiques détaillées sur vos artistes, albums et morceaux favoris.
        </p>
        <Button asChild>
          <Link href="/discover">
            Retourner à la Découverte
          </Link>
        </Button>
      </div>
    </div>
  );
}
