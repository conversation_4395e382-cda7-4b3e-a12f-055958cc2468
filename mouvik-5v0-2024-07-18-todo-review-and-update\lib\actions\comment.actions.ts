"use server";

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';

/**
 * Interface defining the structure of application settings.
 * Extend this interface as more settings are added.
 */
interface AppSettings {
  comments_per_page?: number;
  // Add other settings here as needed
}

/**
 * Retrieves a specific application setting value by its key.
 * @param key The key of the setting to retrieve.
 * @returns The string value of the setting, or null if not found or an error occurs.
 */
export async function getAppSetting(key: keyof AppSettings): Promise<string | null> {
  const supabase = await createSupabaseServerClient();
  const { data, error } = await supabase
    .from('app_settings')
    .select('value')
    .eq('key', key)
    .single();

  if (error) {
    console.error(`Error fetching app setting ${key}:`, error.message);
    return null;
  }
  return data?.value || null;
}

/**
 * Updates (or creates if it doesn't exist) a specific application setting.
 * @param key The key of the setting to update.
 * @param value The new string value for the setting.
 * @returns An object indicating success or failure, with an error message if applicable.
 */
export async function updateAppSetting(key: keyof AppSettings, value: string): Promise<{ success: boolean; error?: string }> {
  const supabase = await createSupabaseServerClient();

  // Server-side validation for 'comments_per_page'
  if (key === 'comments_per_page') {
    const numValue = parseInt(value, 10);
    if (isNaN(numValue) || numValue <= 0) {
      const errorMessage = "La valeur pour 'comments_per_page' doit être un nombre entier positif.";
      console.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  }
  
  const { error } = await supabase
    .from('app_settings')
    .upsert({ key, value }, { onConflict: 'key' });

  if (error) {
    console.error(`Error updating app setting ${key}:`, error.message);
    return { success: false, error: error.message };
  }

  // Revalidate paths that might depend on this setting, if necessary.
  // For comments_per_page, revalidating all comment sections might be too broad.
  // Consider more targeted revalidation if performance becomes an issue.
  // For now, let's assume components will re-fetch or re-render as needed.
  // Example: revalidatePath('/admin/settings'); 
  // revalidatePath('/album/[slug]'); // This would revalidate all album pages
  // Consider more targeted revalidation based on the key if necessary.
  if (key === 'comments_per_page') {
    // Revalidating all pages that use comments might be too much.
    // This depends on how critical it is for the change to be immediately visible everywhere.
    // For now, components will refetch on their own or on next load.
  }

  return { success: true };
}

/**
 * Retrieves the 'comments_per_page' setting with a default fallback.
 * @returns The configured number of comments per page, or a default value (10).
 */
export async function getCommentsPerPageSetting(): Promise<number> {
  const valueStr = await getAppSetting('comments_per_page');
  const defaultValue = 10; // Default if not set or invalid
  if (valueStr) {
    const parsedValue = parseInt(valueStr, 10);
    if (!isNaN(parsedValue) && parsedValue > 0) {
      return parsedValue;
    }
  }
  return defaultValue;
}
