/**
 * 🎼 EXEMPLE D'INTÉGRATION AI COMPOSER
 * 
 * Exemple complet d'intégration du système d'accords
 * dans l'écosystème AI Composer
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useCallback, useRef } from 'react';
import { FileText, Grid3X3, Clock, Music, Sparkles, Save } from 'lucide-react';
import { 
  ChordWorkflowIntegration, 
  useChordWorkflowIntegration,
  ChordSystemTrigger 
} from '../integration/ChordWorkflowIntegration';
import type { 
  UnifiedChordPosition, 
  ChordProgression,
  ChordGridSection 
} from '../types/chord-system';
import type { 
  IntegrationCallbacks,
  SongSectionType 
} from '../integration/ChordIntegrationManager';

// ============================================================================
// SIMULATION DES COMPOSANTS AI COMPOSER EXISTANTS
// ============================================================================

/**
 * Simulation de l'éditeur de texte AI Composer
 */
const MockTextEditor: React.FC<{
  content: string;
  onContentChange: (content: string) => void;
  cursorPosition: number;
  onCursorPositionChange: (position: number) => void;
}> = ({ content, onContentChange, cursorPosition, onCursorPositionChange }) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSelectionChange = () => {
    if (textareaRef.current) {
      onCursorPositionChange(textareaRef.current.selectionStart);
    }
  };

  return (
    <div className="border border-gray-300 rounded-lg">
      <div className="bg-gray-50 px-3 py-2 border-b border-gray-300 flex items-center justify-between">
        <h3 className="font-medium text-gray-900 flex items-center">
          <FileText className="w-4 h-4 mr-2" />
          Éditeur de Texte AI Composer
        </h3>
        <span className="text-sm text-gray-500">Position: {cursorPosition}</span>
      </div>
      <textarea
        ref={textareaRef}
        value={content}
        onChange={(e) => onContentChange(e.target.value)}
        onSelect={handleSelectionChange}
        onKeyUp={handleSelectionChange}
        onClick={handleSelectionChange}
        className="w-full h-40 p-3 resize-none focus:outline-none"
        placeholder="Tapez vos paroles ici... Les accords seront insérés à la position du curseur."
      />
    </div>
  );
};

/**
 * Simulation de la structure de morceau AI Composer
 */
const MockSongStructure: React.FC<{
  sections: ChordGridSection[];
  onSectionsChange: (sections: ChordGridSection[]) => void;
}> = ({ sections, onSectionsChange }) => {
  return (
    <div className="border border-gray-300 rounded-lg">
      <div className="bg-gray-50 px-3 py-2 border-b border-gray-300">
        <h3 className="font-medium text-gray-900 flex items-center">
          <Grid3X3 className="w-4 h-4 mr-2" />
          Structure du Morceau
        </h3>
      </div>
      <div className="p-3">
        {sections.length === 0 ? (
          <p className="text-gray-500 text-center py-4">
            Aucune section. Utilisez le système d'accords pour en ajouter.
          </p>
        ) : (
          <div className="space-y-2">
            {sections.map((section, index) => (
              <div key={section.id} className="p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-blue-900">{section.name}</h4>
                  <span className="text-sm text-blue-600">
                    {section.measures.length} mesure{section.measures.length !== 1 ? 's' : ''}
                  </span>
                </div>
                <div className="mt-2 text-sm text-blue-700">
                  Tonalité: {section.key} | Tempo: {section.tempo} BPM | Mesure: {section.timeSignature}
                </div>
                {section.measures.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {section.measures.map((measure) => 
                      measure.chords.map((chordPlacement, chordIndex) => (
                        <span 
                          key={`${measure.id}-${chordIndex}`}
                          className="px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded"
                        >
                          {chordPlacement.chord.chord}
                        </span>
                      ))
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Simulation de la timeline AI Composer
 */
const MockTimeline: React.FC<{
  progressions: ChordProgression[];
  onProgressionsChange: (progressions: ChordProgression[]) => void;
}> = ({ progressions, onProgressionsChange }) => {
  return (
    <div className="border border-gray-300 rounded-lg">
      <div className="bg-gray-50 px-3 py-2 border-b border-gray-300">
        <h3 className="font-medium text-gray-900 flex items-center">
          <Clock className="w-4 h-4 mr-2" />
          Timeline AI Composer
        </h3>
      </div>
      <div className="p-3">
        {progressions.length === 0 ? (
          <p className="text-gray-500 text-center py-4">
            Aucune progression sur la timeline. Synchronisez depuis le système d'accords.
          </p>
        ) : (
          <div className="space-y-2">
            {progressions.map((progression, index) => (
              <div key={progression.id} className="p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-purple-900">{progression.name}</h4>
                  <span className="text-sm text-purple-600">
                    {progression.chords.length} accord{progression.chords.length !== 1 ? 's' : ''}
                  </span>
                </div>
                <div className="mt-2 text-sm text-purple-700">
                  Tonalité: {progression.key} | Tempo: {progression.tempo} BPM
                </div>
                <div className="mt-2 flex flex-wrap gap-1">
                  {progression.chords.map((chord, chordIndex) => (
                    <span 
                      key={`${progression.id}-${chordIndex}`}
                      className="px-2 py-1 bg-purple-200 text-purple-800 text-xs rounded"
                    >
                      {chord.chord}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL D'EXEMPLE
// ============================================================================

export const AIComposerIntegrationExample: React.FC = () => {
  // État de l'application AI Composer simulée
  const [textContent, setTextContent] = useState('Voici mes paroles...\n\nCouplet 1:\nJe marche sous la pluie\n\nRefrain:\nEt je chante ma vie\n');
  const [cursorPosition, setCursorPosition] = useState(0);
  const [songSections, setSongSections] = useState<ChordGridSection[]>([]);
  const [timelineProgressions, setTimelineProgressions] = useState<ChordProgression[]>([]);
  const [integrationMode, setIntegrationMode] = useState<'text-editor' | 'song-structure' | 'timeline'>('text-editor');

  // Hook d'intégration
  const { 
    isVisible, 
    openChordSystem, 
    closeChordSystem,
    openChordPicker,
    openProgressionBuilder 
  } = useChordWorkflowIntegration(integrationMode);

  // ============================================================================
  // CALLBACKS D'INTÉGRATION
  // ============================================================================

  const integrationCallbacks: IntegrationCallbacks = {
    // Éditeur de texte
    onInsertToTextEditor: useCallback((text: string, position?: number) => {
      const insertPos = position !== undefined ? position : cursorPosition;
      const newContent = 
        textContent.slice(0, insertPos) + 
        text + 
        textContent.slice(insertPos);
      
      setTextContent(newContent);
      setCursorPosition(insertPos + text.length);
      
      // Notification visuelle
      console.log(`✅ Accord inséré dans l'éditeur: "${text}" à la position ${insertPos}`);
    }, [textContent, cursorPosition]),

    onReplaceTextSelection: useCallback((text: string) => {
      // Simulation de remplacement de sélection
      setTextContent(prev => prev + '\n' + text);
      console.log(`✅ Sélection remplacée par: "${text}"`);
    }, []),

    onGetTextEditorContent: useCallback(() => textContent, [textContent]),
    onGetTextEditorPosition: useCallback(() => cursorPosition, [cursorPosition]),

    // Structure de morceau
    onAddToSongStructure: useCallback((section: ChordGridSection, sectionType: SongSectionType) => {
      const newSection = {
        ...section,
        name: `${sectionType.charAt(0).toUpperCase() + sectionType.slice(1)} - ${section.name}`,
        order: songSections.length
      };
      
      setSongSections(prev => [...prev, newSection]);
      console.log(`✅ Section ajoutée à la structure: "${newSection.name}"`);
    }, [songSections.length]),

    onUpdateSongSection: useCallback((sectionId: string, updates: Partial<ChordGridSection>) => {
      setSongSections(prev => 
        prev.map(section => 
          section.id === sectionId 
            ? { ...section, ...updates }
            : section
        )
      );
      console.log(`✅ Section mise à jour: ${sectionId}`);
    }, []),

    onGetSongStructure: useCallback(() => songSections, [songSections]),
    onGetActiveSongSection: useCallback(() => null, []),

    // Timeline
    onSyncWithTimeline: useCallback((progression: ChordProgression, config) => {
      const timelineProgression = {
        ...progression,
        name: `Timeline - ${progression.name}`,
        id: crypto.randomUUID()
      };
      
      setTimelineProgressions(prev => [...prev, timelineProgression]);
      console.log(`✅ Progression synchronisée avec la timeline: "${timelineProgression.name}"`);
    }, []),

    onGetTimelinePosition: useCallback(() => 0, []),
    onSetTimelineMarkers: useCallback((markers) => {
      console.log(`✅ ${markers.length} marqueurs ajoutés à la timeline`);
    }, []),

    // Suggestions IA
    onRequestAISuggestions: useCallback(async (context) => {
      console.log('🤖 Demande de suggestions IA:', context);
      // Simulation de suggestions IA
      return [];
    }, []),

    onApplyAISuggestion: useCallback((chord: UnifiedChordPosition) => {
      console.log(`🤖 Suggestion IA appliquée: ${chord.chord}`);
    }, [])
  };

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* En-tête */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          🎼 Exemple d'Intégration AI Composer
        </h1>
        <p className="text-gray-600">
          Démonstration complète de l'intégration du système d'accords dans l'écosystème AI Composer
        </p>
      </div>

      {/* Contrôles d'intégration */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Contrôles d'Intégration</h2>
        
        <div className="flex flex-wrap gap-4 items-center">
          {/* Mode d'intégration */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Mode:</label>
            <select
              value={integrationMode}
              onChange={(e) => setIntegrationMode(e.target.value as any)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
            >
              <option value="text-editor">Éditeur de Texte</option>
              <option value="song-structure">Structure de Morceau</option>
              <option value="timeline">Timeline</option>
            </select>
          </div>

          {/* Boutons d'action */}
          <ChordSystemTrigger variant="button" onClick={openChordSystem} />
          <ChordSystemTrigger variant="icon" onClick={openChordPicker} />
          
          <button
            onClick={openProgressionBuilder}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            Progression Builder
          </button>

          <button
            onClick={() => {
              setTextContent('');
              setSongSections([]);
              setTimelineProgressions([]);
            }}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Reset
          </button>
        </div>
      </div>

      {/* Interface AI Composer simulée */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Colonne gauche */}
        <div className="space-y-6">
          {/* Éditeur de texte */}
          <MockTextEditor
            content={textContent}
            onContentChange={setTextContent}
            cursorPosition={cursorPosition}
            onCursorPositionChange={setCursorPosition}
          />

          {/* Structure de morceau */}
          <MockSongStructure
            sections={songSections}
            onSectionsChange={setSongSections}
          />
        </div>

        {/* Colonne droite */}
        <div className="space-y-6">
          {/* Timeline */}
          <MockTimeline
            progressions={timelineProgressions}
            onProgressionsChange={setTimelineProgressions}
          />

          {/* Informations d'intégration */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 mb-2 flex items-center">
              <Sparkles className="w-4 h-4 mr-2" />
              État de l'Intégration
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-700">Mode actuel:</span>
                <span className="font-medium text-blue-900 capitalize">{integrationMode}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">Position curseur:</span>
                <span className="font-medium text-blue-900">{cursorPosition}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">Sections créées:</span>
                <span className="font-medium text-blue-900">{songSections.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">Progressions timeline:</span>
                <span className="font-medium text-blue-900">{timelineProgressions.length}</span>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">Instructions</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Cliquez sur "Accords" pour ouvrir le système complet</li>
              <li>• Utilisez l'icône 🎵 pour la sélection rapide</li>
              <li>• Changez le mode pour tester différentes intégrations</li>
              <li>• Les accords s'insèrent à la position du curseur</li>
              <li>• Raccourcis: Ctrl+Shift+C (picker), Ctrl+Shift+P (progression)</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Système d'accords intégré */}
      <ChordWorkflowIntegration
        forcedMode={integrationMode}
        aiComposerCallbacks={integrationCallbacks}
        initialDisplay="minimized"
        position="bottom-right"
        showIntegrationControls={true}
      />
    </div>
  );
};
