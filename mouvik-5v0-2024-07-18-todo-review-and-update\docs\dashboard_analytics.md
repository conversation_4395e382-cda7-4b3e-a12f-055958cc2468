# Dashboard Analytics Enhancement

Ce document détaille les améliorations prévues pour le tableau de bord analytique de Mouvik, en se concentrant sur l'ajout de visualisations de données avancées et l'intégration de métriques supplémentaires pour offrir une expérience utilisateur plus complète.

## Objectifs

- Fournir une vue d'ensemble plus détaillée des performances de l'artiste
- Intégrer des visualisations de données interactives et informatives
- Centraliser l'accès aux métriques clés dans une interface intuitive
- Offrir des insights actionnables basés sur les données collectées

## Structure du Dashboard Amélioré

Le dashboard amélioré sera organisé en sections thématiques pour une meilleure lisibilité et navigation:

### 1. Vue d'Ensemble (Overview)

**Composants:**
- **Carte de Profil Améliorée**: Affichage des métriques clés de l'utilisateur avec tendances
- **KPIs Principaux**: Morceaux, Albums, Écoutes, Vues, Abonnés, Likes, Commentaires
- **Graphique d'Activité**: Visualisation multi-métrique sur une timeline (écoutes, vues, likes)
- **Résumé de Performance**: Comparaison avec la période précédente (% de croissance)

### 2. Analyse d'Audience

**Composants:**
- **Carte de Chaleur Géographique**: Distribution des auditeurs par région
- **Démographie des Auditeurs**: Répartition par âge, genre (si disponible)
- **Sources d'Écoute**: Plateformes, appareils, canaux de découverte
- **Comportement d'Écoute**: Heures de pic d'écoute, durée moyenne d'écoute

### 3. Analyse de Contenu

**Composants:**
- **Top Morceaux**: Classement des morceaux les plus performants avec métriques détaillées
- **Performance par Genre**: Répartition des écoutes/likes par genre musical
- **Analyse des Caractéristiques Musicales**: Visualisation des attributs musicaux (tempo, énergie, etc.)
- **Taux de Complétion**: Pourcentage moyen d'écoute complète par morceau

### 4. Engagement Social

**Composants:**
- **Activité des Commentaires**: Volume et sentiment des commentaires
- **Analyse des Likes**: Patterns de likes par contenu et période
- **Réseau d'Influence**: Visualisation des connections entre followers/following
- **Partages et Mentions**: Tracking des partages sur plateformes externes

## Sources de Données

Les données pour alimenter ces visualisations proviendront de plusieurs tables et fonctions:

1. **Tables Principales**:
   - `profiles`: Données utilisateur
   - `songs`: Catalogue de morceaux
   - `albums`: Catalogue d'albums
   - `plays`: Enregistrements d'écoutes
   - `views`: Vues de pages
   - `likes`: Likes sur le contenu
   - `comments`: Commentaires
   - `follows`: Relations entre utilisateurs

2. **Fonctions RPC Existantes**:
   - `get_dashboard_data`: Données agrégées pour le dashboard
   - `get_user_overview_stats`: Statistiques d'aperçu utilisateur
   - `get_activity_timeline`: Timeline d'activité

3. **Nouvelles Fonctions RPC à Créer**:
   - `get_audience_demographics`: Données démographiques des auditeurs
   - `get_content_performance_analysis`: Analyse détaillée des performances par contenu
   - `get_engagement_metrics`: Métriques d'engagement social détaillées
   - `get_geographic_distribution`: Distribution géographique des auditeurs

## Composants UI à Développer

Nouveaux composants à créer dans le dossier `components/stats/`:

1. **Visualisations Générales**:
   - `EnhancedProfileCard.tsx`: Version améliorée de la carte de profil
   - `MultiMetricTimeline.tsx`: Graphique timeline avec plusieurs métriques
   - `PerformanceSummary.tsx`: Résumé des performances avec comparaisons

2. **Visualisations d'Audience**:
   - `GeoHeatMap.tsx`: Carte de chaleur géographique
   - `DemographicCharts.tsx`: Graphiques démographiques
   - `ListeningSources.tsx`: Visualisation des sources d'écoute
   - `ListeningBehavior.tsx`: Patterns de comportement d'écoute

3. **Visualisations de Contenu**:
   - `TopContentTable.tsx`: Tableau interactif du contenu le plus performant
   - `GenrePerformance.tsx`: Analyse de performance par genre
   - `MusicAttributesRadar.tsx`: Graphique radar des attributs musicaux
   - `CompletionRateChart.tsx`: Visualisation des taux de complétion

4. **Visualisations d'Engagement**:
   - `CommentActivity.tsx`: Activité et sentiment des commentaires
   - `LikeAnalysis.tsx`: Analyse des patterns de likes
   - `InfluenceNetwork.tsx`: Graphique réseau d'influence
   - `SharingMetrics.tsx`: Métriques de partage

## Bibliothèques Recommandées

Pour implémenter ces visualisations, nous recommandons:

- **Recharts** (https://recharts.org/):
  - Pour les graphiques de base (lignes, barres, aires)
  - Déjà utilisé dans le projet pour `LineChart`
  - Facile à intégrer avec des données dynamiques
  - Exemples: `LineChart`, `BarChart`, `AreaChart`, `PieChart`, `ComposedChart`

- **Nivo** (https://nivo.rocks/):
  - Pour les visualisations avancées (cartes de chaleur, graphiques réseau)
  - Offre des visualisations riches et interactives
  - Bonne documentation et exemples
  - Exemples: `HeatMap`, `Network`, `Radar`, `Sankey`, `Stream`

- **react-simple-maps** (https://www.react-simple-maps.io/):
  - Pour les visualisations géographiques
  - Permet de créer des cartes interactives
  - Peut être combiné avec d3-geo pour des projections avancées
  - Exemples: `ComposableMap`, `Geographies`, `Markers`

- **d3.js** (https://d3js.org/):
  - Pour les visualisations personnalisées complexes
  - Bibliothèque puissante pour la manipulation de données et la création de visualisations
  - À utiliser pour les cas spécifiques non couverts par les autres bibliothèques
  - Exemples: Visualisations personnalisées, animations complexes

- **shadcn/ui** (https://ui.shadcn.com/):
  - Pour les composants d'interface utilisateur
  - Déjà utilisé dans le projet
  - Offre des composants cohérents avec le design système
  - Exemples: `Card`, `Tabs`, `Select`, `Button`, `Dialog`

## Plan d'Implémentation

1. **Phase 1: Préparation des Données**
   - Créer/modifier les fonctions RPC Supabase nécessaires
   - Mettre en place les endpoints d'API pour récupérer les données agrégées
   - Tester la performance et l'exactitude des requêtes

2. **Phase 2: Développement des Composants UI**
   - Implémenter les composants de visualisation par section
   - Créer des versions squelettes pour le chargement progressif
   - Tester la réactivité et l'accessibilité

3. **Phase 3: Intégration au Dashboard**
   - Refactoriser la page dashboard pour intégrer les nouvelles sections
   - Implémenter la navigation entre les différentes vues
   - Optimiser les performances de chargement

4. **Phase 4: Tests et Optimisation**
   - Tester avec différents volumes de données
   - Optimiser les requêtes et le rendu des composants
   - Recueillir les retours utilisateurs et itérer

## Prochaines Étapes

1. Valider cette spécification avec l'équipe
2. Prioriser les composants à développer en premier
3. Commencer par les fonctions RPC et la préparation des données
4. Développer un prototype de la nouvelle interface

## Priorités de Développement

Voici une proposition de priorisation des fonctionnalités à développer, basée sur la valeur ajoutée pour l'utilisateur et la complexité technique:

### Priorité Haute (À implémenter en premier)

1. **Centralisation de la Récupération des Données**
   - Amélioration de la fonction RPC `get_dashboard_data` existante
   - Impact: Améliore les performances et simplifie le code frontend

2. **Amélioration des Informations d'Activité Récente**
   - Derniers Commentaires Reçus (Amélioré)
   - Derniers Projets Travaillés/Modifiés
   - Nouveaux Followers Récents
   - Impact: Donne immédiatement plus de contexte et de valeur au dashboard

3. **Graphique d'Activité Multi-Métrique**
   - Remplacer les graphiques actuels par une visualisation plus riche
   - Impact: Améliore significativement la compréhension des tendances

### Priorité Moyenne

1. **Statistiques d'Engagement Détaillées**
   - Ajout de mini-stats aux listes de morceaux/albums récents
   - Impact: Enrichit l'information déjà présente

2. **Top Morceaux avec Métriques Détaillées**
   - Tableau interactif des morceaux les plus performants
   - Impact: Aide l'artiste à identifier ses contenus à succès

3. **"Votre Morceau en Or"**
   - Suggestion basée sur l'engagement
   - Impact: Offre un insight actionnable immédiat

### Priorité Basse (Fonctionnalités avancées)

1. **Analyse d'Audience**
   - Carte de Chaleur Géographique
   - Démographie des Auditeurs
   - Impact: Visualisations avancées nécessitant plus de données

2. **Réseau d'Influence**
   - Visualisation des connections entre followers/following
   - Impact: Fonctionnalité avancée avec valeur ajoutée pour les artistes établis

3. **Analyse des Caractéristiques Musicales**
   - Visualisation des attributs musicaux
   - Impact: Nécessite des données d'analyse audio avancées

## Plan d'Action Immédiat (Prochains Sprints)

1. **Sprint 1: Préparation de l'Infrastructure**
   - Améliorer la fonction RPC `get_dashboard_data`
   - Créer les tables de résumé nécessaires
   - Mettre en place les mécanismes de mise à jour périodique

2. **Sprint 2: Améliorations de Base du Dashboard**
   - Implémenter les composants d'activité récente prioritaires
   - Refactoriser la page dashboard pour une meilleure structure
   - Ajouter les indicateurs de chargement par section

3. **Sprint 3: Visualisations Améliorées**
   - Développer le graphique d'activité multi-métrique
   - Implémenter les statistiques d'engagement détaillées
   - Créer le composant "Votre Morceau en Or"

## Annexes

### Exemple de Structure de Données pour les Visualisations

```typescript
// Exemple pour MultiMetricTimeline
interface TimelineDataPoint {
  date: string;
  plays: number;
  views: number;
  likes: number;
  comments: number;
}

// Exemple pour GeoHeatMap
interface GeoDistributionData {
  country: string;
  region: string;
  count: number;
  percentage: number;
}

// Exemple pour MusicAttributesRadar
interface MusicAttributes {
  songId: string;
  songName: string;
  tempo: number;
  energy: number;
  danceability: number;
  acousticness: number;
  instrumentalness: number;
  liveness: number;
}
```
