/**
 * 🎼 VALIDATION DES DONNÉES D'ACCORDS
 * 
 * Utilitaires de validation pour le système d'accords unifié
 * Assure la cohérence et la qualité des données
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

import type {
  UnifiedChordPosition,
  ChordJsonDefinition,
  ChordJsonPosition,
  InstrumentType,
  DifficultyLevel,
  BarrePosition
} from '../types/chord-system';

// ============================================================================
// VALIDATEURS DE BASE
// ============================================================================

/**
 * Valide si une valeur est un instrument supporté
 */
export function isValidInstrument(value: unknown): value is InstrumentType {
  const validInstruments: InstrumentType[] = [
    'guitar', 'piano', 'ukulele', 'mandolin', 'banjo', 'bass'
  ];
  return typeof value === 'string' && validInstruments.includes(value as InstrumentType);
}

/**
 * Valide si une valeur est un niveau de difficulté valide
 */
export function isValidDifficulty(value: unknown): value is DifficultyLevel {
  const validDifficulties: DifficultyLevel[] = ['beginner', 'intermediate', 'advanced'];
  return typeof value === 'string' && validDifficulties.includes(value as DifficultyLevel);
}

/**
 * Valide si une valeur est un tableau de frettes valide
 */
export function isValidFrets(value: unknown): value is (string | number)[] {
  if (!Array.isArray(value)) return false;
  
  return value.every(fret => {
    if (typeof fret === 'number') {
      return Number.isInteger(fret) && fret >= -1 && fret <= 24;
    }
    if (typeof fret === 'string') {
      return fret === 'x' || fret === 'X' || !isNaN(Number(fret));
    }
    return false;
  });
}

/**
 * Valide si une valeur est un tableau de doigtés valide
 */
export function isValidFingers(value: unknown): value is number[] {
  if (!Array.isArray(value)) return false;
  
  return value.every(finger => 
    typeof finger === 'number' && 
    Number.isInteger(finger) && 
    finger >= 0 && 
    finger <= 4
  );
}

/**
 * Valide si une valeur est un tableau de notes MIDI valide
 */
export function isValidMidiNotes(value: unknown): value is number[] {
  if (!Array.isArray(value)) return false;
  
  return value.every(note => 
    typeof note === 'number' && 
    Number.isInteger(note) && 
    note >= 0 && 
    note <= 127
  );
}

/**
 * Valide si une valeur est une position de barré valide
 */
export function isValidBarrePosition(value: unknown): value is BarrePosition {
  if (!value || typeof value !== 'object') return false;
  
  const barre = value as any;
  return (
    typeof barre.fret === 'number' &&
    typeof barre.fromString === 'number' &&
    typeof barre.toString === 'number' &&
    Number.isInteger(barre.fret) &&
    Number.isInteger(barre.fromString) &&
    Number.isInteger(barre.toString) &&
    barre.fret >= 1 && barre.fret <= 24 &&
    barre.fromString >= 1 && barre.fromString <= 6 &&
    barre.toString >= 1 && barre.toString <= 6 &&
    barre.fromString <= barre.toString
  );
}

// ============================================================================
// VALIDATEURS DE STRUCTURES COMPLEXES
// ============================================================================

/**
 * Valide une position d'accord JSON
 */
export function isValidChordJsonPosition(value: unknown): value is ChordJsonPosition {
  if (!value || typeof value !== 'object') return false;
  
  const position = value as any;
  
  // Validation des champs obligatoires
  if (!isValidFrets(position.frets)) return false;
  
  // Validation des champs optionnels
  if (position.fingers !== undefined && !isValidFingers(position.fingers)) return false;
  if (position.midi !== undefined && !isValidMidiNotes(position.midi)) return false;
  if (position.difficulty !== undefined && !isValidDifficulty(position.difficulty)) return false;
  
  if (position.baseFret !== undefined) {
    if (typeof position.baseFret !== 'number' || 
        !Number.isInteger(position.baseFret) || 
        position.baseFret < 1 || 
        position.baseFret > 24) {
      return false;
    }
  }
  
  if (position.barres !== undefined) {
    if (!Array.isArray(position.barres)) return false;
    if (!position.barres.every(isValidBarrePosition)) return false;
  }
  
  return true;
}

/**
 * Valide une définition JSON complète d'instrument
 */
export function isValidChordJsonDefinition(value: unknown): value is ChordJsonDefinition {
  if (!value || typeof value !== 'object') return false;
  
  const definition = value as any;
  
  // Validation des champs obligatoires
  if (typeof definition.instrument !== 'string') return false;
  if (!Array.isArray(definition.tuning)) return false;
  if (typeof definition.strings !== 'number' || !Number.isInteger(definition.strings)) return false;
  if (!Array.isArray(definition.keys)) return false;
  if (!Array.isArray(definition.suffixes)) return false;
  if (!definition.chords || typeof definition.chords !== 'object') return false;
  
  // Validation du tuning
  if (!definition.tuning.every((note: unknown) => typeof note === 'string')) return false;
  
  // Validation des clés et suffixes
  if (!definition.keys.every((key: unknown) => typeof key === 'string')) return false;
  if (!definition.suffixes.every((suffix: unknown) => typeof suffix === 'string')) return false;
  
  // Validation des accords
  for (const [key, variations] of Object.entries(definition.chords)) {
    if (typeof key !== 'string') return false;
    if (!Array.isArray(variations)) return false;
    
    for (const variation of variations as any[]) {
      if (!variation || typeof variation !== 'object') return false;
      if (typeof variation.suffix !== 'string') return false;
      if (typeof variation.name !== 'string') return false;
      if (!Array.isArray(variation.positions)) return false;
      if (!variation.positions.every(isValidChordJsonPosition)) return false;
    }
  }
  
  return true;
}

/**
 * Valide un accord unifié
 */
export function isValidUnifiedChord(value: unknown): value is UnifiedChordPosition {
  if (!value || typeof value !== 'object') return false;
  
  const chord = value as any;
  
  // Validation des champs obligatoires
  if (typeof chord.id !== 'string' || chord.id.length === 0) return false;
  if (typeof chord.chord !== 'string' || chord.chord.length === 0) return false;
  if (!isValidInstrument(chord.instrument)) return false;
  if (typeof chord.tuning !== 'string' || chord.tuning.length === 0) return false;
  if (!isValidFrets(chord.frets)) return false;
  if (typeof chord.baseFret !== 'number' || !Number.isInteger(chord.baseFret) || chord.baseFret < 1) return false;
  if (!isValidDifficulty(chord.difficulty)) return false;
  
  // Validation des champs optionnels
  if (chord.fingers !== undefined && !isValidFingers(chord.fingers)) return false;
  if (chord.midi !== undefined && !isValidMidiNotes(chord.midi)) return false;
  if (chord.notes !== undefined) {
    if (!Array.isArray(chord.notes) || !chord.notes.every((note: unknown) => typeof note === 'string')) {
      return false;
    }
  }
  if (chord.barres !== undefined) {
    if (!Array.isArray(chord.barres) || !chord.barres.every(isValidBarrePosition)) {
      return false;
    }
  }
  if (chord.tags !== undefined) {
    if (!Array.isArray(chord.tags) || !chord.tags.every((tag: unknown) => typeof tag === 'string')) {
      return false;
    }
  }
  
  return true;
}

// ============================================================================
// VALIDATEURS MÉTIER
// ============================================================================

/**
 * Valide la cohérence entre les frettes et le nombre de cordes
 */
export function validateFretsForInstrument(
  frets: (string | number)[],
  instrument: InstrumentType
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  const expectedStrings: Record<InstrumentType, number> = {
    guitar: 6,
    bass: 4,
    ukulele: 4,
    mandolin: 4,
    banjo: 5,
    piano: 0 // Piano n'a pas de cordes
  };
  
  const expected = expectedStrings[instrument];
  
  if (instrument === 'piano') {
    // Pour le piano, on s'attend à des notes MIDI plutôt que des frettes
    if (frets.length > 0) {
      errors.push('Le piano ne devrait pas avoir de données de frettes');
    }
  } else {
    if (frets.length !== expected) {
      errors.push(`${instrument} devrait avoir ${expected} cordes, mais ${frets.length} frettes trouvées`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Valide la cohérence des doigtés avec les frettes
 */
export function validateFingersWithFrets(
  frets: (string | number)[],
  fingers?: number[]
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!fingers) return { valid: true, errors: [] };
  
  if (fingers.length !== frets.length) {
    errors.push(`Le nombre de doigtés (${fingers.length}) ne correspond pas au nombre de frettes (${frets.length})`);
    return { valid: false, errors };
  }
  
  for (let i = 0; i < frets.length; i++) {
    const fret = frets[i];
    const finger = fingers[i];
    
    // Si la corde est muette (x ou -1), le doigté devrait être 0
    if ((fret === 'x' || fret === 'X' || fret === -1) && finger !== 0) {
      errors.push(`Corde ${i + 1}: corde muette mais doigté ${finger} spécifié`);
    }
    
    // Si la corde est à vide (0), le doigté devrait être 0
    if (fret === 0 && finger !== 0) {
      errors.push(`Corde ${i + 1}: corde à vide mais doigté ${finger} spécifié`);
    }
    
    // Si il y a une frette, il devrait y avoir un doigté (sauf pour les barrés)
    if (typeof fret === 'number' && fret > 0 && finger === 0) {
      // Note: Ceci pourrait être valide pour les barrés, on émet juste un avertissement
      // errors.push(`Corde ${i + 1}: frette ${fret} mais aucun doigté spécifié`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Valide la cohérence des barrés avec les frettes et doigtés
 */
export function validateBarresWithFrets(
  frets: (string | number)[],
  fingers?: number[],
  barres?: BarrePosition[]
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!barres || barres.length === 0) return { valid: true, errors: [] };
  
  for (const barre of barres) {
    // Vérifier que les cordes du barré existent
    if (barre.fromString > frets.length || barre.toString > frets.length) {
      errors.push(`Barré sur cordes ${barre.fromString}-${barre.toString} mais seulement ${frets.length} cordes disponibles`);
      continue;
    }
    
    // Vérifier que les frettes correspondent au barré
    for (let string = barre.fromString; string <= barre.toString; string++) {
      const fretIndex = string - 1; // Conversion 1-based vers 0-based
      const fret = frets[fretIndex];
      
      if (typeof fret === 'number' && fret !== barre.fret) {
        // Note: Ceci pourrait être valide si d'autres doigts sont sur la même frette
        // On émet un avertissement plutôt qu'une erreur
        // errors.push(`Barré frette ${barre.fret} mais corde ${string} sur frette ${fret}`);
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// ============================================================================
// FONCTION DE VALIDATION COMPLÈTE
// ============================================================================

/**
 * Valide complètement un accord unifié avec toutes les vérifications métier
 */
export function validateChordCompletely(chord: unknown): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validation de base de la structure
  if (!isValidUnifiedChord(chord)) {
    errors.push('Structure d\'accord invalide');
    return { valid: false, errors, warnings };
  }
  
  const validChord = chord as UnifiedChordPosition;
  
  // Validation métier
  const fretsValidation = validateFretsForInstrument(validChord.frets, validChord.instrument);
  if (!fretsValidation.valid) {
    errors.push(...fretsValidation.errors);
  }
  
  const fingersValidation = validateFingersWithFrets(validChord.frets, validChord.fingers);
  if (!fingersValidation.valid) {
    warnings.push(...fingersValidation.errors); // Traité comme avertissement
  }
  
  const barresValidation = validateBarresWithFrets(validChord.frets, validChord.fingers, validChord.barres);
  if (!barresValidation.valid) {
    warnings.push(...barresValidation.errors); // Traité comme avertissement
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

// ============================================================================
// CLASSE PRINCIPALE DE VALIDATION
// ============================================================================

/**
 * Classe principale pour toutes les validations d'accords
 */
export class ChordValidation {
  /**
   * Valide un fichier JSON d'instrument
   */
  static validateInstrumentFile(data: unknown): {
    valid: boolean;
    errors: string[];
    warnings: string[];
    stats?: {
      totalChords: number;
      validChords: number;
      invalidChords: number;
    };
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!isValidChordJsonDefinition(data)) {
      errors.push('Format de fichier JSON invalide');
      return { valid: false, errors, warnings };
    }
    
    const definition = data as ChordJsonDefinition;
    let totalChords = 0;
    let validChords = 0;
    let invalidChords = 0;
    
    // Validation de chaque accord
    for (const [key, variations] of Object.entries(definition.chords)) {
      for (const variation of variations) {
        for (const position of variation.positions) {
          totalChords++;
          
          const validation = validateChordCompletely({
            id: crypto.randomUUID(),
            chord: `${key}${variation.suffix}`,
            instrument: definition.instrument as InstrumentType,
            tuning: 'standard',
            frets: position.frets,
            fingers: position.fingers,
            baseFret: position.baseFret || 1,
            barres: position.barres,
            midi: position.midi,
            difficulty: position.difficulty || 'intermediate'
          });
          
          if (validation.valid) {
            validChords++;
          } else {
            invalidChords++;
            errors.push(`${key}${variation.suffix}: ${validation.errors.join(', ')}`);
          }
          
          if (validation.warnings.length > 0) {
            warnings.push(`${key}${variation.suffix}: ${validation.warnings.join(', ')}`);
          }
        }
      }
    }
    
    return {
      valid: invalidChords === 0,
      errors,
      warnings,
      stats: {
        totalChords,
        validChords,
        invalidChords
      }
    };
  }
  
  /**
   * Valide un accord unifié
   */
  static validateChord = validateChordCompletely;
  
  /**
   * Valide une liste d'accords
   */
  static validateChordList(chords: unknown[]): {
    valid: boolean;
    results: Array<{ chord: unknown; valid: boolean; errors: string[]; warnings: string[] }>;
  } {
    const results = chords.map(chord => ({
      chord,
      ...this.validateChord(chord)
    }));
    
    return {
      valid: results.every(result => result.valid),
      results
    };
  }
}
