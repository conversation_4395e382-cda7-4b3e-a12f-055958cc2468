"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { createBrowserClient } from "@/lib/supabase/client"
import { UserPlus, UserCheck } from "lucide-react"

interface FollowButtonProps {
  targetId: string
  targetType?: "user" | "band" | "artist"
  initialFollowers?: number
  initialFollowing?: boolean
  size?: "default" | "sm" | "lg" | "icon"
  variant?: "default" | "outline" | "secondary" | "ghost"
  showCount?: boolean
}

export function FollowButton({
  targetId,
  targetType = "user",
  initialFollowers = 0,
  initialFollowing = false,
  size = "default",
  variant = "outline",
  showCount = true,
}: FollowButtonProps) {
  const [following, setFollowing] = useState(initialFollowing)
  const [followers, setFollowers] = useState(initialFollowers)
  const [isLoading, setIsLoading] = useState(false)
  const supabase = createBrowserClient()
  const { toast } = useToast()

  useEffect(() => {
    const checkIfFollowing = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser()
        if (!user) return

        const { data, error } = await supabase
          .from("follows")
          .select("id")
          .eq("follower_id", user.id)
          .eq("following_id", targetId)
          .single()

        if (error && error.code !== "PGRST116") {
          console.error("Erreur lors de la vérification du follow:", error)
          return
        }

        setFollowing(!!data)
      } catch (error) {
        console.error("Erreur lors de la vérification du follow:", error)
      }
    }

    const getFollowersCount = async () => {
      try {
        const { count, error } = await supabase
          .from("follows")
          .select("id", { count: "exact", head: true })
          .eq("following_id", targetId)

        if (error) {
          console.error("Erreur lors du comptage des followers:", error)
          return
        }

        setFollowers(count || 0)
      } catch (error) {
        console.error("Erreur lors du comptage des followers:", error)
      }
    }

    checkIfFollowing()
    getFollowersCount()
  }, [targetId, supabase])

  const handleFollow = async () => {
    try {
      setIsLoading(true)
      const {
        data: { user },
      } = await supabase.auth.getUser()

      if (!user) {
        toast({
          title: "Vous devez être connecté",
          description: "Connectez-vous pour suivre ce profil",
          variant: "destructive",
        })
        return
      }

      if (following) {
        // Supprimer le follow
        const { error } = await supabase
          .from("follows")
          .delete()
          .eq("follower_id", user.id)
          .eq("following_id", targetId)

        if (error) {
          console.error("Erreur lors de la suppression du follow:", error)
          toast({
            title: "Erreur",
            description: "Impossible d'arrêter de suivre",
            variant: "destructive",
          })
          return
        }

        setFollowing(false)
        setFollowers((prev) => Math.max(0, prev - 1))
      } else {
        // Ajouter un follow
        const { error } = await supabase.from("follows").insert({
          follower_id: user.id,
          following_id: targetId,
        })

        if (error) {
          console.error("Erreur lors de l'ajout du follow:", error)
          toast({
            title: "Erreur",
            description: "Impossible de suivre",
            variant: "destructive",
          })
          return
        }

        setFollowing(true)
        setFollowers((prev) => prev + 1)
      }
    } catch (error) {
      console.error("Erreur lors de la gestion du follow:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      className="flex items-center gap-1"
      onClick={handleFollow}
      disabled={isLoading}
    >
      {following ? (
        <>
          <UserCheck className="mr-1 h-4 w-4" />
          Abonné{showCount && followers > 0 ? ` (${followers})` : ""}
        </>
      ) : (
        <>
          <UserPlus className="mr-1 h-4 w-4" />
          Suivre{showCount && followers > 0 ? ` (${followers})` : ""}
        </>
      )}
    </Button>
  )
}
