"use client";

import * as React from "react";
import { Control, FieldPath, FieldValues } from "react-hook-form";
import { Eye, EyeOff } from "lucide-react";

import { cn } from "@/lib/utils";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { FormField, FormItem, FormControl } from "@/components/ui/form"; // Import necessary form components

interface VisibilityToggleProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends React.HTMLAttributes<HTMLDivElement> {
  control: Control<TFieldValues>;
  name: TName;
  label?: string; // Optional label text if needed, defaults to icon only
  labelClassName?: string;
  switchClassName?: string;
  iconClassName?: string;
}

export function VisibilityToggle<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  control,
  name,
  label,
  className,
  labelClassName,
  switchClassName,
  iconClassName,
  ...props
}: VisibilityToggleProps<TFieldValues, TName>) {
  const id = React.useId(); // Generate unique ID for label association

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem
          className={cn("flex items-center space-x-2 !mt-0", className)} // Basic styling, remove default top margin
           {...props}
         >
           <FormControl>
               {/* Removed extra div wrapper */}
               <Switch
                 id={id} // Associate Switch with Label
                 checked={field.value}
                onCheckedChange={field.onChange}
                aria-label={label || (field.value ? "Rendre privé" : "Rendre public")}
                 className={cn("scale-75 data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-red-500", switchClassName)}
               />
           </FormControl>
           <Label htmlFor={id} className={cn("text-xs text-muted-foreground !mt-0 cursor-pointer", labelClassName)}>
            {field.value ? (
              <Eye className={cn("h-3 w-3 text-green-600", iconClassName)} />
            ) : (
              <EyeOff className={cn("h-3 w-3 text-red-600", iconClassName)} />
            )}
            {label && <span className="ml-1">{label}</span>}
          </Label>
        </FormItem>
      )}
    />
  );
}
