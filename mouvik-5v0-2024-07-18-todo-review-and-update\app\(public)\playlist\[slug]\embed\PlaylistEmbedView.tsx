"use client";
import Link from "next/link";
import Image from "next/image";
import { ListMusic, Music, Play, Clock } from "lucide-react";
import { formatDuration as formatDurationUtil } from '@/lib/utils';

interface PlaylistSongForEmbed {
  id: string;
  title: string;
  duration: number | null;
  cover_url: string | null;
  audio_url?: string | null;
  artist_name?: string;
  profiles: {
    username: string | null;
    display_name: string | null;
  } | null;
  slug?: string | null;
}

interface PublicPlaylistEmbedDetails {
  id: string;
  name: string;
  description: string | null;
  cover_url: string | null;
  user_id: string;
  profiles: {
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
  } | null;
  playlist_songs: { songs: (PlaylistSongForEmbed & { slug?: string | null; audio_url?: string | null; user_id?: string; artist_name?: string; }) | null }[];
}

export default function PlaylistEmbedView({ playlistData, params }: { playlistData: PublicPlaylistEmbedDetails; params: { slug: string } }) {
  // Explicitly import styled-jsx/style INSIDE the client component
  // We need to use require here as import statements must be top-level
  const React = require('react'); 
  const Style = require('styled-jsx/style').default;

  const songsInPlaylist = playlistData.playlist_songs
    .map(ps => ps.songs)
    .filter((song): song is PlaylistSongForEmbed => song !== null);

  const playlistCreator = playlistData.profiles;
  const creatorDisplayName = playlistCreator?.display_name || playlistCreator?.username || "Utilisateur inconnu";
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

  return (
    <div style={{ fontFamily: 'sans-serif', padding: '16px', border: '1px solid #e5e7eb', borderRadius: '8px', backgroundColor: '#fff', color: '#1f2937' }}>
      <Style id="playlist-embed-global-styles">{`
        body { margin: 0; background-color: transparent !important; }
        a { color: #3b82f6; text-decoration: none; }
        a:hover { text-decoration: underline; }
      `}</Style>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
        {playlistData.cover_url ? (
          <Image src={playlistData.cover_url} alt={playlistData.name} width={80} height={80} style={{ borderRadius: '4px', marginRight: '16px', objectFit: 'cover' }} />
        ) : (
          <div style={{ width: '80px', height: '80px', backgroundColor: '#f3f4f6', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: '16px' }}>
            <ListMusic size={40} style={{ color: '#9ca3af' }} />
          </div>
        )}
        <div>
          <h1 style={{ fontSize: '1.5rem', fontWeight: '600', margin: '0 0 4px 0' }}>
            <Link href={`/playlist/${params.slug}`} target="_blank" rel="noopener noreferrer" title={`Ouvrir ${playlistData.name} dans un nouvel onglet`}>
              {playlistData.name}
            </Link>
          </h1>
          <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0' }}>
            Par <Link href={playlistCreator?.username ? `/artists/${playlistCreator.username}` : '#'} target="_blank" rel="noopener noreferrer">{creatorDisplayName}</Link>
          </p>
          {playlistData.description && <p style={{ fontSize: '0.875rem', color: '#4b5563', margin: '8px 0 0 0', whiteSpace: 'pre-wrap' }}>{playlistData.description}</p>}
        </div>
      </div>

      {songsInPlaylist.length > 0 ? (
        <ul style={{ listStyle: 'none', padding: '0', margin: '0' }}>
          {songsInPlaylist.map((song, index) => (
            <li key={song.id} style={{ display: 'flex', alignItems: 'center', padding: '8px 0', borderTop: index > 0 ? '1px solid #f3f4f6' : 'none' }}>
              <span style={{ marginRight: '12px', color: '#6b7280', fontSize: '0.875rem', width: '20px', textAlign: 'right' }}>{index + 1}.</span>
              {song.cover_url ? (
                <Image src={song.cover_url} alt={song.title} width={40} height={40} style={{ borderRadius: '4px', marginRight: '12px', objectFit: 'cover' }} />
              ) : (
                <div style={{ width: '40px', height: '40px', backgroundColor: '#f3f4f6', borderRadius: '4px', display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: '12px' }}>
                  <Music size={20} style={{ color: '#9ca3af' }} />
                </div>
              )}
              <div style={{ flexGrow: 1 }}>
                <Link href={song.slug ? `/song/${song.slug}` : '#'} target="_blank" rel="noopener noreferrer" style={{ fontWeight: '500', fontSize: '1rem' }}>
                  {song.title}
                </Link>
                <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '2px 0 0 0' }}>
                  {song.artist_name || song.profiles?.display_name || song.profiles?.username || 'Artiste inconnu'}
                </p>
              </div>
              {song.duration && (
                <div style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', color: '#6b7280' }}>
                  <Clock size={14} style={{ marginRight: '4px' }} />
                  {formatDurationUtil(song.duration)}
                </div>
              )}
              {song.audio_url && (
                <Link href={song.slug ? `/song/${song.slug}` : '#'} target="_blank" rel="noopener noreferrer" title={`Écouter ${song.title}`} style={{ marginLeft: '12px' }}>
                  <Play size={20} />
                </Link>
              )}
            </li>
          ))}
        </ul>
      ) : (
        <p style={{ textAlign: 'center', color: '#6b7280', padding: '20px 0' }}>Cette playlist est vide.</p>
      )}
      <div style={{ marginTop: '16px', textAlign: 'center', fontSize: '0.75rem', color: '#9ca3af' }}>
        Fourni par <Link href={baseUrl} target="_blank" rel="noopener noreferrer" style={{ fontWeight: '500' }}>VotreNomDeSite</Link>
      </div>
    </div>
  );
}
