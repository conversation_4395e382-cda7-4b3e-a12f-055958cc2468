'use client';

import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, Sparkles, MessageSquare, Music, FileText, Guitar,
  Layers, Volume2, Heart, Clock, Users, Wand2, Target,
  BarChart3, TrendingUp, Lightbulb, Hash, Mic, Settings
} from 'lucide-react';

interface AIOptionsEnrichedProps {
  onAnalyze: (options: AnalysisOptions) => Promise<void>;
  isLoading: boolean;
  selectedSection: string;
  songSections: any[];
  lyricsContent: string;
  styleConfig: any;
}

interface AnalysisOptions {
  // Niveaux d'analyse
  levels: {
    section: boolean;
    song: boolean;
    global: boolean;
  };
  
  // Dimensions d'analyse
  dimensions: {
    structure: boolean;
    emotion: boolean;
    style: boolean;
    harmony: boolean;
    narrative: boolean;
    commercial: boolean;
  };
  
  // Focus spécialisés
  focus: {
    lyrics: boolean;
    melody: boolean;
    chords: boolean;
    arrangement: boolean;
    production: boolean;
    performance: boolean;
  };
  
  // Options avancées
  advanced: {
    detailed: boolean;
    suggestions: boolean;
    examples: boolean;
    comparisons: boolean;
  };
}

const DEFAULT_OPTIONS: AnalysisOptions = {
  levels: { section: true, song: false, global: false },
  dimensions: { structure: true, emotion: false, style: false, harmony: false, narrative: false, commercial: false },
  focus: { lyrics: true, melody: false, chords: false, arrangement: false, production: false, performance: false },
  advanced: { detailed: true, suggestions: true, examples: false, comparisons: false }
};

export const AIOptionsEnriched: React.FC<AIOptionsEnrichedProps> = ({
  onAnalyze,
  isLoading,
  selectedSection,
  songSections,
  lyricsContent,
  styleConfig
}) => {
  
  const [options, setOptions] = useState<AnalysisOptions>(DEFAULT_OPTIONS);

  // Gestionnaire pour mettre à jour les options
  const updateOptions = useCallback((category: keyof AnalysisOptions, key: string, value: boolean) => {
    setOptions(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  }, []);

  // Gestionnaire pour les presets
  const applyPreset = useCallback((preset: string) => {
    switch (preset) {
      case 'quick':
        setOptions({
          levels: { section: true, song: false, global: false },
          dimensions: { structure: true, emotion: true, style: false, harmony: false, narrative: false, commercial: false },
          focus: { lyrics: true, melody: false, chords: false, arrangement: false, production: false, performance: false },
          advanced: { detailed: false, suggestions: true, examples: false, comparisons: false }
        });
        break;
      case 'complete':
        setOptions({
          levels: { section: true, song: true, global: true },
          dimensions: { structure: true, emotion: true, style: true, harmony: true, narrative: true, commercial: true },
          focus: { lyrics: true, melody: true, chords: true, arrangement: true, production: false, performance: false },
          advanced: { detailed: true, suggestions: true, examples: true, comparisons: true }
        });
        break;
      case 'lyrics':
        setOptions({
          levels: { section: true, song: true, global: false },
          dimensions: { structure: true, emotion: true, style: true, harmony: false, narrative: true, commercial: false },
          focus: { lyrics: true, melody: false, chords: false, arrangement: false, production: false, performance: false },
          advanced: { detailed: true, suggestions: true, examples: true, comparisons: false }
        });
        break;
      case 'harmony':
        setOptions({
          levels: { section: true, song: true, global: false },
          dimensions: { structure: true, emotion: false, style: true, harmony: true, narrative: false, commercial: false },
          focus: { lyrics: false, melody: true, chords: true, arrangement: true, production: false, performance: false },
          advanced: { detailed: true, suggestions: true, examples: true, comparisons: false }
        });
        break;
      case 'commercial':
        setOptions({
          levels: { section: false, song: true, global: true },
          dimensions: { structure: true, emotion: true, style: true, harmony: false, narrative: true, commercial: true },
          focus: { lyrics: true, melody: true, chords: false, arrangement: true, production: true, performance: false },
          advanced: { detailed: true, suggestions: true, examples: false, comparisons: true }
        });
        break;
    }
  }, []);

  // Compter les options sélectionnées
  const selectedCount = Object.values(options).reduce((total, category) => {
    return total + Object.values(category).filter(Boolean).length;
  }, 0);

  // Gestionnaire pour lancer l'analyse
  const handleAnalyze = useCallback(async () => {
    await onAnalyze(options);
  }, [options, onAnalyze]);

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Options d'Analyse IA
          </CardTitle>
          <Badge variant="outline" className="text-xs">
            {selectedCount} options
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Presets rapides */}
        <div>
          <h4 className="text-sm font-medium text-white mb-2">Presets Rapides</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => applyPreset('quick')}
              className="gap-1"
            >
              <Sparkles className="h-3 w-3" />
              Rapide
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => applyPreset('complete')}
              className="gap-1"
            >
              <Brain className="h-3 w-3" />
              Complet
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => applyPreset('lyrics')}
              className="gap-1"
            >
              <FileText className="h-3 w-3" />
              Paroles
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => applyPreset('harmony')}
              className="gap-1"
            >
              <Music className="h-3 w-3" />
              Harmonie
            </Button>
          </div>
        </div>

        <Separator className="bg-slate-600" />

        <ScrollArea className="h-80">
          <div className="space-y-4">
            {/* Niveaux d'analyse */}
            <div>
              <h4 className="text-sm font-medium text-white mb-3 flex items-center gap-2">
                <Target className="h-4 w-4 text-blue-400" />
                Niveaux d'Analyse
              </h4>
              <div className="space-y-2">
                {[
                  { key: 'section', label: 'Section Actuelle', description: 'Analyse de la section sélectionnée' },
                  { key: 'song', label: 'Chanson Complète', description: 'Vue d\'ensemble de la chanson' },
                  { key: 'global', label: 'Vision Globale', description: 'Contexte artistique et commercial' }
                ].map((level) => (
                  <div key={level.key} className="flex items-start space-x-3">
                    <Checkbox
                      id={`level-${level.key}`}
                      checked={options.levels[level.key]}
                      onCheckedChange={(checked) => updateOptions('levels', level.key, !!checked)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <label htmlFor={`level-${level.key}`} className="text-sm text-white cursor-pointer">
                        {level.label}
                      </label>
                      <p className="text-xs text-slate-400">{level.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator className="bg-slate-600" />

            {/* Dimensions d'analyse */}
            <div>
              <h4 className="text-sm font-medium text-white mb-3 flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-green-400" />
                Dimensions d'Analyse
              </h4>
              <div className="space-y-2">
                {[
                  { key: 'structure', label: 'Structure', icon: Layers, description: 'Organisation et progression' },
                  { key: 'emotion', label: 'Émotion', icon: Heart, description: 'Impact émotionnel' },
                  { key: 'style', label: 'Style', icon: Sparkles, description: 'Cohérence stylistique' },
                  { key: 'harmony', label: 'Harmonie', icon: Music, description: 'Accords et mélodie' },
                  { key: 'narrative', label: 'Narratif', icon: FileText, description: 'Histoire et message' },
                  { key: 'commercial', label: 'Commercial', icon: TrendingUp, description: 'Potentiel marché' }
                ].map((dimension) => {
                  const Icon = dimension.icon;
                  return (
                    <div key={dimension.key} className="flex items-start space-x-3">
                      <Checkbox
                        id={`dimension-${dimension.key}`}
                        checked={options.dimensions[dimension.key]}
                        onCheckedChange={(checked) => updateOptions('dimensions', dimension.key, !!checked)}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <label htmlFor={`dimension-${dimension.key}`} className="text-sm text-white cursor-pointer flex items-center gap-2">
                          <Icon className="h-3 w-3" />
                          {dimension.label}
                        </label>
                        <p className="text-xs text-slate-400">{dimension.description}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <Separator className="bg-slate-600" />

            {/* Focus spécialisés */}
            <div>
              <h4 className="text-sm font-medium text-white mb-3 flex items-center gap-2">
                <Wand2 className="h-4 w-4 text-purple-400" />
                Focus Spécialisés
              </h4>
              <div className="space-y-2">
                {[
                  { key: 'lyrics', label: 'Paroles', icon: FileText, description: 'Texte et poésie' },
                  { key: 'melody', label: 'Mélodie', icon: Music, description: 'Ligne mélodique' },
                  { key: 'chords', label: 'Accords', icon: Guitar, description: 'Harmonie et progressions' },
                  { key: 'arrangement', label: 'Arrangement', icon: Layers, description: 'Structure musicale' },
                  { key: 'production', label: 'Production', icon: Volume2, description: 'Sonorités et mix' },
                  { key: 'performance', label: 'Performance', icon: Mic, description: 'Interprétation live' }
                ].map((focus) => {
                  const Icon = focus.icon;
                  return (
                    <div key={focus.key} className="flex items-start space-x-3">
                      <Checkbox
                        id={`focus-${focus.key}`}
                        checked={options.focus[focus.key]}
                        onCheckedChange={(checked) => updateOptions('focus', focus.key, !!checked)}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <label htmlFor={`focus-${focus.key}`} className="text-sm text-white cursor-pointer flex items-center gap-2">
                          <Icon className="h-3 w-3" />
                          {focus.label}
                        </label>
                        <p className="text-xs text-slate-400">{focus.description}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <Separator className="bg-slate-600" />

            {/* Options avancées */}
            <div>
              <h4 className="text-sm font-medium text-white mb-3 flex items-center gap-2">
                <Settings className="h-4 w-4 text-orange-400" />
                Options Avancées
              </h4>
              <div className="space-y-2">
                {[
                  { key: 'detailed', label: 'Analyse Détaillée', description: 'Explications approfondies' },
                  { key: 'suggestions', label: 'Suggestions Concrètes', description: 'Actions recommandées' },
                  { key: 'examples', label: 'Exemples Pratiques', description: 'Références et illustrations' },
                  { key: 'comparisons', label: 'Comparaisons', description: 'Benchmarking avec succès' }
                ].map((advanced) => (
                  <div key={advanced.key} className="flex items-start space-x-3">
                    <Checkbox
                      id={`advanced-${advanced.key}`}
                      checked={options.advanced[advanced.key]}
                      onCheckedChange={(checked) => updateOptions('advanced', advanced.key, !!checked)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <label htmlFor={`advanced-${advanced.key}`} className="text-sm text-white cursor-pointer">
                        {advanced.label}
                      </label>
                      <p className="text-xs text-slate-400">{advanced.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>

        <Separator className="bg-slate-600" />

        {/* Bouton d'analyse */}
        <Button
          onClick={handleAnalyze}
          disabled={isLoading || selectedCount === 0}
          className="w-full gap-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
        >
          <Brain className="h-4 w-4" />
          {isLoading ? 'Analyse en cours...' : `Analyser (${selectedCount} options)`}
        </Button>

        {/* Informations contextuelles */}
        <div className="bg-slate-700/30 rounded-lg p-3">
          <h4 className="text-sm font-medium text-white mb-2">Contexte Actuel</h4>
          <div className="text-xs text-slate-400 space-y-1">
            <div>Section: {songSections.find(s => s.id === selectedSection)?.title || 'Aucune'}</div>
            <div>Contenu: {lyricsContent.trim().split(/\s+/).filter(Boolean).length} mots</div>
            <div>Genre: {styleConfig.genres?.[0] || 'Non défini'}</div>
            <div>Tonalité: {styleConfig.key || 'C'} • Tempo: {styleConfig.bpm || 120} BPM</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
