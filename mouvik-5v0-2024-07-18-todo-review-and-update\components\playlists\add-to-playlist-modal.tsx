"use client";

import { useState, useEffect } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription, DialogClose } from '@/components/ui/dialog';
// Checkbox will be replaced by <PERSON><PERSON> with icons
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';
import { Loader2, Plus, PlusCircle, CheckCircle2 } from 'lucide-react'; // Added PlusCircle, CheckCircle2
import { usePlanLimits } from '@/hooks/use-plan-limits';
import { cn } from '@/lib/utils'; // Import cn

interface Playlist {
  id: string;
  name: string;
}

interface AddToPlaylistModalProps {
  songId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function AddToPlaylistModal({ songId, isOpen, onClose }: AddToPlaylistModalProps) {
  const supabase = getSupabaseClient();
  const { user } = useUser();
  const { limits: planLimits, isLoading: isLoadingPlanLimits } = usePlanLimits(); 
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [selectedPlaylists, setSelectedPlaylists] = useState<Set<string>>(new Set());
  const [newPlaylistName, setNewPlaylistName] = useState('');
  const [showNewPlaylistInput, setShowNewPlaylistInput] = useState(false);
  // const [isLoadingPlaylists, setIsLoadingPlaylists] = useState(true); // Will be replaced by isLoadingInitialDataModal
  const [isSaving, setIsSaving] = useState(false);
  const [userPlaylistCount, setUserPlaylistCount] = useState<number | null>(null); 
  const [playlistCreationCost, setPlaylistCreationCost] = useState<number | null>(null); 
  const [isLoadingInitialDataModal, setIsLoadingInitialDataModal] = useState(true); 

  const isAdmin = user?.user_role === 'admin'; // Define isAdmin in component scope

  useEffect(() => {
    if (isOpen && user) {
      const fetchDataForModal = async () => {
        setIsLoadingInitialDataModal(true); // Use new loading state
        // Fetch existing playlists
        const { data: playlistData, error: playlistsError } = await supabase
          .from('playlists')
          .select('id, name')
          .eq('user_id', user.id)
          .order('name', { ascending: true });
        
        if (playlistsError) {
          console.error("Error fetching user playlists for modal:", playlistsError);
          toast({ title: "Erreur", description: "Impossible de charger vos playlists.", variant: "destructive" });
        } else {
          setPlaylists(playlistData || []);
        }

        // Fetch current playlist count for quota check
        const { data: countData, error: countError } = await supabase.rpc('get_user_playlist_count', { user_id_param: user.id });
        if (countError) {
          console.error("Error fetching playlist count for modal:", countError);
          // Don't block modal for this, but log it. Quota check might be less accurate.
        } else {
          setUserPlaylistCount(typeof countData === 'number' ? countData : 0);
        }

        // Fetch playlist creation cost
        const { data: costData, error: costError } = await supabase
          .from('creation_costs')
          .select('cost')
          .eq('resource_type', 'playlist')
          .single();

        if (costError || !costData) {
          console.error("Error fetching playlist creation cost for modal:", costError);
          // Don't toast error here, let it default or be handled by form if critical
          setPlaylistCreationCost(0); // Fallback if cost not found
        } else {
          setPlaylistCreationCost(costData.cost);
        }
        setIsLoadingInitialDataModal(false); // Use new loading state
      };
      fetchDataForModal();
    }
  }, [isOpen, user, supabase]);

  const handleTogglePlaylist = (playlistId: string) => {
    // console.log('Toggling playlist:', playlistId); // DEBUGGING REMOVED
    setSelectedPlaylists(prev => {
      const newSet = new Set(prev);
      if (newSet.has(playlistId)) {
        newSet.delete(playlistId);
      } else {
        newSet.add(playlistId);
      }
      return newSet;
    });
  };

  const handleCreateAndAddPlaylist = async () => {
    if (!user || !newPlaylistName.trim()) return;
    setIsSaving(true);

    // Quota Check (Admin Bypass)
    const isAdmin = user?.user_role === 'admin';
    const effectiveMaxPlaylists = user?.custom_max_playlists ?? planLimits?.max_playlists ?? null;
    const canCreatePlaylist = isAdmin || effectiveMaxPlaylists === null || (userPlaylistCount !== null && userPlaylistCount < effectiveMaxPlaylists);

    if (!isAdmin && !canCreatePlaylist) {
      toast({ title: "Limite de playlists atteinte", description: `Vous avez atteint votre quota de ${effectiveMaxPlaylists} playlists.`, variant: "destructive" });
      setIsSaving(false);
      return;
    }

    const costToCreate = isAdmin ? 0 : (playlistCreationCost ?? 0); // Use fetched cost
    if (!isAdmin && (user.coins_balance === null || user.coins_balance === undefined || user.coins_balance < costToCreate)) {
      toast({ title: "Pièces insuffisantes", description: `Vous avez besoin de ${costToCreate} pièces pour créer une playlist. Votre solde: ${user.coins_balance || 0}.`, variant: "destructive" });
      setIsSaving(false);
      return;
    }

    try {
      // p_cost is no longer sent to RPC
      const rpcParams = {
        p_user_id: user.id,
        p_name: newPlaylistName.trim(),
        p_description: null,
        p_is_public: false, 
        p_slug: null,       
        p_cover_url: null,
        p_banner_url: null,
        p_genres: null,
        p_moods: null,
        p_instrumentation: null,
        // p_cost parameter removed
      };

      const { data: rpcResponse, error: rpcError } = await supabase.rpc('create_playlist_with_coin_deduction', rpcParams);

      if (rpcError) throw rpcError;

      if (rpcResponse.status === 'error') {
        if (rpcResponse.message === 'insufficient_coins') {
          toast({ title: "Pièces insuffisantes", description: `Il vous faut ${rpcResponse.required} pièces. Solde: ${rpcResponse.balance}.`, variant: "destructive" });
        } else {
          toast({ title: "Erreur de création de playlist", description: rpcResponse.message || "Une erreur inconnue est survenue.", variant: "destructive" });
        }
        setIsSaving(false);
        return;
      }
      
      const newPlaylistId = rpcResponse.playlist_id;
      const createdPlaylistData = { id: newPlaylistId, name: newPlaylistName.trim() }; 

      const costApplied = rpcResponse.cost_applied; // Get cost applied from RPC
      const toastCostMessage = costApplied > 0 ? `${costApplied} pièces déduites.` : "Aucune pièce déduite.";
      toast({ title: "Playlist créée", description: `"${createdPlaylistData.name}" créée. ${toastCostMessage} Nouveau solde: ${rpcResponse.new_balance}.` });
      
      if (!isAdmin && userPlaylistCount !== null) setUserPlaylistCount(userPlaylistCount + 1); 

      // Add song to this new playlist
      const { error: addError } = await supabase
        .from('playlist_songs')
        .insert({ playlist_id: newPlaylistId, song_id: songId }); // Removed user_id as it's not in the table
      
      if (addError) {
        // If adding song fails, the playlist is created and coins deducted. This state should be handled.
        // For now, just toast an error for the song addition part.
        console.error("Error adding song to newly created playlist:", addError);
        toast({ title: "Erreur d'ajout", description: `La playlist a été créée, mais le morceau n'a pas pu y être ajouté: ${addError.message}`, variant: "default" }); // Changed to default
      } else {
        toast({ title: "Succès", description: `Morceau ajouté à "${createdPlaylistData.name}".` });
      }

      setPlaylists(prevPlaylists => {
        if (prevPlaylists.find(p => p.id === newPlaylistId)) {
          return prevPlaylists;
        }
        return [...prevPlaylists, createdPlaylistData];
      });
      setSelectedPlaylists(prevSelected => {
        const newSelected = new Set(prevSelected);
        newSelected.add(newPlaylistId);
        return newSelected;
      });
      setShowNewPlaylistInput(false);
      setNewPlaylistName('');

    } catch (error: any) {
      console.error("Error in handleCreateAndAddPlaylist (RPC or song addition):", error);
      toast({ title: "Erreur", description: error.message || "Une erreur technique est survenue.", variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddToSelectedPlaylists = async () => {
    if (!user || selectedPlaylists.size === 0) return;
    setIsSaving(true);
    try {
      const insertPromises = Array.from(selectedPlaylists).map(playlistId => 
        supabase.from('playlist_songs').insert({ playlist_id: playlistId, song_id: songId }) // Removed user_id
      );
      const results = await Promise.allSettled(insertPromises);
      
      let successCount = 0;
      results.forEach(result => {
        if (result.status === 'fulfilled' && !result.value.error) {
          successCount++;
        } else if (result.status === 'fulfilled' && result.value.error) {
          // Handle potential duplicate errors gracefully (code 23505 for unique constraint violation)
          if (result.value.error.code !== '23505') {
             console.error("Error adding song to playlist:", result.value.error);
          } else {
            successCount++; // Count as success if it's a duplicate error (already exists)
          }
        } else if (result.status === 'rejected') {
          console.error("Error adding song to playlist (rejected promise):", result.reason);
        }
      });

      if (successCount > 0) {
        toast({ title: "Succès", description: `Morceau ajouté à ${successCount} playlist(s).` });
      }
      if (successCount < selectedPlaylists.size) {
         toast({ title: "Attention", description: `Certains ajouts ont échoué (peut-être déjà présents).`, variant: "default" });
      }
      onClose(); // Close modal on success
    } catch (error: any) { // Should be caught by Promise.allSettled, but as a fallback
      console.error("Error adding to selected playlists:", error);
      toast({ title: "Erreur", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSave = () => {
    if (showNewPlaylistInput && newPlaylistName.trim()) {
      handleCreateAndAddPlaylist(); // This will also add the song
    } else if (selectedPlaylists.size > 0) {
      handleAddToSelectedPlaylists();
    } else {
      toast({title: "Aucune sélection", description: "Veuillez sélectionner une playlist ou en créer une nouvelle.", variant: "default"});
    }
  };


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {/* Added bg-background text-foreground to ensure proper theming from root */}
      <DialogContent className="sm:max-w-[425px] bg-background text-foreground">
        <DialogHeader>
          {/* Ensured title also uses foreground color for visibility */}
          <DialogTitle className="text-foreground">Ajouter à une playlist</DialogTitle>
          <DialogDescription>
            Sélectionnez une ou plusieurs de vos playlists, ou créez-en une nouvelle.
            {showNewPlaylistInput && !isAdmin && playlistCreationCost !== null && playlistCreationCost > 0 && (
              <span className="block text-xs mt-1 text-muted-foreground">Coût de création : {playlistCreationCost} pièces.</span>
            )}
             {showNewPlaylistInput && isAdmin && (
              <span className="block text-xs mt-1 text-green-600">Mode Admin : Création gratuite.</span>
            )}
          </DialogDescription>
        </DialogHeader>
        
        {isLoadingInitialDataModal ? ( // Use new loading state
          <div className="flex justify-center items-center h-32"><Loader2 className="h-6 w-6 animate-spin" /></div>
        ) : (
          <div className="space-y-4 py-4 max-h-[300px] overflow-y-auto">
            {playlists.length === 0 && !showNewPlaylistInput && (
              <p className="text-sm text-muted-foreground text-center">Vous n'avez aucune playlist.</p>
            )}
            {playlists.map((playlist) => (
              <div key={playlist.id} className="flex items-center justify-between p-1.5 hover:bg-muted/50 rounded-md">
                <Label htmlFor={`playlist-toggle-${playlist.id}`} className="font-normal cursor-pointer text-sm text-foreground hover:text-accent-foreground flex-grow">
                  {playlist.name}
                </Label>
                <Button
                  id={`playlist-toggle-${playlist.id}`}
                  variant={selectedPlaylists.has(playlist.id) ? "secondary" : "outline"}
                  size="icon"
                  onClick={() => handleTogglePlaylist(playlist.id)}
                  className={cn(
                    "h-8 w-8 flex-shrink-0",
                    selectedPlaylists.has(playlist.id) && "ring-2 ring-green-500 ring-offset-1 ring-offset-background"
                  )}
                >
                  {selectedPlaylists.has(playlist.id) ? <CheckCircle2 className="h-5 w-5 text-green-500" /> : <PlusCircle className="h-5 w-5" />}
                  <span className="sr-only">{selectedPlaylists.has(playlist.id) ? "Retirer de la sélection" : "Ajouter à la sélection"}</span>
                </Button>
              </div>
            ))}
          </div>
        )}

        <div className="pt-4">
          {showNewPlaylistInput ? (
            <div className="space-y-2">
              <Label htmlFor="newPlaylistName" className="text-foreground/90">Nom de la nouvelle playlist</Label>
              <Input 
                id="newPlaylistName" 
                value={newPlaylistName} 
                onChange={(e) => setNewPlaylistName(e.target.value)}
                placeholder="Ex: Mes favoris Rock"
                // Ensure input is styled for current background
                className="bg-background border-foreground/30 text-foreground placeholder:text-muted-foreground" 
              />
            </div>
          ) : (
            // Ensure button text is visible
            <Button variant="outline" size="sm" onClick={() => setShowNewPlaylistInput(true)} className="w-full text-foreground hover:text-foreground/80 border-foreground/30 hover:bg-muted/50">
              <Plus className="mr-2 h-4 w-4" /> Créer une nouvelle playlist
            </Button>
          )}
        </div>

        <DialogFooter className="pt-4">
          <Button variant="ghost" onClick={onClose} disabled={isSaving}>Annuler</Button>
          <Button onClick={handleSave} disabled={isSaving || isLoadingInitialDataModal}> {/* Use new loading state */}
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {showNewPlaylistInput && newPlaylistName.trim() ? "Créer et Ajouter" : "Ajouter"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
