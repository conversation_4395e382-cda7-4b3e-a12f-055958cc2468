/**
 * 🎼 ENHANCED LYRICS EDITOR EXAMPLE - Exemple d'Intégration Complète
 * 
 * Démonstration du nouveau système d'édition paroles + accords
 * Remplacement de LyricsEditorWithAI avec toutes les fonctionnalités
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useRef, useCallback } from 'react';
import { 
  Music, Sparkles, Save, Settings, Download, 
  Play, Pause, RotateCcw, Eye, FileText
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { 
  LyricsChordWorkflow, 
  type ChordPlacement, 
  type AiHistoryItem, 
  type AiConfig 
} from '../index';

// ============================================================================
// EXEMPLE D'INTÉGRATION COMPLÈTE
// ============================================================================

export const EnhancedLyricsEditorExample: React.FC = () => {
  // État simulé du formulaire de morceau
  const [lyricsContent, setLyricsContent] = useState(`Voici mes nouvelles paroles...

Couplet 1:
Je marche sous la pluie d'automne
Les feuilles dansent autour de moi
Chaque goutte qui résonne
Me rappelle pourquoi je crois

Refrain:
En cette mélodie du cœur
Qui guide mes pas vers demain
Dans la joie comme la douleur
La musique trace mon chemin

Couplet 2:
Les accords s'entremêlent
Comme les souvenirs du passé
Chaque note révèle
Un secret bien gardé`);

  const [chords, setChords] = useState<ChordPlacement[]>([]);
  const [aiHistory, setAiHistory] = useState<AiHistoryItem[]>([]);
  const [showAiHistory, setShowAiHistory] = useState(false);
  const [currentTab, setCurrentTab] = useState('editor');

  // Configuration IA simulée
  const [aiConfig] = useState<AiConfig>({
    provider: 'openai',
    model: 'gpt-4',
    temperature: 0.7
  });

  const [aiGeneralPrompt] = useState(`Tu es un assistant musical expert. 
Aide-moi à créer des progressions d'accords harmonieuses et créatives 
qui correspondent au style et à l'émotion des paroles.`);

  // Métadonnées du morceau simulées
  const [songMetadata] = useState({
    title: 'Mélodie du Cœur',
    genre: 'Folk Acoustique',
    key: 'C',
    tempo: 85,
    mood: 'Mélancolique et Espoir'
  });

  const quillRef = useRef<any>(null);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleLyricsChange = useCallback((newContent: string) => {
    setLyricsContent(newContent);
    console.log('Paroles mises à jour:', newContent.length, 'caractères');
  }, []);

  const handleChordsChange = useCallback((newChords: ChordPlacement[]) => {
    setChords(newChords);
    console.log('Accords mis à jour:', newChords.length, 'accords');
  }, []);

  const addAiHistory = useCallback((userPrompt: string, assistantResponse: string) => {
    const newItem: AiHistoryItem = {
      id: crypto.randomUUID(),
      userPrompt,
      assistantResponse,
      timestamp: new Date().toISOString(),
      type: 'chords'
    };
    
    setAiHistory(prev => [...prev, newItem]);
    console.log('Nouvel élément historique IA:', newItem);
  }, []);

  const handleSave = useCallback((lyrics: string, chords: ChordPlacement[]) => {
    // Simulation de sauvegarde
    const saveData = {
      lyrics,
      chords: chords.map(c => ({
        chord: c.chord.chord,
        position: c.textPosition,
        timestamp: c.timestamp
      })),
      metadata: songMetadata,
      savedAt: new Date().toISOString()
    };

    console.log('Sauvegarde simulée:', saveData);
    
    toast({
      title: "Sauvegardé avec succès !",
      description: `${lyrics.length} caractères et ${chords.length} accords sauvegardés.`
    });
  }, [songMetadata]);

  const handleExport = useCallback(() => {
    // Simulation d'export
    const exportData = {
      title: songMetadata.title,
      lyrics: lyricsContent,
      chords: chords.map(c => `${c.chord.chord} (pos: ${c.textPosition})`),
      metadata: songMetadata
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${songMetadata.title.replace(/\s+/g, '_')}_lyrics_chords.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Export réussi !",
      description: "Le fichier a été téléchargé."
    });
  }, [lyricsContent, chords, songMetadata]);

  const handlePreview = useCallback(() => {
    // Simulation de preview
    const preview = {
      lyrics: lyricsContent,
      chords: chords.map(c => c.chord.chord).join(' - '),
      progression: chords.length > 0 ? 'Progression détectée' : 'Aucune progression'
    };

    console.log('Preview:', preview);
    
    toast({
      title: "Preview généré",
      description: `${chords.length} accords dans la progression.`
    });
  }, [lyricsContent, chords]);

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* En-tête */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center">
          <Music className="w-8 h-8 text-blue-600 mr-3" />
          Enhanced Lyrics Editor - Démonstration
        </h1>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto">
          Nouveau système d'édition intégrant paroles, accords et suggestions IA. 
          Remplacement moderne de LyricsEditorWithAI avec interface unifiée.
        </p>
      </div>

      {/* Métadonnées du morceau */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Informations du Morceau
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Titre:</span>
              <p className="text-gray-900">{songMetadata.title}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Genre:</span>
              <p className="text-gray-900">{songMetadata.genre}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Tonalité:</span>
              <p className="text-gray-900">{songMetadata.key}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Tempo:</span>
              <p className="text-gray-900">{songMetadata.tempo} BPM</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Ambiance:</span>
              <p className="text-gray-900">{songMetadata.mood}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interface principale avec onglets */}
      <Tabs value={currentTab} onValueChange={setCurrentTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="editor" className="flex items-center">
            <Music className="w-4 h-4 mr-2" />
            Éditeur Principal
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center">
            <Eye className="w-4 h-4 mr-2" />
            Aperçu
          </TabsTrigger>
          <TabsTrigger value="export" className="flex items-center">
            <Download className="w-4 h-4 mr-2" />
            Export & Partage
          </TabsTrigger>
        </TabsList>

        {/* Onglet Éditeur */}
        <TabsContent value="editor" className="space-y-4">
          <LyricsChordWorkflow
            lyricsContent={lyricsContent}
            handleLyricsChange={handleLyricsChange}
            quillRef={quillRef}
            formControl={{} as any} // Simulation du form control
            aiConfig={aiConfig}
            aiGeneralPrompt={aiGeneralPrompt}
            addAiHistory={addAiHistory}
            aiHistory={aiHistory}
            showAiHistory={showAiHistory}
            setShowAiHistory={setShowAiHistory}
            chords={chords}
            onChordsChange={handleChordsChange}
            onSave={handleSave}
            autoSave={true}
            songMetadata={songMetadata}
          />
        </TabsContent>

        {/* Onglet Aperçu */}
        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Aperçu du Morceau</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Paroles ({lyricsContent.length} caractères)</h4>
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg max-h-60 overflow-y-auto">
                  <div dangerouslySetInnerHTML={{ __html: lyricsContent }} />
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  Progression d'Accords ({chords.length} accords)
                </h4>
                {chords.length === 0 ? (
                  <p className="text-gray-600 italic">Aucun accord ajouté pour le moment</p>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {chords.map((chord, index) => (
                      <span
                        key={chord.id}
                        className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                      >
                        {index + 1}. {chord.chord.chord}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Button onClick={handlePreview} className="flex items-center">
                  <Play className="w-4 h-4 mr-2" />
                  Générer Aperçu
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Export */}
        <TabsContent value="export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Export et Partage</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button onClick={handleExport} className="flex items-center justify-center">
                  <Download className="w-4 h-4 mr-2" />
                  Exporter JSON
                </Button>
                
                <Button variant="outline" className="flex items-center justify-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Exporter PDF
                </Button>
                
                <Button variant="outline" className="flex items-center justify-center">
                  <Music className="w-4 h-4 mr-2" />
                  Exporter MIDI
                </Button>
                
                <Button variant="outline" className="flex items-center justify-center">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Partager Lien
                </Button>
              </div>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">Statistiques</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-blue-700">Caractères:</span>
                    <p className="font-medium text-blue-900">{lyricsContent.length}</p>
                  </div>
                  <div>
                    <span className="text-blue-700">Mots:</span>
                    <p className="font-medium text-blue-900">
                      {lyricsContent.split(/\s+/).filter(w => w.length > 0).length}
                    </p>
                  </div>
                  <div>
                    <span className="text-blue-700">Accords:</span>
                    <p className="font-medium text-blue-900">{chords.length}</p>
                  </div>
                  <div>
                    <span className="text-blue-700">Historique IA:</span>
                    <p className="font-medium text-blue-900">{aiHistory.length}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Actions globales */}
      <div className="flex items-center justify-center space-x-4 pt-6 border-t border-gray-200">
        <Button 
          onClick={() => handleSave(lyricsContent, chords)}
          className="flex items-center"
        >
          <Save className="w-4 h-4 mr-2" />
          Sauvegarder Tout
        </Button>
        
        <Button 
          variant="outline"
          onClick={() => {
            setLyricsContent('');
            setChords([]);
            setAiHistory([]);
            toast({ title: "Réinitialisé", description: "Tous les contenus ont été effacés." });
          }}
          className="flex items-center"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          Réinitialiser
        </Button>
      </div>
    </div>
  );
};
