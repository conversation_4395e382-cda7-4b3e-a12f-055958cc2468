# Schéma de Base de Données - Module Analytique

Ce document décrit les tables et fonctions SQL liées au système d'analyse et de statistiques de la plateforme Mouvik.

## Tables

### Table `plays`

Enregistre chaque écoute d'un morceau par un utilisateur.

| Colonne | Type | Description |
|---------|------|-------------|
| id | UUID | Identifiant unique de l'écoute (clé primaire) |
| song_id | UUID | Référence au morceau écouté (clé étrangère vers songs.id) |
| user_id | UUID | Utilisateur qui a écouté le morceau (clé étrangère vers auth.users.id) |
| created_at | TIMESTAMP | Date et heure de l'écoute |
| duration | INTEGER | Durée de l'écoute en secondes |
| completed | BOOLEAN | Indique si l'écoute a été complétée |
| source | TEXT | Source de l'écoute (playlist, album, recherche, etc.) |
| device_type | TEXT | Type d'appareil utilisé (mobile, desktop, etc.) |

```sql
CREATE TABLE plays (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  song_id UUID REFERENCES songs(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  duration INTEGER,
  completed BOOLEAN DEFAULT FALSE,
  source TEXT,
  device_type TEXT
);

CREATE INDEX plays_song_id_idx ON plays(song_id);
CREATE INDEX plays_user_id_idx ON plays(user_id);
CREATE INDEX plays_created_at_idx ON plays(created_at);
```

### Table `audio_analysis`

Stocke les caractéristiques audio des morceaux pour l'analyse et la visualisation.

| Colonne | Type | Description |
|---------|------|-------------|
| id | UUID | Identifiant unique (clé primaire) |
| song_id | UUID | Référence au morceau (clé étrangère vers songs.id) |
| energy | FLOAT | Niveau d'énergie du morceau (0.0 à 1.0) |
| danceability | FLOAT | Niveau de dansabilité (0.0 à 1.0) |
| valence | FLOAT | Positivité émotionnelle (0.0 à 1.0) |
| acousticness | FLOAT | Caractère acoustique (0.0 à 1.0) |
| instrumentalness | FLOAT | Caractère instrumental (0.0 à 1.0) |
| speechiness | FLOAT | Présence vocale (0.0 à 1.0) |
| liveness | FLOAT | Caractère live (0.0 à 1.0) |
| created_at | TIMESTAMP | Date de création de l'analyse |
| updated_at | TIMESTAMP | Date de mise à jour de l'analyse |

```sql
CREATE TABLE audio_analysis (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  song_id UUID REFERENCES songs(id) ON DELETE CASCADE UNIQUE NOT NULL,
  energy FLOAT CHECK (energy BETWEEN 0 AND 1),
  danceability FLOAT CHECK (danceability BETWEEN 0 AND 1),
  valence FLOAT CHECK (valence BETWEEN 0 AND 1),
  acousticness FLOAT CHECK (acousticness BETWEEN 0 AND 1),
  instrumentalness FLOAT CHECK (instrumentalness BETWEEN 0 AND 1),
  speechiness FLOAT CHECK (speechiness BETWEEN 0 AND 1),
  liveness FLOAT CHECK (liveness BETWEEN 0 AND 1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX audio_analysis_song_id_idx ON audio_analysis(song_id);
```

### Table `audience_demographics`

Stocke les données démographiques agrégées des auditeurs pour chaque artiste.

| Colonne | Type | Description |
|---------|------|-------------|
| id | UUID | Identifiant unique (clé primaire) |
| artist_id | UUID | Référence à l'artiste (clé étrangère vers auth.users.id) |
| age_group | TEXT | Groupe d'âge (18-24, 25-34, etc.) |
| gender | TEXT | Genre (homme, femme, non-binaire, etc.) |
| country | TEXT | Pays de l'auditeur |
| device_type | TEXT | Type d'appareil utilisé |
| count | INTEGER | Nombre d'auditeurs dans ce segment |
| last_updated | TIMESTAMP | Date de dernière mise à jour |

```sql
CREATE TABLE audience_demographics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  age_group TEXT,
  gender TEXT,
  country TEXT,
  device_type TEXT,
  count INTEGER NOT NULL DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(artist_id, age_group, gender, country, device_type)
);

CREATE INDEX audience_demographics_artist_id_idx ON audience_demographics(artist_id);
```

## Fonctions SQL

### 1. Vue d'ensemble des statistiques

```sql
CREATE OR REPLACE FUNCTION get_user_overview_stats(p_user_id UUID, p_time_range TEXT)
RETURNS JSON AS $$
DECLARE
  result JSON;
  current_period_start TIMESTAMP;
  previous_period_start TIMESTAMP;
BEGIN
  -- Déterminer les périodes en fonction du range
  IF p_time_range = '7d' THEN
    current_period_start := NOW() - INTERVAL '7 days';
    previous_period_start := current_period_start - INTERVAL '7 days';
  ELSIF p_time_range = '30d' THEN
    current_period_start := NOW() - INTERVAL '30 days';
    previous_period_start := current_period_start - INTERVAL '30 days';
  ELSIF p_time_range = '90d' THEN
    current_period_start := NOW() - INTERVAL '90 days';
    previous_period_start := current_period_start - INTERVAL '90 days';
  ELSE -- '1y' par défaut
    current_period_start := NOW() - INTERVAL '1 year';
    previous_period_start := current_period_start - INTERVAL '1 year';
  END IF;

  -- Calculer les statistiques
  WITH current_stats AS (
    SELECT
      COUNT(p.id) AS total_plays,
      COUNT(DISTINCT p.user_id) AS unique_listeners,
      COUNT(DISTINCT c.id) AS total_comments,
      COUNT(DISTINCT lk.id) AS total_likes
    FROM songs s
    LEFT JOIN plays p ON s.id = p.song_id AND p.created_at >= current_period_start
    LEFT JOIN comments c ON s.id = c.resource_id AND c.resource_type = 'song' AND c.created_at >= current_period_start
    LEFT JOIN likes lk ON s.id = lk.resource_id AND lk.resource_type = 'song' AND lk.created_at >= current_period_start
    WHERE s.user_id = p_user_id
  ),
  previous_stats AS (
    SELECT
      COUNT(p.id) AS total_plays,
      COUNT(DISTINCT p.user_id) AS unique_listeners,
      COUNT(DISTINCT c.id) AS total_comments,
      COUNT(DISTINCT lk.id) AS total_likes
    FROM songs s
    LEFT JOIN plays p ON s.id = p.song_id AND p.created_at BETWEEN previous_period_start AND current_period_start
    LEFT JOIN comments c ON s.id = c.resource_id AND c.resource_type = 'song' AND c.created_at BETWEEN previous_period_start AND current_period_start
    LEFT JOIN likes lk ON s.id = lk.resource_id AND lk.resource_type = 'song' AND lk.created_at BETWEEN previous_period_start AND current_period_start
    WHERE s.user_id = p_user_id
  )
  
  -- Calculer les variations et retourner le résultat
  SELECT json_build_object(
    'total_plays', cs.total_plays,
    'plays_change', CASE WHEN ps.total_plays = 0 THEN 100 ELSE ((cs.total_plays - ps.total_plays) * 100.0 / NULLIF(ps.total_plays, 0)) END,
    'unique_listeners', cs.unique_listeners,
    'listeners_change', CASE WHEN ps.unique_listeners = 0 THEN 100 ELSE ((cs.unique_listeners - ps.unique_listeners) * 100.0 / NULLIF(ps.unique_listeners, 0)) END,
    'total_comments', cs.total_comments,
    'comments_change', CASE WHEN ps.total_comments = 0 THEN 100 ELSE ((cs.total_comments - ps.total_comments) * 100.0 / NULLIF(ps.total_comments, 0)) END,
    'total_likes', cs.total_likes,
    'likes_change', CASE WHEN ps.total_likes = 0 THEN 100 ELSE ((cs.total_likes - ps.total_likes) * 100.0 / NULLIF(ps.total_likes, 0)) END,
    'engagement_rate', CASE WHEN cs.total_plays = 0 THEN 0 ELSE ((cs.total_likes + cs.total_comments) * 100.0 / NULLIF(cs.total_plays, 0)) END,
    'engagement_change', CASE 
      WHEN ps.total_plays = 0 OR (ps.total_likes + ps.total_comments) = 0 THEN 100 
      ELSE (
        ((cs.total_likes + cs.total_comments) * 100.0 / NULLIF(cs.total_plays, 0)) - 
        ((ps.total_likes + ps.total_comments) * 100.0 / NULLIF(ps.total_plays, 0))
      ) 
    END
  ) INTO result
  FROM current_stats cs, previous_stats ps;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;
```

### 2. Timeline d'activité

```sql
CREATE OR REPLACE FUNCTION get_activity_timeline(p_user_id UUID, p_metrics TEXT[], p_interval TEXT, p_days INTEGER)
RETURNS JSON AS $$
DECLARE
  result JSON;
  start_date TIMESTAMP;
BEGIN
  start_date := NOW() - (p_days || ' days')::INTERVAL;
  
  WITH dates AS (
    SELECT generate_series(
      date_trunc(p_interval, start_date),
      date_trunc(p_interval, NOW()),
      (1 || ' ' || p_interval)::INTERVAL
    ) AS date
  ),
  plays_data AS (
    SELECT 
      date_trunc(p_interval, p.created_at) AS date,
      COUNT(p.id) AS plays
    FROM plays p
    JOIN songs s ON p.song_id = s.id
    WHERE s.user_id = p_user_id AND p.created_at >= start_date
    GROUP BY 1
  ),
  likes_data AS (
    SELECT 
      date_trunc(p_interval, l.created_at) AS date,
      COUNT(l.id) AS likes
    FROM likes l
    JOIN songs s ON l.resource_id = s.id AND l.resource_type = 'song'
    WHERE s.user_id = p_user_id AND l.created_at >= start_date
    GROUP BY 1
  ),
  comments_data AS (
    SELECT 
      date_trunc(p_interval, c.created_at) AS date,
      COUNT(c.id) AS comments
    FROM comments c
    JOIN songs s ON c.resource_id = s.id AND c.resource_type = 'song'
    WHERE s.user_id = p_user_id AND c.created_at >= start_date
    GROUP BY 1
  )
  
  SELECT json_agg(
    json_build_object(
      'date', d.date,
      'plays', COALESCE(pd.plays, 0),
      'likes', COALESCE(ld.likes, 0),
      'comments', COALESCE(cd.comments, 0)
    )
    ORDER BY d.date
  ) INTO result
  FROM dates d
  LEFT JOIN plays_data pd ON d.date = pd.date
  LEFT JOIN likes_data ld ON d.date = ld.date
  LEFT JOIN comments_data cd ON d.date = cd.date;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;
```

## Déclencheurs (Triggers)

### Mise à jour du compteur d'écoutes

```sql
CREATE OR REPLACE FUNCTION update_song_plays()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE songs
  SET plays = plays + 1
  WHERE id = NEW.song_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER after_play_insert
AFTER INSERT ON plays
FOR EACH ROW
EXECUTE FUNCTION update_song_plays();
```

### Mise à jour des statistiques d'audience

```sql
CREATE OR REPLACE FUNCTION update_audience_demographics()
RETURNS TRIGGER AS $$
DECLARE
  v_artist_id UUID;
  v_age_group TEXT;
  v_gender TEXT;
  v_country TEXT;
  v_device_type TEXT;
BEGIN
  -- Récupérer l'ID de l'artiste à partir du morceau
  SELECT user_id INTO v_artist_id FROM songs WHERE id = NEW.song_id;
  
  -- Récupérer les données démographiques de l'utilisateur qui a écouté
  SELECT 
    CASE 
      WHEN age < 18 THEN '<18'
      WHEN age BETWEEN 18 AND 24 THEN '18-24'
      WHEN age BETWEEN 25 AND 34 THEN '25-34'
      WHEN age BETWEEN 35 AND 44 THEN '35-44'
      WHEN age BETWEEN 45 AND 54 THEN '45-54'
      ELSE '55+'
    END,
    gender,
    country
  INTO v_age_group, v_gender, v_country
  FROM profiles
  WHERE id = NEW.user_id;
  
  v_device_type := NEW.device_type;
  
  -- Mettre à jour les statistiques démographiques
  INSERT INTO audience_demographics (
    artist_id, age_group, gender, country, device_type, count, last_updated
  ) VALUES (
    v_artist_id, v_age_group, v_gender, v_country, v_device_type, 1, NOW()
  )
  ON CONFLICT (artist_id, age_group, gender, country, device_type)
  DO UPDATE SET 
    count = audience_demographics.count + 1,
    last_updated = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER after_play_insert_demographics
AFTER INSERT ON plays
FOR EACH ROW
EXECUTE FUNCTION update_audience_demographics();
```
