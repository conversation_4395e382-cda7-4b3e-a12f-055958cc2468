# Fiche technique de référence — Page "Créer une chanson"

## Objectif
Ce document sert de mémo technique pour la page de création de chanson (`/app/(authenticated)/songs/create/page.tsx`). Il liste tous les champs de formulaire, constantes, structures, hooks, composants, et logiques clés pour faciliter l’évolution et la maintenance.

---

## Système de Versionning (Song Vault)
Une fonctionnalité clé du nouveau formulaire est l'intégration d'un système de versionning via le composant `SongVault`.

- **Sauvegarde de versions**: Les utilisateurs peuvent sauvegarder l'état actuel du formulaire comme une nouvelle version, en lui donnant un nom et des notes optionnelles. Cela est géré par une modale (`Dialog`).
- **Chargement de versions**: Les versions existantes peuvent être listées et chargées dans le formulaire, remplaçant les données actuelles. La fonction `handleLoadVersion` utilise un appel RPC Supabase (`rpc_load_song_version_data`) pour récupérer les données de la version.
- **Suppression de versions**: Les versions peuvent être supprimées après confirmation. La fonction `handleDeleteVersion` utilise un appel RPC Supabase (`rpc_delete_song_version`).
- **Interface**: Le `SongVault` est accessible via un panneau `Collapsible`.
- **Données de version**: Chaque version stocke un instantané des champs du formulaire (`LocalSongVersion`).

## Gestion des Accords et de la Structure
- **Champ `chords`**: Un champ texte (`chords: z.string().nullable().optional()`) est dédié au stockage des accords. Le format exact (JSON, texte simple, etc.) est flexible mais la fonction `parseChordString` tente actuellement de parser du JSON ou des chaînes d'accords séparées par des espaces.
- **Champ `structure`**: Un champ texte (`structure: z.string().nullable().optional()`) permet de définir la structure de la chanson (ex: couplet, refrain). Le format attendu est probablement du JSON.
- **Édition**: Des composants placeholders (`StructureEditor`, `SectionTypeSelector`) suggèrent une future interface dédiée à l'édition de la structure.
- **Diagrammes d'accords**: Le type `ChordInstrument` est un placeholder indiquant une intention future de gérer des diagrammes d'accords pour différents instruments, mais n'est pas encore implémenté dans la logique de parsing ou d'affichage actuelle.

## Autres Changements Notables
- **Statistiques**: Ajout d'un champ `plays` pour suivre le nombre d'écoutes.
- **Contributeurs**: Le champ `contributors` permet une gestion plus structurée des personnes ayant participé à la création, avec leur nom et rôle.
- **Thèmes**: Ajout d'un champ `themes` pour mieux catégoriser les chansons.
- **Notifications**: Utilisation de `sonner` pour les toasts, offrant une expérience utilisateur moderne pour les notifications.

---

## Schéma de validation (zod) - Mis à jour
```ts
export const songSchema = z.object({
  id: z.string().uuid().optional(),
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist_name: z.string().min(1, { message: "Le nom de l'artiste est requis." }),
  featured_artists: z.array(z.string()).optional(), // Artistes en featuring
  album_id: z.string().uuid().nullable().optional(),
  duration: z.number().int().min(0).nullable().optional(), // Durée en secondes
  bpm: z.number().int().min(0).max(400).nullable().optional(), // Battements par minute
  key: z.string().trim().nullable().optional(), // Tonalité
  time_signature: z.string().regex(/^(\d+\/\d+)$|^$/, { message: "Signature rythmique invalide (ex: 4/4)" }).nullable().optional(), 
  capo: z.number().int().min(0).nullable().optional(), // Position du capodastre
  tuning_frequency: z.number().positive().nullable().optional(), // Fréquence d'accordage (ex: 440Hz)
  genres: z.array(z.string().trim().min(1)).default([]), // Liste des genres
  subgenre: z.array(z.string()).optional(), // Liste des sous-genres
  moods: z.array(z.string().trim().min(1)).default([]), // Liste des ambiances
  themes: z.array(z.string().trim().min(1)).default([]), // Liste des thèmes
  instruments: z.array(z.string().trim().min(1)).default([]), // Liste des instruments
  tags: z.array(z.string().trim().min(1)).default([]), // Liste des tags
  lyrics: z.string().nullable().optional(),
  lyrics_language: z.string().trim().nullable().optional(), // Langue des paroles
  description: z.string().nullable().optional(), // Description/Notes publiques
  composer_name: z.string().nullable().optional(), // Nom du compositeur
  writers: z.array(z.string().trim().min(1)).default([]), // Liste des auteurs
  producers: z.array(z.string().trim().min(1)).default([]), // Liste des producteurs
  bloc_note: z.string().nullable().optional(), // Notes internes (non visibles publiquement)
  right_column_notepad: z.string().nullable().optional(), // Bloc-notes colonne de droite (usage spécifique UI)
  status: z.string().nullable().optional(), // Statut de la chanson (ex: draft, completed)
  progress_data: z.any().optional(), // Données de progression (format libre)
  slug: z.string().trim().nullable().optional(), // Slug pour URL conviviale
  attribution_type: z.enum(['user', 'band']).nullable().optional(), // Type d'attribution (artiste solo ou groupe)
  custom_css: z.string().nullable().optional(), // CSS personnalisé
  chords: z.string().nullable().optional(), // Accords (format texte ou JSON)
  structure: z.string().nullable().optional(), // Structure de la chanson (format texte ou JSON)
  notes: z.string().nullable().optional(), // Notes additionnelles (potentiellement redondant avec description ou bloc_note, à clarifier)
  is_public: z.boolean().default(false), // Visibilité publique
  is_favorite: z.boolean().default(false), // Marqué comme favori
  is_incomplete: z.boolean().default(true), // Marqué comme incomplet
  is_cover: z.boolean().default(false), // Est une reprise
  is_instrumental: z.boolean().default(false), // Est instrumental
  is_explicit: z.boolean().default(false), // Contenu explicite
  is_archived: z.boolean().default(false).optional(), // Archivé (soft delete)
  release_date: z.date().nullable().optional(), // Date de sortie
  audio_url: z.string().url({ message: "URL audio invalide." }).nullable().optional(),
  cover_art_url: z.string().url({ message: "URL de la pochette invalide." }).nullable().optional(),
  creator_user_id: z.string().uuid({ message: "ID créateur invalide." }),
  allow_comments: z.boolean().default(true).optional(), // Autoriser les commentaires
  allow_downloads: z.boolean().default(false).optional(), // Autoriser les téléchargements
  band_id: z.string().uuid().nullable().optional(), // ID du groupe associé
  contributors: z.array(z.object({
    id: z.string().uuid(), 
    name: z.string().min(1, { message: "Nom du contributeur requis."}),
    role: z.string().min(1, { message: "Rôle du contributeur requis."}),
  })).default([]),
  plays: z.number().int().min(0).default(0) // Nombre d'écoutes
});
```

## Champs principaux du formulaire - Mis à jour
- **id**: Identifiant unique de la chanson (UUID, optionnel à la création).
- **title**: Titre de la chanson (obligatoire).
- **artist_name**: Nom de l'artiste principal (obligatoire).
- **featured_artists**: Artistes en featuring (tableau de chaînes, optionnel).
- **album_id**: ID de l'album associé (UUID, nullable, optionnel).
- **duration**: Durée en secondes (entier, nullable, optionnel).
- **bpm**: Battements par minute (entier, de 0 à 400, nullable, optionnel).
- **key**: Tonalité (chaîne, nullable, optionnel).
- **time_signature**: Signature rythmique (ex: "4/4", nullable, optionnel).
- **capo**: Position du capodastre (entier, positif, nullable, optionnel).
- **tuning_frequency**: Fréquence d'accordage en Hz (nombre positif, nullable, optionnel).
- **genres**: Liste des genres (tableau de chaînes, initialisé à vide).
- **subgenre**: Liste des sous-genres (tableau de chaînes, optionnel).
- **moods**: Liste des ambiances (tableau de chaînes, initialisé à vide).
- **themes**: Liste des thèmes (tableau de chaînes, initialisé à vide).
- **instruments**: Liste des instruments (tableau de chaînes, initialisé à vide).
- **tags**: Liste des tags (tableau de chaînes, initialisé à vide).
- **lyrics**: Paroles de la chanson (chaîne, nullable, optionnel).
- **lyrics_language**: Langue des paroles (chaîne, nullable, optionnel).
- **description**: Description ou notes publiques sur la chanson (chaîne, nullable, optionnel).
- **composer_name**: Nom du compositeur (chaîne, nullable, optionnel).
- **writers**: Liste des auteurs/paroliers (tableau de chaînes, initialisé à vide).
- **producers**: Liste des producteurs (tableau de chaînes, initialisé à vide).
- **bloc_note**: Notes internes pour l'utilisateur (chaîne, nullable, optionnel).
- **right_column_notepad**: Bloc-notes spécifique à la colonne de droite dans l'interface (chaîne, nullable, optionnel).
- **status**: Statut de la chanson, ex: 'draft', 'in_progress', 'completed' (chaîne, nullable, optionnel).
- **progress_data**: Données de progression structurées ou non (type `any`, optionnel).
- **slug**: Slug pour l'URL (chaîne, nullable, optionnel).
- **attribution_type**: Type d'attribution, 'user' ou 'band' (chaîne enum, nullable, optionnel).
- **custom_css**: CSS personnalisé pour la page de la chanson (chaîne, nullable, optionnel).
- **chords**: Accords de la chanson (chaîne, nullable, optionnel).
- **structure**: Structure de la chanson, ex: sections (chaîne, nullable, optionnel).
- **notes**: Notes additionnelles (chaîne, nullable, optionnel) - *Ce champ pourrait être redondant avec `description` ou `bloc_note`. À évaluer pour clarification ou suppression.*
- **is_public**: Visibilité publique de la chanson (booléen, défaut `false`).
- **is_favorite**: Marque la chanson comme favorite (booléen, défaut `false`).
- **is_incomplete**: Indique si la chanson est considérée comme incomplète (booléen, défaut `true`).
- **is_cover**: Indique s'il s'agit d'une reprise (booléen, défaut `false`).
- **is_instrumental**: Indique si la chanson est instrumentale (booléen, défaut `false`).
- **is_explicit**: Indique si le contenu est explicite (booléen, défaut `false`).
- **is_archived**: Indique si la chanson est archivée (booléen, défaut `false`, optionnel).
- **release_date**: Date de sortie (date, nullable, optionnel).
- **audio_url**: URL du fichier audio principal (URL valide, nullable, optionnel).
- **cover_art_url**: URL de l'image de pochette (URL valide, nullable, optionnel).
- **creator_user_id**: ID de l'utilisateur créateur (UUID, obligatoire).
- **allow_comments**: Autoriser les commentaires (booléen, défaut `true`, optionnel).
- **allow_downloads**: Autoriser les téléchargements (booléen, défaut `false`, optionnel).
- **band_id**: ID du groupe associé (UUID, nullable, optionnel).
- **contributors**: Liste des contributeurs avec nom et rôle (tableau d'objets, initialisé à vide).
- **plays**: Nombre de lectures/écoutes (entier, défaut `0`).

*Note : La gestion de la visibilité est centralisée par `is_public`. Le champ `status` permet de suivre l'état d'avancement.*

## Constantes et listes utiles
- **`LANGUAGE_OPTIONS`**: Options pour la langue des paroles.
  ```ts
  const LANGUAGE_OPTIONS = [
    { value: 'fr', label: 'Français' },
    { value: 'en', label: 'English' },
    // ... autres langues
  ];
  ```
- **`SONG_STATUS_OPTIONS`**: Options pour le statut de la chanson.
  ```ts
  const SONG_STATUS_OPTIONS = [
    { value: 'draft', label: 'Brouillon' },
    { value: 'in_progress', label: 'En cours' },
    // ... autres statuts
  ];
  ```
- **`ATTRIBUTION_TYPE_OPTIONS`**: Options pour le type d'attribution (droits d'auteur).
  ```ts
  const ATTRIBUTION_TYPE_OPTIONS = [
    { value: 'all_rights_reserved', label: 'Tous droits réservés' },
    { value: 'cc_by', label: 'Creative Commons - Attribution (CC BY)' },
    // ... autres types d'attribution
  ];
  ```
- **`GENRES_OPTIONS`**: Options pour les genres musicaux.
  ```ts
  const GENRES_OPTIONS = [
    { value: 'acoustic', label: 'Acoustique' },
    { value: 'alternative_rock', label: 'Rock Alternatif' },
    // ... autres genres
  ];
  ```
- **`SUBGENRES_OPTIONS`**: Options pour les sous-genres musicaux (avec regroupement).
  ```ts
  const SUBGENRES_OPTIONS = [
    { value: 'hard_rock', label: 'Hard Rock', group: 'Rock' },
    { value: 'techno', label: 'Techno', group: 'Électronique' },
    // ... autres sous-genres
  ];
  ```
- **`MOODS_OPTIONS`**: Options pour les ambiances/moods.
  ```ts
  const MOODS_OPTIONS = [
    { value: 'aggressive', label: 'Agressif' },
    { value: 'calm', label: 'Calme' },
    // ... autres ambiances
  ];
  ```
- **`THEMES_OPTIONS`**: Options pour les thèmes de la chanson.
  ```ts
  const THEMES_OPTIONS = [
    { value: 'love', label: 'Amour' },
    { value: 'nature', label: 'Nature' },
    // ... autres thèmes
  ];
  ```
- **`INSTRUMENTS_OPTIONS`**: Options pour les instruments.
  ```ts
  const INSTRUMENTS_OPTIONS = [
    { value: 'guitar_acoustic', label: 'Guitare acoustique' },
    { value: 'piano', label: 'Piano' },
    // ... autres instruments
  ];
  ```
- **`MUSICAL_KEY_OPTIONS`**: Options pour les tonalités musicales (majeures et mineures).
  ```ts
  const MUSICAL_KEY_OPTIONS = [
    { value: 'C', label: 'C' },
    { value: 'Cm', label: 'Cm' },
    // ... autres tonalités
  ];
  ```
- **`NO_ALBUM_SELECTED_VALUE`**: Valeur spéciale pour indiquer qu'aucun album n'est sélectionné.
- **Constantes de `localStorage` pour l'IA** :
  - `ai_selected_provider_mouvik`
  - `ollama_selected_model_mouvik`
  - `openai_selected_model_mouvik`
  - `openrouter_selected_model_mouvik`
  - `anthropic_selected_model_mouvik`

## Interfaces
- **`Album`** (inchangée, pour référence si utilisée pour `album_id`):
  ```ts
  interface Album {
    id: string;
    title: string;
    // ... autres champs potentiels d'un album
  }
  ```
- **`SongFormValues`** (déduite du nouveau `songSchema`):
  ```ts
  type SongFormValues = z.infer<typeof songSchema>;
  ```
- **`AiConfig`**: Configuration pour les appels à l'IA.
  ```ts
  interface AiConfig {
    provider: string; // ex: 'openai', 'ollama'
    model: string;
    temperature: number;
    // autres paramètres spécifiques à l'IA
  }
  ```
- **`AiHistoryItem`**: Élément de l'historique des interactions IA.
  ```ts
  interface AiHistoryItem {
    role: 'user' | 'assistant';
    content: string;
    timestamp?: string; 
  }
  ```
- **`LocalSongVersion`**: Représente une version sauvegardée d'une chanson (utilisée par `SongVault`).
  ```ts
  interface LocalSongVersion extends Partial<SongFormValues> {
    version_id: string; // ou id
    version_name: string;
    created_at: string;
    version_number?: number; 
    notes?: string; // Notes spécifiques à cette version
  }
  ```
- **`ChordInstrument`** (placeholder pour une future gestion détaillée des diagrammes d'accords):
  ```ts
  type ChordInstrument = {
    name: string; // Nom de l'instrument (ex: 'Guitare', 'Ukulélé')
    // TODO: Définir la structure des diagrammes (positions des frettes, doigtés, etc.)
    diagrams: { fret: number, string: number, finger?: number }[]; 
  };
  ```
- **`LocalFileState`**: Gestion de l'état local pour les fichiers uploadés.
  ```ts
  interface LocalFileState {
    file: FileWithPath | null;
    previewUrl: string | null;
    uploadProgress: number;
    error: string | null;
    isUploading: boolean;
  }
  ```

## Composants clés utilisés - Mis à jour
- **`SongForm`**: Le composant principal encapsulant toute la logique du formulaire.
- **`Tabs` (shadcn/ui)**: Utilisé pour organiser le formulaire en plusieurs sections : "Infos Générales", "Paroles & Accords", "Structure", "Avancé".
- **`Card` (shadcn/ui)**: Utilisé pour structurer visuellement les différentes parties de chaque onglet.
- **`RichLyricsEditor` (placeholder)**: Un composant (actuellement un `Textarea` placeholder) destiné à l'édition des paroles. L'intégration d'un véritable éditeur riche est implicite.
- **`FormField` (react-hook-form & shadcn/ui)**: Utilisé pour chaque champ du formulaire, intégrant label, input, et messages d'erreur.
- **`Input`, `Textarea`, `Select`, `Switch`, `Checkbox`, `DatePicker`, `MultiSelect` (shadcn/ui)**: Composants UI de base pour les champs.
- **`Button` (shadcn/ui)**: Pour les actions (soumettre, annuler, sauvegarder version, etc.).
- **`Collapsible` (shadcn/ui)**: Utilisé pour afficher/masquer le panneau `SongVault`.
- **`SongVault`**: Composant crucial pour la gestion des versions de la chanson. Permet de lister, charger et supprimer des versions. Interagit avec le backend via des appels RPC Supabase.
- **`Dialog` (shadcn/ui)**: Utilisé pour les modales, notamment pour nommer et ajouter des notes lors de la sauvegarde d'une nouvelle version.
- **`ConfirmDeleteVersionModal`**: Modale de confirmation pour la suppression d'une version.
- **Composants IA (placeholders ou concepts)**:
    - **`AiQuickActions` (placeholder)**: Pour des actions IA rapides.
    - **`AiConfigMenu` (placeholder)**: Pour configurer les paramètres IA.
    - **`AiChatInterface` (placeholder)**: Pour une interface de chat avec l'IA.
- **Gestion des fichiers (Drag & Drop)**: Utilisation de `react-dropzone` (implicite via `FileWithPath`) pour la sélection des fichiers audio et de pochette, avec prévisualisation et gestion de l'état de l'upload.
- **`AudioWaveformPreview` (placeholder)**: Prévu pour afficher une prévisualisation de la forme d'onde du fichier audio.

## Configuration Supabase Storage

Liste des buckets de stockage Supabase publics utilisés par l'application :

- `song-covers`
- `song-gallery`
- `song-maquettes`
- `audio-tracks`
- `avatars`
- `album-pdfs`
- `album-gallery`
- `album-covers`
- `covers`
- `audio`
- `profiles`
- `song-audio-vault`
- `profile-headers`
- `playlist-covers`
- `playlist-banners`
- `band-avatars`
- `band-covers`

---

## Hooks et logique
- **`useForm`, `FormProvider`, `useFieldArray`, `Controller` (react-hook-form)**: Gestion complète du formulaire, y compris les champs dynamiques (ex: contributeurs).
- **`zodResolver`**: Pour la validation du formulaire avec le schéma Zod (`songSchema`).
- **`useRouter` (next/navigation)**: Pour la navigation.
- **`useState`, `useEffect`, `useCallback`, `useRef`, `useMemo`, `forwardRef`, `useImperativeHandle` (React)**: Hooks React standards pour la gestion d'état, les effets de bord, la mémoïsation, et l'exposition de fonctions via les refs.
- **`createClientComponentClient` (Supabase)**: Pour interagir avec la base de données Supabase côté client (appels RPC pour le versionning).
- **`toast` (sonner)**: Pour afficher des notifications (remplace l'ancien `useToast`).
- **`useDropzone` (react-dropzone)**: Pour la gestion du téléversement de fichiers par glisser-déposer.
- **`uuidv4`**: Pour générer des identifiants uniques (ex: pour les contributeurs).
- **Logique de versionning**: Fonctions `handleLoadVersion`, `handleDeleteVersion`, `handleSaveNewVersion` utilisant des appels RPC Supabase (`rpc_load_song_version_data`, `rpc_delete_song_version`, et une logique pour sauvegarder les données actuelles comme nouvelle version).
- **Logique de traitement des `initialSongData` (`processedInitialValues`)**: Pour transformer les données initiales (potentiellement issues d'anciennes structures ou de versions) afin qu'elles correspondent au schéma actuel avant de peupler le formulaire.
- **Gestion de l'état local des fichiers**: États pour `localCoverArtFile` et `localAudioFile` (incluant fichier, URL de preview, progression de l'upload, erreurs).

## Points d’architecture - Mis à jour
- **Organisation par Onglets**: Le formulaire est divisé en onglets ("Infos Générales", "Paroles & Accords", "Structure", "Avancé") grâce au composant `Tabs` de shadcn/ui, améliorant la clarté et la navigation.
- **Gestion des versions (Versioning)**: Une fonctionnalité majeure a été ajoutée avec le composant `SongVault`. Elle permet de sauvegarder des instantanés du formulaire, de charger des versions antérieures et de les supprimer. Cette logique s'appuie sur des appels RPC à Supabase.
- **Séparation des données**: Les paroles (`lyrics`), les accords (`chords`), et la structure (`structure`) sont désormais des champs distincts, permettant une gestion plus fine.
- **Gestion des fichiers améliorée**: La sélection et la prévisualisation des fichiers audio et de pochette sont gérées avec des états locaux dédiés (`LocalFileState`) et `react-dropzone`.
- **Traitement des données initiales**: Une logique robuste (`processedInitialValues`) transforme les données entrantes pour assurer la compatibilité avec le schéma actuel du formulaire, gérant les champs hérités ou les variations de type.
- **État de soumission dynamique**: Le statut de soumission (`draft` ou `publish`/`update`) est géré dynamiquement, influençant le libellé des boutons et potentiellement les données envoyées.
- **Intégration IA (Conceptuelle)**: Bien que de nombreux composants IA soient des placeholders, l'architecture prévoit des espaces pour la configuration de l'IA, l'historique des interactions, et des actions rapides, principalement autour de l'édition des paroles.
- **Visibilité publique simplifiée**: La gestion de la visibilité publique est centralisée via un unique champ `is_public` dans le schéma, abandonnant les multiples flags granulaires `is_*_public`.
- **Utilisation de `RHFFormProvider`**: Permet un accès flexible au contexte du formulaire pour les composants enfants.

## Pour évoluer ou déboguer
- **Formulaire principal / État global**: Voir `SongForm.tsx`.
- **Éditeur de paroles / Actions IA sur paroles**: Voir `LyricsEditorWithAI.tsx`.
- **Panneau Assistant IA / Actions IA générales**: Voir `AiAssistantPanel.tsx` et `AiQuickActions.tsx`.
- **Visibilité des champs**: Voir le helper `VisibilityToggle` et les champs `is_*_public` dans `SongForm.tsx`.
- **Schéma de données / Zod**: Vérifier `songFormSchema` dans `SongForm.tsx` et le schéma SQL correspondant.

---

## Système de Versionning (Song Vault)
Une fonctionnalité clé du nouveau formulaire est l'intégration d'un système de versionning via le composant `SongVault`.

- **Sauvegarde de versions**: Les utilisateurs peuvent sauvegarder l'état actuel du formulaire comme une nouvelle version, en lui donnant un nom et des notes optionnelles. Cela est géré par une modale (`Dialog`).
- **Chargement de versions**: Les versions existantes peuvent être listées et chargées dans le formulaire, remplaçant les données actuelles. La fonction `handleLoadVersion` utilise un appel RPC Supabase (`rpc_load_song_version_data`) pour récupérer les données de la version.
- **Suppression de versions**: Les versions peuvent être supprimées après confirmation. La fonction `handleDeleteVersion` utilise un appel RPC Supabase (`rpc_delete_song_version`).
- **Interface**: Le `SongVault` est accessible via un panneau `Collapsible`.
- **Données de version**: Chaque version stocke un instantané des champs du formulaire (`LocalSongVersion`).

## Gestion des Accords et de la Structure
- **Champ `chords`**: Un champ texte (`chords: z.string().nullable().optional()`) est dédié au stockage des accords. Le format exact (JSON, texte simple, etc.) est flexible mais la fonction `parseChordString` tente actuellement de parser du JSON ou des chaînes d'accords séparées par des espaces.
- **Champ `structure`**: Un champ texte (`structure: z.string().nullable().optional()`) permet de définir la structure de la chanson (ex: couplet, refrain). Le format attendu est probablement du JSON.
- **Édition**: Des composants placeholders (`StructureEditor`, `SectionTypeSelector`) suggèrent une future interface dédiée à l'édition de la structure.
- **Diagrammes d'accords**: Le type `ChordInstrument` est un placeholder indiquant une intention future de gérer des diagrammes d'accords pour différents instruments, mais n'est pas encore implémenté dans la logique de parsing ou d'affichage actuelle.

## Autres Changements Notables
- **Statistiques**: Ajout d'un champ `plays` pour suivre le nombre d'écoutes.
- **Contributeurs**: Le champ `contributors` permet une gestion plus structurée des personnes ayant participé à la création, avec leur nom et rôle.
- **Thèmes**: Ajout d'un champ `themes` pour mieux catégoriser les chansons.
- **Notifications**: Utilisation de `sonner` pour les toasts, offrant une expérience utilisateur moderne pour les notifications.

---

*Document généré automatiquement pour servir de référence rapide lors du développement ou de la refonte de la page "Créer une chanson".*
