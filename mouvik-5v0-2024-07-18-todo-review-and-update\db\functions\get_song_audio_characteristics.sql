-- Fonction pour obtenir les caractéristiques audio d'une chanson spécifique
CREATE OR REPLACE FUNCTION get_song_audio_characteristics(p_song_id UUID)
RETURNS TABLE (
  energy FLOAT,
  danceability FLOAT,
  valence FLOAT,
  acousticness FLOAT,
  instrumentalness FLOAT,
  speechiness FLOAT,
  liveness FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    aa.energy,
    aa.danceability,
    aa.valence,
    aa.acousticness,
    aa.instrumentalness,
    aa.speechiness,
    aa.liveness
  FROM
    audio_analysis aa
  WHERE
    aa.song_id = p_song_id
  LIMIT 1; -- S'assurer qu'une seule ligne est retournée si jamais il y avait des doublons
END;
$$;

COMMENT ON FUNCTION get_song_audio_characteristics(UUID) IS 'Récupère les caractéristiques audio (énergie, dansabilité, etc.) pour un morceau spécifique à partir de la table audio_analysis.';

-- Exemple d'appel :
-- SELECT * FROM get_song_audio_characteristics('VOTRE_SONG_ID_ICI');
