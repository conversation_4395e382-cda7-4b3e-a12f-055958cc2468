# API MOUVIK

## Vue d'ensemble

MOUVIK utilise une combinaison d'API REST et de requêtes directes à la base de données via Supabase pour gérer les données. Cette documentation décrit les principales API disponibles dans l'application.

## API Supabase

### Authentification

```typescript
// Inscription d'un utilisateur
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password',
});

// Connexion d'un utilisateur
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password',
});

// Déconnexion
const { error } = await supabase.auth.signOut();

// Récupération de la session
const { data: { session } } = await supabase.auth.getSession();
```

### Gestion des profils

```typescript
// Récupération d'un profil
const { data, error } = await supabase
  .from('profiles')
  .select('*')
  .eq('id', userId)
  .single();

// Mise à jour d'un profil
const { data, error } = await supabase
  .from('profiles')
  .update({
    username: 'newusername',
    display_name: 'New Display Name',
    full_name: 'New Full Name',
    bio: 'New bio details',
    location_city: 'Paris',
    location_country: 'France',
    genres: ['electronic', 'ambient'],
    influences: ['aphex twin', 'boards of canada'],
    tags: ['experimental', 'idm'],
    equipment: 'Synthesizers, Drum Machines',
    // Add other relevant profile fields as needed
  })
  .eq('id', userId);
```

### Gestion des chansons

```typescript
// Récupération des chansons d'un utilisateur
const { data, error } = await supabase
  .from('songs')
  .select('*')
  .eq('user_id', userId)
  .order('created_at', { ascending: false });

// Création d'une chanson
const { data, error } = await supabase
  .from('songs')
  .insert({
    user_id: userId,
    title: 'Song Title',
    description: 'Song Description',
    genre: ['pop', 'electronic'], // Example of array
    moods: ['upbeat', 'energetic'], // Example of array
    instrumentation: ['synth', 'drum machine'], // Example of array
    status: 'draft',
    // Add other relevant song fields as needed
  })
  .select();

// Mise à jour d'une chanson
const { data, error } = await supabase
  .from('songs')
  .update({
    title: 'Updated Title',
    description: 'Updated Description',
  })
  .eq('id', songId);

// Suppression d'une chanson
const { error } = await supabase
  .from('songs')
  .delete()
  .eq('id', songId);
```

### Gestion des albums

```typescript
// Récupération des albums d'un utilisateur
const { data, error } = await supabase
  .from('albums')
  .select('*, album_songs(count)')
  .eq('user_id', userId)
  .order('created_at', { ascending: false })
  .group('id');

// Création d'un album
const { data, error } = await supabase
  .from('albums')
  .insert({
    user_id: userId,
    title: 'Album Title',
    description: 'Album Description',
    genre: ['rock', 'alternative'], // Example of array
    moods: ['introspective'], // Example of array
    instrumentation: ['guitar', 'bass', 'drums'], // Example of array
    status: 'draft',
    // Add other relevant album fields as needed
  })
  .select();

// Ajout de chansons à un album
const { error } = await supabase
  .from('album_songs')
  .insert([
    { album_id: albumId, song_id: songId1, track_number: 1 },
    { album_id: albumId, song_id: songId2, track_number: 2 },
  ]);
```

### Fonctionnalités sociales

```typescript
// Suivre un utilisateur
const { data, error } = await supabase
  .from('follows')
  .insert({
    follower_id: currentUserId,
    following_id: targetUserId,
  });

// Aimer une ressource
const { data, error } = await supabase
  .from('likes')
  .insert({
    user_id: userId,
    resource_type: 'song',
    resource_id: songId,
  });

// Commenter une ressource
const { data, error } = await supabase
  .from('comments')
  .insert({
    user_id: userId,
    resource_type: 'song',
    resource_id: songId,
    content: 'Great song!',
  });
```

### Stockage de fichiers

```typescript
// Téléchargement d'un fichier audio
const { data, error } = await supabase.storage
  .from('songs')
  .upload(`${userId}/${filename}`, file);

// Téléchargement d'une image de couverture (album/song)
const { data, error } = await supabase.storage
  .from('covers') // Or a more generic 'images' bucket
  .upload(`covers/${userId}/${filename}`, file);

// Téléchargement d'un avatar de profil
const { data: avatarData, error: avatarError } = await supabase.storage
  .from('avatars')
  .upload(`${userId}/avatar.png`, avatarFile, { upsert: true });

// Téléchargement d'une bannière de profil
const { data: headerData, error: headerError } = await supabase.storage
  .from('profile-headers')
  .upload(`${userId}/header.png`, headerFile, { upsert: true });

// Récupération d'une URL publique (exemple pour un avatar)
const { data: publicUrlData } = supabase.storage
  .from('avatars')
  .getPublicUrl(`${userId}/avatar.png`);
const avatarPublicUrl = publicUrlData.publicUrl;
```

## API Routes Next.js

### Traitement audio

```
POST /api/audio/waveform
```

Génère une forme d'onde pour un fichier audio.

Paramètres:
- `url`: URL du fichier audio

Réponse:
```json
{
  "waveformUrl": "https://example.com/waveform.json"
}
```

### Génération IA

```
POST /api/ai/generate-lyrics
```

Génère des paroles pour une chanson.

Paramètres:
- `prompt`: Description ou thème pour les paroles
- `style`: Style musical
- `length`: Long
