"use client"

import React, { useRef, useEffect } from "react"

interface WaveformPreviewProps {
  audioUrl: string
  height?: number
  color?: string // Default: '#38bdf8' (cyan-400)
  background?: string // Default: 'transparent'
}

// Utilise l'API Audio pour extraire les samples et dessiner la waveform complète (SoundCloud style)
export function WaveformPreview({ audioUrl, height = 64, color = "#38bdf8", background = "transparent" }: WaveformPreviewProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    if (!audioUrl || !canvasRef.current) return
    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let isMounted = true
    let audioContext: AudioContext | null = null
    let sourceBuffer: AudioBuffer | null = null

    // Fetch and decode audio data
    fetch(audioUrl)
      .then(res => res.arrayBuffer())
      .then(arrayBuffer => {
        audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        return audioContext.decodeAudioData(arrayBuffer)
      })
      .then(buffer => {
        if (!isMounted) return
        sourceBuffer = buffer
        // Draw waveform
        drawWaveform(buffer)
      })
      .catch(console.error)

    function drawWaveform(buffer: AudioBuffer) {
      const width = canvas.width = canvas.offsetWidth
      const h = canvas.height = height
      if (!ctx) return;
      ctx.clearRect(0, 0, width, h)
      ctx.fillStyle = background
      ctx.fillRect(0, 0, width, h)

      // Use only the first channel for simplicity
      const data = buffer.getChannelData(0)
      const step = Math.ceil(data.length / width)
      const amp = h / 2
      if (!ctx) return;
      ctx.strokeStyle = color
      ctx.lineWidth = 2
      ctx.beginPath()
      for (let i = 0; i < width; i++) {
        let min = 1.0, max = -1.0
        for (let j = 0; j < step; j++) {
          const datum = data[(i * step) + j] || 0
          if (datum < min) min = datum
          if (datum > max) max = datum
        }
        if (!ctx) return;
        ctx.moveTo(i, (1 + min) * amp)
        ctx.lineTo(i, (1 + max) * amp)
      }
      if (!ctx) return;
      ctx.stroke()
    }

    return () => {
      isMounted = false
      if (audioContext) audioContext.close()
    }
  }, [audioUrl, height, color, background])

  return (
    <canvas
      ref={canvasRef}
      className="w-full bg-transparent rounded-lg shadow-md"
      style={{ height }}
    />
  )
}
