import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/hooks/use-toast';

// Types pour la configuration IA
export interface AIConfig {
  provider: 'ollama' | 'openai' | 'openrouter' | 'anthropic';
  model: string;
  temperature: number;
  maxTokens: number;
  apiKey?: string;
}

// Configuration par défaut
const DEFAULT_AI_CONFIG: AIConfig = {
  provider: 'ollama',
  model: 'llama3.2',
  temperature: 0.7,
  maxTokens: 2000
};

// Clés de stockage local
const STORAGE_KEYS = {
  provider: 'ai_selected_provider_mouvik',
  ollamaModel: 'ollama_selected_model_mouvik',
  openaiModel: 'openai_selected_model_mouvik',
  openaiKey: 'openai_api_key_mouvik',
  openrouterModel: 'openrouter_selected_model_mouvik',
  openrouterKey: 'openrouter_api_key_mouvik',
  anthropicModel: 'anthropic_selected_model_mouvik',
  anthropicKey: 'anthropic_api_key_mouvik',
  temperature: 'ai_temperature_mouvik',
  maxTokens: 'ai_max_tokens_mouvik'
};

// Hook pour la gestion de la configuration IA
export const useAIComposerConfig = () => {
  const [config, setConfig] = useState<AIConfig>(DEFAULT_AI_CONFIG);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConfigured, setIsConfigured] = useState(false);

  // Charger la configuration depuis le localStorage
  useEffect(() => {
    const loadConfig = () => {
      try {
        const provider = (localStorage.getItem(STORAGE_KEYS.provider) as AIConfig['provider']) || 'ollama';
        const temperature = parseFloat(localStorage.getItem(STORAGE_KEYS.temperature) || '0.7');
        const maxTokens = parseInt(localStorage.getItem(STORAGE_KEYS.maxTokens) || '2000');
        
        let model = '';
        let apiKey = '';
        
        switch (provider) {
          case 'ollama':
            model = localStorage.getItem(STORAGE_KEYS.ollamaModel) || 'llama3.2';
            break;
          case 'openai':
            model = localStorage.getItem(STORAGE_KEYS.openaiModel) || 'gpt-4-turbo';
            apiKey = localStorage.getItem(STORAGE_KEYS.openaiKey) || '';
            break;
          case 'openrouter':
            model = localStorage.getItem(STORAGE_KEYS.openrouterModel) || 'openai/gpt-4-turbo';
            apiKey = localStorage.getItem(STORAGE_KEYS.openrouterKey) || '';
            break;
          case 'anthropic':
            model = localStorage.getItem(STORAGE_KEYS.anthropicModel) || 'claude-3-sonnet-20240229';
            apiKey = localStorage.getItem(STORAGE_KEYS.anthropicKey) || '';
            break;
        }
        
        const newConfig: AIConfig = {
          provider,
          model,
          temperature,
          maxTokens,
          apiKey
        };
        
        setConfig(newConfig);
        setIsConfigured(provider === 'ollama' || (apiKey.length > 0));
      } catch (error) {
        console.error('Erreur lors du chargement de la configuration IA:', error);
        setError('Erreur lors du chargement de la configuration');
      }
    };
    
    loadConfig();
  }, []);

  // Sauvegarder la configuration
  const saveConfig = useCallback((newConfig: Partial<AIConfig>) => {
    try {
      const updatedConfig = { ...config, ...newConfig };
      
      if (newConfig.provider) {
        localStorage.setItem(STORAGE_KEYS.provider, newConfig.provider);
      }
      
      if (newConfig.model) {
        const modelKey = `${updatedConfig.provider}Model` as keyof typeof STORAGE_KEYS;
        if (STORAGE_KEYS[modelKey]) {
          localStorage.setItem(STORAGE_KEYS[modelKey], newConfig.model);
        }
      }
      
      if (newConfig.apiKey !== undefined) {
        const keyKey = `${updatedConfig.provider}Key` as keyof typeof STORAGE_KEYS;
        if (STORAGE_KEYS[keyKey]) {
          localStorage.setItem(STORAGE_KEYS[keyKey], newConfig.apiKey);
        }
      }
      
      if (newConfig.temperature !== undefined) {
        localStorage.setItem(STORAGE_KEYS.temperature, newConfig.temperature.toString());
      }
      
      if (newConfig.maxTokens !== undefined) {
        localStorage.setItem(STORAGE_KEYS.maxTokens, newConfig.maxTokens.toString());
      }
      
      setConfig(updatedConfig);
      setIsConfigured(Boolean(updatedConfig.provider === 'ollama' || (updatedConfig.apiKey && updatedConfig.apiKey.length > 0)));
      setError(null);
      
      toast({
        title: 'Configuration sauvegardée',
        description: 'La configuration IA a été mise à jour avec succès.'
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      setError('Erreur lors de la sauvegarde de la configuration');
      toast({
        title: 'Erreur',
        description: 'Impossible de sauvegarder la configuration.',
        variant: 'destructive'
      });
    }
  }, [config]);

  // Appel API générique
  const callAI = useCallback(async (prompt: string, systemPrompt?: string): Promise<string> => {
    if (!isConfigured) {
      throw new Error('Configuration IA non configurée');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      let response: string;
      
      switch (config.provider) {
        case 'ollama':
          response = await callOllama(prompt, systemPrompt);
          break;
        case 'openai':
          response = await callOpenAI(prompt, systemPrompt);
          break;
        case 'openrouter':
          response = await callOpenRouter(prompt, systemPrompt);
          break;
        case 'anthropic':
          response = await callAnthropic(prompt, systemPrompt);
          break;
        default:
          throw new Error(`Provider non supporté: ${config.provider}`);
      }
      
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [config, isConfigured]);

  // Appel Ollama
  const callOllama = async (prompt: string, systemPrompt?: string): Promise<string> => {
    const response = await fetch('http://localhost:11434/api/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: config.model,
        prompt: systemPrompt ? `${systemPrompt}\n\n${prompt}` : prompt,
        stream: false,
        options: {
          temperature: config.temperature,
          num_predict: config.maxTokens
        }
      })
    });
    
    if (!response.ok) {
      throw new Error(`Erreur Ollama: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.response;
  };

  // Appel OpenAI
  const callOpenAI = async (prompt: string, systemPrompt?: string): Promise<string> => {
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    messages.push({ role: 'user', content: prompt });
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify({
        model: config.model,
        messages,
        temperature: config.temperature,
        max_tokens: config.maxTokens
      })
    });
    
    if (!response.ok) {
      throw new Error(`Erreur OpenAI: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
  };

  // Appel OpenRouter
  const callOpenRouter = async (prompt: string, systemPrompt?: string): Promise<string> => {
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    messages.push({ role: 'user', content: prompt });
    
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'MOUVIK AI Composer'
      },
      body: JSON.stringify({
        model: config.model,
        messages,
        temperature: config.temperature,
        max_tokens: config.maxTokens
      })
    });
    
    if (!response.ok) {
      throw new Error(`Erreur OpenRouter: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.choices[0].message.content;
  };

  // Appel Anthropic
  const callAnthropic = async (prompt: string, systemPrompt?: string): Promise<string> => {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey!,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: config.model,
        max_tokens: config.maxTokens,
        temperature: config.temperature,
        system: systemPrompt,
        messages: [{ role: 'user', content: prompt }]
      })
    });
    
    if (!response.ok) {
      throw new Error(`Erreur Anthropic: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.content[0].text;
  };

  return {
    config,
    saveConfig,
    callAI,
    isLoading,
    error,
    isConfigured
  };
};