import { createSupabaseServerClient } from "@/lib/supabase/server";
import { notFound } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
// Card import is not directly used by PublicPlaylistPage, but by PlaylistSongItemClient
// import { Card } from "@/components/ui/card"; 
import { ListMusic, Music, Play, Edit3, Share2, Heart, Clock, UserCircle2, ThumbsDown, Rss as FollowIcon } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { ResourceViewTracker } from "@/components/stats/resource-view-tracker";
import { LikeButton } from "@/components/social/like-button";
import { DislikeButton } from "@/components/social/dislike-button";
import { FollowPlaylistButton } from "@/components/social/follow-playlist-button";
import { PlayPlaylistButton } from "@/components/playlists/play-playlist-button"; 
import { CommentSection } from '@/components/comments/comment-section'; 
import { SharePopover } from '@/components/shared/share-popover'; 
import Image from "next/image"; 
import { ResourceHeaderBanner } from "@/components/shared/resource-header-banner"; 
import { ResourceStatsDisplay } from '@/components/shared/ResourceStatsDisplay'; // Added
// AudioSliderPlayer, useAudio, usePlaySong are now used in PlaylistSongItemClient
// import { AudioSliderPlayer } from '@/components/audio-slider-player';
// import { useAudio } from '@/contexts/audio-context'; 
// import { usePlaySong } from '@/hooks/use-play-song'; 
import { formatDuration as formatDurationUtil } from '@/lib/utils'; 
import type { Song } from "@/types"; 
import { PlaylistSongItemClient } from '@/components/playlists/playlist-song-item-client'; // Import the new client component

// Interface for PlaylistSong is now defined in PlaylistSongItemClient or a shared types file if used elsewhere
// For clarity, keep a version here if it's specific to the data shape from getPlaylistData
interface PlaylistSongForPage {
  id: string;
  title: string;
  duration: number | null;
  cover_url: string | null;
  audio_url?: string | null; 
  user_id?: string; 
  artist_name?: string; 
  profiles: {
    username: string | null;
    display_name: string | null;
  } | null;
  slug?: string | null; 
}

interface PublicPlaylistDetails {
  id: string;
  name: string;
  description: string | null;
  is_public: boolean;
  cover_url: string | null;
  user_id: string; 
  created_at: string;
  updated_at: string;
  like_count?: number;
  dislike_count?: number;
  follower_count?: number;
  plays?: number; 
  slug: string | null;
  banner_url?: string | null; 
  genres?: string[] | null; 
  moods?: string[] | null; 
  instrumentation?: string[] | null; 
  are_comments_public?: boolean; 
  profiles: {
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
  } | null;
  playlist_songs: { songs: (PlaylistSongForPage & { slug?: string | null; audio_url?: string | null; user_id?: string; artist_name?: string; }) | null }[]; 
}

async function getPlaylistData(slug: string, supabaseClient: any): Promise<PublicPlaylistDetails | null> {
  const { data: playlistDataResult, error: playlistError } = await supabaseClient
    .from("playlists")
    .select(`
      id, name, description, is_public, cover_url, user_id, created_at, updated_at, slug,
      like_count, dislike_count, follower_count, plays,
      banner_url, genres, moods, instrumentation, are_comments_public,
      profiles:user_id (username, display_name, avatar_url),
      playlist_songs:playlist_songs_view(
        song_id,
        songs:songs (id, title, duration, cover_url, audio_url, user_id, slug, profiles:user_id (username, display_name)) 
      )
    `)
    .eq("slug", slug)
    .eq("is_public", true)
    .single();
  
  if (playlistError || !playlistDataResult) {
    console.error("Error fetching public playlist by slug:", slug, playlistError);
    return null;
  }
  return playlistDataResult as PublicPlaylistDetails;
}

// Removed local PlaylistSongItem definition

export default async function PublicPlaylistPage({ params }: { params: { slug: string } }) {
  const supabase = createSupabaseServerClient(); 
  const { data: { user: currentUser } } = await supabase.auth.getUser(); 

  const playlistData = await getPlaylistData(params.slug, supabase);

  if (!playlistData) {
    notFound();
  }

  let isLikedByCurrentUser = false;
  let isDislikedByCurrentUser = false;
  let isFollowedByCurrentUser = false;
  if (currentUser && playlistData) {
    const { data: likeData } = await supabase.from('likes').select('id').eq('resource_id', playlistData.id).eq('resource_type', 'playlist').eq('user_id', currentUser.id).maybeSingle();
    isLikedByCurrentUser = !!likeData;
    const { data: dislikeData } = await supabase.from('dislikes').select('id').eq('resource_id', playlistData.id).eq('resource_type', 'playlist').eq('user_id', currentUser.id).maybeSingle();
    isDislikedByCurrentUser = !!dislikeData;
    const { data: followData } = await supabase.from('playlist_followers').select('id').eq('playlist_id', playlistData.id).eq('user_id', currentUser.id).maybeSingle();
    isFollowedByCurrentUser = !!followData;
  }
  
  const songsInPlaylist = playlistData.playlist_songs
    .map(ps => ps.songs)
    .filter((song): song is PlaylistSongForPage => song !== null);

  const totalDurationSeconds = songsInPlaylist.reduce((acc, song) => acc + (song.duration || 0), 0);
  const formatDuration = (seconds: number) => { 
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes} min ${remainingSeconds.toString().padStart(2, "0")} sec`;
  };

  const playlistCreator = playlistData.profiles;
  const creatorDisplayName = playlistCreator?.display_name || playlistCreator?.username || "Utilisateur inconnu";

  return (
    <div className="pb-8">
      <ResourceViewTracker resourceId={playlistData.id} resourceType="playlist" />

      <ResourceHeaderBanner
        coverUrl={playlistData.cover_url}
        avatarUrl={playlistData.banner_url} 
        defaultIcon={<ListMusic className="w-20 h-20 text-muted-foreground" />}
        resourceTypeLabel="Playlist"
        title={playlistData.name}
        artistName={creatorDisplayName}
        artistLink={playlistCreator?.username ? `/artists/${playlistCreator.username}` : undefined}
        description={playlistData.description}
        details={
          <>
            <span>{songsInPlaylist.length} morceaux</span>
            {totalDurationSeconds > 0 && ( <><span>•</span><span className="flex items-center gap-1"><Clock className="h-4 w-4" /> {formatDuration(totalDurationSeconds)}</span></>)}
            <span>•</span>
            <span>Créée {formatDistanceToNow(new Date(playlistData.created_at), { locale: fr, addSuffix: true })}</span>
          </>
        }
        actionButtons={
          <>
            <PlayPlaylistButton
              playlistId={playlistData.id}
              playlistName={playlistData.name}
              songs={songsInPlaylist as Song[]} 
              initialPlayCount={playlistData.plays || 0}
            />
            {currentUser && (
              <LikeButton
                resourceId={playlistData.id}
                resourceType="playlist"
                initialLikes={playlistData.like_count || 0}
                initialIsLiked={isLikedByCurrentUser}
                userId={currentUser.id}
                size="default" 
              />
            )}
            {currentUser && (
              <DislikeButton
                resourceId={playlistData.id}
                resourceType="playlist"
                initialDislikes={playlistData.dislike_count || 0}
                initialIsDisliked={isDislikedByCurrentUser}
                userId={currentUser.id}
                size="default" 
              />
            )}
            {currentUser && (
              <FollowPlaylistButton
                playlistId={playlistData.id}
                userId={currentUser.id}
                initialFollowerCount={playlistData.follower_count || 0}
                initialIsFollowed={isFollowedByCurrentUser}
                size="default" 
              />
            )}
            <SharePopover
              resourceType="playlist"
              resourceSlug={playlistData.slug || playlistData.id}
              resourceTitle={playlistData.name}
              triggerButton={
                <Button variant="outline" size="lg" title="Partager"> 
                  <Share2 className="mr-2 h-4 w-4" /> Partager
                </Button>
              }
            />
            {currentUser?.id === playlistData.user_id && (
              <Button variant="outline" asChild title="Modifier la playlist" size="default" className="px-3"> 
                <Link href={`/playlists/${playlistData.id}/edit`}><Edit3 className="mr-2 h-4 w-4" /> Éditer</Link> 
              </Button>
            )}
          </>
        }
        stats={
          <ResourceStatsDisplay
            resourceType="playlist"
            likeCount={playlistData.like_count}
            dislikeCount={playlistData.dislike_count}
            playCount={playlistData.plays}
            followerCount={playlistData.follower_count}
            // viewCount={playlistData.view_count} // Add if/when view_count is available for playlists
          />
        }
      />

      <div className="container mx-auto max-w-5xl py-8 px-4">
        <div className="flex flex-wrap gap-2 mb-6">
            {playlistData.genres?.map((genre: string) => <Badge key={genre} variant="secondary">{genre}</Badge>)}
            {playlistData.moods?.map((mood: string) => <Badge key={mood} variant="outline" className="border-blue-500 text-blue-500">{mood}</Badge>)}
            {playlistData.instrumentation?.map((inst: string) => <Badge key={inst} variant="outline" className="border-green-500 text-green-500">{inst}</Badge>)}
        </div>
        
        <div>
          <h2 className="text-2xl font-semibold mb-4">Morceaux dans la playlist</h2>
          {songsInPlaylist.length > 0 ? (
            <div className="space-y-2">
              {songsInPlaylist.map((song, index) => (
                <PlaylistSongItemClient 
                  key={song.id} 
                  song={song} 
                  index={index} 
                  artistDisplayName={creatorDisplayName}
                />
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-8">Cette playlist est vide pour le moment.</p>
          )}
        </div>

        <div className="mt-12">
          <CommentSection
            resourceId={playlistData.id}
            resourceType="playlist"
            resourceCreatorId={playlistData.user_id}
            areCommentsPublic={playlistData.are_comments_public ?? false}
            isModeratorView={currentUser?.id === playlistData.user_id}
          />
        </div>
      </div>
    </div>
  );
}
