"use client";

import Link from 'next/link';
import Image from 'next/image';
import type { Album } from '@/types';
import { Disc, PlayCircle } from 'lucide-react';

interface AlbumForCompactCard extends Album {
  songs_count?: number;
}

interface AlbumCardCompactProps {
  album: AlbumForCompactCard;
}

export function AlbumCardCompact({ album }: AlbumCardCompactProps) {
  const viewPageUrl = `/albums/${album.id}`;

  return (
    <Link href={viewPageUrl} className="block group rounded-md overflow-hidden shadow hover:shadow-lg transition-shadow duration-200 bg-card">
      <div className="relative aspect-square">
        {album.cover_url ? (
          <Image
            src={album.cover_url}
            alt={album.title}
            width={150}
            height={150}
            className="object-cover w-full h-full"
          />
        ) : (
          <div className="w-full h-full bg-muted flex items-center justify-center">
            <Disc className="w-10 h-10 text-muted-foreground" />
          </div>
        )}
        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
          <PlayCircle className="w-10 h-10 text-white" />
        </div>
      </div>
      <div className="p-2">
        <p className="text-xs font-medium truncate group-hover:underline" title={album.title}>
          {album.title}
        </p>
        {album.songs_count !== undefined && (
          <p className="text-xs text-muted-foreground">
            {album.songs_count} morceau{album.songs_count === 1 ? '' : 'x'}
          </p>
        )}
      </div>
    </Link>
  );
}