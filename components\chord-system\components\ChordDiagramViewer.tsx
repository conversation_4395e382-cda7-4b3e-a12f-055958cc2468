/**
 * 🎼 CHORD DIAGRAM VIEWER - Visualisation de Diagrammes d'Accords
 * 
 * Composant pour afficher les diagrammes d'accords multi-instruments
 * Rendu SVG optimisé avec support interactif
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useMemo, useCallback } from 'react';
import { Volume2, Info, Copy, Download } from 'lucide-react';
import type { 
  UnifiedChordPosition, 
  InstrumentType,
  BarrePosition 
} from '../types/chord-system';

// ============================================================================
// TYPES ET INTERFACES
// ============================================================================

interface ChordDiagramViewerProps {
  /** Accord à afficher */
  chord: UnifiedChordPosition;
  /** Taille du diagramme */
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  /** Mode interactif (hover, click) */
  interactive?: boolean;
  /** Afficher les labels (noms des notes) */
  showLabels?: boolean;
  /** Afficher les informations détaillées */
  showDetails?: boolean;
  /** Afficher les contrôles (audio, copie, etc.) */
  showControls?: boolean;
  /** Callback lors du clic sur une corde */
  onStringClick?: (stringIndex: number, fret: number | string) => void;
  /** Callback lors de la lecture audio */
  onPlayAudio?: (chord: UnifiedChordPosition) => void;
  /** Classe CSS personnalisée */
  className?: string;
}

interface DiagramDimensions {
  width: number;
  height: number;
  fretHeight: number;
  stringSpacing: number;
  nutHeight: number;
  marginTop: number;
  marginBottom: number;
  marginLeft: number;
  marginRight: number;
}

// ============================================================================
// CONSTANTES ET CONFIGURATION
// ============================================================================

const SIZE_CONFIGS = {
  small: {
    width: 80,
    height: 100,
    fretHeight: 12,
    stringSpacing: 12,
    nutHeight: 3,
    marginTop: 20,
    marginBottom: 15,
    marginLeft: 15,
    marginRight: 15
  },
  medium: {
    width: 120,
    height: 150,
    fretHeight: 18,
    stringSpacing: 18,
    nutHeight: 4,
    marginTop: 25,
    marginBottom: 20,
    marginLeft: 20,
    marginRight: 20
  },
  large: {
    width: 160,
    height: 200,
    fretHeight: 24,
    stringSpacing: 24,
    nutHeight: 5,
    marginTop: 30,
    marginBottom: 25,
    marginLeft: 25,
    marginRight: 25
  },
  xlarge: {
    width: 200,
    height: 250,
    fretHeight: 30,
    stringSpacing: 30,
    nutHeight: 6,
    marginTop: 35,
    marginBottom: 30,
    marginLeft: 30,
    marginRight: 30
  }
} as const;

const INSTRUMENT_CONFIGS = {
  guitar: { strings: 6, defaultFrets: 5, tuning: ['E', 'A', 'D', 'G', 'B', 'E'] },
  bass: { strings: 4, defaultFrets: 5, tuning: ['E', 'A', 'D', 'G'] },
  ukulele: { strings: 4, defaultFrets: 4, tuning: ['G', 'C', 'E', 'A'] },
  mandolin: { strings: 4, defaultFrets: 5, tuning: ['G', 'D', 'A', 'E'] },
  banjo: { strings: 5, defaultFrets: 5, tuning: ['D', 'G', 'B', 'D'] },
  piano: { strings: 0, defaultFrets: 0, tuning: [] }
} as const;

// ============================================================================
// UTILITAIRES
// ============================================================================

/**
 * Calcule les dimensions du diagramme selon la taille
 */
function getDimensions(size: string, instrument: InstrumentType): DiagramDimensions {
  const config = SIZE_CONFIGS[size as keyof typeof SIZE_CONFIGS] || SIZE_CONFIGS.medium;
  const instrumentConfig = INSTRUMENT_CONFIGS[instrument];
  
  return {
    ...config,
    width: config.stringSpacing * (instrumentConfig.strings - 1) + config.marginLeft + config.marginRight,
    height: config.fretHeight * 5 + config.marginTop + config.marginBottom
  };
}

/**
 * Normalise les données de frettes
 */
function normalizeFrets(frets: (string | number)[]): (string | number)[] {
  return frets.map(fret => {
    if (typeof fret === 'string') {
      if (fret.toLowerCase() === 'x') return 'x';
      const num = parseInt(fret, 10);
      return isNaN(num) ? 'x' : num;
    }
    return fret === -1 ? 'x' : fret;
  });
}

/**
 * Calcule la position Y d'une frette
 */
function getFretY(fret: number, baseFret: number, fretHeight: number, marginTop: number): number {
  const relativeFret = fret - baseFret + 1;
  return marginTop + (relativeFret * fretHeight);
}

/**
 * Obtient la couleur d'un doigté
 */
function getFingerColor(finger: number): string {
  const colors = {
    0: 'transparent',
    1: '#ef4444', // rouge
    2: '#f97316', // orange
    3: '#eab308', // jaune
    4: '#22c55e'  // vert
  };
  return colors[finger as keyof typeof colors] || '#6b7280';
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Diagramme SVG pour instruments à cordes
 */
const StringedInstrumentDiagram: React.FC<{
  chord: UnifiedChordPosition;
  dimensions: DiagramDimensions;
  interactive: boolean;
  showLabels: boolean;
  onStringClick?: (stringIndex: number, fret: number | string) => void;
}> = ({ chord, dimensions, interactive, showLabels, onStringClick }) => {
  const { width, height, fretHeight, stringSpacing, nutHeight, marginTop, marginLeft } = dimensions;
  const instrumentConfig = INSTRUMENT_CONFIGS[chord.instrument as keyof typeof INSTRUMENT_CONFIGS];
  const normalizedFrets = normalizeFrets(chord.frets);
  const baseFret = chord.baseFret || 1;
  const maxFret = Math.max(...normalizedFrets.filter(f => typeof f === 'number') as number[], baseFret + 4);
  const fretCount = Math.max(5, maxFret - baseFret + 1);

  // Calcul des positions
  const stringPositions = Array.from({ length: instrumentConfig.strings }, (_, i) => 
    marginLeft + (i * stringSpacing)
  );

  return (
    <svg width={width} height={height} className="chord-diagram">
      {/* Définitions pour les patterns */}
      <defs>
        <pattern id="fretboard" patternUnits="userSpaceOnUse" width={stringSpacing} height={fretHeight}>
          <rect width={stringSpacing} height={fretHeight} fill="none" stroke="#f3f4f6" strokeWidth="0.5" />
        </pattern>
      </defs>

      {/* Fond du manche */}
      <rect
        x={marginLeft - 5}
        y={marginTop}
        width={stringSpacing * (instrumentConfig.strings - 1) + 10}
        height={fretHeight * fretCount}
        fill="#fef7ed"
        stroke="#d1d5db"
        strokeWidth="1"
        rx="3"
      />

      {/* Frettes horizontales */}
      {Array.from({ length: fretCount + 1 }, (_, i) => (
        <line
          key={`fret-${i}`}
          x1={marginLeft - 5}
          y1={marginTop + (i * fretHeight)}
          x2={marginLeft + stringSpacing * (instrumentConfig.strings - 1) + 5}
          y2={marginTop + (i * fretHeight)}
          stroke={i === 0 ? "#374151" : "#9ca3af"}
          strokeWidth={i === 0 ? nutHeight : 1}
        />
      ))}

      {/* Cordes verticales */}
      {stringPositions.map((x, i) => (
        <line
          key={`string-${i}`}
          x1={x}
          y1={marginTop}
          x2={x}
          y2={marginTop + (fretHeight * fretCount)}
          stroke="#6b7280"
          strokeWidth="1.5"
        />
      ))}

      {/* Barrés */}
      {chord.barres?.map((barre, index) => {
        const fretY = getFretY(barre.fret, baseFret, fretHeight, marginTop);
        const startX = stringPositions[barre.fromString - 1];
        const endX = stringPositions[barre.toString - 1];
        
        return (
          <g key={`barre-${index}`}>
            {/* Ligne du barré */}
            <line
              x1={startX}
              y1={fretY - fretHeight / 2}
              x2={endX}
              y2={fretY - fretHeight / 2}
              stroke="#374151"
              strokeWidth="8"
              strokeLinecap="round"
              opacity="0.7"
            />
            {/* Label du barré */}
            <text
              x={startX - 12}
              y={fretY - fretHeight / 2 + 2}
              fontSize="10"
              fill="#374151"
              textAnchor="middle"
              fontWeight="bold"
            >
              {barre.fret}
            </text>
          </g>
        );
      })}

      {/* Positions des doigts */}
      {normalizedFrets.map((fret, stringIndex) => {
        const x = stringPositions[stringIndex];
        const finger = chord.fingers?.[stringIndex] || 0;

        if (fret === 'x' || fret === 0) {
          // Corde muette ou à vide
          const symbol = fret === 'x' ? '×' : '○';
          return (
            <g key={`position-${stringIndex}`}>
              <circle
                cx={x}
                cy={marginTop - 10}
                r="8"
                fill={fret === 'x' ? '#ef4444' : '#22c55e'}
                stroke="#fff"
                strokeWidth="2"
                className={interactive ? 'cursor-pointer hover:opacity-80' : ''}
                onClick={() => interactive && onStringClick?.(stringIndex, fret)}
              />
              <text
                x={x}
                y={marginTop - 6}
                fontSize="10"
                fill="white"
                textAnchor="middle"
                fontWeight="bold"
                pointerEvents="none"
              >
                {symbol}
              </text>
            </g>
          );
        }

        if (typeof fret === 'number' && fret > 0) {
          const fretY = getFretY(fret, baseFret, fretHeight, marginTop);
          
          return (
            <g key={`position-${stringIndex}`}>
              {/* Cercle du doigt */}
              <circle
                cx={x}
                cy={fretY - fretHeight / 2}
                r="10"
                fill={getFingerColor(finger)}
                stroke="#fff"
                strokeWidth="2"
                className={interactive ? 'cursor-pointer hover:opacity-80' : ''}
                onClick={() => interactive && onStringClick?.(stringIndex, fret)}
              />
              {/* Numéro du doigt */}
              {finger > 0 && (
                <text
                  x={x}
                  y={fretY - fretHeight / 2 + 3}
                  fontSize="10"
                  fill="white"
                  textAnchor="middle"
                  fontWeight="bold"
                  pointerEvents="none"
                >
                  {finger}
                </text>
              )}
            </g>
          );
        }

        return null;
      })}

      {/* Labels des cordes */}
      {showLabels && instrumentConfig.tuning.map((note, i) => (
        <text
          key={`label-${i}`}
          x={stringPositions[i]}
          y={height - 5}
          fontSize="10"
          fill="#6b7280"
          textAnchor="middle"
          fontWeight="500"
        >
          {note}
        </text>
      ))}

      {/* Numéro de frette de base */}
      {baseFret > 1 && (
        <text
          x={marginLeft - 15}
          y={marginTop + fretHeight / 2 + 3}
          fontSize="12"
          fill="#374151"
          textAnchor="middle"
          fontWeight="bold"
        >
          {baseFret}
        </text>
      )}
    </svg>
  );
};

/**
 * Affichage pour piano (clavier)
 */
const PianoDiagram: React.FC<{
  chord: UnifiedChordPosition;
  dimensions: DiagramDimensions;
  showLabels: boolean;
}> = ({ chord, dimensions, showLabels }) => {
  const { width, height } = dimensions;
  
  // Pour le piano, on affiche les notes MIDI si disponibles
  if (!chord.midi || chord.midi.length === 0) {
    return (
      <div 
        className="flex items-center justify-center bg-gray-100 rounded-lg"
        style={{ width, height }}
      >
        <div className="text-center text-gray-500">
          <Info className="w-6 h-6 mx-auto mb-2" />
          <p className="text-xs">Données MIDI non disponibles</p>
        </div>
      </div>
    );
  }

  // Simplification : affichage des notes MIDI sous forme de liste
  return (
    <div 
      className="flex flex-col items-center justify-center bg-white border-2 border-gray-300 rounded-lg p-2"
      style={{ width, height }}
    >
      <div className="text-center">
        <h4 className="text-sm font-semibold text-gray-900 mb-2">{chord.chord}</h4>
        <div className="space-y-1">
          {chord.midi.map((note, index) => (
            <div key={index} className="text-xs text-gray-600">
              MIDI {note}
            </div>
          ))}
        </div>
        {showLabels && chord.notes && (
          <div className="mt-2 text-xs text-blue-600">
            {chord.notes.join(' - ')}
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const ChordDiagramViewer: React.FC<ChordDiagramViewerProps> = ({
  chord,
  size = 'medium',
  interactive = false,
  showLabels = true,
  showDetails = false,
  showControls = false,
  onStringClick,
  onPlayAudio,
  className = ''
}) => {
  const dimensions = useMemo(() => 
    getDimensions(size, chord.instrument), 
    [size, chord.instrument]
  );

  const handleStringClick = useCallback((stringIndex: number, fret: number | string) => {
    if (interactive && onStringClick) {
      onStringClick(stringIndex, fret);
    }
  }, [interactive, onStringClick]);

  const handlePlayAudio = useCallback(() => {
    if (onPlayAudio) {
      onPlayAudio(chord);
    }
  }, [onPlayAudio, chord]);

  const copyChordData = useCallback(() => {
    const chordData = {
      name: chord.chord,
      instrument: chord.instrument,
      frets: chord.frets,
      fingers: chord.fingers,
      baseFret: chord.baseFret
    };
    
    navigator.clipboard.writeText(JSON.stringify(chordData, null, 2))
      .then(() => {
        // TODO: Afficher une notification de succès
        console.log('Données d\'accord copiées');
      })
      .catch(err => {
        console.error('Erreur lors de la copie:', err);
      });
  }, [chord]);

  return (
    <div className={`chord-diagram-viewer ${className}`}>
      {/* En-tête avec nom et contrôles */}
      {(showDetails || showControls) && (
        <div className="flex items-center justify-between mb-2">
          {showDetails && (
            <div>
              <h3 className="font-semibold text-gray-900">{chord.chord}</h3>
              <p className="text-xs text-gray-500 capitalize">
                {chord.instrument} • {chord.tuning} • {chord.difficulty}
              </p>
            </div>
          )}
          
          {showControls && (
            <div className="flex items-center space-x-1">
              {chord.midi && chord.midi.length > 0 && (
                <button
                  onClick={handlePlayAudio}
                  className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded"
                  title="Écouter l'accord"
                >
                  <Volume2 className="w-4 h-4" />
                </button>
              )}
              
              <button
                onClick={copyChordData}
                className="p-1 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded"
                title="Copier les données"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
      )}

      {/* Diagramme principal */}
      <div className="chord-diagram-container">
        {chord.instrument === 'piano' ? (
          <PianoDiagram
            chord={chord}
            dimensions={dimensions}
            showLabels={showLabels}
          />
        ) : (
          <StringedInstrumentDiagram
            chord={chord}
            dimensions={dimensions}
            interactive={interactive}
            showLabels={showLabels}
            onStringClick={handleStringClick}
          />
        )}
      </div>

      {/* Informations détaillées */}
      {showDetails && (
        <div className="mt-2 text-xs text-gray-600 space-y-1">
          {chord.baseFret && chord.baseFret > 1 && (
            <div>Frette de base: {chord.baseFret}</div>
          )}
          
          {chord.barres && chord.barres.length > 0 && (
            <div>
              Barrés: {chord.barres.map(b => `${b.fret}ème frette (cordes ${b.fromString}-${b.toString})`).join(', ')}
            </div>
          )}
          
          {chord.midi && chord.midi.length > 0 && (
            <div>Notes MIDI: {chord.midi.join(', ')}</div>
          )}
          
          {chord.notes && chord.notes.length > 0 && (
            <div>Notes: {chord.notes.join(' - ')}</div>
          )}
        </div>
      )}
    </div>
  );
};
