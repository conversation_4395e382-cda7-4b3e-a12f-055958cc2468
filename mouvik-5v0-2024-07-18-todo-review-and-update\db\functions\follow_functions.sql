-- Function to get follower count for a user (artist)
-- Assumes following_id in the 'follows' table is the user_id of the artist being followed.
CREATE OR REPLACE FUNCTION get_follower_count(artist_user_id uuid)
RETURNS integer AS $$
DECLARE
  count integer;
BEGIN
  SELECT COUNT(*)
  INTO count
  FROM public.follows
  WHERE following_id = artist_user_id;
  RETURN COALESCE(count, 0);
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to check if a user is following another user (artist)
-- Assumes follower_id is the current user and following_id is the target artist.
CREATE OR REPLACE FUNCTION is_following(p_current_user_id uuid, p_target_artist_id uuid)
RETURNS boolean AS $$
DECLARE
  is_followed boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.follows
    WHERE follower_id = p_current_user_id AND following_id = p_target_artist_id
  )
  INTO is_followed;
  RETURN is_followed;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to follow an artist
-- RLS policies on 'follows' table should ensure that the follower_id matches auth.uid()
CREATE OR REPLACE FUNCTION follow_artist(p_follower_id uuid, p_following_id uuid)
RETURNS void AS $$
BEGIN
  IF p_follower_id = p_following_id THEN
    RAISE EXCEPTION 'Un utilisateur ne peut pas se suivre lui-même.';
  END IF;

  INSERT INTO public.follows (follower_id, following_id)
  VALUES (p_follower_id, p_following_id)
  ON CONFLICT (follower_id, following_id) DO NOTHING; -- Assumes a UNIQUE constraint on (follower_id, following_id)
                                                      -- If the PK is just 'id', this ON CONFLICT needs adjustment or prior check.
                                                      -- Based on schema, PK is 'id', so a unique constraint on (follower_id, following_id) is needed for ON CONFLICT.
                                                      -- If no such constraint, use a SELECT EXISTS check first.
END;
$$ LANGUAGE plpgsql;

-- Function to unfollow an artist
-- RLS policies on 'follows' table should ensure that the follower_id matches auth.uid()
CREATE OR REPLACE FUNCTION unfollow_artist(p_follower_id uuid, p_following_id uuid)
RETURNS void AS $$
BEGIN
  DELETE FROM public.follows
  WHERE follower_id = p_follower_id AND following_id = p_following_id;
END;
$$ LANGUAGE plpgsql;

-- It's recommended to have a UNIQUE constraint on (follower_id, following_id) in the 'follows' table
-- if it doesn't exist already, to make ON CONFLICT work reliably and prevent duplicate follow entries.
-- Example:
-- ALTER TABLE public.follows
-- ADD CONSTRAINT unique_follow_pair UNIQUE (follower_id, following_id);
-- This should be run if the `follows` table only has `id` as PK and no other unique constraint on the pair.
