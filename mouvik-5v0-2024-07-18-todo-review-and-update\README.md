# MOUVIK - Music Creation and Sharing Platform

## Architecture Overview

MOUVIK is a full-stack application built with Next.js, React, and Supabase. It provides tools for music creation, collaboration, and sharing.

### Core Modules

- **Authentication**: User registration, login, and profile management
- **Music Creation**: Tools for creating and editing songs and albums
- **Social Features**: Following, liking, commenting, and sharing
- **Discovery**: Exploring music by genre, artist, and recommendations
- **Analytics**: Tracking plays, views, and engagement

### Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL, Authentication, Storage)
- **State Management**: React Context API, React Query
- **Deployment**: Vercel

### Directory Structure

\`\`\`
/app                    # Next.js app directory
  /(authenticated)      # Protected routes requiring authentication
  /(public)             # Public-facing routes
  /(marketing)          # Marketing and landing pages
/components             # Reusable React components
  /ui                   # Base UI components (shadcn/ui)
  /forms                # Form components
  /layout               # Layout components
  /audio                # Audio-related components
  /social               # Social feature components
/lib                    # Utility functions and shared logic
  /supabase             # Supabase client and utilities
  /actions              # Server actions
  /constants            # Application constants
  /hooks                # Custom React hooks
/types                  # TypeScript type definitions
/public                 # Static assets
\`\`\`

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables (see `.env.example`)
4. Run the development server: `npm run dev`

## Development Guidelines

### Component Structure

- Use functional components with TypeScript
- Implement proper prop typing
- Extract reusable logic to custom hooks
- Follow the container/presentational pattern where appropriate

### State Management

- Use React Context for global state
- Use React Query for server state
- Use local state for component-specific state

### Styling

- Use Tailwind CSS for styling
- Follow the design system defined in `tailwind.config.ts`
- Use shadcn/ui components as building blocks

### Data Fetching

- Use Supabase client for data fetching
- Implement proper error handling and loading states
- Use React Query for caching and revalidation

### Testing

- Write unit tests for utility functions
- Write integration tests for key user flows
- Use Cypress for end-to-end testing
\`\`\`

Now, let's create a module for form components that will be reused across the application:

\`\`\`
