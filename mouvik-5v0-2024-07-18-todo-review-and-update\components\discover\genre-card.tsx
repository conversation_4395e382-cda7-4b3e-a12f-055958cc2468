import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"

interface GenreCardProps {
  genre: {
    id: number
    name: string
    count: number
    image: string
  }
}

export function GenreCard({ genre }: GenreCardProps) {
  return (
    <Link href={`/discover/genres/${genre.id}`}>
      <Card className="overflow-hidden h-[150px] relative group">
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent z-10" />
        <img
          src={genre.image || "/placeholder.svg"}
          alt={genre.name}
          className="w-full h-full object-cover transition-transform group-hover:scale-105"
        />
        <CardContent className="absolute bottom-0 left-0 p-4 z-20 text-white">
          <h3 className="font-bold text-xl">{genre.name}</h3>
          <p className="text-sm text-white/80">{genre.count} titres</p>
        </CardContent>
      </Card>
    </Link>
  )
}
