'use client';

import { useState, useEffect, useMemo } from 'react';

// Types pour les données d'accords
export interface ChordData {
  instrument: string;
  tuning: string[];
  strings: number;
  fretRange: number[];
  chords: Record<string, ChordVariation[]>;
}

export interface ChordVariation {
  suffix: string;
  positions: ChordPosition[];
}

export interface ChordPosition {
  frets: (string | number)[];
  fingers: number[];
  baseFret: number;
  barres: Array<{ fret: number; fromString: number; toString: number }>;
  midi: number[];
  difficulty: number;
}

export interface InstrumentConfig {
  value: string;
  label: string;
  file: string;
  tuning: string[];
  strings: number;
}

// Configuration des instruments disponibles
export const AVAILABLE_INSTRUMENTS: InstrumentConfig[] = [
  { 
    value: 'guitar', 
    label: 'Guitare Standard', 
    file: 'guitar_complete_extended.json',
    tuning: ['E', 'A', 'D', 'G', 'B', 'E'],
    strings: 6
  },
  { 
    value: 'guitar_drop_d', 
    label: 'Guitare Drop D', 
    file: 'guitar_drop_d_complete.json',
    tuning: ['D', 'A', 'D', 'G', 'B', 'E'],
    strings: 6
  },
  { 
    value: 'guitar_open_g', 
    label: 'Guitare Open G', 
    file: 'guitar_open_g_complete.json',
    tuning: ['D', 'G', 'D', 'G', 'B', 'D'],
    strings: 6
  },
  { 
    value: 'guitar_dadgad', 
    label: 'Guitare DADGAD', 
    file: 'guitar_dadgad_tuning.json',
    tuning: ['D', 'A', 'D', 'G', 'A', 'D'],
    strings: 6
  },
  { 
    value: 'ukulele', 
    label: 'Ukulélé', 
    file: 'ukulele_complete_all_keys.json',
    tuning: ['G', 'C', 'E', 'A'],
    strings: 4
  },
  { 
    value: 'piano', 
    label: 'Piano', 
    file: 'piano.json',
    tuning: [],
    strings: 0
  },
  { 
    value: 'banjo', 
    label: 'Banjo 5 cordes', 
    file: 'banjo_5string_complete.json',
    tuning: ['G', 'D', 'G', 'B', 'D'],
    strings: 5
  },
  { 
    value: 'mandolin', 
    label: 'Mandoline', 
    file: 'mandolin_gdae_tuning.json',
    tuning: ['G', 'D', 'A', 'E'],
    strings: 4
  },
  { 
    value: 'bouzouki', 
    label: 'Bouzouki', 
    file: 'bouzouki_gdae_complete.json',
    tuning: ['G', 'D', 'A', 'E'],
    strings: 4
  }
];

// Hook pour gérer la bibliothèque d'accords
export const useChordLibrary = (instrumentValue: string = 'guitar') => {
  const [chordData, setChordData] = useState<ChordData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const instrumentConfig = useMemo(() => 
    AVAILABLE_INSTRUMENTS.find(i => i.value === instrumentValue) || AVAILABLE_INSTRUMENTS[0],
    [instrumentValue]
  );

  // Charger les données d'accords
  useEffect(() => {
    const loadChordData = async () => {
      if (!instrumentConfig) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // Essayer de charger depuis le dossier public d'abord
        let response = await fetch(`/chords/${instrumentConfig.file}`);
        
        // Si ça échoue, essayer depuis lib/chords
        if (!response.ok) {
          response = await fetch(`/lib/chords/${instrumentConfig.file}`);
        }
        
        if (response.ok) {
          const data = await response.json();
          setChordData(data);
        } else {
          // Fallback vers les données de base si le fichier n'existe pas
          const fallbackData = await loadFallbackChords(instrumentConfig.value);
          setChordData(fallbackData);
        }
      } catch (err) {
        console.error('Erreur lors du chargement des accords:', err);
        setError('Impossible de charger les données d\'accords');
        
        // Essayer de charger les données de fallback
        try {
          const fallbackData = await loadFallbackChords(instrumentConfig.value);
          setChordData(fallbackData);
        } catch (fallbackErr) {
          console.error('Erreur lors du chargement des données de fallback:', fallbackErr);
        }
      } finally {
        setLoading(false);
      }
    };

    loadChordData();
  }, [instrumentConfig]);

  // Fonction pour charger des données de fallback
  const loadFallbackChords = async (instrument: string): Promise<ChordData> => {
    // Données de base pour différents instruments
    const fallbackData: Record<string, ChordData> = {
      guitar: {
        instrument: 'guitar',
        tuning: ['E', 'A', 'D', 'G', 'B', 'E'],
        strings: 6,
        fretRange: [0, 24],
        chords: {
          'C': [{
            suffix: 'major',
            positions: [{
              frets: ['x', '3', '2', '0', '1', '0'],
              fingers: [0, 3, 2, 0, 1, 0],
              baseFret: 1,
              barres: [],
              midi: [60, 64, 67, 72, 76],
              difficulty: 2
            }]
          }],
          'G': [{
            suffix: 'major',
            positions: [{
              frets: ['3', '2', '0', '0', '3', '3'],
              fingers: [3, 2, 0, 0, 4, 4],
              baseFret: 1,
              barres: [],
              midi: [55, 59, 62, 67, 71, 74],
              difficulty: 2
            }]
          }],
          'Am': [{
            suffix: 'minor',
            positions: [{
              frets: ['x', '0', '2', '2', '1', '0'],
              fingers: [0, 0, 2, 3, 1, 0],
              baseFret: 1,
              barres: [],
              midi: [57, 60, 64, 69, 72],
              difficulty: 1
            }]
          }],
          'F': [{
            suffix: 'major',
            positions: [{
              frets: ['1', '3', '3', '2', '1', '1'],
              fingers: [1, 3, 4, 2, 1, 1],
              baseFret: 1,
              barres: [{ fret: 1, fromString: 0, toString: 5 }],
              midi: [53, 57, 60, 65, 69, 72],
              difficulty: 4
            }]
          }]
        }
      },
      ukulele: {
        instrument: 'ukulele',
        tuning: ['G', 'C', 'E', 'A'],
        strings: 4,
        fretRange: [0, 15],
        chords: {
          'C': [{
            suffix: 'major',
            positions: [{
              frets: ['0', '0', '0', '3'],
              fingers: [0, 0, 0, 3],
              baseFret: 1,
              barres: [],
              midi: [67, 72, 76, 81],
              difficulty: 1
            }]
          }],
          'G': [{
            suffix: 'major',
            positions: [{
              frets: ['0', '2', '3', '2'],
              fingers: [0, 1, 3, 2],
              baseFret: 1,
              barres: [],
              midi: [67, 74, 79, 83],
              difficulty: 2
            }]
          }]
        }
      }
    };

    return fallbackData[instrument] || fallbackData.guitar;
  };

  // Fonction pour rechercher des accords
  const searchChords = (searchTerm: string) => {
    if (!chordData) return [];
    
    return Object.entries(chordData.chords)
      .filter(([chordName]) => 
        chordName.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort(([a], [b]) => a.localeCompare(b));
  };

  // Fonction pour obtenir un accord spécifique
  const getChord = (chordName: string) => {
    if (!chordData) return null;
    return chordData.chords[chordName] || null;
  };

  // Fonction pour obtenir tous les noms d'accords
  const getAllChordNames = () => {
    if (!chordData) return [];
    return Object.keys(chordData.chords).sort();
  };

  // Fonction pour obtenir les accords par difficulté
  const getChordsByDifficulty = (maxDifficulty: number = 3) => {
    if (!chordData) return [];
    
    return Object.entries(chordData.chords)
      .filter(([_, variations]) => 
        variations.some(variation => 
          variation.positions.some(position => position.difficulty <= maxDifficulty)
        )
      )
      .sort(([a], [b]) => a.localeCompare(b));
  };

  return {
    chordData,
    loading,
    error,
    instrumentConfig,
    searchChords,
    getChord,
    getAllChordNames,
    getChordsByDifficulty
  };
};

export default useChordLibrary;