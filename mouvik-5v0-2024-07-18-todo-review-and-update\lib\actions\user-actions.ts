import { createBrowserClient } from "@/lib/supabase/client"
import { z } from "zod"

const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, {
      message: "Le nom d'utilisateur doit comporter au moins 2 caractères.",
    })
    .max(30, {
      message: "Le nom d'utilisateur ne doit pas dépasser 30 caractères.",
    }),
  email: z.string().email({
    message: "Veuillez entrer une adresse e-mail valide.",
  }),
  bio: z.string().max(160).optional(),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

export async function updateUserProfile(data: ProfileFormValues) {
  const supabase = createBrowserClient()

  const { error } = await supabase.auth.updateUser({
    email: data.email,
    data: {
      username: data.username,
      bio: data.bio,
    },
  })

  if (error) {
    throw new Error("Erreur lors de la mise à jour du profil")
  }
}
