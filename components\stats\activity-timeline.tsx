"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"
import { Loader2, Calendar, ArrowUpRight, ArrowDownRight } from "lucide-react"

interface ActivityTimelineProps {
  userId: string
  timeRange: '7d' | '30d' | '90d' | '1y' | 'all'
}

interface TimelinePoint {
  date: string; // from activity_date
  plays: number;
  likes: number;
  comments: number;
  views: number;
  new_followers: number;
}

// Data from RPC get_activity_timeline will be an array of objects matching TimelinePoint (after mapping activity_date)
// No separate ActivityRpcDataPoint interface needed if mapping is straightforward.

export function ActivityTimeline({ userId, timeRange }: ActivityTimelineProps) {
  const [timelineData, setTimelineData] = useState<TimelinePoint[]>([])
  const [interval, setInterval] = useState<'day' | 'week' | 'month'>('day')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [totalPlays, setTotalPlays] = useState(0);
  const [totalLikes, setTotalLikes] = useState(0);
  const [totalComments, setTotalComments] = useState(0); // Added for completeness
  const [totalViews, setTotalViews] = useState(0);
  const [totalFollowers, setTotalFollowers] = useState(0);

  useEffect(() => {
    const fetchTimelineData = async () => {
      if (!userId) {
        setIsLoading(false);
        return;
      }
      setIsLoading(true)
      setError(null)
      const supabase = createBrowserClient()
      
      let p_days = 30;
      let rpc_interval = interval; 

      if (timeRange === '7d') p_days = 7;
      else if (timeRange === '30d') p_days = 30;
      else if (timeRange === '90d') p_days = 90;
      else if (timeRange === '1y') {
        p_days = 365;
      }

      const { data, error: rpcError } = await supabase.rpc('get_activity_timeline', {
        p_user_id: userId,
        p_interval: rpc_interval,
        p_days: p_days
      });
      
      if (rpcError) {
        console.error("Erreur RPC get_activity_timeline:", rpcError)
        setError(rpcError.message)
        setTimelineData([])
      } else if (data) {
        // Supabase RPC returning TABLE will give an array of objects with column names
        // e.g., { activity_date, plays, likes, comments, views, new_followers }
        const formattedData = (data as any[]).map(point => ({
          date: point.activity_date, // Map activity_date to date
          plays: point.plays || 0,
          likes: point.likes || 0,
          comments: point.comments || 0,
          views: point.views || 0,
          new_followers: point.new_followers || 0,
        }));
        setTimelineData(formattedData as TimelinePoint[]);

        let currentTotalPlays = 0;
        let currentTotalLikes = 0;
        let currentTotalComments = 0;
        let currentTotalViews = 0;
        let currentTotalFollowers = 0;

        formattedData.forEach(point => {
          currentTotalPlays += point.plays;
          currentTotalLikes += point.likes;
          currentTotalComments += point.comments;
          currentTotalViews += point.views;
          currentTotalFollowers += point.new_followers;
        });
        setTotalPlays(currentTotalPlays);
        setTotalLikes(currentTotalLikes);
        setTotalComments(currentTotalComments);
        setTotalViews(currentTotalViews);
        setTotalFollowers(currentTotalFollowers);
      } else {
        setTimelineData([])
      }
      setIsLoading(false)
    }
    
    fetchTimelineData()
  }, [userId, timeRange, interval]) 
  
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    if (interval === 'week') {
      // Simplification: afficher le début de la semaine
      return new Intl.DateTimeFormat('fr-FR', { day: 'numeric', month: 'short' }).format(date);
    }
    if (interval === 'month') {
      return new Intl.DateTimeFormat('fr-FR', { month: 'short', year: '2-digit' }).format(date);
    }
    return new Intl.DateTimeFormat('fr-FR', { day: 'numeric', month: 'short' }).format(date)
  }
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Évolution de l'activité</CardTitle>
          <CardDescription>Chargement des données...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Évolution de l'activité</CardTitle>
          <CardDescription>Erreur lors du chargement</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">{error}</p>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-start justify-between">
        <div>
          <CardTitle>Évolution de l'activité</CardTitle>
          <CardDescription>Tendances d'engagement sur la période</CardDescription>
        </div>
        <Select value={interval} onValueChange={(value: 'day' | 'week' | 'month') => setInterval(value)}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Intervalle" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">Jour</SelectItem>
            <SelectItem value="week">Semaine</SelectItem>
            <SelectItem value="month">Mois</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
          <div className="flex flex-col space-y-1">
            <span className="text-xs text-muted-foreground">Écoutes totales</span>
            <span className="text-2xl font-bold">{totalPlays}</span>
          </div>
          <div className="flex flex-col space-y-1">
            <span className="text-xs text-muted-foreground">Likes totaux</span>
            <span className="text-2xl font-bold">{totalLikes}</span>
          </div>
          <div className="flex flex-col space-y-1">
            <span className="text-xs text-muted-foreground">Commentaires totaux</span>
            <span className="text-2xl font-bold">{totalComments}</span>
          </div>
          <div className="flex flex-col space-y-1">
            <span className="text-xs text-muted-foreground">Vues totales (morceaux)</span>
            <span className="text-2xl font-bold">{totalViews}</span>
          </div>
          <div className="flex flex-col space-y-1">
            <span className="text-xs text-muted-foreground">Nouveaux followers</span>
            <span className="text-2xl font-bold">{totalFollowers}</span>
          </div>
        </div>
        
        <Tabs defaultValue="line" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="line">Ligne</TabsTrigger>
            <TabsTrigger value="stacked">Empilé</TabsTrigger>
          </TabsList>
          
          <TabsContent value="line">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={timelineData} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={formatDate} 
                  interval={'preserveStartEnd'} 
                  minTickGap={30}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(label) => formatDate(label)}
                  formatter={(value: number, name: string) => {
                    let displayName = name;
                    if (name === 'plays') displayName = 'Écoutes';
                    else if (name === 'likes') displayName = 'Likes';
                    else if (name === 'comments') displayName = 'Commentaires';
                    else if (name === 'views') displayName = 'Vues';
                    else if (name === 'new_followers') displayName = 'Nvx Followers';
                    return [value, displayName];
                  }}
                />
                <Legend />
                <Line type="monotone" dataKey="plays" name="Écoutes" stroke="#4ECDC4" strokeWidth={2} dot={false} activeDot={{ r: 6 }} />
                <Line type="monotone" dataKey="likes" name="Likes" stroke="#FF6B6B" strokeWidth={2} dot={false} activeDot={{ r: 6 }} />
                <Line type="monotone" dataKey="comments" name="Commentaires" stroke="#C44D58" strokeWidth={2} dot={false} activeDot={{ r: 6 }} />
                <Line type="monotone" dataKey="views" name="Vues" stroke="#FFA500" strokeWidth={2} dot={false} activeDot={{ r: 6 }} />
                <Line type="monotone" dataKey="new_followers" name="Nvx Followers" stroke="#8A2BE2" strokeWidth={2} dot={false} activeDot={{ r: 6 }} />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
          
          <TabsContent value="stacked">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={timelineData} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={formatDate} 
                  interval={'preserveStartEnd'} 
                  minTickGap={30}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(label) => formatDate(label)}
                  formatter={(value: number, name: string) => {
                    let displayName = name;
                    if (name === 'plays') displayName = 'Écoutes';
                    else if (name === 'likes') displayName = 'Likes';
                    else if (name === 'comments') displayName = 'Commentaires';
                    else if (name === 'views') displayName = 'Vues';
                    else if (name === 'new_followers') displayName = 'Nvx Followers';
                    return [value, displayName];
                  }}
                />
                <Legend />
                <Area type="monotone" stackId="1" dataKey="plays" name="Écoutes" stroke="#4ECDC4" fill="#4ECDC4" fillOpacity={0.6} />
                <Area type="monotone" stackId="1" dataKey="likes" name="Likes" stroke="#FF6B6B" fill="#FF6B6B" fillOpacity={0.6} />
                <Area type="monotone" stackId="1" dataKey="comments" name="Commentaires" stroke="#C44D58" fill="#C44D58" fillOpacity={0.6} />
                <Area type="monotone" stackId="1" dataKey="views" name="Vues" stroke="#FFA500" fill="#FFA500" fillOpacity={0.6} />
                <Area type="monotone" stackId="1" dataKey="new_followers" name="Nvx Followers" stroke="#8A2BE2" fill="#8A2BE2" fillOpacity={0.6} />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
