// c:\_DEV_projects\TOOL\mouvik-5v0\components\songs\SongFormHeader.tsx
import React from 'react';
import Image from 'next/image';
import { UseFormSetValue } from 'react-hook-form';
import { SongFormValues } from './song-schema';
import { FileInput } from '@/components/ui/file-input';
import { AudioWaveformPreview } from './AudioWaveformPreview';
import AudioRecorder from '@/components/AudioRecorder';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'; // Removed CardContent

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  History, PanelRightClose, PanelRightOpen,
  ImagePlus, X, 
  Save, // Added Save icon
  Trash2, UploadCloud, Mic, StopCircle // Added audio/recording icons
} from 'lucide-react';
import type { LocalFileState } from './hooks/useLocalFileManagement';

interface SongFormHeaderProps {
  mode: 'create' | 'edit';
  songTitle?: string;
  artistName?: string;
  onSave: () => void; // Prop for save action
  isDirty?: boolean; // Prop to indicate form changes
  onViewHistory?: () => void;
  isVaultPanelCollapsed?: boolean;
  onToggleVaultPanel?: () => void;
  lastSavedTimestamp?: Date | null; // Changed to Date | null

  // Props for cover art and audio
  coverArtUrl?: string | null;
  localCoverArtFile?: LocalFileState;
  onCoverArtSelect?: (file: File | null) => void; // Changed to match useLocalFileManagement signature
  onClearCoverArt?: () => void;
  coverArtInputRef?: React.RefObject<HTMLInputElement>;

  // Audio Management Props
  setValue: UseFormSetValue<SongFormValues>;
  localAudioFile: LocalFileState;
  handleAudioFileSelect: (file: File | null) => void;
  handleClearAudio: () => void;
  recordedAudioBlob: Blob | null;
  recordedAudioPreviewUrl?: string | null;
  onRecordingComplete: (audioBlob: Blob | null, previewUrl?: string | null) => void;
  onRecordingError: (error: string) => void;
  handleClearRecordedAudio: () => void;
  isSubmitting?: boolean;
}

export const SongFormHeader: React.FC<SongFormHeaderProps> = ({
  mode,
  songTitle,
  artistName,
  onViewHistory,
  isVaultPanelCollapsed,
  onToggleVaultPanel,
  lastSavedTimestamp,
  coverArtUrl,
  localCoverArtFile,
  onCoverArtSelect,
  onClearCoverArt,
  coverArtInputRef,
  // Audio props
  setValue,
  localAudioFile,
  handleAudioFileSelect,
  handleClearAudio,
  recordedAudioBlob,
  recordedAudioPreviewUrl,
  onRecordingComplete,
  onRecordingError,
  handleClearRecordedAudio,
  isSubmitting,
  onSave, // Added from props
  isDirty, // Added from props
}) => {
  console.log('[SongFormHeader] Rendering. isSubmitting:', isSubmitting, 'isDirty:', isDirty);

  const titleText = mode === 'create' ? 'Créer un nouveau morceau' : `Modifier: ${songTitle || 'Morceau'}`;


  return (
    <Card className="mb-6 bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg overflow-hidden">
      <div className="p-4 md:p-6">
        <div className="flex flex-col md:flex-row gap-4 md:gap-6 items-start">
          {/* Cover Art Section */}
          <div className="w-full md:w-1/3 lg:w-1/4 flex flex-col items-center space-y-2">
            <div
              className="relative w-full aspect-square rounded-lg overflow-hidden border-2 border-dashed border-white/30 flex items-center justify-center bg-white/10 cursor-pointer group"
              onClick={() => !coverArtUrl && coverArtInputRef?.current?.click()}
              title={coverArtUrl ? "Pochette" : "Cliquer pour ajouter une pochette"}
            >
              {coverArtUrl ? (
                <>
                  <Image
                    src={coverArtUrl}
                    alt="Pochette"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
                    style={{ objectFit: 'cover' }}
                    priority
                  />
                  {onClearCoverArt && (
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-1 right-1 h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity z-10 p-1"
                      onClick={(e) => { e.stopPropagation(); onClearCoverArt(); }}
                      title="Supprimer la pochette"
                    >
                      <X size={16} />
                    </Button>
                  )}
                </>
              ) : (
                <div className="text-center text-white/70 p-2">
                  <ImagePlus size={32} className="mx-auto mb-2" />
                  <p className="text-sm font-medium">Ajouter Pochette</p>
                </div>
              )}
            </div>
            <Input
              type="file"
              className="hidden"
              ref={coverArtInputRef}
              onChange={(e) => {
                if (onCoverArtSelect && e.target.files && e.target.files[0]) {
                  onCoverArtSelect(e.target.files[0]);
                } else if (onCoverArtSelect) {
                  onCoverArtSelect(null); // Clear if no file is selected
                }
              }}
              accept="image/*"
            />
            {localCoverArtFile?.file?.name && !coverArtUrl && (
              <p className="text-xs text-white/80 truncate w-full text-center" title={localCoverArtFile.file?.name}>
                Prêt: {localCoverArtFile.file?.name}
              </p>
            )}
          </div>

          {/* Main Info and Audio Section */}
          <div className="flex-grow space-y-4 w-full md:w-2/3 lg:w-3/4">
            <div className="flex justify-between items-start gap-4">
              <div>
                <CardTitle className="text-2xl lg:text-3xl font-bold truncate text-shadow" title={titleText}>
                  {titleText}
                </CardTitle>
                {artistName && (
                  <CardDescription className="text-lg text-white/80 truncate text-shadow-sm" title={artistName}>
                    par {artistName}
                  </CardDescription>
                )}
              </div>
              <Button
                onClick={onSave}
                disabled={isSubmitting || !isDirty}
                size="lg"
                className="bg-green-600 hover:bg-green-700 text-white font-semibold shrink-0 whitespace-nowrap"
                aria-label="Enregistrer les modifications"
              >
                <Save className="mr-2 h-5 w-5" />
                Enregistrer
              </Button>
            </div>

            {/* Audio Section */}
            <div className="space-y-3 pt-3 border-t border-white/20">
              <h3 className="text-md font-semibold text-white/90">Fichier Audio Principal</h3>
              
              {/* Audio Preview (Uploaded or Recorded) */}
              {localAudioFile.previewUrl && (
                <div className="mb-3">
                  <AudioWaveformPreview 
                    audioUrl={localAudioFile.previewUrl} 
                    audioFileName={localAudioFile.file?.name || "Aperçu audio"} 
                    waveColor="#A78BFA" // Light purple to match gradient
                    progressColor="#8B5CF6" // Darker purple
                    cursorColor="#FFFFFF"
                    height={60}
                  />
                  {/* Clear Recording Button - specific to when recorded audio is the active preview */}
                  {recordedAudioBlob && localAudioFile.previewUrl === recordedAudioPreviewUrl && (
                     <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2 bg-red-500/20 hover:bg-red-500/30 border-red-500/50 text-red-300 hover:text-red-200 w-full md:w-auto"
                        onClick={handleClearRecordedAudio}
                        disabled={isSubmitting}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Retirer l'enregistrement
                      </Button>
                  )}
                </div>
              )}

              {/* Audio File Input */}
              {!recordedAudioBlob && (
                <FileInput
                  id="audioFileHeader"
                  label="Téléverser un fichier audio (MP3, WAV, etc.)"
                  onFileSelect={handleAudioFileSelect}
                  currentFile={localAudioFile.file}
                  onClear={handleClearAudio} // This will clear the uploaded file
                  accept="audio/*"
                  disabled={isSubmitting || !!recordedAudioBlob}
                  buttonText="Choisir un fichier audio"
                  clearButtonText="Retirer l'audio"
                  className="text-white placeholder-white/50 [&>label]:text-white/90 [&_button]:bg-white/10 [&_button]:hover:bg-white/20 [&_button]:border-white/30 [&_button:disabled]:opacity-70"
                />
              )}

              {/* Audio Recorder */}
              {!localAudioFile.file && (
                <AudioRecorder
                  onRecordingComplete={onRecordingComplete}
                  onRecordingError={onRecordingError}
                  disabled={isSubmitting || !!localAudioFile.file || !!recordedAudioBlob}
                />
              )}
            </div>
          </div>
        </div>

        {/* Footer with actions and info */}
        <div className="mt-4 pt-4 border-t border-white/20 flex flex-col sm:flex-row items-center justify-between gap-2 w-full">
          <div className="text-xs text-white/70">
            {lastSavedTimestamp ? `Dernière sauvegarde: ${lastSavedTimestamp}` : (mode === 'edit' ? 'Modifications non enregistrées' : 'Nouveau morceau')}
          </div>
          <div className="flex items-center space-x-2">
            {mode === 'edit' && onViewHistory && (
              <Button variant="outline" size="sm" onClick={onViewHistory} className="bg-white/10 hover:bg-white/20 border-white/30 text-white">
                <History className="mr-1.5 h-3.5 w-3.5" />
                Historique
              </Button>
            )}
            {onToggleVaultPanel && (
              <Button variant="outline" size="sm" onClick={onToggleVaultPanel} title={isVaultPanelCollapsed ? "Ouvrir Panneau Versions" : "Fermer Panneau Versions"} className="bg-white/10 hover:bg-white/20 border-white/30 text-white">
                {isVaultPanelCollapsed ? <PanelRightOpen className="h-3.5 w-3.5" /> : <PanelRightClose className="h-3.5 w-3.5" />}
                <span className="ml-1.5 hidden sm:inline">{isVaultPanelCollapsed ? "Versions" : "Fermer"}</span>
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SongFormHeader;
