"use client";
import { useEffect, useState } from "react";
import { getSupabaseClient } from "@/lib/supabase/client";
import { GlobalAudioPlayer } from "@/components/audio/global-audio-player";

import { usePathname } from "next/navigation";

export function AuthPlayerWrapper() {
  const [authenticated, setAuthenticated] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const supabase = getSupabaseClient();
    supabase.auth.getSession().then(({ data: { session } }) => {
      setAuthenticated(!!session);
    });
    const { data: listener } = supabase.auth.onAuthStateChange((event, session) => {
      setAuthenticated(!!session);
    });
    return () => {
      listener?.subscription.unsubscribe();
    };
  }, []);

  // Ne jamais afficher le player sur la page d'accueil publique
  if (!authenticated || pathname === "/") return null;
  return (
    <div
      style={{
        position: "fixed",
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
      }}
    >
      <GlobalAudioPlayer />
    </div>
  );
}
