"use client";

import { useAudioPlayerStore } from "@/lib/stores/audioPlayerStore";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, ListMusic, GripVertical } from "lucide-react";
import Image from "next/image";
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd";

interface QueueModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function QueueModal({ isOpen, onClose }: QueueModalProps) {
  const queue = useAudioPlayerStore(state => state.queue);
  const setQueue = useAudioPlayerStore(state => state.setQueue);
  const clearQueue = useAudioPlayerStore(state => state.clearQueue);
  const playFromQueue = useAudioPlayerStore(state => state.playFromQueue); // Changed from playSong to playFromQueue
  const currentSong = useAudioPlayerStore(state => state.currentSong);

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }
    const items = Array.from(queue);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setQueue(items, false); // playImmediately = false
  };

  const handleRemoveFromQueue = (indexToRemove: number) => {
    const newQueue = queue.filter((_, index) => index !== indexToRemove);
    setQueue(newQueue, false); // playImmediately = false
  };

  const handlePlayFromQueue = (song: (typeof queue)[0], index: number) => {
    // To play a song from queue and make it current,
    // we need to set currentSong and adjust the queue to be songs *after* it.
    // Or, the playSong in context could handle setting current and removing from queue if it's already there.
    // For now, let's assume playSong just plays it and it remains in queue until 'nextSong' logic handles it.
    // A more robust queue management would involve setting the current song and then the rest of the queue.
    
    playFromQueue(index); // Use the new playFromQueue method from the store
  };


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <ListMusic className="mr-2 h-5 w-5" />
            File d'attente
          </DialogTitle>
          <DialogDescription>
            Gérez les morceaux à venir. Vous pouvez les réorganiser par glisser-déposer.
          </DialogDescription>
        </DialogHeader>
        
        <ScrollArea className="max-h-[60vh] pr-3">
          {queue.length === 0 ? (
            <p className="text-sm text-muted-foreground py-8 text-center">La file d'attente est vide.</p>
          ) : (
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="queue">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                    {queue.map((song, index) => (
                      <Draggable key={song.id + `-${index}`} draggableId={song.id + `-${index}`} index={index}>
                        {(providedDraggable, snapshot) => (
                          <div
                            ref={providedDraggable.innerRef}
                            {...providedDraggable.draggableProps}
                            className={`flex items-center p-2 rounded-md border ${
                              snapshot.isDragging ? "bg-primary/20 shadow-lg" : "bg-card hover:bg-muted/50"
                            } ${currentSong?.id === song.id ? "border-primary" : "border-transparent"}`}
                          >
                            <button {...providedDraggable.dragHandleProps} className="p-1 mr-2 text-muted-foreground hover:text-foreground">
                              <GripVertical className="h-5 w-5" />
                            </button>
                            <div className="relative h-10 w-10 rounded overflow-hidden mr-3 flex-shrink-0">
                              <Image src={song.cover_url || "/placeholder.svg"} alt={song.title || "Pochette"} fill className="object-cover" />
                            </div>
                            <div className="flex-grow min-w-0 cursor-pointer" onClick={() => handlePlayFromQueue(song, index)}>
                              <p className={`font-medium truncate ${currentSong?.id === song.id ? "text-primary" : ""}`}>{song.title}</p>
                              <p className="text-xs text-muted-foreground truncate">{song.artist || "Artiste inconnu"}</p>
                            </div>
                            <Button variant="ghost" size="icon" onClick={() => handleRemoveFromQueue(index)} className="h-8 w-8 text-muted-foreground hover:text-destructive">
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </ScrollArea>

        {queue.length > 0 && (
          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={clearQueue}>Vider la file d'attente</Button>
            <DialogClose asChild><Button>Fermer</Button></DialogClose>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
