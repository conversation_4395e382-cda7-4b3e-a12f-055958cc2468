// Keep existing types

export interface UserProfile {
  id: string;
  username: string | null;
  display_name: string | null;
  avatar_url: string | null;
  bio?: string | null; // Added for featured artist bio
  // Add other profile fields as needed by cards/views
}

export interface Song {
  editor_data?: any; // Ajouté pour compatibilité avec SongForm et édition
  id: string;
  title: string;
  duration_ms: number | null; // Renamed from duration, matches schema
  cover_art_url: string | null; // Renamed from cover_url, matches schema
  audio_url: string | null; // Essential for playback
  creator_user_id: string; // Creator of the song
  artist_name?: string; // Often denormalized or joined
  album_id?: string | null;
  album_title?: string | null; // Denormalized or joined
  profiles?: UserProfile | null; // Creator profile data
  is_public?: boolean; // Added for public status
  slug?: string | null; // Added for public song pages
  genres?: string[] | null; // Standardized to genres (plural)
  subgenre?: string[] | null;
  mood?: string[] | null;
  theme?: string[] | null;
  instrumentation?: string[] | null;
  lyrics?: string | null;
  bpm?: number | null;
  key?: string | null;
  time_signature?: string | null;
  capo?: number | null; // Capo position
  release_date?: string | null; // ISO date string
  status?: 'draft' | 'published' | 'archived' | 'private' | 'unlisted' | null;
  is_explicit?: boolean | null;
  ai_content_origin?: 'none' | 'assisted' | 'hybrid' | '100%_ia' | null;
  ai_assist_percentage?: number | null;
  tags?: string[] | null;
  like_count?: number | null;
  dislike_count?: number | null;
  is_liked_by_current_user?: boolean | null; // Optional: for initial state from server
  is_disliked_by_current_user?: boolean | null; // Optional: for initial state from server
  plays?: number | null; // Number of plays
  // Add other song fields as needed
  [key: string]: any; // Allow other properties for flexibility with Supabase joins
}


export interface Album {
  id: string;
  title: string;
  cover_url: string | null;
  user_id: string; // Creator of the album
  artist_name?: string; // Denormalized or joined
  release_date?: string | null;
  profiles?: UserProfile | null; // Creator profile data
  created_at?: string; // Added for sorting
  updated_at?: string; // Added for consistency
  is_public?: boolean; // Added for LED indicator and toggling
  slug?: string | null; // Added for potential public album pages
  description?: string | null; // Added description
  genre?: string[] | null; // Column name is 'genre' (singular) but type is ARRAY
  moods?: string[] | null; // Column name is 'moods' (plural), type ARRAY
  instrumentation?: string[] | null; // Column name is 'instrumentation' (singular), type ARRAY
  // Add other album fields
  like_count?: number | null;
  is_liked_by_current_user?: boolean | null;
  follower_count?: number | null;
  is_followed_by_current_user?: boolean | null;
  view_count?: number | null;
  plays?: number | null;
}

export interface Band {
  id: string;
  name: string;
  avatar_url: string | null;
  cover_url?: string | null; // from schema inspection
  creator_id: string; 
  bio?: string | null; // Added for featured band bio
  description?: string | null;
  location?: string | null;
  banner_url?: string | null;
  created_at?: string;
  updated_at?: string;
  genres?: string[] | null; // from schema inspection
  moods?: string[] | null;
  instrumentation?: string[] | null;
  countries?: string[] | null;
  slug?: string | null; // from schema inspection
  is_public?: boolean; // from schema inspection
  are_comments_public?: boolean;
  follower_count?: number | null; // from schema inspection
  // Add other band fields
}

export interface BandMember {
  id: string; // band_members table's own id
  band_id: string;
  user_id: string;
  role: string | null;
  is_admin?: boolean;
  joined_at?: string;
  permissions?: string[] | null;
  profiles?: UserProfile | null; // Joined user profile data
}

export interface Playlist {
  id: string;
  name: string;
  cover_url: string | null;
  user_id: string; // Creator of the playlist
  is_public: boolean;
  created_at: string;
  songs_count?: number; 
  profiles?: UserProfile | null; 
  slug?: string | null; // Added for public links
  genres?: string[] | null; // Added for display
  moods?: string[] | null; // Added for display
  instrumentation?: string[] | null; // Already present in PlaylistFromView, ensure it's here too
  view_count?: number;
  like_count?: number;
  follower_count?: number;
  plays?: number;
  is_liked_by_current_user?: boolean | null;
  is_followed_by_current_user?: boolean | null;
}

// For Plan Limits hook
export interface PlanLimitsData {
  tier: 'free' | 'pro' | 'studio';
  uploads_per_month: number | null;
  vault_space_gb: number | null;
  vault_max_files: number | null;
  ia_credits_month: number | null;
  coins_month: number | null;
  page_customisation: boolean;
  analytics_level: 'basic' | 'extended' | 'pro';
  max_playlists: number | null;
  max_friends: number | null;
}

export interface UserUsageStats {
  monthly_uploads_count: number;
  vault_total_files: number;
  vault_total_size_bytes: number;
  // Add other stats as needed
}

// Type for song data used in AudioContext and players
export type AudioContextSong = Pick<Song, 'id' | 'title' | 'audio_url' | 'cover_url' | 'duration'> & {
  artist?: string; // Simplified artist display name
};

// Type for items in the audio queue
export interface QueueItem extends AudioContextSong {
  queueItemId: string; // Unique ID for this instance in the queue
}
