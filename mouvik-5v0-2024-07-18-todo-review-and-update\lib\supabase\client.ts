import { createBrowserClient as _createBrowserClient } from "@supabase/ssr"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Création du client Supabase côté client avec le helper Next.js
// Pas besoin de singleton, cette fonction est légère.
// Create a Supabase client for client components
// Note: We keep the function name getSupabaseClient for compatibility
// with existing code that might call it, but it now uses the ssr helper.
export function createBrowserClient() {
  return _createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

export const getSupabaseClient = () => {
  return createBrowserClient()
}
