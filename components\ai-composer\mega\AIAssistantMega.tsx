'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { 
  Brain, Sparkles, MessageSquare, History, Settings, Target,
  BarChart3, TrendingUp, Lightbulb, Wand2, Music, FileText,
  Guitar, Layers, Volume2, Heart, Clock, Users, Send,
  CheckCircle2, AlertCircle, Info, Zap, Eye, Star
} from 'lucide-react';

// Import des composants IA existants
import { AiConfigMenu } from '@/components/ia/ai-config-menu';
import { AIConnectionManager } from './AIConnectionManager';
import { useAIManager } from './AIManager';
import { PromptContext } from './AIPromptEngine';

interface AIAssistantMegaProps {
  isConfigured: boolean;
  aiLoading: boolean;
  aiError: string | null;
  aiConfig?: any;
  setAiConfig?: (config: any) => void;
  currentSection: string;
  songSections: any[];
  styleConfig: any;
  lyricsContent: string;
  aiHistory: any[];
  lastAiResult: string;
  setAiHistory: (history: any[]) => void;
  setLastAiResult: (result: string) => void;
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
  generalPrompt: string;
  projectMetrics: any;
}

type AnalysisDimension = 'structure' | 'emotion' | 'style' | 'harmony' | 'narrative' | 'commercial';
type AnalysisLevel = 'section' | 'song' | 'global';

export const AIAssistantMega: React.FC<AIAssistantMegaProps> = ({
  isConfigured,
  aiLoading,
  aiError,
  aiConfig,
  setAiConfig,
  currentSection,
  songSections,
  styleConfig,
  lyricsContent,
  aiHistory,
  lastAiResult,
  setAiHistory,
  setLastAiResult,
  onAIGenerate,
  generalPrompt,
  projectMetrics
}) => {

  const [activeTab, setActiveTab] = useState('suggestions');
  const [chatInput, setChatInput] = useState('');
  const [analysisDimension, setAnalysisDimension] = useState<AnalysisDimension>('structure');
  const [analysisLevel, setAnalysisLevel] = useState<AnalysisLevel>('section');

  // Utiliser le nouveau gestionnaire IA
  const {
    aiLoading: managerLoading,
    aiError: managerError,
    lastAiResult: managerResult,
    aiActivities,
    testConnection,
    generateSuggestion,
    analyzeContent,
    chatWithAI,
    suggestChords,
    improveChords,
    clearActivities
  } = useAIManager(aiConfig);

  // Créer le contexte pour les prompts
  const createPromptContext = (): PromptContext => {
    const currentSectionData = songSections.find(s => s.id === currentSection);
    return {
      // Contexte musical
      genre: styleConfig.genres?.[0] || 'pop',
      key: styleConfig.key || 'C',
      bpm: styleConfig.bpm || 120,
      timeSignature: styleConfig.timeSignature || '4/4',
      mood: styleConfig.mood || 'énergique',
      capo: styleConfig.capo || 0,

      // Contexte du projet
      generalPrompt: generalPrompt || '',
      songTitle: styleConfig.title || 'Nouvelle chanson',
      artist: styleConfig.artist || 'Artiste',
      description: styleConfig.description || '',

      // Contexte de la section
      currentSection: {
        id: currentSection,
        type: currentSectionData?.type || 'verse',
        title: currentSectionData?.title || 'Section',
        content: lyricsContent,
        chords: currentSectionData?.chords || [],
        duration: currentSectionData?.duration || 16
      },

      // Contexte global
      allSections: songSections,
      totalWords: lyricsContent.trim().split(/\s+/).filter(Boolean).length,
      totalSections: songSections.length,
      completionScore: projectMetrics.overall || 0,

      // Historique IA
      recentInteractions: aiHistory.slice(-5).map(h => h.content || '')
    };
  };

  // Dimensions d'analyse multi-niveaux
  const analysisDimensions = [
    {
      id: 'structure' as AnalysisDimension,
      label: 'Structure',
      icon: Layers,
      color: 'text-blue-400',
      description: 'Organisation et progression'
    },
    {
      id: 'emotion' as AnalysisDimension,
      label: 'Émotion',
      icon: Heart,
      color: 'text-red-400',
      description: 'Impact émotionnel et ressenti'
    },
    {
      id: 'style' as AnalysisDimension,
      label: 'Style',
      icon: Star,
      color: 'text-purple-400',
      description: 'Cohérence stylistique'
    },
    {
      id: 'harmony' as AnalysisDimension,
      label: 'Harmonie',
      icon: Music,
      color: 'text-green-400',
      description: 'Accords et mélodie'
    },
    {
      id: 'narrative' as AnalysisDimension,
      label: 'Narratif',
      icon: FileText,
      color: 'text-orange-400',
      description: 'Histoire et message'
    },
    {
      id: 'commercial' as AnalysisDimension,
      label: 'Commercial',
      icon: TrendingUp,
      color: 'text-yellow-400',
      description: 'Potentiel commercial'
    }
  ];

  // Niveaux d'analyse
  const analysisLevels = [
    { id: 'section' as AnalysisLevel, label: 'Section', icon: Target, description: 'Section actuelle' },
    { id: 'song' as AnalysisLevel, label: 'Chanson', icon: Music, description: 'Chanson complète' },
    { id: 'global' as AnalysisLevel, label: 'Global', icon: Eye, description: 'Vision d\'ensemble' }
  ];

  // Actions IA spécialisées enrichies par dimension
  const getAIActionsForDimension = useCallback((dimension: AnalysisDimension, level: AnalysisLevel) => {
    const currentSectionData = songSections.find(s => s.id === currentSection);
    const sectionType = currentSectionData?.type || 'verse';
    const wordCount = lyricsContent.trim().split(/\s+/).filter(Boolean).length;
    const genre = styleConfig.genres?.[0] || 'pop';
    const key = styleConfig.key || 'C';
    const bpm = styleConfig.bpm || 120;

    const contextInfo = `
CONTEXTE MUSICAL :
- Genre : ${genre}
- Tonalité : ${key} majeur
- Tempo : ${bpm} BPM
- Section actuelle : ${sectionType} (${wordCount} mots)
- Sections totales : ${songSections.length}
- Vision du projet : ${generalPrompt.substring(0, 200)}...

CONTENU ACTUEL :
"${lyricsContent.substring(0, 300)}${lyricsContent.length > 300 ? '...' : ''}"

ACCORDS ACTUELS : ${currentSectionData?.chords?.map(c => c.name).join(' - ') || 'Aucun'}
`;

    const detailedPrompts = {
      structure: {
        section: `${contextInfo}

ANALYSE STRUCTURELLE DE SECTION :
Analyse en détail la structure de cette section de ${sectionType} :

1. ORGANISATION INTERNE :
   - Découpage en phrases musicales
   - Équilibre des parties A/B
   - Points de tension et résolution
   - Cohérence métrique

2. FONCTION DANS LA CHANSON :
   - Rôle par rapport aux autres sections
   - Transition entrante et sortante
   - Niveau d'énergie approprié
   - Mémorabilité et impact

3. SUGGESTIONS CONCRÈTES :
   - Réorganisation des phrases
   - Ajouts ou suppressions
   - Variations rythmiques
   - Points d'accroche à renforcer

Donne des conseils précis et applicables immédiatement.`,

        song: `${contextInfo}

ANALYSE STRUCTURELLE GLOBALE :
Analyse la structure complète de cette chanson :

SECTIONS ACTUELLES : ${songSections.map(s => `${s.type} (${s.title})`).join(' → ')}

1. ARCHITECTURE GÉNÉRALE :
   - Équilibre des sections (durées, intensités)
   - Progression dramatique
   - Points culminants et respirations
   - Cohérence du parcours musical

2. COMPARAISON AVEC LES STANDARDS ${genre.toUpperCase()} :
   - Structure typique du genre
   - Écarts créatifs justifiés
   - Opportunités d'innovation
   - Risques commerciaux

3. OPTIMISATIONS RECOMMANDÉES :
   - Réorganisation des sections
   - Ajout/suppression de parties
   - Variations et reprises
   - Transitions à améliorer

Propose un plan de restructuration détaillé.`,

        global: `${contextInfo}

ÉVALUATION STRUCTURELLE COMPLÈTE :
Évalue la cohérence structurelle de tout le projet :

1. VISION ARCHITECTURALE :
   - Adéquation structure/vision artistique
   - Cohérence avec les influences citées
   - Innovation vs conventions
   - Potentiel d'évolution

2. ANALYSE COMPARATIVE :
   - Benchmarking avec succès du genre
   - Identification des forces uniques
   - Points de différenciation
   - Opportunités d'amélioration

3. ROADMAP STRUCTURELLE :
   - Étapes de développement
   - Priorités d'optimisation
   - Expérimentations à tenter
   - Validation par le public cible

Fournis une stratégie structurelle complète.`
      },

      emotion: {
        section: `${contextInfo}

ANALYSE ÉMOTIONNELLE DE SECTION :
Analyse l'impact émotionnel de cette section :

1. PALETTE ÉMOTIONNELLE :
   - Émotions primaires exprimées
   - Nuances et subtilités
   - Intensité émotionnelle (1-10)
   - Authenticité du ressenti

2. TECHNIQUES EXPRESSIVES :
   - Choix lexicaux et leur impact
   - Rythme et prosodie
   - Images et métaphores
   - Progression émotionnelle

3. OPTIMISATIONS ÉMOTIONNELLES :
   - Mots plus puissants
   - Contrastes à créer
   - Moments de vulnérabilité
   - Pics d'intensité à placer

Propose des modifications concrètes pour maximiser l'impact émotionnel.`,

        song: `${contextInfo}

PARCOURS ÉMOTIONNEL GLOBAL :
Analyse le voyage émotionnel de la chanson complète :

1. CARTOGRAPHIE ÉMOTIONNELLE :
   - Émotion de chaque section
   - Transitions émotionnelles
   - Cohérence du parcours
   - Points de rupture/surprise

2. DYNAMIQUE ÉMOTIONNELLE :
   - Montée en puissance
   - Moments de répit
   - Climax émotionnel
   - Résolution satisfaisante

3. STRATÉGIE D'IMPACT :
   - Émotions cibles du public
   - Techniques de connexion
   - Universalité vs spécificité
   - Mémorabilité émotionnelle

Dessine un parcours émotionnel optimisé.`,

        global: `${contextInfo}

COHÉRENCE ÉMOTIONNELLE TOTALE :
Évalue l'alignement émotionnel avec la vision artistique :

1. AUTHENTICITÉ ARTISTIQUE :
   - Fidélité à la vision personnelle
   - Cohérence avec les influences
   - Originalité émotionnelle
   - Signature artistique

2. RÉSONANCE UNIVERSELLE :
   - Émotions partagées humainement
   - Accessibilité culturelle
   - Intemporalité du message
   - Potentiel de connexion

3. STRATÉGIE ÉMOTIONNELLE :
   - Positionnement sur le marché
   - Différenciation émotionnelle
   - Public cible émotionnel
   - Impact à long terme

Définis une identité émotionnelle unique et puissante.`
      },

      style: {
        section: `${contextInfo}

ANALYSE STYLISTIQUE DE SECTION :
Vérifie la cohérence stylistique de cette section :

1. CODES DU GENRE ${genre.toUpperCase()} :
   - Respect des conventions
   - Innovations acceptables
   - Écarts créatifs
   - Authenticité stylistique

2. ÉLÉMENTS STYLISTIQUES :
   - Vocabulaire et registre
   - Rythme et métrique
   - Références culturelles
   - Sonorités caractéristiques

3. OPTIMISATIONS STYLISTIQUES :
   - Renforcement des codes
   - Touches personnelles
   - Équilibre tradition/innovation
   - Signature artistique

Propose des ajustements pour parfaire le style.`,

        song: `${contextInfo}

UNITÉ STYLISTIQUE GLOBALE :
Analyse la cohérence stylistique de toute la chanson :

1. HOMOGÉNÉITÉ STYLISTIQUE :
   - Constance du registre
   - Évolution contrôlée
   - Variations justifiées
   - Identité reconnaissable

2. INFLUENCES ET RÉFÉRENCES :
   - Intégration des influences citées
   - Originalité de la synthèse
   - Évitement du pastiche
   - Création d'un style propre

3. POSITIONNEMENT ARTISTIQUE :
   - Place dans le paysage musical
   - Différenciation concurrentielle
   - Évolution stylistique
   - Potentiel de reconnaissance

Définis une identité stylistique forte et cohérente.`,

        global: `${contextInfo}

VISION STYLISTIQUE COMPLÈTE :
Évalue la cohérence stylistique globale du projet :

1. SIGNATURE ARTISTIQUE :
   - Éléments distinctifs
   - Reconnaissance immédiate
   - Évolution vs constance
   - Potentiel iconique

2. ÉCOSYSTÈME STYLISTIQUE :
   - Cohérence avec l'univers artistique
   - Déclinaisons possibles
   - Évolution future
   - Pérennité du style

3. STRATÉGIE STYLISTIQUE :
   - Positionnement marché
   - Différenciation artistique
   - Évolution de carrière
   - Impact culturel

Construis une identité stylistique durable et impactante.`
      },

      harmony: {
        section: `${contextInfo}

ANALYSE HARMONIQUE DE SECTION :
Analyse et optimise l'harmonie de cette section :

1. PROGRESSION ACTUELLE :
   - Analyse fonctionnelle des accords
   - Couleurs harmoniques
   - Tensions et résolutions
   - Richesse harmonique

2. ADÉQUATION AU GENRE ${genre.toUpperCase()} :
   - Progressions typiques
   - Innovations harmoniques
   - Complexité appropriée
   - Accessibilité mélodique

3. SUGGESTIONS D'ENRICHISSEMENT :
   - Accords de substitution
   - Extensions harmoniques
   - Modulations possibles
   - Variations rythmiques

Propose une progression harmonique optimisée avec tablatures.`,

        song: `${contextInfo}

ARCHITECTURE HARMONIQUE GLOBALE :
Analyse l'harmonie complète de la chanson :

1. COHÉRENCE HARMONIQUE :
   - Unité tonale
   - Progressions récurrentes
   - Variations par section
   - Développement harmonique

2. DYNAMIQUE HARMONIQUE :
   - Tension/détente
   - Points culminants
   - Modulations stratégiques
   - Retours thématiques

3. PLAN HARMONIQUE OPTIMISÉ :
   - Répartition des couleurs
   - Progression dramatique
   - Mémorabilité des accroches
   - Sophistication équilibrée

Dessine un plan harmonique complet avec alternatives.`,

        global: `${contextInfo}

VISION HARMONIQUE COMPLÈTE :
Évalue la richesse harmonique du projet entier :

1. PALETTE HARMONIQUE :
   - Richesse du vocabulaire
   - Originalité des couleurs
   - Signature harmonique
   - Évolution stylistique

2. STRATÉGIE HARMONIQUE :
   - Complexité vs accessibilité
   - Innovation vs tradition
   - Reconnaissance vs surprise
   - Évolution artistique

3. DÉVELOPPEMENT HARMONIQUE :
   - Approfondissement possible
   - Expérimentations futures
   - Influence sur le style
   - Potentiel pédagogique

Construis une identité harmonique unique et évolutive.`
      },

      narrative: {
        section: `${contextInfo}

ANALYSE NARRATIVE DE SECTION :
Analyse la force narrative de cette section :

1. CONTENU NARRATIF :
   - Message principal
   - Progression de l'histoire
   - Personnages/situations
   - Émotions véhiculées

2. TECHNIQUES NARRATIVES :
   - Point de vue adopté
   - Temporalité
   - Images et métaphores
   - Rythme narratif

3. OPTIMISATIONS NARRATIVES :
   - Clarification du message
   - Renforcement des images
   - Amélioration du flow
   - Impact émotionnel

Propose une version narrative optimisée.`,

        song: `${contextInfo}

COHÉRENCE NARRATIVE GLOBALE :
Analyse l'histoire complète racontée par la chanson :

1. STRUCTURE NARRATIVE :
   - Début/développement/fin
   - Progression logique
   - Points de retournement
   - Résolution satisfaisante

2. FORCE DU MESSAGE :
   - Clarté du propos
   - Universalité du thème
   - Originalité de l'angle
   - Impact mémoriel

3. OPTIMISATION NARRATIVE :
   - Renforcement de l'arc
   - Amélioration des transitions
   - Enrichissement des détails
   - Puissance de la conclusion

Construis une narration puissante et cohérente.`,

        global: `${contextInfo}

VISION NARRATIVE COMPLÈTE :
Évalue la force narrative selon la vision artistique :

1. ALIGNEMENT NARRATIF :
   - Fidélité à la vision
   - Cohérence thématique
   - Authenticité du message
   - Originalité de l'approche

2. IMPACT NARRATIF :
   - Résonance universelle
   - Mémorabilité du message
   - Potentiel d'identification
   - Force transformatrice

3. STRATÉGIE NARRATIVE :
   - Positionnement thématique
   - Évolution du message
   - Déclinaisons possibles
   - Héritage artistique

Définis une vision narrative puissante et durable.`
      },

      commercial: {
        section: `${contextInfo}

POTENTIEL COMMERCIAL DE SECTION :
Évalue l'attractivité commerciale de cette section :

1. FACTEURS D'ACCROCHE :
   - Mémorabilité immédiate
   - Facilité de chant
   - Phrases accrocheuses
   - Potentiel viral

2. ANALYSE MARCHÉ ${genre.toUpperCase()} :
   - Codes commerciaux respectés
   - Différenciation attractive
   - Tendances actuelles
   - Intemporalité

3. OPTIMISATIONS COMMERCIALES :
   - Hooks plus puissants
   - Simplification stratégique
   - Répétitions efficaces
   - Moments marquants

Propose des ajustements pour maximiser l'impact commercial.`,

        song: `${contextInfo}

ATTRACTIVITÉ COMMERCIALE GLOBALE :
Analyse le potentiel commercial de la chanson complète :

1. STRUCTURE COMMERCIALE :
   - Intro accrocheuse
   - Refrain mémorable
   - Durée optimale
   - Progression engageante

2. BENCHMARKING MARCHÉ :
   - Comparaison avec les hits
   - Facteurs de succès identifiés
   - Opportunités de marché
   - Risques commerciaux

3. STRATÉGIE COMMERCIALE :
   - Optimisations prioritaires
   - Public cible précis
   - Canaux de diffusion
   - Potentiel de crossover

Fournis un plan d'optimisation commerciale détaillé.`,

        global: `${contextInfo}

VISION COMMERCIALE COMPLÈTE :
Évalue le potentiel commercial selon les tendances actuelles :

1. POSITIONNEMENT MARCHÉ :
   - Niche vs mainstream
   - Différenciation concurrentielle
   - Timing de sortie
   - Évolution des goûts

2. STRATÉGIE COMMERCIALE :
   - Développement de l'audience
   - Monétisation optimale
   - Partenariats stratégiques
   - Expansion internationale

3. VISION LONG TERME :
   - Durabilité commerciale
   - Évolution de carrière
   - Construction de catalogue
   - Impact sur l'industrie

Construis une stratégie commerciale ambitieuse et réaliste.`
      }
    };

    return detailedPrompts[dimension][level];
  }, [songSections, currentSection, lyricsContent, styleConfig, generalPrompt]);

  // Nouvelles suggestions IA enrichies
  const enhancedSuggestions = [
    {
      id: 'continue-writing',
      label: 'Continuer l\'écriture',
      icon: SkipForward,
      description: 'Développer le contenu existant avec cohérence',
      action: () => generateSuggestion(createPromptContext(), 'continueWriting'),
      color: 'bg-blue-500'
    },
    {
      id: 'improve-content',
      label: 'Améliorer le contenu',
      icon: Wand2,
      description: 'Optimiser rimes, flow et impact émotionnel',
      action: () => generateSuggestion(createPromptContext(), 'improveContent'),
      color: 'bg-green-500'
    },
    {
      id: 'generate-rhymes',
      label: 'Suggestions de rimes',
      icon: Hash,
      description: 'Proposer des rimes créatives et originales',
      action: () => generateSuggestion(createPromptContext(), 'generateRhymes'),
      color: 'bg-purple-500'
    },
    {
      id: 'suggest-chords',
      label: 'Suggérer accords',
      icon: Music,
      description: 'Progression harmonique adaptée au contenu',
      action: () => suggestChords(createPromptContext()),
      color: 'bg-orange-500'
    },
    {
      id: 'analyze-structure',
      label: 'Analyser structure',
      icon: Layers,
      description: 'Optimiser l\'organisation et les transitions',
      action: () => generateSuggestion(createPromptContext(), 'analyzeStructure'),
      color: 'bg-indigo-500'
    }
  ];

  // Gestionnaire pour l'analyse IA
  const handleAIAnalysis = useCallback(async () => {
    try {
      const result = await analyzeContent(createPromptContext(), analysisDimension, analysisLevel);
      setLastAiResult(result);
      setAiHistory(prev => [...prev,
        { role: 'user', content: `Analyse ${analysisDimension} niveau ${analysisLevel}` },
        { role: 'assistant', content: result }
      ]);
    } catch (error) {
      console.error('Erreur analyse IA:', error);
    }
  }, [analysisDimension, analysisLevel, analyzeContent, setLastAiResult, setAiHistory]);

  // Gestionnaire pour le chat
  const handleChatSubmit = useCallback(async () => {
    if (!chatInput.trim() || !isConfigured) return;

    try {
      const result = await chatWithAI(createPromptContext(), chatInput);
      setLastAiResult(result);
      setAiHistory(prev => [...prev,
        { role: 'user', content: chatInput },
        { role: 'assistant', content: result }
      ]);
      setChatInput('');
    } catch (error) {
      console.error('Erreur chat IA:', error);
    }
  }, [chatInput, isConfigured, chatWithAI, setLastAiResult, setAiHistory]);

  // Gestionnaire pour les suggestions enrichies
  const handleEnhancedSuggestion = useCallback(async (suggestion: any) => {
    if (!isConfigured) return;

    try {
      const result = await suggestion.action();
      setLastAiResult(result);
      setAiHistory(prev => [...prev,
        { role: 'user', content: suggestion.label },
        { role: 'assistant', content: result }
      ]);
    } catch (error) {
      console.error('Erreur suggestion IA:', error);
    }
  }, [isConfigured, setLastAiResult, setAiHistory]);

  // Suggestions intelligentes enrichies et concrètes
  const smartSuggestions = React.useMemo(() => {
    const suggestions = [];
    const currentSectionData = songSections.find(s => s.id === currentSection);
    const wordCount = lyricsContent.trim().split(/\s+/).filter(Boolean).length;
    const lineCount = lyricsContent.split('\n').length;
    const hasChords = currentSectionData?.chords?.length > 0;
    const sectionType = currentSectionData?.type || 'verse';

    // Suggestions basées sur le contenu actuel
    if (wordCount === 0) {
      suggestions.push({
        type: 'urgent',
        icon: FileText,
        title: 'Commencer l\'écriture',
        description: `Créez les premières paroles pour ce ${sectionType}`,
        action: `Écris des paroles accrocheuses pour un ${sectionType} dans le style ${styleConfig.genres?.[0] || 'pop'}. Thème : ${generalPrompt.substring(0, 100)}...`,
        priority: 10
      });
    }

    if (wordCount > 0 && wordCount < 15) {
      suggestions.push({
        type: 'info',
        icon: SkipForward,
        title: 'Développer les paroles',
        description: `Votre ${sectionType} est trop court (${wordCount} mots)`,
        action: `Continue et développe ces paroles pour créer un ${sectionType} complet : "${lyricsContent.substring(0, 50)}..."`,
        priority: 8
      });
    }

    if (wordCount > 80) {
      suggestions.push({
        type: 'warning',
        icon: Target,
        title: 'Condenser le contenu',
        description: `Votre ${sectionType} est très long (${wordCount} mots)`,
        action: `Condense ces paroles en gardant l'essentiel et l'impact : "${lyricsContent.substring(0, 100)}..."`,
        priority: 6
      });
    }

    // Suggestions harmoniques
    if (!hasChords && wordCount > 10) {
      suggestions.push({
        type: 'info',
        icon: Music,
        title: 'Ajouter des accords',
        description: 'Votre section n\'a pas d\'accompagnement harmonique',
        action: `Suggère une progression d'accords parfaite pour ces paroles de ${sectionType} : "${lyricsContent.substring(0, 100)}..." en ${styleConfig.key || 'C'} majeur`,
        priority: 7
      });
    }

    if (hasChords && currentSectionData.chords.length < 3) {
      suggestions.push({
        type: 'info',
        icon: Guitar,
        title: 'Enrichir l\'harmonie',
        description: `Seulement ${currentSectionData.chords.length} accord(s) dans cette section`,
        action: `Enrichis cette progression d'accords : ${currentSectionData.chords.map(c => c.name).join(' - ')} pour un ${sectionType} plus expressif`,
        priority: 5
      });
    }

    // Suggestions stylistiques
    if (wordCount > 20) {
      const rhymeWords = lyricsContent.match(/\b\w+\b/g)?.slice(-5) || [];
      suggestions.push({
        type: 'creative',
        icon: Hash,
        title: 'Améliorer les rimes',
        description: 'Optimiser la structure rimique',
        action: `Améliore les rimes de ces paroles en gardant le sens : "${lyricsContent.substring(0, 150)}..." Propose des alternatives plus musicales.`,
        priority: 4
      });
    }

    // Suggestions narratives
    if (wordCount > 15 && !lyricsContent.includes('?') && !lyricsContent.includes('!')) {
      suggestions.push({
        type: 'creative',
        icon: Heart,
        title: 'Ajouter de l\'émotion',
        description: 'Votre texte manque d\'exclamations ou questions',
        action: `Rends ces paroles plus émotionnelles et expressives : "${lyricsContent.substring(0, 100)}..." Ajoute des questions rhétoriques ou exclamations.`,
        priority: 3
      });
    }

    // Suggestions de structure globale
    const verseCount = songSections.filter(s => s.type === 'verse').length;
    const chorusCount = songSections.filter(s => s.type === 'chorus').length;

    if (verseCount > 0 && chorusCount === 0) {
      suggestions.push({
        type: 'warning',
        icon: Layers,
        title: 'Ajouter un refrain',
        description: 'Votre chanson n\'a pas de refrain',
        action: `Crée un refrain accrocheur qui résume le message principal de la chanson. Style : ${styleConfig.genres?.[0] || 'pop'}`,
        priority: 9
      });
    }

    if (verseCount > 2 && songSections.filter(s => s.type === 'bridge').length === 0) {
      suggestions.push({
        type: 'info',
        icon: Target,
        title: 'Ajouter un pont',
        description: 'Un pont apporterait de la variété',
        action: `Crée un pont musical qui offre une perspective différente ou un moment de réflexion dans la chanson`,
        priority: 4
      });
    }

    // Suggestions commerciales
    if (projectMetrics.overall > 70) {
      suggestions.push({
        type: 'success',
        icon: TrendingUp,
        title: 'Optimiser pour le succès',
        description: 'Votre chanson est bien avancée',
        action: `Analyse le potentiel commercial de cette chanson et suggère des ajustements pour maximiser son impact : structure, accroches, mémorabilité`,
        priority: 2
      });
    }

    // Suggestions techniques avancées
    if (wordCount > 30) {
      suggestions.push({
        type: 'advanced',
        icon: Wand2,
        title: 'Analyse prosodique',
        description: 'Optimiser le rythme des paroles',
        action: `Analyse la prosodie de ces paroles : "${lyricsContent.substring(0, 100)}..." Suggère des ajustements pour améliorer le flow et la musicalité`,
        priority: 3
      });
    }

    // Suggestions créatives avancées
    if (projectMetrics.overall > 50) {
      suggestions.push({
        type: 'creative',
        icon: Sparkles,
        title: 'Variations créatives',
        description: 'Explorer des alternatives artistiques',
        action: `Propose 3 variations créatives de cette section : 1) Version plus intense, 2) Version plus douce, 3) Version avec métaphores originales`,
        priority: 1
      });
    }

    // Trier par priorité et retourner les 8 meilleures
    return suggestions
      .sort((a, b) => (b.priority || 0) - (a.priority || 0))
      .slice(0, 8);
  }, [projectMetrics, songSections, currentSection, lyricsContent, styleConfig, generalPrompt]);

  return (
    <div className="h-full flex flex-col bg-slate-800/30">
      {/* En-tête */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="font-bold text-white">Assistant IA Mega</h2>
              <p className="text-sm text-slate-400">Analyse multi-dimensionnelle</p>
            </div>
          </div>
          
          {/* Statut */}
          <div className="flex items-center gap-2">
            {isConfigured ? (
              <Badge variant="default" className="gap-1 bg-green-500">
                <CheckCircle2 className="h-3 w-3" />
                Actif
              </Badge>
            ) : (
              <Badge variant="destructive" className="gap-1">
                <AlertCircle className="h-3 w-3" />
                Config
              </Badge>
            )}
            {aiLoading && (
              <Badge variant="secondary" className="gap-1">
                <Sparkles className="h-3 w-3 animate-spin" />
                Analyse...
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-5 bg-slate-700/50 m-2">
            <TabsTrigger value="suggestions" className="gap-1">
              <Lightbulb className="h-3 w-3" />
              Suggestions
            </TabsTrigger>
            <TabsTrigger value="analysis" className="gap-1">
              <BarChart3 className="h-3 w-3" />
              Analyse
            </TabsTrigger>
            <TabsTrigger value="chat" className="gap-1">
              <MessageSquare className="h-3 w-3" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="connection" className="gap-1">
              <Zap className="h-3 w-3" />
              Connexion
            </TabsTrigger>
            <TabsTrigger value="config" className="gap-1">
              <Settings className="h-3 w-3" />
              Config
            </TabsTrigger>
          </TabsList>
          
          <div className="flex-1 overflow-hidden">
            {/* Suggestions enrichies */}
            <TabsContent value="suggestions" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  {!isConfigured && (
                    <Card className="bg-orange-500/10 border-orange-500/20">
                      <CardContent className="p-3">
                        <div className="flex items-center gap-2 text-orange-400 text-sm">
                          <AlertCircle className="h-4 w-4" />
                          Configurez votre IA pour utiliser ces fonctionnalités
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <h3 className="text-sm font-medium text-white mb-3">Suggestions IA Enrichies</h3>

                  {enhancedSuggestions.map((suggestion) => {
                    const Icon = suggestion.icon;
                    return (
                      <Button
                        key={suggestion.id}
                        variant="outline"
                        size="sm"
                        onClick={() => handleEnhancedSuggestion(suggestion)}
                        disabled={!isConfigured || managerLoading}
                        className={`w-full h-auto p-3 flex flex-col items-start gap-2 ${suggestion.color} text-white border-slate-600 hover:border-slate-500`}
                      >
                        <div className="flex items-center gap-2 w-full">
                          <Icon className="h-4 w-4" />
                          <span className="font-medium">{suggestion.label}</span>
                          {managerLoading && <Loader2 className="h-3 w-3 animate-spin ml-auto" />}
                        </div>
                        <span className="text-xs opacity-80 text-left">
                          {suggestion.description}
                        </span>
                      </Button>
                    );
                  })}

                  {/* Suggestions intelligentes existantes */}
                  <div className="mt-6">
                    <h3 className="text-sm font-medium text-white mb-3">Suggestions Contextuelles</h3>
                    {smartSuggestions.map((suggestion, index) => {
                      const Icon = suggestion.icon;
                      return (
                        <Card
                          key={index}
                          className={`cursor-pointer transition-all bg-slate-700/50 border-slate-600 hover:border-slate-500 mb-2`}
                          onClick={() => handleEnhancedSuggestion({
                            action: () => generateSuggestion(createPromptContext(), 'continueWriting')
                          })}
                        >
                          <CardContent className="p-3">
                            <div className="flex items-start gap-3">
                              <Icon className={`h-5 w-5 mt-0.5 ${
                                suggestion.type === 'success' ? 'text-green-400' :
                                suggestion.type === 'warning' ? 'text-yellow-400' :
                                'text-blue-400'
                              }`} />
                              <div className="flex-1">
                                <div className="font-medium text-white text-sm mb-1">
                                  {suggestion.title}
                                </div>
                                <div className="text-xs text-slate-400 mb-2">
                                  {suggestion.description}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Analyse multi-dimensionnelle */}
            <TabsContent value="analysis" className="h-full m-0 p-4">
              <div className="space-y-4">
                {/* Sélecteur de dimension */}
                <div>
                  <h3 className="text-sm font-medium text-white mb-2">Dimension d'analyse</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {analysisDimensions.map((dimension) => {
                      const Icon = dimension.icon;
                      return (
                        <Button
                          key={dimension.id}
                          variant={analysisDimension === dimension.id ? "default" : "outline"}
                          size="sm"
                          onClick={() => setAnalysisDimension(dimension.id)}
                          className={`gap-2 h-auto p-3 flex flex-col items-start ${
                            analysisDimension === dimension.id ? 'bg-slate-600' : ''
                          }`}
                        >
                          <div className="flex items-center gap-2 w-full">
                            <Icon className={`h-4 w-4 ${dimension.color}`} />
                            <span className="font-medium">{dimension.label}</span>
                          </div>
                          <span className="text-xs opacity-80 text-left">
                            {dimension.description}
                          </span>
                        </Button>
                      );
                    })}
                  </div>
                </div>

                {/* Sélecteur de niveau */}
                <div>
                  <h3 className="text-sm font-medium text-white mb-2">Niveau d'analyse</h3>
                  <div className="flex gap-1 bg-slate-700/50 rounded-lg p-1">
                    {analysisLevels.map((level) => {
                      const Icon = level.icon;
                      return (
                        <Button
                          key={level.id}
                          variant={analysisLevel === level.id ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setAnalysisLevel(level.id)}
                          className="gap-1 flex-1"
                          title={level.description}
                        >
                          <Icon className="h-3 w-3" />
                          {level.label}
                        </Button>
                      );
                    })}
                  </div>
                </div>

                {/* Bouton d'analyse */}
                <Button
                  onClick={handleAIAnalysis}
                  disabled={!isConfigured || aiLoading}
                  className="w-full gap-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                >
                  <Wand2 className="h-4 w-4" />
                  Analyser {analysisDimensions.find(d => d.id === analysisDimension)?.label} - {analysisLevels.find(l => l.id === analysisLevel)?.label}
                </Button>

                {/* Résultat de la dernière analyse */}
                {lastAiResult && (
                  <Card className="bg-slate-700/50 border-slate-600">
                    <CardHeader>
                      <CardTitle className="text-sm text-white">Dernière Analyse</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm text-slate-300">
                        {lastAiResult.substring(0, 200)}
                        {lastAiResult.length > 200 && '...'}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            {/* Suggestions intelligentes */}
            <TabsContent value="suggestions" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-white">Suggestions Intelligentes</h3>
                  
                  {smartSuggestions.map((suggestion, index) => {
                    const Icon = suggestion.icon;
                    return (
                      <Card 
                        key={index}
                        className={`cursor-pointer transition-all bg-slate-700/50 border-slate-600 hover:border-slate-500`}
                        onClick={() => onAIGenerate(suggestion.action, 'suggestion')}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-start gap-3">
                            <Icon className={`h-5 w-5 mt-0.5 ${
                              suggestion.type === 'success' ? 'text-green-400' :
                              suggestion.type === 'warning' ? 'text-yellow-400' :
                              'text-blue-400'
                            }`} />
                            <div className="flex-1">
                              <div className="font-medium text-white text-sm mb-1">
                                {suggestion.title}
                              </div>
                              <div className="text-xs text-slate-400 mb-2">
                                {suggestion.description}
                              </div>
                              <div className="text-xs text-slate-300 italic">
                                Action: {suggestion.action}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                  
                  {smartSuggestions.length === 0 && (
                    <div className="text-center text-slate-400 py-8">
                      <Lightbulb className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>Votre projet semble bien équilibré !</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Chat IA */}
            <TabsContent value="chat" className="h-full m-0 p-4">
              <div className="h-full flex flex-col">
                <div className="flex-1 mb-4">
                  <ScrollArea className="h-full border border-slate-600 rounded-lg p-3 bg-slate-800/50">
                    {aiHistory.length === 0 ? (
                      <div className="text-center text-slate-400 py-8">
                        <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Commencez une conversation avec l'IA</p>
                        <p className="text-xs mt-1">Toutes vos questions bénéficient du contexte général</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {aiHistory.slice(-10).map((message, index) => (
                          <div 
                            key={index}
                            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div 
                              className={`max-w-[80%] p-3 rounded-lg text-sm ${
                                message.role === 'user' 
                                  ? 'bg-blue-500 text-white' 
                                  : 'bg-slate-700 text-white border border-slate-600'
                              }`}
                            >
                              {message.content}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
                
                <div className="flex gap-2">
                  <Textarea
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && (e.preventDefault(), handleChatSubmit())}
                    placeholder="Posez une question à l'IA (contexte général inclus)..."
                    className="flex-1 bg-slate-800 border-slate-600 text-white text-sm placeholder-slate-400 resize-none"
                    rows={2}
                    disabled={!isConfigured || aiLoading}
                  />
                  <Button 
                    onClick={handleChatSubmit}
                    disabled={!isConfigured || aiLoading || !chatInput.trim()}
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600 self-end"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Connexion IA */}
            <TabsContent value="connection" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <AIConnectionManager
                  aiConfig={aiConfig}
                  setAiConfig={setAiConfig}
                  onTestConnection={testConnection}
                />
              </ScrollArea>
            </TabsContent>

            {/* Configuration */}
            <TabsContent value="config" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <AiConfigMenu inlineMode={true} />
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};
