'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { 
  Brain, Sparkles, MessageSquare, History, Settings, Target,
  BarChart3, TrendingUp, Lightbulb, Wand2, Music, FileText,
  Guitar, Layers, Volume2, Heart, Clock, Users, Send,
  CheckCircle2, AlertCircle, Info, Zap, Eye, Star
} from 'lucide-react';

// Import des composants IA existants
import { AiConfigMenu } from '@/components/ia/ai-config-menu';

interface AIAssistantMegaProps {
  isConfigured: boolean;
  aiLoading: boolean;
  aiError: string | null;
  aiConfig?: any;
  setAiConfig?: (config: any) => void;
  currentSection: string;
  songSections: any[];
  styleConfig: any;
  lyricsContent: string;
  aiHistory: any[];
  lastAiResult: string;
  setAiHistory: (history: any[]) => void;
  setLastAiResult: (result: string) => void;
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
  generalPrompt: string;
  projectMetrics: any;
}

type AnalysisDimension = 'structure' | 'emotion' | 'style' | 'harmony' | 'narrative' | 'commercial';
type AnalysisLevel = 'section' | 'song' | 'global';

export const AIAssistantMega: React.FC<AIAssistantMegaProps> = ({
  isConfigured,
  aiLoading,
  aiError,
  aiConfig,
  setAiConfig,
  currentSection,
  songSections,
  styleConfig,
  lyricsContent,
  aiHistory,
  lastAiResult,
  setAiHistory,
  setLastAiResult,
  onAIGenerate,
  generalPrompt,
  projectMetrics
}) => {
  
  const [activeTab, setActiveTab] = useState('analysis');
  const [chatInput, setChatInput] = useState('');
  const [analysisDimension, setAnalysisDimension] = useState<AnalysisDimension>('structure');
  const [analysisLevel, setAnalysisLevel] = useState<AnalysisLevel>('section');

  // Dimensions d'analyse multi-niveaux
  const analysisDimensions = [
    {
      id: 'structure' as AnalysisDimension,
      label: 'Structure',
      icon: Layers,
      color: 'text-blue-400',
      description: 'Organisation et progression'
    },
    {
      id: 'emotion' as AnalysisDimension,
      label: 'Émotion',
      icon: Heart,
      color: 'text-red-400',
      description: 'Impact émotionnel et ressenti'
    },
    {
      id: 'style' as AnalysisDimension,
      label: 'Style',
      icon: Star,
      color: 'text-purple-400',
      description: 'Cohérence stylistique'
    },
    {
      id: 'harmony' as AnalysisDimension,
      label: 'Harmonie',
      icon: Music,
      color: 'text-green-400',
      description: 'Accords et mélodie'
    },
    {
      id: 'narrative' as AnalysisDimension,
      label: 'Narratif',
      icon: FileText,
      color: 'text-orange-400',
      description: 'Histoire et message'
    },
    {
      id: 'commercial' as AnalysisDimension,
      label: 'Commercial',
      icon: TrendingUp,
      color: 'text-yellow-400',
      description: 'Potentiel commercial'
    }
  ];

  // Niveaux d'analyse
  const analysisLevels = [
    { id: 'section' as AnalysisLevel, label: 'Section', icon: Target, description: 'Section actuelle' },
    { id: 'song' as AnalysisLevel, label: 'Chanson', icon: Music, description: 'Chanson complète' },
    { id: 'global' as AnalysisLevel, label: 'Global', icon: Eye, description: 'Vision d\'ensemble' }
  ];

  // Actions IA spécialisées par dimension
  const getAIActionsForDimension = useCallback((dimension: AnalysisDimension, level: AnalysisLevel) => {
    const basePrompts = {
      structure: {
        section: 'Analyse la structure de cette section et suggère des améliorations',
        song: 'Analyse la structure globale de la chanson et propose des optimisations',
        global: 'Évalue la cohérence structurelle du projet et suggère une progression idéale'
      },
      emotion: {
        section: 'Analyse l\'impact émotionnel de cette section et suggère des renforcements',
        song: 'Évalue le parcours émotionnel de la chanson et propose des ajustements',
        global: 'Analyse la cohérence émotionnelle du projet selon la vision générale'
      },
      style: {
        section: 'Vérifie la cohérence stylistique de cette section avec le genre choisi',
        song: 'Analyse l\'unité stylistique de la chanson et suggère des harmonisations',
        global: 'Évalue la cohérence stylistique globale selon la vision du projet'
      },
      harmony: {
        section: 'Suggère des progressions d\'accords pour enrichir cette section',
        song: 'Analyse l\'harmonie globale et propose des améliorations mélodiques',
        global: 'Évalue la richesse harmonique du projet et suggère des développements'
      },
      narrative: {
        section: 'Analyse le message narratif de cette section et suggère des renforcements',
        song: 'Évalue la cohérence narrative de la chanson et propose des améliorations',
        global: 'Analyse la force narrative du projet selon la vision générale'
      },
      commercial: {
        section: 'Évalue le potentiel commercial de cette section et suggère des optimisations',
        song: 'Analyse l\'attractivité commerciale de la chanson et propose des améliorations',
        global: 'Évalue le potentiel commercial global selon les tendances actuelles'
      }
    };

    return basePrompts[dimension][level];
  }, []);

  // Gestionnaire pour l'analyse IA
  const handleAIAnalysis = useCallback(async () => {
    const prompt = getAIActionsForDimension(analysisDimension, analysisLevel);
    await onAIGenerate(prompt, `analysis-${analysisDimension}-${analysisLevel}`);
  }, [analysisDimension, analysisLevel, getAIActionsForDimension, onAIGenerate]);

  // Gestionnaire pour le chat
  const handleChatSubmit = useCallback(async () => {
    if (!chatInput.trim() || !isConfigured) return;
    
    try {
      await onAIGenerate(chatInput, 'chat');
      setChatInput('');
    } catch (error) {
      console.error('Erreur chat IA:', error);
    }
  }, [chatInput, isConfigured, onAIGenerate]);

  // Suggestions intelligentes basées sur l'état du projet
  const smartSuggestions = React.useMemo(() => {
    const suggestions = [];
    
    if (projectMetrics.completion < 30) {
      suggestions.push({
        type: 'info',
        icon: Lightbulb,
        title: 'Développer le contenu',
        description: 'Ajoutez plus de paroles pour enrichir votre chanson',
        action: 'Générer des paroles pour développer l\'histoire'
      });
    }
    
    if (projectMetrics.structure < 50) {
      suggestions.push({
        type: 'warning',
        icon: Layers,
        title: 'Améliorer la structure',
        description: 'Votre chanson manque de sections variées',
        action: 'Analyser et optimiser la structure de la chanson'
      });
    }
    
    if (projectMetrics.harmony < 40) {
      suggestions.push({
        type: 'info',
        icon: Music,
        title: 'Enrichir l\'harmonie',
        description: 'Ajoutez des accords pour plus de richesse musicale',
        action: 'Suggérer des progressions d\'accords appropriées'
      });
    }
    
    if (projectMetrics.aiUsage < 20) {
      suggestions.push({
        type: 'success',
        icon: Brain,
        title: 'Explorer l\'IA',
        description: 'Utilisez plus l\'IA pour enrichir votre création',
        action: 'Découvrir les possibilités créatives de l\'IA'
      });
    }
    
    return suggestions;
  }, [projectMetrics]);

  return (
    <div className="h-full flex flex-col bg-slate-800/30">
      {/* En-tête */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="font-bold text-white">Assistant IA Mega</h2>
              <p className="text-sm text-slate-400">Analyse multi-dimensionnelle</p>
            </div>
          </div>
          
          {/* Statut */}
          <div className="flex items-center gap-2">
            {isConfigured ? (
              <Badge variant="default" className="gap-1 bg-green-500">
                <CheckCircle2 className="h-3 w-3" />
                Actif
              </Badge>
            ) : (
              <Badge variant="destructive" className="gap-1">
                <AlertCircle className="h-3 w-3" />
                Config
              </Badge>
            )}
            {aiLoading && (
              <Badge variant="secondary" className="gap-1">
                <Sparkles className="h-3 w-3 animate-spin" />
                Analyse...
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-4 bg-slate-700/50 m-2">
            <TabsTrigger value="analysis" className="gap-1">
              <BarChart3 className="h-3 w-3" />
              Analyse
            </TabsTrigger>
            <TabsTrigger value="suggestions" className="gap-1">
              <Lightbulb className="h-3 w-3" />
              Suggestions
            </TabsTrigger>
            <TabsTrigger value="chat" className="gap-1">
              <MessageSquare className="h-3 w-3" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="config" className="gap-1">
              <Settings className="h-3 w-3" />
              Config
            </TabsTrigger>
          </TabsList>
          
          <div className="flex-1 overflow-hidden">
            {/* Analyse multi-dimensionnelle */}
            <TabsContent value="analysis" className="h-full m-0 p-4">
              <div className="space-y-4">
                {/* Sélecteur de dimension */}
                <div>
                  <h3 className="text-sm font-medium text-white mb-2">Dimension d'analyse</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {analysisDimensions.map((dimension) => {
                      const Icon = dimension.icon;
                      return (
                        <Button
                          key={dimension.id}
                          variant={analysisDimension === dimension.id ? "default" : "outline"}
                          size="sm"
                          onClick={() => setAnalysisDimension(dimension.id)}
                          className={`gap-2 h-auto p-3 flex flex-col items-start ${
                            analysisDimension === dimension.id ? 'bg-slate-600' : ''
                          }`}
                        >
                          <div className="flex items-center gap-2 w-full">
                            <Icon className={`h-4 w-4 ${dimension.color}`} />
                            <span className="font-medium">{dimension.label}</span>
                          </div>
                          <span className="text-xs opacity-80 text-left">
                            {dimension.description}
                          </span>
                        </Button>
                      );
                    })}
                  </div>
                </div>

                {/* Sélecteur de niveau */}
                <div>
                  <h3 className="text-sm font-medium text-white mb-2">Niveau d'analyse</h3>
                  <div className="flex gap-1 bg-slate-700/50 rounded-lg p-1">
                    {analysisLevels.map((level) => {
                      const Icon = level.icon;
                      return (
                        <Button
                          key={level.id}
                          variant={analysisLevel === level.id ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setAnalysisLevel(level.id)}
                          className="gap-1 flex-1"
                          title={level.description}
                        >
                          <Icon className="h-3 w-3" />
                          {level.label}
                        </Button>
                      );
                    })}
                  </div>
                </div>

                {/* Bouton d'analyse */}
                <Button
                  onClick={handleAIAnalysis}
                  disabled={!isConfigured || aiLoading}
                  className="w-full gap-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                >
                  <Wand2 className="h-4 w-4" />
                  Analyser {analysisDimensions.find(d => d.id === analysisDimension)?.label} - {analysisLevels.find(l => l.id === analysisLevel)?.label}
                </Button>

                {/* Résultat de la dernière analyse */}
                {lastAiResult && (
                  <Card className="bg-slate-700/50 border-slate-600">
                    <CardHeader>
                      <CardTitle className="text-sm text-white">Dernière Analyse</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm text-slate-300">
                        {lastAiResult.substring(0, 200)}
                        {lastAiResult.length > 200 && '...'}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            {/* Suggestions intelligentes */}
            <TabsContent value="suggestions" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-white">Suggestions Intelligentes</h3>
                  
                  {smartSuggestions.map((suggestion, index) => {
                    const Icon = suggestion.icon;
                    return (
                      <Card 
                        key={index}
                        className={`cursor-pointer transition-all bg-slate-700/50 border-slate-600 hover:border-slate-500`}
                        onClick={() => onAIGenerate(suggestion.action, 'suggestion')}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-start gap-3">
                            <Icon className={`h-5 w-5 mt-0.5 ${
                              suggestion.type === 'success' ? 'text-green-400' :
                              suggestion.type === 'warning' ? 'text-yellow-400' :
                              'text-blue-400'
                            }`} />
                            <div className="flex-1">
                              <div className="font-medium text-white text-sm mb-1">
                                {suggestion.title}
                              </div>
                              <div className="text-xs text-slate-400 mb-2">
                                {suggestion.description}
                              </div>
                              <div className="text-xs text-slate-300 italic">
                                Action: {suggestion.action}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                  
                  {smartSuggestions.length === 0 && (
                    <div className="text-center text-slate-400 py-8">
                      <Lightbulb className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>Votre projet semble bien équilibré !</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* Chat IA */}
            <TabsContent value="chat" className="h-full m-0 p-4">
              <div className="h-full flex flex-col">
                <div className="flex-1 mb-4">
                  <ScrollArea className="h-full border border-slate-600 rounded-lg p-3 bg-slate-800/50">
                    {aiHistory.length === 0 ? (
                      <div className="text-center text-slate-400 py-8">
                        <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Commencez une conversation avec l'IA</p>
                        <p className="text-xs mt-1">Toutes vos questions bénéficient du contexte général</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {aiHistory.slice(-10).map((message, index) => (
                          <div 
                            key={index}
                            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div 
                              className={`max-w-[80%] p-3 rounded-lg text-sm ${
                                message.role === 'user' 
                                  ? 'bg-blue-500 text-white' 
                                  : 'bg-slate-700 text-white border border-slate-600'
                              }`}
                            >
                              {message.content}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
                
                <div className="flex gap-2">
                  <Textarea
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && (e.preventDefault(), handleChatSubmit())}
                    placeholder="Posez une question à l'IA (contexte général inclus)..."
                    className="flex-1 bg-slate-800 border-slate-600 text-white text-sm placeholder-slate-400 resize-none"
                    rows={2}
                    disabled={!isConfigured || aiLoading}
                  />
                  <Button 
                    onClick={handleChatSubmit}
                    disabled={!isConfigured || aiLoading || !chatInput.trim()}
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600 self-end"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Configuration */}
            <TabsContent value="config" className="h-full m-0 p-4">
              <ScrollArea className="h-full">
                <AiConfigMenu inlineMode={true} />
              </ScrollArea>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};
