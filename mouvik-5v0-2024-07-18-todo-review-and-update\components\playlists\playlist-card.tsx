"use client";

import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react'; // Added useState import
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge'; 
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit3, Trash2, PlayCircle, ListMusic, UserCircle, Eye, ThumbsUp, Users as FollowersIcon, Headphones, Copy, Globe, Lock, Loader2 } from 'lucide-react'; // Added Loader2
import type { Playlist } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useUser } from '@/contexts/user-context'; // Added useUser
import { getSupabaseClient } from '@/lib/supabase/client'; // Added Supabase client
import { cn } from '@/lib/utils';
// import { PlayPlaylistButton } from './play-playlist-button';

interface PlaylistCardProps {
  playlist: Playlist;
  onDelete?: (playlistId: string) => void;
  onUpdateStatus?: (playlistId: string, newStatus: { is_public: boolean; slug: string | null }) => void; // Added for status update
}

export function PlaylistCard({ playlist, onDelete, onUpdateStatus }: PlaylistCardProps) {
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);

  // Recalculate viewPageUrl based on current playlist prop, useful if parent updates it
  const viewPageUrl = playlist.is_public && playlist.slug ? `/playlist/${playlist.slug}` : `/playlists/${playlist.id}`;

  const handleDelete = async () => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la playlist "${playlist.name}" ?`)) {
      if (onDelete) {
        onDelete(playlist.id);
      } else {
        toast({ title: "Suppression (Placeholder)", description: `Playlist ${playlist.id} serait supprimée.` });
        console.log("Delete playlist (placeholder):", playlist.id);
      }
    }
  };

  const handlePlay = () => {
    toast({ title: "Lecture (Placeholder)", description: `Lancer la lecture de la playlist "${playlist.name}".` });
    console.log("Play playlist:", playlist.id);
  };
  
  const handleDuplicate = () => {
    toast({ title: "Duplication (Placeholder)", description: `La playlist "${playlist.name}" serait dupliquée.` });
    console.log("Duplicate playlist (placeholder):", playlist.id);
  };

  // TODO: Implement togglePublic function when backend is ready
  const togglePublicStatus = async (e: React.MouseEvent) => {
    e.preventDefault(); 
    e.stopPropagation();
    if (!user || isTogglingStatus) return;

    setIsTogglingStatus(true);
    try {
      const { data, error } = await supabase.rpc('toggle_playlist_public_status', {
        p_playlist_id: playlist.id,
        p_user_id: user.id,
      });

      if (error) throw error;

      if (data && data.length > 0) {
        const { new_is_public, new_slug } = data[0];
        toast({
          title: "Statut de la playlist mis à jour",
          description: `La playlist "${playlist.name}" est maintenant ${new_is_public ? 'publique' : 'privée'}.`,
        });
        if (onUpdateStatus) {
          onUpdateStatus(playlist.id, { is_public: new_is_public, slug: new_slug });
        }
      } else {
        throw new Error("Réponse invalide de la fonction RPC.");
      }
    } catch (err: any) {
      console.error("Error toggling playlist status:", err);
      toast({
        title: "Erreur",
        description: err.message || "Impossible de changer le statut de la playlist.",
        variant: "destructive",
      });
    } finally {
      setIsTogglingStatus(false);
    }
  };

  return (
    <Card className="overflow-hidden flex flex-col h-full group/card">
      <CardHeader className="p-0 relative">
        {playlist.is_public !== undefined && (
          <div 
            title={playlist.is_public ? "Playlist publique (cliquer pour changer)" : "Playlist privée (cliquer pour changer)"}
            onClick={togglePublicStatus}
            className={cn(
                "absolute top-2 left-2 z-10 w-4 h-4 rounded-full cursor-pointer flex items-center justify-center transition-all",
                "ring-2 ring-offset-2 ring-offset-card", // Base ring for halo effect
                isTogglingStatus 
                  ? "bg-yellow-500 ring-yellow-500/50 animate-pulse" // Yellow for loading, with halo
                  : playlist.is_public 
                    ? "bg-green-500 ring-green-500/50 hover:bg-green-400" // Green filled, green halo
                    : "bg-red-500 ring-red-500/50 hover:bg-red-400" // Red filled, red halo
            )}
          >
            {isTogglingStatus && <Loader2 className="h-2.5 w-2.5 animate-spin text-white" />}
          </div>
        )}
        <Link href={viewPageUrl} className="block aspect-square relative group">
          {playlist.cover_url ? (
            <Image
              src={playlist.cover_url}
              alt={playlist.name}
              width={300}
              height={300}
              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <ListMusic className="w-16 h-16 text-muted-foreground" />
            </div>
          )}
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <PlayCircle className="w-12 h-12 text-white" />
          </div>
        </Link>
      </CardHeader>
      <CardContent className="p-4 flex-grow">
        <Link href={viewPageUrl}>
          <CardTitle className="text-lg font-semibold hover:underline mb-1 truncate" title={playlist.name}>
            {playlist.name}
          </CardTitle>
        </Link>
        <div className="text-xs text-muted-foreground mb-2 flex items-center flex-wrap">
            {playlist.profiles ? (
              <Link href={`/artists/${playlist.profiles.username}`} className="hover:underline flex items-center gap-1 mr-1.5">
                {playlist.profiles.avatar_url ? (
                    <Image src={playlist.profiles.avatar_url} alt={playlist.profiles.display_name || playlist.profiles.username || 'avatar'} width={16} height={16} className="rounded-full" />
                ) : (
                    <UserCircle className="w-4 h-4" />
                )}
                {playlist.profiles.display_name || playlist.profiles.username}
              </Link>
            ) : (
              <span className="mr-1.5">Créateur inconnu</span>
            )}
            <span className="mr-1.5">• {playlist.songs_count ?? 0} morceau{playlist.songs_count === 1 || playlist.songs_count === 0 ? '' : 's'}</span>
            {/* Public/Private Badge removed from here, now an LED on cover */}
        </div>

        {/* Display Taxonomy Tags */}
        {(playlist.genres && playlist.genres.length > 0) || (playlist.moods && playlist.moods.length > 0) ? (
          <div className="mb-2 flex flex-wrap gap-1 max-h-10 overflow-hidden group-hover/card:max-h-none transition-all duration-300">
            {playlist.genres?.slice(0,2).map(genre => <Badge key={genre} variant="secondary" className="text-xs px-1.5 py-0.5">{genre}</Badge>)}
            {playlist.moods?.slice(0,2).map(mood => <Badge key={mood} variant="outline" className="text-xs px-1.5 py-0.5">{mood}</Badge>)}
          </div>
        ) : null}
        
        {/* Display other counts if available */}
        <div className="flex items-center gap-x-3 gap-y-1 flex-wrap text-xs text-muted-foreground">
          {playlist.view_count !== undefined && playlist.view_count > 0 && (
            <span className="flex items-center gap-1" title="Vues"><Eye className="w-3 h-3" /> {playlist.view_count}</span>
          )}
          {playlist.like_count !== undefined && playlist.like_count > 0 && (
            <span className="flex items-center gap-1" title="Likes"><ThumbsUp className="w-3 h-3" /> {playlist.like_count}</span>
          )}
          {playlist.follower_count !== undefined && playlist.follower_count > 0 && (
            <span className="flex items-center gap-1" title="Followers"><FollowersIcon className="w-3 h-3" /> {playlist.follower_count}</span>
          )}
           {playlist.plays !== undefined && playlist.plays > 0 && (
            <span className="flex items-center gap-1" title="Écoutes"><Headphones className="w-3 h-3" /> {playlist.plays}</span>
          )}
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-2 flex justify-between items-center">
        <Button onClick={handlePlay} size="sm" variant="outline" className="flex-grow mr-2">
            <PlayCircle className="mr-2 h-4 w-4" /> Écouter
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Options</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => router.push(`/playlists/${playlist.id}/edit`)}>
              <Edit3 className="mr-2 h-4 w-4" />
              Modifier
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}>
              <Copy className="mr-2 h-4 w-4" />
              Dupliquer
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10">
              <Trash2 className="mr-2 h-4 w-4" />
              Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );
}
