-- Enable <PERSON><PERSON> on the comments table if not already enabled
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies on comments table to ensure a clean slate
DROP POLICY IF EXISTS "Allow authenticated users to insert comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to update content of their own comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to mark their own comments as deleted_by_user" ON public.comments;
DROP POLICY IF EXISTS "Allow public read access to visible comments" ON public.comments;
DROP POLICY IF EXISTS "Allow users to see their own non-deleted comments" ON public.comments; -- Corrected name
DROP POLICY IF EXISTS "Allow resource creators to update comments on their resources" ON public.comments;

-- Policy: Allow authenticated users to insert comments
CREATE POLICY "Allow authenticated users to insert comments"
ON public.comments
FOR INSERT
TO authenticated 
WITH CHECK (auth.uid() = user_id); 

-- Policy: Allow public read access to 'visible' comments
CREATE POLICY "Allow public read access to visible comments"
ON public.comments
FOR SELECT
USING (status = 'visible'::text);

-- Policy: Allow authenticated users to see their own comments regardless of status, 
-- unless deleted by a moderator (moderator-deleted comments are typically fully hidden from non-mods).
CREATE POLICY "Allow users to see their own non-deleted comments"
ON public.comments
FOR SELECT
TO authenticated
USING (auth.uid() = user_id AND status <> 'deleted_by_moderator'::text);

-- NO UPDATE POLICIES. All updates (content or status) must go through RPC functions.
-- NO DELETE POLICIES. Deletion is handled by changing status via RPC functions.

-- The functions for updating content and status (including moderation) will be defined
-- in a separate script (e.g., comment_actions_functions.sql) and will use SECURITY DEFINER
-- or appropriate checks within them.
