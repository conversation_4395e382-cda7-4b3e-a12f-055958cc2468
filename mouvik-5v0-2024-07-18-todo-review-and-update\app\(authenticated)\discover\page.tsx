import { Suspense } from "react"
import Link from "next/link"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { Music, Filter, PlusCircle, ChevronRight, Play } from 'lucide-react'; // Consolidated imports
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { GenreCard } from "@/components/discover/genre-card";
import { FeaturedArtistCard } from "@/components/discover/featured-artist-card";
import { BandCard } from '@/components/bands/band-card';
import { SongCard } from '@/components/songs/song-card';
import { PlaylistCard } from "@/components/discover/playlist-card";
import { AlbumCard } from "@/components/discover/album-card";
import { TrendingTrack } from "@/components/discover/trending-track";
import { PlanLimitDetails } from "@/hooks/use-plan-limits"; // For plan limits type
import type { Album, UserProfile, Band, Song } from "@/types"; // Added Song for track recommendations
import ImageWithFallback from "@/components/ui/image-with-fallback";

export default async function DiscoverPage() {
  const supabase = createSupabaseServerClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  console.log('Discover Page - User object:', user);

  let userPlaylistCount = 0;
  let userProfile: { custom_max_playlists: number | null, subscription_tier: string | null, genres: string[] | null, tags: string[] | null } | null = null;
  let userProfileGenres: string[] = [];
  let userProfileTags: string[] = [];
  let planLimitsForUser: Pick<PlanLimitDetails, 'max_playlists'> | null = null;
  let effectiveMaxPlaylists: number | null = null;
  let canCreatePlaylist = false;

  if (user) {
    // Fetch user's playlist count
    const { data: countData, error: countError } = await supabase.rpc('get_user_playlist_count', { user_id_param: user.id });
    if (countError) console.error("Error fetching playlist count:", countError);
    else userPlaylistCount = typeof countData === 'number' ? countData : 0;

    // Fetch user's profile for custom_max_playlists and subscription_tier
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('custom_max_playlists, subscription_tier, genres, tags')
      .eq('id', user.id)
      .single();
    if (profileError) console.error("Error fetching user profile for limits:", profileError);
    else {
      userProfile = profileData;
    console.log('Discover Page - User profile data:', userProfile);
      userProfileGenres = profileData?.genres || [];
      console.log('Discover Page - User profile genres:', userProfileGenres);
      userProfileTags = profileData?.tags || [];
    }

    // Fetch plan limits for the user's tier
    if (userProfile?.subscription_tier) {
      const { data: tierLimitsData, error: tierLimitsError } = await supabase
        .from('plan_limits')
        .select('max_playlists')
        .eq('tier', userProfile.subscription_tier)
        .single();
      if (tierLimitsError) console.error("Error fetching plan limits for tier:", tierLimitsError);
      else planLimitsForUser = tierLimitsData;
    }
    
    effectiveMaxPlaylists = userProfile?.custom_max_playlists ?? planLimitsForUser?.max_playlists ?? null; // null means unlimited
    canCreatePlaylist = effectiveMaxPlaylists === null || userPlaylistCount < effectiveMaxPlaylists;
  }

  // Fetch user's own playlists instead of generic featured ones for "Créé pour vous"
  // Or, keep featuredPlaylists and add a new section for "Mes Playlists"
  // For now, let's assume "Créé pour vous" should show user's playlists.
  let userPlaylists: any[] = [];
  if (user) {
    const { data, error } = await supabase
      .from("playlists")
      .select("*") // Adjust select as needed
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });
    if (error) console.error("Error fetching user playlists:", error);
    else userPlaylists = data || [];
  }


  // Fetch new releases
  const { data: newReleases } = await supabase
    .from("albums")
    .select("*")
    .eq("visibility", "public")
    .order("release_date", { ascending: false })
    .limit(4)

  // Fetch trending tracks
  const { data: trendingTracks } = await supabase
    .from("songs")
    .select("*, profiles(name, avatar_url)")
    .order("plays", { ascending: false })
    .limit(5)

  // Fetch featured artists
  const { data: featuredArtists } = await supabase.from("profiles").select("*").limit(5)

  const genres = [
    { id: 1, name: "Électronique", count: 845, image: "/electronic-music-concert.png" },
    { id: 2, name: "Jazz", count: 523, image: "/lively-jazz-concert.png" },
    { id: 3, name: "Rock", count: 678, image: "/rock-concert.png" },
    { id: 4, name: "Hip-Hop", count: 763, image: "/hip-hop-concert.png" },
    { id: 5, name: "Classique", count: 412, image: "/placeholder.svg?key=no59c" },
    { id: 6, name: "Acoustique", count: 389, image: "/placeholder.svg?key=q4qq3" },
    { id: 7, name: "Indie", count: 476, image: "/placeholder.svg?key=d8pb4" },
    { id: 8, name: "Ambient", count: 342, image: "/placeholder.svg?key=hpda0" },
  ]

  // Filter out error objects for releases, tracks
  // validPlaylists will now be userPlaylists
  const validReleases = Array.isArray(newReleases) ? newReleases.filter(r => r && typeof r === 'object' && 'id' in r) : []
  const validTracks = Array.isArray(trendingTracks) ? trendingTracks.filter(t => t && typeof t === 'object' && 'id' in t) : []

  // Fetch Album recommendations based on user's preferred genres
  let genreBasedAlbumRecommendations: Album[] = [];
  if (user && userProfileGenres.length > 0) {
    const { data: albumRecsData, error: albumRecsError } = await supabase
      .from('albums')
      .select('*, profiles!user_id(id, display_name, username, avatar_url)') // Fetch related profile explicitly
      .overlaps('genre', userProfileGenres)
      .eq('visibility', 'public')
      .order('release_date', { ascending: false })
      .limit(5);

    if (albumRecsError) {
      console.error("Error fetching genre-based album recommendations:", albumRecsError);
    } else {
      genreBasedAlbumRecommendations = albumRecsData || [];
    }
  }

  // Fetch Artist recommendations based on user's preferred genres
  let genreBasedArtistRecommendations: UserProfile[] = [];
  let featuredArtistProfile: UserProfile | null = null; // Define featuredArtistProfile

  if (user && userProfileGenres.length > 0) {
    const { data: artistRecsData, error: artistRecsError } = await supabase
      .from('profiles')
      .select('id, username, display_name, avatar_url, bio, genres') // Refined select
      .eq('is_artist', true)
      .not('bio', 'is', null) // Ensure bio exists
      .not('avatar_url', 'is', null) // Ensure avatar exists
      .overlaps('genres', userProfileGenres)
      // TODO: Exclude artists already followed or interacted with by the user
      // TODO: Order by follower_count or recent activity
      .limit(5); // Fetch a list, will pick one for featured

    if (artistRecsError) {
      console.error("Error fetching genre-based artist recommendations:", artistRecsError.message);
    } else {
      genreBasedArtistRecommendations = artistRecsData || [];
      if (genreBasedArtistRecommendations.length > 0) {
        featuredArtistProfile = genreBasedArtistRecommendations[0]; // Assign the first artist as featured
        // Optionally, if you don't want the featured artist to also appear in the list below:
        // genreBasedArtistRecommendations = genreBasedArtistRecommendations.slice(1);
      }
    }
  }

  // Fetch Featured Band based on user's preferred genres
  let featuredBand: Band | null = null;
  if (user && userProfileGenres.length > 0) {
    const { data: bandData, error: bandError } = await supabase
      .from('bands')
      .select('id, name, description, avatar_url, genres, slug, creator_id')
      .not('description', 'is', null) // Ensure description (aliased as bio) exists
      .not('avatar_url', 'is', null) // Ensure avatar exists
      .overlaps('genres', userProfileGenres)
      .order('created_at', { ascending: false }) // Placeholder ordering, consider relevance
      .limit(1)
      .maybeSingle();

    if (bandError) {
      console.error('Error fetching featured band:', bandError.message);
    } else {
      featuredBand = bandData;
    }
  }


  // Fetch Band recommendations based on user's preferred genres
  let genreBasedBandRecommendations: Band[] = [];
  if (user && userProfileGenres.length > 0) {
    const { data: bandRecsData, error: bandRecsError } = await supabase
      .from('bands')
      .select('*') // Select necessary fields, '*' for now
      .eq('is_public', true)
      .overlaps('genres', userProfileGenres)
      // TODO: Exclude bands already followed or interacted with by the user
      .order('follower_count', { ascending: false, nullsFirst: true })
      .limit(5);

    if (bandRecsError) {
      console.error("Error fetching genre-based band recommendations:", bandRecsError);
    } else {
      genreBasedBandRecommendations = bandRecsData || [];
    }
  }

  // Fetch new releases from followed artists and bands
  let followedReleases: Album[] = [];
  if (user) {
    const { data: followedProfilesData, error: followedProfilesError } = await supabase
      .from('profile_followers')
      .select('followed_profile_id')
      .eq('follower_id', user.id);

    const { data: followedBandsData, error: followedBandsError } = await supabase
      .from('band_followers')
      .select('band_id')
      .eq('user_id', user.id);

    if (followedProfilesError) console.error('Error fetching followed profiles:', followedProfilesError);
    if (followedBandsError) console.error('Error fetching followed bands:', followedBandsError);

    const followedProfileIds = followedProfilesData?.map(f => f.followed_profile_id) || [];
    const followedBandIds = followedBandsData?.map(f => f.band_id) || [];

    if (followedProfileIds.length > 0 || followedBandIds.length > 0) {
      let query = supabase
        .from('albums')
        .select('*, profiles(*), bands(*)') // Fetch related profiles and bands for display
        .eq('is_public', true)
        .order('release_date', { ascending: false })
        .limit(10);

      // Build the .or() condition string carefully
      let orFilterParts: string[] = [];
      if (followedProfileIds.length > 0) {
        // UUIDs should not be individually quoted when part of a comma-separated list for .in()
        orFilterParts.push(`user_id.in.(${followedProfileIds.join(',')})`);
      }
      if (followedBandIds.length > 0) {
        orFilterParts.push(`band_id.in.(${followedBandIds.join(',')})`);
      }

      if (orFilterParts.length > 0) {
        query = query.or(orFilterParts.join(','));
        const { data: releasesData, error: releasesError } = await query;
        if (releasesError) {
          console.error("Error fetching releases from followed entities:", releasesError);
        } else {
          followedReleases = releasesData || [];
        }
      }
    }
  }

  // Fetch Song recommendations based on user's liked songs' genres
  let genreBasedSongRecommendations: Song[] = [];
  if (user) {
    // Step 1: Get all song_ids liked by the user
    const { data: likedSongIdsData, error: likedSongIdsError } = await supabase
      .from('song_likes')
      .select('song_id')
      .eq('user_id', user.id);

    if (likedSongIdsError) {
      console.error("Error fetching liked song IDs:", likedSongIdsError);
    } else if (likedSongIdsData && likedSongIdsData.length > 0) {
      const likedSongIds = likedSongIdsData.map(like => like.song_id).filter(id => id) as string[];

      if (likedSongIds.length > 0) {
        // Step 2: Get the genres of these liked songs
        const { data: likedSongsDetails, error: likedSongsDetailsError } = await supabase
          .from('songs')
          .select('id, genre')
          .in('id', likedSongIds);

        if (likedSongsDetailsError) {
          console.error("Error fetching details of liked songs:", likedSongsDetailsError);
        } else if (likedSongsDetails && likedSongsDetails.length > 0) {
          const genreCounts: { [genre: string]: number } = {};
          likedSongsDetails.forEach(song => {
            if (song.genre) { // song.genre is TEXT, so it's a string or null
              genreCounts[song.genre] = (genreCounts[song.genre] || 0) + 1;
            }
          });

          const popularGenres = Object.entries(genreCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 3) // Take top 3 genres
            .map(([genre]) => genre);

          if (popularGenres.length > 0) {
            const { data: songRecsData, error: songRecsError } = await supabase
              .from('songs')
              .select('*, profiles(*), albums(id, title, cover_url)') // Fetch necessary fields for SongCard
              .eq('is_public', true)
              .in('genre', popularGenres)
              .not('id', 'in', `(${likedSongIds.join(',')})`) // Exclude already liked songs
              .order('like_count', { ascending: false, nullsFirst: true })
              .limit(10);
            
            if (songRecsError) {
              console.error("Error fetching genre-based song recommendations:", songRecsError);
            } else {
              genreBasedSongRecommendations = songRecsData || [];
            }
          }
        }
      }
    }
  }

  return (
    <div className="flex flex-col gap-8 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Découvrir</h1>
          <p className="text-muted-foreground">Explorez de nouvelles musiques adaptées à vos goûts</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Input type="search" placeholder="Rechercher de la musique..." className="w-[250px] pl-8" />
            <Music className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="mb-6 border-b border-border">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <Link href="/discover" className="border-primary text-primary whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
            Recommandations
          </Link>
          <Link href="/insights" className="border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
            Insights ✨
          </Link>
          {/* Add other top-level navigation items here if needed */}
        </nav>
      </div>

      <div className="flex flex-wrap gap-2">
        {/* Définition des listes de genres */}
        {(() => {
          const ALL_MUSIC_GENRES = [
            'Pop', 'Rock', 'Hip Hop', 'Rap Français', 'Rap US', 'Drill', 'Trap',
            'Électronique', 'House', 'Techno', 'EDM', 'Trance', 'Ambiant',
            'Lofi Hip Hop', 'Jazz', 'Smooth Jazz', 'Blues', 'Soul', 'Funk', 'R&B',
            'Disco', 'Musique Classique', 'Opéra', 'Musique de Film',
            'Musique du Monde', 'Afrobeat', 'Reggae', 'Dancehall', 'Ska',
            'Metal', 'Heavy Metal', 'Metal Progressif', 'Hard Rock', 'Punk Rock',
            'Folk', 'Country', 'Indie Pop', 'Indie Rock', 'Rock Alternatif',
            'Grunge', 'Acoustique', 'Chanson Française', 'Variété Internationale',
            'K-Pop', 'J-Rock', 'Synthwave', 'Gospel'
          ];
          const MAX_INITIAL_GENRES_COUNT = 11; // Adjusted to 11 as per original plan
          const genresToDisplay = ['Tout', ...ALL_MUSIC_GENRES.slice(0, MAX_INITIAL_GENRES_COUNT)];

          return (
            <>
              {genresToDisplay.map((genreName) => {
                const href = genreName === 'Tout' ? '/discover' : `/genres/${encodeURIComponent(genreName.toLowerCase().replace(/\s+/g, '-'))}`;
                return (
                  <Link key={genreName} href={href} passHref>
                    <Badge
                      variant="outline"
                      className={`hover:bg-primary/10 ${genreName === 'Tout' ? 'bg-primary/10' : ''} cursor-pointer`}
                    >
                      {genreName}
                    </Badge>
                  </Link>
                );
              })}
              {ALL_MUSIC_GENRES.length > MAX_INITIAL_GENRES_COUNT && (
                <Link href="/genres/explore" passHref>
                  <Badge variant="secondary" className="cursor-pointer hover:bg-primary/20">
                    Voir Plus <ChevronRight className="h-4 w-4 ml-1" />
                  </Badge>
                </Link>
              )}
            </>
          );
        })()}
      </div>

      {/* User's Playlists Section */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold">Mes Playlists</h2>
            {user && (
              <p className="text-sm text-muted-foreground">
                {userPlaylistCount} / {effectiveMaxPlaylists === null ? 'Illimité' : effectiveMaxPlaylists}
              </p>
            )}
          </div>
          {user && (
            <Button asChild disabled={!canCreatePlaylist} title={!canCreatePlaylist ? "Limite de playlists atteinte" : "Créer une nouvelle playlist"}>
              <Link href="/playlists/create"> {/* Assuming this route will exist */}
                <PlusCircle className="mr-2 h-4 w-4" /> Créer une playlist
              </Link>
            </Button>
          )}
        </div>
        {userPlaylists.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            {userPlaylists.map((playlist) => (
              <PlaylistCard key={playlist.id} playlist={playlist} />
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground">Vous n'avez pas encore créé de playlists.</p>
        )}
      </section>

      {/* Personalized Album Recommendations Section */}
      {user && genreBasedAlbumRecommendations.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Albums pour vous (basé sur vos genres)</h2>
            {/* Optional: Link to a page with more genre-based recommendations */}
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {genreBasedAlbumRecommendations.map((album: Album) => (
              <AlbumCard key={album.id} album={album} />
            ))}
          </div>
        </section>
      )}

      {/* Personalized Song Recommendations Section */}
      {user && genreBasedSongRecommendations.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Titres pour vous (basé sur vos favoris)</h2>
            {/* Optional: Link to a page with more recommendations */}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {genreBasedSongRecommendations.map((song: Song) => (
              <SongCard key={song.id} song={song as any} /> // Cast to any for now, SongCard expects SongForCard which is Song + optional joined fields, should be compatible.
            ))}
          </div>
        </section>
      )}

      {/* Personalized Artist Recommendations Section */}
      {user && genreBasedArtistRecommendations.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Artistes pour vous (basé sur vos genres)</h2>
            {/* Optional: Link to a page with more genre-based recommendations */}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {genreBasedArtistRecommendations.map((artist: UserProfile) => (
              <FeaturedArtistCard key={artist.id} artist={artist} />
            ))}
          </div>
        </section>
      )}

      {/* New Releases from Followed Entities Section */}
      {user && followedReleases.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Nouveautés de vos suivis</h2>
            {/* Optional: Link to a page with more releases */}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {followedReleases.map((album: Album) => (
              <AlbumCard key={album.id} album={album} />
            ))}
          </div>
        </section>
      )}

      {/* Personalized Band Recommendations Section */}
      {user && genreBasedBandRecommendations.length > 0 && (
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Groupes pour vous (basé sur vos genres)</h2>
            {/* Optional: Link to a page with more genre-based recommendations */}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {genreBasedBandRecommendations.map((band: Band) => (
              <BandCard key={band.id} band={band} />
            ))}
          </div>
        </section>
      )}

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Nouveautés</h2>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/discover/new-releases">
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Suspense fallback={<AlbumCardSkeleton count={4} />}>
            {(validReleases || []).map((album) => (
              <AlbumCard key={album.id} album={album} />
            ))}
          </Suspense>
        </div>
      </section>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Catégories</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {genres.map((genre) => (
            <GenreCard key={genre.id} genre={genre} />
          ))}
        </div>
      </section>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Artistes & Groupes</h2>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/discover/artists">
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Suspense fallback={<ArtistCardSkeleton count={5} />}>
            {(featuredArtists || []).map((artist) => (
              <FeaturedArtistCard key={artist.id} artist={artist} />
            ))}
          </Suspense>
        </div>
      </section>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Tendances</h2>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/discover/trending">
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="flex flex-col gap-2">
          <Suspense fallback={<TrendingTrackSkeleton count={validTracks.length || 5} />}>
            {(validTracks || []).map((track, index) => (
              <TrendingTrack key={track.id} track={track} position={index + 1} />
            ))}
          </Suspense>
        </div>
      </section>

      {/* Featured Artist Section */}
      {featuredArtistProfile && (
        <section>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Artiste vedette</h2>
            {/* Optional: Link to artist's profile or more artists */}
          </div>
          <div className="relative w-full h-[300px] rounded-lg overflow-hidden group">
            <ImageWithFallback
              src={featuredArtistProfile.avatar_url || '/images/placeholder-artist.svg'}
              fallbackSrc="/images/placeholder-artist.svg"
              alt={featuredArtistProfile.display_name || featuredArtistProfile.username || 'Artiste vedette'}
              width={1200}
              height={600}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent z-10"></div>
            <div className="absolute bottom-0 left-0 p-6 z-20 text-white">
              <h3 className="text-3xl font-bold mb-1">{featuredArtistProfile.display_name || featuredArtistProfile.username}</h3>
              <p className="text-sm text-gray-300 mb-3 line-clamp-2">{featuredArtistProfile.bio || 'Découvrez cet artiste.'}</p>
              <div className="flex gap-2">
                <Button asChild>
                  <Link href={`/profiles/${featuredArtistProfile.username}`}>
                    <Play className="mr-2 h-4 w-4" />
                    Profil
                  </Link>
                </Button>
                {/* <Button variant="outline" className="bg-white/10 hover:bg-white/20 border-white/20 text-white">
                  Suivre
                </Button> */}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Featured Band Section */}
      {featuredBand && (
        <section className="mt-8"> {/* Add margin-top if both sections are present */}
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Groupe vedette</h2>
            {/* Optional: Link to band's profile or more bands */}
          </div>
          <div className="relative w-full h-[300px] rounded-lg overflow-hidden group">
            <ImageWithFallback
              src={featuredBand.avatar_url || '/images/placeholder-band.svg'} // Assuming bands have avatar_url
              fallbackSrc="/images/placeholder-band.svg"
              alt={featuredBand.name || 'Groupe vedette'}
              width={1200}
              height={600}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent z-10"></div>
            <div className="absolute bottom-0 left-0 p-6 z-20 text-white">
              <h3 className="text-3xl font-bold mb-1">{featuredBand.name}</h3>
              <p className="text-sm text-gray-300 mb-3 line-clamp-2">{featuredBand.description || 'Découvrez ce groupe.'}</p>
              <div className="flex gap-2">
                <Button asChild>
                  <Link href={`/bands/${featuredBand.slug}`}> {/* Assuming slug for band page */}
                    <Play className="mr-2 h-4 w-4" />
                    Profil
                  </Link>
                </Button>
                {/* <Button variant="outline" className="bg-white/10 hover:bg-white/20 border-white/20 text-white">
                  Suivre
                </Button> */}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  )
}

function AlbumCardSkeleton({ count = 4 }) {
  return Array(count)
    .fill(0)
    .map((_, i) => (
      <Card key={i} className="overflow-hidden">
        <Skeleton className="h-[200px]" />
        <CardContent className="p-4">
          <Skeleton className="h-5 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </CardContent>
      </Card>
    ))
}

function ArtistCardSkeleton({ count = 5 }) {
  return Array(count)
    .fill(0)
    .map((_, i) => (
      <div key={i} className="flex flex-col items-center gap-2">
        <Skeleton className="h-24 w-24 rounded-full" />
        <Skeleton className="h-5 w-20" />
        <Skeleton className="h-4 w-16" />
      </div>
    ))
}

function SongCardSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4`}>
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-4">
            <Skeleton className="h-40 w-full mb-2" /> {/* Approximate for SongCard image */}
            <Skeleton className="h-6 w-3/4 mb-1" /> {/* For song title */}
            <Skeleton className="h-4 w-1/2" /> {/* For artist name */}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function TrendingTrackSkeleton({ count = 5 }: { count?: number }) {
  return Array(count)
    .fill(0)
    .map((_, i) => (
      <div key={i} className="flex items-center justify-between p-2">
        <div className="flex items-center gap-3">
          <Skeleton className="h-6 w-6" />
          <Skeleton className="h-12 w-12" />
          <div>
            <Skeleton className="h-5 w-32 mb-1" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
        <Skeleton className="h-8 w-16" />
      </div>
    ))
}
