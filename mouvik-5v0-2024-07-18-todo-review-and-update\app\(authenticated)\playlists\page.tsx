"use client"; 

import { useState, useEffect } from 'react';
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input"; 
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Slider } from "@/components/ui/slider"; // Added Slider
import { ListMusic, PlusCircle, Music, Heart, History, LayoutGrid, ListFilter, Search as SearchIcon, Trash2, Loader2, Rows3, ZoomIn, ZoomOut } from "lucide-react"; // Added Zoom icons & Rows3
import { getSupabaseClient } from '@/lib/supabase/client'; 
import type { Playlist as PlaylistType } from "@/types"; 
import { PlaylistCard } from "@/components/playlists/playlist-card";
import { PlaylistListItem } from "@/components/playlists/playlist-list-item";
import { PlaylistCardCompact } from "@/components/playlists/playlist-card-compact";
import { useUser } from '@/contexts/user-context'; 
import { toast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils'; // For dynamic class names

interface PlaylistFromView {
  id: string;
  name: string;
  cover_url: string | null;
  created_at: string;
  is_public: boolean;
  songs_count: number;
  user_id: string; 
  creator_username: string | null;
  creator_display_name: string | null;
  creator_avatar_url: string | null;
  total_duration_seconds: number | null;
  view_count?: number;
  like_count?: number;
  follower_count?: number;
  plays?: number;
  playlist_genres?: string[] | null; 
  playlist_moods?: string[] | null;  
  playlist_instrumentation?: string[] | null; 
}

export default function UserPlaylistsPage() {
  const supabase = getSupabaseClient(); 
  const { user } = useUser(); 
  const router = useRouter();

  const [allPlaylists, setAllPlaylists] = useState<PlaylistType[]>([]); 
  const [displayedPlaylists, setDisplayedPlaylists] = useState<PlaylistType[]>([]); 
  const [isLoading, setIsLoading] = useState(true);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('created_at_desc'); 
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid'); // Removed 'grid-compact'
  const [gridCols, setGridCols] = useState(5); // Default for 'grid'
  const [listItemDensity, setListItemDensity] = useState(1); // 0: compact, 1: default, 2: spacious

  const [userPlaylistCount, setUserPlaylistCount] = useState(0);
  const [effectiveMaxPlaylists, setEffectiveMaxPlaylists] = useState<number | null>(null);
  const [canCreatePlaylist, setCanCreatePlaylist] = useState(true);

  useEffect(() => {
    if (!user) {
      router.push('/login'); 
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      // Fetch playlists from the view
      const { data: playlistsData, error: playlistsError } = await supabase
        .from('user_playlist_details') 
        .select('*') 
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (playlistsError) {
        console.error("Error fetching user playlists:", playlistsError);
        toast({ title: "Erreur", description: "Impossible de charger les playlists.", variant: "destructive" });
        setAllPlaylists([]); 
      } else {
        const mappedPlaylists: PlaylistType[] = (playlistsData || []).map((p: PlaylistFromView) => ({
          id: p.id,
          name: p.name,
          cover_url: p.cover_url,
          user_id: p.user_id,
          is_public: p.is_public,
          created_at: p.created_at,
          songs_count: p.songs_count,
          view_count: p.view_count,
          like_count: p.like_count,
          follower_count: p.follower_count,
          plays: p.plays,
          genres: p.playlist_genres,
          moods: p.playlist_moods,
          instrumentation: p.playlist_instrumentation,
          profiles: { 
            id: p.user_id, 
            username: p.creator_username,
            display_name: p.creator_display_name,
            avatar_url: p.creator_avatar_url,
          }
        }));
        setAllPlaylists(mappedPlaylists);
      }
      
      const { data: countData } = await supabase.rpc('get_user_playlist_count', { user_id_param: user.id });
      const currentCount = typeof countData === 'number' ? countData : 0;
      setUserPlaylistCount(currentCount);
      
      const placeholderMax = user?.custom_max_playlists ?? 10; 
      setEffectiveMaxPlaylists(placeholderMax);
      setCanCreatePlaylist(user?.user_role === 'admin' || currentCount < placeholderMax);

      setIsLoading(false);
    };

    if (user) { 
        fetchData();
    }
  }, [user, supabase, router]); 

  useEffect(() => {
    let processedPlaylists = [...allPlaylists];
    if (searchTerm) {
      processedPlaylists = processedPlaylists.filter(p => 
        p.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    switch (sortOption) {
      case 'name_asc':
        processedPlaylists.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'name_desc':
        processedPlaylists.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case 'created_at_asc':
        processedPlaylists.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        break;
      case 'created_at_desc':
      default:
        processedPlaylists.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
    }
    setDisplayedPlaylists(processedPlaylists);
  }, [allPlaylists, searchTerm, sortOption]);

  const handleUpdatePlaylistStatus = (playlistId: string, newStatus: { is_public: boolean; slug: string | null }) => {
    console.log(`Updating status for playlist ${playlistId} to:`, newStatus); // DEBUG
    const updateInList = (list: PlaylistType[]) => 
      list.map(p => 
        p.id === playlistId 
          ? { ...p, is_public: newStatus.is_public, slug: newStatus.slug } 
          : p
      );
    setAllPlaylists(prev => {
      const newList = updateInList(prev);
      console.log(`New allPlaylists list after status update for ${playlistId}:`, newList.find(p=>p.id === playlistId)); // DEBUG
      return newList;
    });
  };

  const handlePlaylistStatsChange = (playlistId: string, newStats: { 
    like_count: number; 
    dislike_count: number; 
    // Playlists might not have user-specific is_liked/is_disliked state propagated this way
    // but we'll update counts if they are part of PlaylistType
  }) => {
    setAllPlaylists(prevPlaylists => 
      prevPlaylists.map(p => 
        p.id === playlistId 
          ? { 
              ...p, 
              like_count: newStats.like_count, 
              dislike_count: newStats.dislike_count,
            } 
          : p
      )
    );
  };

  const handleDeletePlaylist = async (playlistId: string) => {
    if (!user) return;
    const { error } = await supabase
      .from('playlists')
      .delete()
      .eq('id', playlistId)
      .eq('user_id', user.id); 

    if (error) {
      toast({ title: "Erreur de suppression", description: error.message, variant: "destructive" });
    } else {
      toast({ title: "Playlist supprimée", description: "La playlist a été supprimée avec succès." });
      setAllPlaylists(prev => prev.filter(p => p.id !== playlistId)); 
      if (userPlaylistCount !== null) setUserPlaylistCount(userPlaylistCount - 1);
    }
  };

  // useEffect for gridCols based on viewMode is no longer needed if 'grid-compact' is removed.

  const handleGridColsChange = (newCols: number) => {
    const minCols = 2; // Define for 'grid'
    const maxCols = 6; // Define for 'grid' (example, adjust as needed for playlists)
    setGridCols(Math.max(minCols, Math.min(maxCols, newCols)));
  };

  if (isLoading) {
    return <div className="container py-8 flex justify-center items-center min-h-[calc(100vh-200px)]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }

  const getGridClass = () => {
    let classes = "grid gap-6 "; // Default gap for 'grid'
  
    if (gridCols <= 1) classes += "grid-cols-1";
    else if (gridCols === 2) classes += "grid-cols-1 sm:grid-cols-2";
    else if (gridCols === 3) classes += "grid-cols-1 sm:grid-cols-2 md:grid-cols-3";
    else if (gridCols === 4) classes += "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4";
    else if (gridCols === 5) classes += "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5";
    else if (gridCols >= 6) classes += "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6";
    
    if (!classes.includes("grid-cols-")) {
        classes += "xl:grid-cols-5"; // Default fallback for grid
    }
    return classes;
  };


  return (
    <div className="container py-8">
      <div className="flex flex-col md:flex-row items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <ListMusic className="mr-3 h-8 w-8 text-primary" />
            Mes Playlists
          </h1>
          <p className="text-muted-foreground text-sm">
            {userPlaylistCount} / {effectiveMaxPlaylists === null ? 'Illimité' : effectiveMaxPlaylists} playlists créées.
          </p>
        </div>
        <div className="flex items-center gap-2 flex-wrap"> {/* Added flex-wrap */}
            <div className="relative">
                <SearchIcon className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input 
                  placeholder="Rechercher playlists..." 
                  className="pl-8 w-40 md:w-auto" // Adjusted width
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
            </div>
            <TooltipProvider>
              <Tooltip>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="icon" className="h-9 w-9">
                        <ListFilter className="h-4 w-4" />
                        <span className="sr-only">Trier par</span>
                      </Button>
                    </TooltipTrigger>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setSortOption('created_at_desc')}>
                      Date (Plus Récents) {sortOption === 'created_at_desc' && <span className="ml-auto text-xs">✓</span>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('created_at_asc')}>
                      Date (Plus Anciens) {sortOption === 'created_at_asc' && <span className="ml-auto text-xs">✓</span>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('name_asc')}>
                      Nom (A-Z) {sortOption === 'name_asc' && <span className="ml-auto text-xs">✓</span>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('name_desc')}>
                      Nom (Z-A) {sortOption === 'name_desc' && <span className="ml-auto text-xs">✓</span>}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <TooltipContent>
                  <p>Trier par: {
                      sortOption === 'name_asc' ? 'Nom A-Z' :
                      sortOption === 'name_desc' ? 'Nom Z-A' :
                      sortOption === 'created_at_asc' ? 'Date (Anciens)' :
                      'Date (Récents)'
                    }
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <Button variant={viewMode === 'grid' ? "secondary" : "outline"} size="icon" onClick={() => setViewMode('grid')} title="Vue Grille Standard">
              <LayoutGrid className="h-4 w-4" />
            </Button>
            {/* Removed grid-compact button */}
            <Button variant={viewMode === 'list' ? "secondary" : "outline"} size="icon" onClick={() => setViewMode('list')} title="Vue Liste">
              <ListMusic className="h-4 w-4" />
            </Button>
            
            {/* Grid Density Slider */}
            {viewMode === 'grid' && (
              <div className="flex items-center gap-1.5 ml-1" title="Densité de la grille">
                {/* Bouton GAUCHE (-) => Éléments PLUS PETITS (plus de colonnes) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleGridColsChange(gridCols + 1)} title="Éléments plus petits"><ZoomOut className="h-4 w-4 text-muted-foreground" /></Button>
                <Slider 
                  value={[ 6 - gridCols ]} // Inverted value: maxCols (6 for grid) - currentCols
                  min={0} 
                  max={6 - 2} // maxSlider = maxCols (6) - minCols (2) = 4
                  step={1} 
                  className="w-[80px]" 
                  onValueChange={(v) => handleGridColsChange(6 - v[0])} 
                  dir="ltr" 
                />
                {/* Bouton DROITE (+) => Éléments PLUS GRANDS (moins de colonnes) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleGridColsChange(gridCols - 1)} title="Éléments plus grands"><ZoomIn className="h-4 w-4 text-muted-foreground" /></Button>
              </div>
            )}

            {/* List Item Density Slider */}
            {viewMode === 'list' && (
              <div className="flex items-center gap-1.5 ml-1" title="Densité de la liste">
                 {/* Bouton GAUCHE (-) => Éléments PLUS PETITS (compact, density 0) */}
                 <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setListItemDensity(prev => Math.max(0, prev - 1))} title="Plus compact">
                  <ZoomOut className="h-4 w-4 text-muted-foreground" />
                </Button>
                <Slider
                  value={[listItemDensity]} // Direct value: 0 (compact) to 2 (spacious)
                  min={0} 
                  max={2} 
                  step={1}
                  className="w-[80px]"
                  onValueChange={(value) => setListItemDensity(value[0])} // Slider left (0) = compact, Slider right (2) = spacious
                  dir="ltr"
                />
                {/* Bouton DROITE (+) => Éléments PLUS GRANDS (spacious, density 2) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setListItemDensity(prev => Math.min(2, prev + 1))} title="Plus espacé">
                  <ZoomIn className="h-4 w-4 text-muted-foreground" />
                </Button>
              </div>
            )}

            <Button asChild disabled={!canCreatePlaylist} title={!canCreatePlaylist ? "Limite de playlists atteinte" : "Créer une nouvelle playlist"}>
              <Link href="/playlists/create">
                <PlusCircle className="mr-2 h-4 w-4" /> Créer
              </Link>
            </Button>
        </div>
      </div>

      {viewMode === 'grid' && (
        <div className={cn(getGridClass())}> 
          <Link href="/playlists/liked-songs" className="block group">
            <Card className="h-full flex flex-col hover:border-primary transition-colors duration-200">
              <CardHeader className="p-4 flex-grow">
                <div className="aspect-square bg-gradient-to-br from-red-500 to-pink-500 rounded-md flex items-center justify-center mb-2">
                  <Heart className="h-16 w-16 text-white" />
                </div>
                <CardTitle className="text-lg group-hover:text-primary transition-colors">Morceaux Likés</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <CardDescription>Tous les morceaux que vous avez aimés.</CardDescription>
              </CardContent>
            </Card>
          </Link>
          <Link href="/playlists/recently-played" className="block group">
            <Card className="h-full flex flex-col hover:border-primary transition-colors duration-200">
              <CardHeader className="p-4 flex-grow">
                <div className="aspect-square bg-gradient-to-br from-sky-500 to-indigo-500 rounded-md flex items-center justify-center mb-2">
                  <History className="h-16 w-16 text-white" />
                </div>
                <CardTitle className="text-lg group-hover:text-primary transition-colors">Écoutés Récemment</CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <CardDescription>Vos dernières écoutes.</CardDescription>
              </CardContent>
            </Card>
          </Link>
          {displayedPlaylists.map((playlist) => (
            <PlaylistCard 
              key={playlist.id} 
              playlist={playlist} 
              onDelete={handleDeletePlaylist} 
              onUpdateStatus={handleUpdatePlaylistStatus}
              // onStatsChange={handlePlaylistStatsChange} // TODO: Implement if PlaylistCard gets Like/Dislike buttons
            /> 
          ))}
        </div>
      )}

      {/* Removed grid-compact rendering section */}

      {viewMode === 'list' && ( 
        <div className="space-y-3">
          {displayedPlaylists.map((playlist) => (
            <PlaylistListItem 
              key={playlist.id} 
              playlist={playlist} 
              onDelete={handleDeletePlaylist} 
              onUpdateStatus={handleUpdatePlaylistStatus} 
              density={listItemDensity}
              // onStatsChange={handlePlaylistStatsChange} // TODO: Implement if PlaylistListItem gets Like/Dislike buttons
            />
          ))}
        </div>
      )}

      {allPlaylists.length > 0 && displayedPlaylists.length === 0 && searchTerm && (
         <div className="text-center py-12 col-span-full"> 
          <SearchIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold mb-2">Aucune playlist trouvée</h3>
          <p className="text-muted-foreground mb-4">Essayez d'affiner votre recherche.</p>
        </div>
      )}

      {allPlaylists.length === 0 && !isLoading && ( 
        <div className="text-center py-12 col-span-full">
          <Music className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold mb-2">Aucune playlist personnelle</h3>
          <p className="text-muted-foreground mb-4">Commencez par créer votre première playlist !</p>
          <Button asChild disabled={!canCreatePlaylist} title={!canCreatePlaylist ? "Limite de playlists atteinte" : "Créer une nouvelle playlist"}>
            <Link href="/playlists/create">
              <PlusCircle className="mr-2 h-4 w-4" /> Créer une playlist
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
