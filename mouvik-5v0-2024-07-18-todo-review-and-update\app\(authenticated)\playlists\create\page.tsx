"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';
import { usePlanLimits } from '@/hooks/use-plan-limits';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/hooks/use-toast';
import { Loader2, PlusCircle, ArrowLeft, ImageUp, Palette, Music2, Guitar } from 'lucide-react'; // Added icons
import Link from 'next/link';
import { ImageUploader } from "@/components/ui/image-uploader"; // Added
import { MultiSelect } from "@/components/ui/multi-select"; // Added
import { genreOptions, moodOptions, instrumentationOptions } from '@/lib/constants/song-options'; // Added

const playlistFormSchema = z.object({
  name: z.string().min(1, "Le nom est requis.").max(100, "Le nom ne doit pas dépasser 100 caractères."),
  description: z.string().max(500, "La description ne doit pas dépasser 500 caractères.").optional().nullable(),
  is_public: z.boolean().default(true),
  cover_url: z.string().url("URL de pochette invalide.").optional().nullable(),
  banner_url: z.string().url("URL de bannière invalide.").optional().nullable(),
  genres: z.array(z.string()).optional(),
  moods: z.array(z.string()).optional(),
  instrumentation: z.array(z.string()).optional(),
  are_comments_public: z.boolean().default(false).optional(), // Added for comment visibility
});

type PlaylistFormData = z.infer<typeof playlistFormSchema>;

export default function CreatePlaylistPage() {
  const supabase = getSupabaseClient();
  const { user } = useUser();
  const router = useRouter();
  const { limits: planLimits, isLoading: isLoadingPlanLimits } = usePlanLimits();
  
  const [userPlaylistCount, setUserPlaylistCount] = useState<number | null>(null);
  const [playlistCreationCost, setPlaylistCreationCost] = useState<number | null>(null); // Added
  const [isLoadingInitialData, setIsLoadingInitialData] = useState(true); // Renamed from isLoadingCount
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { register, handleSubmit, formState: { errors }, control, watch, setValue } = useForm<PlaylistFormData>({
    resolver: zodResolver(playlistFormSchema),
    defaultValues: {
      name: '',
      description: '',
      is_public: true,
      cover_url: '', // Initialize as empty string or null if schema allows
      banner_url: '', // Initialize as empty string or null
      genres: [],
      moods: [],
      instrumentation: [],
      are_comments_public: false, // Default to private
    },
  });

  useEffect(() => {
    if (!user) {
      router.push('/login'); 
      return;
    }

    // user object from useUser() should already have user_role and custom_max_playlists
    // if UserProvider is correctly fetching and providing the full profile.
    // The planLimits hook provides plan-based limits.

    const fetchInitialData = async () => { // Renamed function
      setIsLoadingInitialData(true);
      // Fetch playlist count
      const { data: countData, error: countError } = await supabase.rpc('get_user_playlist_count', { user_id_param: user.id });
      if (countError) {
        console.error("Error fetching playlist count:", countError);
        toast({ title: "Erreur", description: "Impossible de vérifier votre quota de playlists.", variant: "destructive" });
      } else {
        setUserPlaylistCount(typeof countData === 'number' ? countData : 0);
      }

      // Fetch playlist creation cost
      const { data: costData, error: costError } = await supabase
        .from('creation_costs')
        .select('cost')
        .eq('resource_type', 'playlist')
        .single();
      
      if (costError || !costData) {
        console.error("Error fetching playlist creation cost:", costError);
        toast({ title: "Erreur de coût", description: "Impossible de récupérer le coût de création. Coût par défaut (0) appliqué.", variant: "default" });
        setPlaylistCreationCost(0); // Fallback if cost not found
      } else {
        setPlaylistCreationCost(costData.cost);
      }
      setIsLoadingInitialData(false);
    };

    fetchInitialData(); // Call renamed function
  }, [user, supabase, router]);

  const isAdmin = user?.user_role === 'admin';
  const effectiveMaxPlaylists = user?.custom_max_playlists ?? planLimits?.max_playlists ?? null;
  // Admin bypasses playlist count limit
  const canCreatePlaylist = isAdmin || effectiveMaxPlaylists === null || (userPlaylistCount !== null && userPlaylistCount < effectiveMaxPlaylists);

  const onSubmit: SubmitHandler<PlaylistFormData> = async (data) => {
    if (!user) {
      toast({ title: "Erreur", description: "Utilisateur non authentifié.", variant: "destructive" });
      return;
    }
    // Admin bypasses this check
    if (!isAdmin && !canCreatePlaylist && userPlaylistCount !== null) { 
        toast({ title: "Limite atteinte", description: "Vous avez atteint votre limite de playlists.", variant: "destructive" });
        return;
    }

    setIsSubmitting(true);
    let slugToSave: string | null = null;

    if (data.is_public && data.name) {
      const { data: slugData, error: slugError } = await supabase.rpc('slugify', { value: data.name });
      if (slugError || !slugData) {
        console.error("Error generating slug:", slugError);
        toast({ title: "Erreur de slug", description: "Impossible de générer un slug pour la playlist.", variant: "destructive" });
        setIsSubmitting(false);
        return;
      }
      slugToSave = slugData;
      // Basic uniqueness handling (client-side retry with suffix)
      let isUnique = false;
      let attempt = 0;
      let tempSlug = slugToSave;
      while (!isUnique && attempt < 5) { // Try up to 5 times
        const { data: existing, error: checkError } = await supabase.from('playlists').select('id').eq('slug', tempSlug).maybeSingle();
        if (checkError) { /* handle error, maybe break */ break; }
        if (!existing) {
          isUnique = true;
          slugToSave = tempSlug;
        } else {
          attempt++;
          tempSlug = `${slugData}-${Math.random().toString(36).substring(2, 7)}`; // Append random suffix
        }
      }
      if (!isUnique) {
        toast({ title: "Erreur de slug", description: "Impossible de générer un slug unique. Essayez un nom différent.", variant: "destructive" });
        setIsSubmitting(false);
        return;
      }
    }

    const costToCreate = isAdmin ? 0 : (playlistCreationCost ?? 0); // Use fetched cost, admin pays 0

    // Check client-side balance first for quick feedback (admin bypasses cost)
    if (!isAdmin && (user.coins_balance === null || user.coins_balance === undefined || user.coins_balance < costToCreate)) {
        toast({ title: "Pièces insuffisantes", description: `Vous avez besoin de ${costToCreate} pièces pour créer une playlist. Votre solde est de ${user.coins_balance || 0}.`, variant: "destructive" });
        setIsSubmitting(false);
        return;
    }

    try {
      // p_cost is no longer sent, RPC fetches it and handles admin cost=0
      const rpcParams = {
        p_user_id: user.id,
        p_name: data.name,
        p_description: data.description || null,
        p_is_public: data.is_public,
        p_slug: data.is_public ? slugToSave : null,
        p_cover_url: data.cover_url || null,
        p_banner_url: data.banner_url || null,
        p_genres: data.genres && data.genres.length > 0 ? data.genres : null,
        p_moods: data.moods && data.moods.length > 0 ? data.moods : null,
        p_instrumentation: data.instrumentation && data.instrumentation.length > 0 ? data.instrumentation : null
        // p_are_comments_public: data.are_comments_public, // Removed as per error PGRST202
      };

      const { data: rpcResponse, error: rpcError } = await supabase.rpc('create_playlist_with_coin_deduction', rpcParams);

      if (rpcError) throw rpcError;

      if (rpcResponse.status === 'error') {
        if (rpcResponse.message === 'insufficient_coins') {
          toast({ title: "Pièces insuffisantes", description: `Il vous faut ${rpcResponse.required} pièces. Solde: ${rpcResponse.balance}.`, variant: "destructive" });
        } else {
          toast({ title: "Erreur de création", description: rpcResponse.message || "Une erreur inconnue est survenue.", variant: "destructive" });
        }
        setIsSubmitting(false); // Ensure submission state is reset
        return; 
      }
      
      // Successfully created playlist
      const newPlaylistId = rpcResponse.playlist_id;
      const costApplied = rpcResponse.cost_applied; // Get cost applied from RPC response
      const toastCostMessage = costApplied > 0 ? `${costApplied} pièces déduites.` : "Aucune pièce déduite.";
      toast({ title: "Succès", description: `Playlist "${data.name}" créée. ${toastCostMessage} Nouveau solde: ${rpcResponse.new_balance}.` });
      
      if (!isAdmin && userPlaylistCount !== null) setUserPlaylistCount(userPlaylistCount + 1); 
      // Update user context with new coin balance if possible, or rely on next full fetch
      // This part is tricky without a dedicated setUser/updateUser in UserContext
      // For now, the sidebar will update on next full page load or when UserProvider re-fetches.

      router.refresh(); 
      router.push(`/playlists/${newPlaylistId}`);

    } catch (error: any) {
      console.error("Error creating playlist via RPC:", error);
      toast({ title: "Erreur de création", description: error.message || "Une erreur technique est survenue.", variant: "destructive" });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingPlanLimits || isLoadingInitialData) { // Use renamed state
    return <div className="container py-8 flex justify-center items-center min-h-[calc(100vh-200px)]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }
  
  if (userPlaylistCount !== null && !canCreatePlaylist) {
     return (
      <div className="container py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Limite de Playlists Atteinte</h1>
        <p className="text-muted-foreground mb-6">
          Vous avez atteint votre quota de {effectiveMaxPlaylists} playlists. Pour en créer plus, veuillez mettre à niveau votre plan ou supprimer des playlists existantes.
        </p>
        <Button asChild variant="outline">
          <Link href="/playlists"><ArrowLeft className="mr-2 h-4 w-4" />Retour à Mes Playlists</Link>
        </Button>
        {/* Placeholder for "Buy Slot" button - to be implemented if user confirms this feature */}
      </div>
    );
  }

  return (
    <div className="container max-w-2xl py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold flex items-center">
          <PlusCircle className="mr-3 h-8 w-8 text-primary" />
          Créer une Nouvelle Playlist
        </h1>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/playlists"><ArrowLeft className="mr-2 h-4 w-4" />Annuler</Link>
        </Button>
      </div>
      
      {/* Display playlist creation cost */}
      {!isAdmin && playlistCreationCost !== null && playlistCreationCost > 0 && (
        <p className="text-sm text-muted-foreground mb-4">
          Coût de création : {playlistCreationCost} pièces. Votre solde : {user?.coins_balance ?? 0} pièces.
        </p>
      )}
      {isAdmin && (
         <p className="text-sm text-green-600 mb-4">Mode Admin : Création de playlist gratuite.</p>
      )}


      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <Label htmlFor="name">Nom de la playlist</Label>
          <Input id="name" {...register('name')} className="mt-1" />
          {errors.name && <p className="text-sm text-destructive mt-1">{errors.name.message}</p>}
        </div>

        <div>
          <Label htmlFor="description">Description (optionnel)</Label>
          <Textarea id="description" {...register('description')} className="mt-1" rows={4} />
          {errors.description && <p className="text-sm text-destructive mt-1">{errors.description.message}</p>}
        </div>

        <div className="flex items-center space-x-2">
          <Switch id="is_public" {...register('is_public')} defaultChecked={true} />
          <Label htmlFor="is_public">Rendre la playlist publique</Label>
        </div>
        {errors.is_public && <p className="text-sm text-destructive mt-1">{errors.is_public.message}</p>}

        <div className="space-y-2">
          <Label htmlFor="cover_url" className="flex items-center"><ImageUp className="mr-2 h-4 w-4 text-muted-foreground" />Pochette (URL ou téléversement)</Label>
          <ImageUploader 
            onImageUploaded={(url) => setValue('cover_url', url, { shouldValidate: true })} 
            existingImageUrl={watch('cover_url') || undefined} 
            bucketName="playlist-covers"  // Changed underscore to hyphen
            // aspectRatio="square" // Removed, defaults to "free"
            maxWidth={1200} maxHeight={1200} 
          />
          <p className="text-xs text-muted-foreground mt-1">Max 1200x1200px, 5MB. Ratio libre.</p>
          {errors.cover_url && <p className="text-sm text-destructive mt-1">{errors.cover_url.message}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="banner_url" className="flex items-center"><ImageUp className="mr-2 h-4 w-4 text-muted-foreground" />Bannière (URL ou téléversement)</Label>
          <ImageUploader 
            onImageUploaded={(url) => setValue('banner_url', url, { shouldValidate: true })} 
            existingImageUrl={watch('banner_url') || undefined} 
            bucketName="playlist-banners" 
            // aspectRatio="landscape" // Removed, defaults to "free"
            maxWidth={1600} maxHeight={900}
          />
          <p className="text-xs text-muted-foreground mt-1">Max 1600x900px, 5MB. Ratio libre.</p>
          {errors.banner_url && <p className="text-sm text-destructive mt-1">{errors.banner_url.message}</p>}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="genres" className="flex items-center"><Music2 className="mr-2 h-4 w-4 text-muted-foreground" />Genres</Label>
            <MultiSelect options={genreOptions} selected={watch('genres') || []} onChange={(selected) => setValue('genres', selected)} placeholder="Sélectionnez des genres" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="moods" className="flex items-center"><Palette className="mr-2 h-4 w-4 text-muted-foreground" />Ambiances</Label>
            <MultiSelect options={moodOptions} selected={watch('moods') || []} onChange={(selected) => setValue('moods', selected)} placeholder="Sélectionnez des ambiances" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="instrumentation" className="flex items-center"><Guitar className="mr-2 h-4 w-4 text-muted-foreground" />Instrumentation</Label>
            <MultiSelect options={instrumentationOptions} selected={watch('instrumentation') || []} onChange={(selected) => setValue('instrumentation', selected)} placeholder="Sélectionnez des instruments" />
          </div>
        </div>

        <div className="flex items-center space-x-2 pt-2">
          <Switch id="are_comments_public" {...register('are_comments_public')} defaultChecked={false} />
          <Label htmlFor="are_comments_public">Rendre les commentaires publics ?</Label>
        </div>
        {errors.are_comments_public && <p className="text-sm text-destructive mt-1">{errors.are_comments_public.message}</p>}


        <div className="flex justify-end gap-4 pt-4"> 
          <Button type="button" variant="outline" onClick={() => router.back()} disabled={isSubmitting}>
            Annuler
          </Button>
          <Button type="submit" disabled={isSubmitting || !canCreatePlaylist}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Créer la playlist
          </Button>
        </div>
      </form>
    </div>
  );
}
