"use client";
import React from "react";

export default function MobileMenuButton() {
  return (
    <button
      type="button"
      aria-label="Ouvrir le menu"
      className="fixed top-4 left-4 z-30 md:hidden bg-blue-600 text-white rounded-full p-2 shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-300"
      onClick={() => {
        window.dispatchEvent(new CustomEvent("sidebar:open-mobile"));
      }}
    >
      <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-panel-left"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M9 3v18"/></svg>
    </button>
  );
}
