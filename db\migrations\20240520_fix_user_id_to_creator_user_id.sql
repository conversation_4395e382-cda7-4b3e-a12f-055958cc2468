-- Migration pour remplacer les références à user_id par creator_user_id
-- <PERSON><PERSON><PERSON> le: 2024-05-20
-- Auteur: <PERSON><PERSON><PERSON>

-- Début de la transaction
BEGIN;

-- 1. Mettre à jour la vue artist_stats pour utiliser creator_user_id
CREATE OR REPLACE VIEW public.artist_stats AS
SELECT 
    p.id as artist_id,
    p.username,
    p.display_name,
    p.avatar_url,
    (SELECT COUNT(*) FROM public.follows WHERE following_id = p.id AND following_type = 'user') as followers_count,
    (SELECT COUNT(*) FROM public.follows WHERE follower_id = p.id) as following_count,
    (SELECT COUNT(*) FROM public.songs WHERE creator_user_id = p.id) as songs_count,
    (SELECT COUNT(*) FROM public.albums WHERE creator_user_id = p.id) as albums_count,
    COALESCE((SELECT SUM(plays) FROM public.songs WHERE creator_user_id = p.id), 0) as total_plays,
    p.view_count
FROM public.profiles p;

-- 2. Mettre à jour la fonction get_artist_stats
CREATE OR REPLACE FUNCTION public.get_artist_stats(artist_id UUID)
RETURNS TABLE (
  total_plays BIGINT,
  total_followers BIGINT,
  total_albums BIGINT,
  total_songs BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(SUM(s.plays), 0)::BIGINT AS total_plays,
    (SELECT COUNT(*) FROM follows WHERE following_id = artist_id AND following_type = 'user')::BIGINT AS total_followers,
    (SELECT COUNT(*) FROM albums WHERE creator_user_id = artist_id)::BIGINT AS total_albums,
    (SELECT COUNT(*) FROM songs WHERE creator_user_id = artist_id)::BIGINT AS total_songs;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Mettre à jour la fonction get_dashboard_data
CREATE OR REPLACE FUNCTION public.get_dashboard_data(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
-- Corps de la fonction inchangé, mais utiliser creator_user_id au lieu de user_id
-- ...
$$;

-- 4. Mettre à jour la vue songs_with_albums
CREATE OR REPLACE VIEW public.songs_with_albums AS
SELECT 
    s.*,
    jsonb_build_object(
        'id', a.id,
        'title', a.title,
        'cover_url', a.cover_url
    ) as album
FROM public.songs s
LEFT JOIN public.albums a ON s.album_id = a.id;

-- 5. Donner les permissions nécessaires
GRANT SELECT ON public.artist_stats TO authenticated, service_role;
GRANT SELECT ON public.songs_with_albums TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_artist_stats(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_dashboard_data(UUID) TO authenticated, service_role;

-- Fin de la transaction
COMMIT;

-- Instructions post-migration:
-- 1. Tester les vues et fonctions mises à jour
-- 2. Vérifier que les statistiques affichées sont correctes
-- 3. Mettre à jour la documentation pour refléter les changements
