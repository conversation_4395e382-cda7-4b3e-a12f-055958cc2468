-- Table pour les groupes/bands
CREATE TABLE IF NOT EXISTS bands (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  genre VARCHAR(100),
  location VARCHAR(100),
  avatar_url TEXT,
  banner_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les membres des groupes
CREATE TABLE IF NOT EXISTS band_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  band_id UUID NOT NULL REFERENCES bands(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(100),
  is_admin BOOLEAN DEFAULT FALSE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(band_id, user_id)
);

-- Table pour les projets des groupes
CREATE TABLE IF NOT EXISTS band_projects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  band_id UUID NOT NULL REFERENCES bands(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  type VARCHAR(50) NOT NULL, -- album, single, EP, etc.
  status VARCHAR(50) NOT NULL DEFAULT 'in_progress', -- in_progress, completed, archived
  progress INTEGER DEFAULT 0, -- 0-100
  cover_url TEXT,
  release_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les versions des morceaux dans les projets
CREATE TABLE IF NOT EXISTS project_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES band_projects(id) ON DELETE CASCADE,
  version_number VARCHAR(10) NOT NULL,
  description TEXT,
  audio_url TEXT,
  waveform_url TEXT,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les activités des groupes
CREATE TABLE IF NOT EXISTS band_activities (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  band_id UUID NOT NULL REFERENCES bands(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type VARCHAR(50) NOT NULL, -- version_added, comment_added, member_joined, etc.
  resource_type VARCHAR(50), -- project, version, etc.
  resource_id UUID,
  content TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les commentaires sur les projets/versions
CREATE TABLE IF NOT EXISTS project_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES band_projects(id) ON DELETE CASCADE,
  version_id UUID REFERENCES project_versions(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les statistiques des groupes
CREATE TABLE IF NOT EXISTS band_stats (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  band_id UUID NOT NULL REFERENCES bands(id) ON DELETE CASCADE,
  total_plays INTEGER DEFAULT 0,
  total_likes INTEGER DEFAULT 0,
  total_shares INTEGER DEFAULT 0,
  total_comments INTEGER DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les genres musicaux (pour la page Discover)
CREATE TABLE IF NOT EXISTS genres (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  icon_url TEXT,
  color VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les playlists curated (pour la page Discover)
CREATE TABLE IF NOT EXISTS curated_playlists (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  cover_url TEXT,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  is_featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les morceaux dans les playlists curated
CREATE TABLE IF NOT EXISTS curated_playlist_songs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  playlist_id UUID NOT NULL REFERENCES curated_playlists(id) ON DELETE CASCADE,
  song_id UUID NOT NULL REFERENCES songs(id) ON DELETE CASCADE,
  position INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(playlist_id, song_id)
);

-- Mise à jour des triggers pour les nouvelles tables
DO $$
BEGIN
  -- Trigger pour bands
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_bands_updated_at') THEN
    CREATE TRIGGER update_bands_updated_at
    BEFORE UPDATE ON bands
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;

  -- Trigger pour band_projects
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_band_projects_updated_at') THEN
    CREATE TRIGGER update_band_projects_updated_at
    BEFORE UPDATE ON band_projects
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;

  -- Trigger pour curated_playlists
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_curated_playlists_updated_at') THEN
    CREATE TRIGGER update_curated_playlists_updated_at
    BEFORE UPDATE ON curated_playlists
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  END IF;
END
$$;

-- Insertion de quelques genres pour la page Discover
INSERT INTO genres (name, color) VALUES
('Tout', '#1DB954'),
('Pop', '#E91E63'),
('Rock', '#FF5722'),
('Hip Hop', '#9C27B0'),
('Électronique', '#2196F3'),
('Jazz', '#FFC107'),
('Classique', '#795548'),
('Alternative', '#607D8B'),
('R&B', '#3F51B5'),
('Indie', '#009688'),
('Metal', '#F44336'),
('Folk', '#4CAF50')
ON CONFLICT (name) DO NOTHING;
