"use client"

import { useEffect, useState } from "react"
import { Eye } from "lucide-react"
import { createBrowserClient } from "@/lib/supabase/client"

interface ViewCounterProps {
  resourceId: string
  resourceType: "song" | "album" | "artist" | "band"
  count?: number
}

export function ViewCounter({ resourceId, resourceType, count = 0 }: ViewCounterProps) {
  const [views, setViews] = useState(count)
  const [viewRecorded, setViewRecorded] = useState(false)
  const supabase = createBrowserClient()

  useEffect(() => {
    const recordView = async () => {
      if (viewRecorded) return

      try {
        const {
          data: { session },
        } = await supabase.auth.getSession()

        // Enregistrer la vue
        await supabase.from("views").insert({
          resource_type: resourceType,
          resource_id: resourceId,
          user_id: session?.user?.id || null,
          ip_address: null, // On ne stocke pas l'IP pour des raisons de confidentialité
        })

        setViews((prev) => prev + 1)
        setViewRecorded(true)
      } catch (error) {
        console.error("Erreur lors de l'enregistrement de la vue:", error)
      }
    }

    // Enregistrer la vue après un délai pour s'assurer que l'utilisateur a réellement vu la page
    const timer = setTimeout(() => {
      recordView()
    }, 5000)

    return () => clearTimeout(timer)
  }, [resourceId, resourceType, supabase, viewRecorded])

  return (
    <div className="flex items-center gap-1 text-sm">
      <Eye className="h-4 w-4" />
      <span>{views}</span>
    </div>
  )
}
