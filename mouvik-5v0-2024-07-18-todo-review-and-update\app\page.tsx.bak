'use client'

import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON>2, Wand2, <PERSON><PERSON>, <PERSON>hare2, <PERSON><PERSON><PERSON>2, Users } from "lucide-react"
import { Button } from "@/components/ui/button"
import { AuthPopup } from '@/components/auth/auth-popup'
import React, { useRef } from 'react'

export default function HomePage() {
  const authRef = useRef<any>(null)
  const handleOpenAuth = () => {
    if (authRef.current && typeof authRef.current.openAuth === 'function') {
      authRef.current.openAuth()
    }
  }

  return (
    <div className="min-h-screen flex flex-col bg-[var(--darkest-bg)]">
      {/* Header */}
      <header className="flex h-20 items-center justify-between py-6 px-4 md:px-8 z-40">
        <div className="flex items-center gap-4">
          <img
            src="/LOGO_Mouvik.png"
            alt="MOUVIK"
            className="h-8 w-auto"
          />
        </div>
        <nav className="hidden gap-6 md:flex">
          <Link href="#features" className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">Fonctionnalités</Link>
          <Link href="#pricing" className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">Tarifs</Link>
          <Link href="#testimonials" className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">Témoignages</Link>
          <Link href="#faq" className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary">FAQ</Link>
        </nav>
        <div className="flex items-center gap-2">
          <AuthPopup ref={authRef} />
        </div>
      </header>

      {/* Hero Full Width Image */}
      <section className="relative w-full">
        <div className="relative w-full">
          <img
            src="/img_indexintro3.jpg"
            alt="Mouvik Hero"
            className="w-full max-h-[420px] min-h-[180px] object-cover object-center"
            style={{ display: 'block' }}
          />
          {/* Dégradé bas de l'image */}
          <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-b from-transparent via-[rgba(10,20,25,0.1)] to-[var(--darkest-bg)] pointer-events-none" />
          {/* FX Oscillation */}
          <div className="absolute top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-teal-400 rounded-full opacity-50 animate-pulse"></div>
        </div>
        {/* Zone titre/sous-titre claire sous l'image */}
        <div className="w-full flex flex-col items-center text-center mt-6 mb-8 px-4">
          <h1 className="text-3xl md:text-5xl font-extrabold text-white mb-2 drop-shadow-lg">Créez, collaborez, innovez avec MOUVIK</h1>
          <p className="max-w-2xl text-lg md:text-xl text-slate-200 mb-6">La plateforme musicale nouvelle génération : IA, outils pros, collaboration mondiale et simplicité totale.</p>
          <Button
            className="btn-gradient px-8 py-4 rounded-full text-lg font-bold shadow-lg text-white border-2 border-white/10"
            style={{boxShadow: '0 8px 32px 0 rgba(0,0,0,0.2)'}}
            size="lg"
            onClick={handleOpenAuth}
          >
            Commencer à créer
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="container-center py-12 md:py-20 mt-8">
        <div className="mx-auto flex flex-col items-center space-y-4 text-center mb-10">
          <h2 className="font-bold text-3xl sm:text-4xl md:text-5xl teal-glow mb-2">Fonctionnalités puissantes</h2>
          <p className="max-w-2xl leading-normal text-muted-foreground sm:text-lg sm:leading-7">
            Tout ce dont vous avez besoin pour créer, produire et partager votre musique avec le monde.
          </p>
        </div>
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <div className="bg-glass rounded-xl p-6 flex flex-col items-center">
            <Wand2 className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-bold mb-1 text-white text-lg">Composition assistée par IA</h3>
            <p className="text-sm text-slate-300">Suggestions intelligentes pour mélodies, accords et arrangements selon votre style.</p>
          </div>
          <div className="bg-glass rounded-xl p-6 flex flex-col items-center">
            <Layers className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-bold mb-1 text-white text-lg">Mixage professionnel</h3>
            <p className="text-sm text-slate-300">Outils de mixage studio et mastering automatisé pour un rendu parfait.</p>
          </div>
          <div className="bg-glass rounded-xl p-6 flex flex-col items-center">
            <Users className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-bold mb-1 text-white text-lg">Outils de collaboration</h3>
            <p className="text-sm text-slate-300">Travaillez avec d'autres musiciens et producteurs, partout dans le monde.</p>
          </div>
          <div className="bg-glass rounded-xl p-6 flex flex-col items-center">
            <Music2 className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-bold mb-1 text-white text-lg">Stockage cloud</h3>
            <p className="text-sm text-slate-300">Stockez tous vos projets en sécurité et accédez-y depuis n'importe quel appareil.</p>
          </div>
          <div className="bg-glass rounded-xl p-6 flex flex-col items-center">
            <Share2 className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-bold mb-1 text-white text-lg">Distribution facile</h3>
            <p className="text-sm text-slate-300">Partagez votre musique sur toutes les plateformes en quelques clics.</p>
          </div>
          <div className="bg-glass rounded-xl p-6 flex flex-col items-center">
            <BarChart2 className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-bold mb-1 text-white text-lg">Analyses et statistiques</h3>
            <p className="text-sm text-slate-300">Suivez les performances de votre musique avec des analyses détaillées.</p>
          </div>
        </div>
      </section>

      {/* Showcase Section */}
      <section className="container-center py-12 md:py-20 relative">
        <div className="absolute inset-0 z-0 opacity-20">
          <img src="https://page.genspark.site/v1/base64_upload/00e6489335312825d5fc3da29bafda21" alt="Abstract Background" className="w-full h-full object-cover" />
        </div>
        <div className="relative z-10">
          <div className="flex flex-col lg:flex-row items-center">
            <div className="lg:w-1/2 mb-10 lg:mb-0 order-2 lg:order-1">
              <h2 className="text-4xl font-bold mb-6 gradient-text">Unleash Your Creative Potential</h2>
              <p className="text-xl text-gray-300 mb-6">
                Mouvik combines cutting-edge technology with intuitive design to give you the most powerful music creation platform available today.
              </p>
              <ul className="space-y-4 mb-8">
                <li className="flex items-start">
                  <i className="fas fa-check-circle text-teal-400 mt-1 mr-3"></i>
                  <span>Multi-track recording with unlimited layers</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check-circle text-teal-400 mt-1 mr-3"></i>
                  <span>Advanced lyrics editor with chord integration</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check-circle text-teal-400 mt-1 mr-3"></i>
                  <span>Comprehensive sound library with thousands of samples</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check-circle text-teal-400 mt-1 mr-3"></i>
                  <span>Real-time collaboration with band members and producers</span>
                </li>
              </ul>
              <button className="btn-primary px-8 py-3 rounded-md text-lg">Explore Features</button>
            </div>
            <div className="lg:w-1/2 flex justify-center order-1 lg:order-2">
              <img src="https://page.genspark.site/v1/base64_upload/f8519234e8ebc5f0b274e5df13b80ac1" alt="Frog musician with guitar" className="floating w-full max-w-md rounded-lg" />
            </div>
          </div>
        </div>
      </section>

      <div className="section-divider"></div>

      {/* Testimonials Section */}
      <section id="testimonials" className="container-center py-12 md:py-20 bg-[#151c22] rounded-2xl my-8">
        <div className="mx-auto flex flex-col items-center space-y-4 text-center mb-10">
          <h2 className="font-bold text-3xl sm:text-4xl md:text-5xl teal-glow mb-2">Ce que disent nos utilisateurs</h2>
          <p className="max-w-2xl leading-normal text-muted-foreground sm:text-lg sm:leading-7">
            Rejoignez des milliers de musiciens qui créent déjà de la musique incroyable avec MOUVIK.
          </p>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          <div className="bg-glass rounded-xl p-6 text-left">
            <div className="mb-2 font-bold text-white">Alex Johnson</div>
            <div className="text-sm mb-2 text-slate-400">Producteur électronique</div>
            <div className="text-sm mb-4 text-slate-200">"MOUVIK a complètement transformé mon workflow. Les suggestions d'IA sont précises et m'ont aidé à surmonter les blocages créatifs."</div>
            <div className="flex text-primary">★★★★★</div>
          </div>
          <div className="bg-glass rounded-xl p-6 text-left">
            <div className="mb-2 font-bold text-white">Sarah Williams</div>
            <div className="text-sm mb-2 text-slate-400">Chanteuse & compositrice</div>
            <div className="text-sm mb-4 text-slate-200">"Le mixage automatisé et la collaboration à distance m'ont permis d'aller beaucoup plus loin dans mes projets !"</div>
            <div className="flex text-primary">★★★★★</div>
          </div>
          <div className="bg-glass rounded-xl p-6 text-left">
            <div className="mb-2 font-bold text-white">Omar El Khalil</div>
            <div className="text-sm mb-2 text-slate-400">Beatmaker</div>
            <div className="text-sm mb-4 text-slate-200">"J'adore la simplicité d'utilisation et la puissance des outils IA de MOUVIK. C'est devenu mon studio principal !"</div>
            <div className="flex text-primary">★★★★★</div>
          </div>
        </div>
      </section>

      <div className="section-divider"></div>

      {/* Call to Action Section */}
      <section className="py-20 px-4 relative">
        <div className="absolute inset-0 z-0 opacity-30">
          <img src="https://page.genspark.site/v1/base64_upload/00e6489335312825d5fc3da29bafda21" alt="Abstract Background" className="w-full h-full object-cover" />
        </div>
        <div className="container mx-auto relative z-10 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Transform Your Music?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of musicians who are already creating better music with Mouvik.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <button className="btn-primary px-8 py-3 rounded-md text-lg">Start Free Trial</button>
            <button className="btn-secondary px-8 py-3 rounded-md text-lg">Learn More</button>
          </div>
          <div className="flex gap-4 justify-center mt-6">
            <Link href="#pricing">Tarifs</Link>
            <Link href="#testimonials">Témoignages</Link>
            <Link href="#faq">FAQ</Link>
          </div>
          <div className="text-xs mt-8">2023 MOUVIK. Tous droits réservés.</div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 mt-8 border-t border-slate-800 text-center text-muted-foreground px-4 md:px-8">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
          <div className="flex items-center gap-2 justify-center md:justify-start">
            <img src="/LOGO_Mouvik.png" alt="Logo Mouvik" className="h-7 w-auto mr-2" />
            <span className="font-semibold">MOUVIK</span>
          </div>
          <div className="flex gap-4 justify-center md:justify-end">
            <Link href="#features">Fonctionnalités</Link>
            <Link href="#pricing">Tarifs</Link>
            <Link href="#testimonials">Témoignages</Link>
            <Link href="#faq">FAQ</Link>
          </div>
        </div>
      </footer>
    </div>
  )
}
