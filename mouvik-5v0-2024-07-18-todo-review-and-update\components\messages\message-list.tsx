"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { Search, Plus, MessageSquare } from "lucide-react"
import { createBrowserClient } from '@/lib/supabase/client'
import { useToast } from "@/hooks/use-toast"

interface MessageListProps {
  conversations: any[]
  currentUserId: string
  selectedConversationId: string | null
}

export function MessageList({ conversations, currentUserId, selectedConversationId }: MessageListProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [newMessageEmail, setNewMessageEmail] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const router = useRouter()
  const supabase = createBrowserClient()
  const { toast } = useToast()

  const filteredConversations = conversations.filter((conversation) => {
    const otherParticipant = conversation.participants[0]?.users
    if (!otherParticipant) return false

    const name = otherParticipant.user_metadata?.full_name || otherParticipant.email?.split("@")[0] || ""
    return name.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const handleSearch = async () => {
    if (!newMessageEmail.trim()) return

    setIsSearching(true)

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("id, email, full_name, avatar_url")
        .ilike("email", `%${newMessageEmail}%`)
        .limit(5)

      if (error) {
        console.error("Erreur lors de la recherche:", error)
        return
      }

      setSearchResults(data || [])
    } catch (error) {
      console.error("Erreur lors de la recherche:", error)
    } finally {
      setIsSearching(false)
    }
  }

  const startConversation = async (userId: string) => {
    try {
      // Vérifier si une conversation existe déjà
      const { data: existingConversations } = await supabase
        .from("conversation_participants")
        .select("conversation_id")
        .eq("user_id", currentUserId)

      if (existingConversations) {
        for (const convo of existingConversations) {
          const { data: otherParticipant } = await supabase
            .from("conversation_participants")
            .select("user_id")
            .eq("conversation_id", convo.conversation_id)
            .eq("user_id", userId)
            .single()

          if (otherParticipant) {
            // Conversation existante trouvée
            setIsDialogOpen(false)
            router.push(`/messages?conversation=${convo.conversation_id}`)
            return
          }
        }
      }

      // Créer une nouvelle conversation
      const { data: newConversation, error: conversationError } = await supabase
        .from("conversations")
        .insert({})
        .select()
        .single()

      if (conversationError) {
        console.error("Erreur lors de la création de la conversation:", conversationError)
        return
      }

      // Ajouter les participants
      const participants = [
        { conversation_id: newConversation.id, user_id: currentUserId },
        { conversation_id: newConversation.id, user_id: userId },
      ]

      const { error: participantsError } = await supabase.from("conversation_participants").insert(participants)

      if (participantsError) {
        console.error("Erreur lors de l'ajout des participants:", participantsError)
        return
      }

      setIsDialogOpen(false)
      router.push(`/messages?conversation=${newConversation.id}`)
    } catch (error) {
      console.error("Erreur lors de la création de la conversation:", error)
      toast({
        title: "Erreur",
        description: "Impossible de créer la conversation",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="rounded-lg border bg-card">
      <div className="p-4">
        <div className="flex items-center gap-2">
          <Input
            placeholder="Rechercher..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1"
          />
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="icon" variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Nouveau message</DialogTitle>
              </DialogHeader>
              <div className="mt-4 space-y-4">
                <div className="flex items-center gap-2">
                  <Input
                    placeholder="Rechercher par email..."
                    value={newMessageEmail}
                    onChange={(e) => setNewMessageEmail(e.target.value)}
                  />
                  <Button size="sm" onClick={handleSearch} disabled={isSearching}>
                    <Search className="h-4 w-4" />
                  </Button>
                </div>

                <div className="max-h-60 overflow-y-auto">
                  {searchResults.length > 0 ? (
                    <div className="space-y-2">
                      {searchResults.map((user) => (
                        <div
                          key={user.id}
                          className="flex cursor-pointer items-center gap-3 rounded-md p-2 hover:bg-gray-100"
                          onClick={() => startConversation(user.id)}
                        >
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={user.avatar_url || ""} alt={user.full_name || user.email} />
                            <AvatarFallback>
                              {(user.full_name || user.email).substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{user.full_name || user.email.split("@")[0]}</p>
                            <p className="text-sm text-gray-500">{user.email}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    newMessageEmail &&
                    !isSearching && <p className="text-center text-sm text-gray-500">Aucun résultat trouvé</p>
                  )}
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="max-h-[500px] overflow-y-auto">
        {filteredConversations.length > 0 ? (
          <div className="divide-y">
            {filteredConversations.map((conversation) => {
              const otherParticipant = conversation.participants[0]?.users
              if (!otherParticipant) return null

              const name =
                otherParticipant.user_metadata?.full_name || otherParticipant.email?.split("@")[0] || "Utilisateur"
              const avatarUrl = otherParticipant.user_metadata?.avatar_url
              const lastMessage = conversation.lastMessage
              const isSelected = selectedConversationId === conversation.id

              return (
                <Link
                  key={conversation.id}
                  href={`/messages?conversation=${conversation.id}`}
                  className={`block p-4 hover:bg-gray-50 ${isSelected ? "bg-gray-100" : ""}`}
                >
                  <div className="flex items-start gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={avatarUrl || ""} alt={name} />
                      <AvatarFallback>{name.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 overflow-hidden">
                      <div className="flex items-center justify-between">
                        <p className="font-medium">{name}</p>
                        {lastMessage && (
                          <p className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(lastMessage.created_at), { addSuffix: true, locale: fr })}
                          </p>
                        )}
                      </div>
                      {lastMessage ? (
                        <p className="truncate text-sm text-gray-500">
                          {lastMessage.sender_id === currentUserId ? "Vous: " : ""}
                          {lastMessage.content}
                        </p>
                      ) : (
                        <p className="text-sm text-gray-400">Nouvelle conversation</p>
                      )}
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        ) : (
          <div className="flex h-40 items-center justify-center">
            <div className="text-center">
              <MessageSquare className="mx-auto h-8 w-8 text-gray-400" />
              <p className="mt-2 text-gray-500">Aucune conversation</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
