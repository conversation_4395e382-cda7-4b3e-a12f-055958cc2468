/**
 * 🎼 TESTS DE VALIDATION DU SYSTÈME D'ACCORDS
 * 
 * Tests unitaires pour valider la compatibilité avec les données JSON existantes
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

import { describe, it, expect, beforeAll } from '@jest/globals';
import { ChordValidation } from '../utils/ChordValidation';
import type { ChordJsonDefinition, UnifiedChordPosition } from '../types/chord-system';
import { createUnifiedChord, isValidUnifiedChord } from '../index';

// ============================================================================
// DONNÉES DE TEST
// ============================================================================

// Simulation des données JSON existantes
const mockGuitarData: ChordJsonDefinition = {
  instrument: 'guitar',
  tuning: ['E', 'A', 'D', 'G', 'B', 'E'],
  strings: 6,
  fretRange: [0, 24],
  keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
  suffixes: ['major', 'minor', '7', 'maj7'],
  chords: {
    'C': [
      {
        suffix: 'major',
        name: 'Cmajor',
        positions: [
          {
            frets: ['x', 3, 2, 0, 1, 0],
            fingers: [0, 3, 2, 0, 1, 0],
            baseFret: 1,
            barres: [],
            midi: [60, 64, 67, 72, 76],
            difficulty: 'beginner'
          },
          {
            frets: [8, 10, 10, 9, 8, 8],
            fingers: [1, 3, 4, 2, 1, 1],
            baseFret: 8,
            barres: [{ fret: 8, fromString: 1, toString: 6 }],
            midi: [60, 64, 67, 72, 76, 79],
            difficulty: 'intermediate'
          }
        ]
      }
    ]
  }
};

const mockUkuleleData: ChordJsonDefinition = {
  instrument: 'ukulele',
  tuning: ['G', 'C', 'E', 'A'],
  strings: 4,
  fretRange: [0, 15],
  keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
  suffixes: ['major', 'minor', '7'],
  chords: {
    'C': [
      {
        suffix: 'major',
        name: 'Cmajor',
        positions: [
          {
            frets: [0, 0, 0, 3],
            fingers: [0, 0, 0, 1],
            baseFret: 1,
            barres: [],
            midi: [67, 60, 64, 72],
            difficulty: 'beginner'
          }
        ]
      }
    ]
  }
};

// ============================================================================
// TESTS DE VALIDATION DES TYPES
// ============================================================================

describe('Validation des Types de Base', () => {
  it('devrait valider les instruments supportés', () => {
    expect(ChordValidation.validateInstrumentFile(mockGuitarData)).toMatchObject({
      valid: true,
      errors: [],
      stats: expect.objectContaining({
        totalChords: expect.any(Number),
        validChords: expect.any(Number)
      })
    });
  });

  it('devrait valider les données ukulélé', () => {
    expect(ChordValidation.validateInstrumentFile(mockUkuleleData)).toMatchObject({
      valid: true,
      errors: []
    });
  });

  it('devrait rejeter des données invalides', () => {
    const invalidData = {
      instrument: 'invalid',
      strings: 'not-a-number',
      chords: 'not-an-object'
    };

    const result = ChordValidation.validateInstrumentFile(invalidData);
    expect(result.valid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });
});

// ============================================================================
// TESTS DE CONVERSION
// ============================================================================

describe('Conversion vers Accords Unifiés', () => {
  it('devrait convertir une position JSON en accord unifié', () => {
    const jsonPosition = mockGuitarData.chords['C'][0].positions[0];
    const unifiedChord = createUnifiedChord(
      jsonPosition,
      'Cmajor',
      'guitar',
      'standard'
    );

    expect(isValidUnifiedChord(unifiedChord)).toBe(true);
    expect(unifiedChord.chord).toBe('Cmajor');
    expect(unifiedChord.instrument).toBe('guitar');
    expect(unifiedChord.frets).toEqual(['x', 3, 2, 0, 1, 0]);
    expect(unifiedChord.difficulty).toBe('beginner');
  });

  it('devrait gérer les barrés correctement', () => {
    const jsonPosition = mockGuitarData.chords['C'][0].positions[1]; // Position avec barré
    const unifiedChord = createUnifiedChord(
      jsonPosition,
      'Cmajor',
      'guitar',
      'standard'
    );

    expect(isValidUnifiedChord(unifiedChord)).toBe(true);
    expect(unifiedChord.barres).toHaveLength(1);
    expect(unifiedChord.barres![0]).toMatchObject({
      fret: 8,
      fromString: 1,
      toString: 6
    });
  });

  it('devrait valider les accords ukulélé', () => {
    const jsonPosition = mockUkuleleData.chords['C'][0].positions[0];
    const unifiedChord = createUnifiedChord(
      jsonPosition,
      'Cmajor',
      'ukulele',
      'gcea'
    );

    expect(isValidUnifiedChord(unifiedChord)).toBe(true);
    expect(unifiedChord.frets).toHaveLength(4); // 4 cordes pour ukulélé
  });
});

// ============================================================================
// TESTS DE VALIDATION MÉTIER
// ============================================================================

describe('Validation Métier', () => {
  it('devrait valider la cohérence frettes/instrument', () => {
    const validGuitarChord: UnifiedChordPosition = {
      id: 'test-1',
      chord: 'Cmajor',
      instrument: 'guitar',
      tuning: 'standard',
      frets: [0, 3, 2, 0, 1, 0], // 6 frettes pour guitare
      baseFret: 1,
      difficulty: 'beginner'
    };

    const result = ChordValidation.validateChord(validGuitarChord);
    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it('devrait détecter les incohérences frettes/instrument', () => {
    const invalidChord: UnifiedChordPosition = {
      id: 'test-2',
      chord: 'Cmajor',
      instrument: 'ukulele',
      tuning: 'gcea',
      frets: [0, 3, 2, 0, 1, 0], // 6 frettes pour ukulélé (devrait être 4)
      baseFret: 1,
      difficulty: 'beginner'
    };

    const result = ChordValidation.validateChord(invalidChord);
    expect(result.valid).toBe(false);
    expect(result.errors.some(error => error.includes('4 cordes'))).toBe(true);
  });

  it('devrait valider les doigtés avec les frettes', () => {
    const chordWithFingers: UnifiedChordPosition = {
      id: 'test-3',
      chord: 'Cmajor',
      instrument: 'guitar',
      tuning: 'standard',
      frets: ['x', 3, 2, 0, 1, 0],
      fingers: [0, 3, 2, 0, 1, 0],
      baseFret: 1,
      difficulty: 'beginner'
    };

    const result = ChordValidation.validateChord(chordWithFingers);
    expect(result.valid).toBe(true);
    expect(result.warnings).toHaveLength(0);
  });

  it('devrait détecter les incohérences doigtés/frettes', () => {
    const invalidFingers: UnifiedChordPosition = {
      id: 'test-4',
      chord: 'Cmajor',
      instrument: 'guitar',
      tuning: 'standard',
      frets: ['x', 3, 2, 0, 1, 0],
      fingers: [1, 3, 2, 0, 1, 0], // Doigté sur corde muette
      baseFret: 1,
      difficulty: 'beginner'
    };

    const result = ChordValidation.validateChord(invalidFingers);
    expect(result.warnings.some(warning => warning.includes('corde muette'))).toBe(true);
  });
});

// ============================================================================
// TESTS D'INTÉGRATION AVEC DONNÉES RÉELLES
// ============================================================================

describe('Intégration avec Données JSON Réelles', () => {
  let realGuitarData: ChordJsonDefinition;
  let realUkuleleData: ChordJsonDefinition;

  beforeAll(async () => {
    // Simulation du chargement des vrais fichiers JSON
    // En production, ces données viendraient des fichiers lib/chords/*.json
    try {
      // Note: Dans un vrai test, on chargerait les vrais fichiers
      realGuitarData = mockGuitarData; // Placeholder
      realUkuleleData = mockUkuleleData; // Placeholder
    } catch (error) {
      console.warn('Impossible de charger les données réelles, utilisation des mocks');
      realGuitarData = mockGuitarData;
      realUkuleleData = mockUkuleleData;
    }
  });

  it('devrait valider toutes les données guitare réelles', () => {
    const result = ChordValidation.validateInstrumentFile(realGuitarData);
    
    expect(result.valid).toBe(true);
    expect(result.stats?.totalChords).toBeGreaterThan(0);
    expect(result.stats?.validChords).toBe(result.stats?.totalChords);
    
    // Log des statistiques pour debug
    console.log('Guitare - Stats:', result.stats);
    if (result.warnings.length > 0) {
      console.log('Guitare - Avertissements:', result.warnings.slice(0, 5)); // Premiers 5
    }
  });

  it('devrait valider toutes les données ukulélé réelles', () => {
    const result = ChordValidation.validateInstrumentFile(realUkuleleData);
    
    expect(result.valid).toBe(true);
    expect(result.stats?.totalChords).toBeGreaterThan(0);
    
    console.log('Ukulélé - Stats:', result.stats);
    if (result.warnings.length > 0) {
      console.log('Ukulélé - Avertissements:', result.warnings.slice(0, 5));
    }
  });

  it('devrait convertir tous les accords réels sans erreur', () => {
    const allChords: UnifiedChordPosition[] = [];
    
    // Conversion de tous les accords guitare
    for (const [key, variations] of Object.entries(realGuitarData.chords)) {
      for (const variation of variations) {
        for (const position of variation.positions) {
          const unified = createUnifiedChord(
            position,
            `${key}${variation.suffix}`,
            'guitar',
            'standard'
          );
          allChords.push(unified);
        }
      }
    }
    
    expect(allChords.length).toBeGreaterThan(0);
    
    // Validation de tous les accords convertis
    const validationResults = ChordValidation.validateChordList(allChords);
    
    const validCount = validationResults.results.filter(r => r.valid).length;
    const totalCount = validationResults.results.length;
    
    console.log(`Conversion: ${validCount}/${totalCount} accords valides`);
    
    // Au moins 90% des accords devraient être valides
    expect(validCount / totalCount).toBeGreaterThan(0.9);
  });
});

// ============================================================================
// TESTS DE PERFORMANCE
// ============================================================================

describe('Performance de Validation', () => {
  it('devrait valider rapidement un grand nombre d\'accords', () => {
    const startTime = performance.now();
    
    // Création de 1000 accords de test
    const testChords: UnifiedChordPosition[] = [];
    for (let i = 0; i < 1000; i++) {
      testChords.push({
        id: `test-${i}`,
        chord: 'Cmajor',
        instrument: 'guitar',
        tuning: 'standard',
        frets: [0, 3, 2, 0, 1, 0],
        baseFret: 1,
        difficulty: 'beginner'
      });
    }
    
    // Validation de tous les accords
    const results = ChordValidation.validateChordList(testChords);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    expect(results.valid).toBe(true);
    expect(duration).toBeLessThan(1000); // Moins d'1 seconde pour 1000 accords
    
    console.log(`Performance: ${testChords.length} accords validés en ${duration.toFixed(2)}ms`);
  });
});

// ============================================================================
// TESTS DE RÉGRESSION
// ============================================================================

describe('Tests de Régression', () => {
  it('devrait maintenir la compatibilité avec les formats existants', () => {
    // Test avec le format utilisé dans SongFormLyricsChordTab.tsx
    const legacyFormat = {
      frets: [-1, 3, 2, 0, 1, 0],
      fingers: [0, 3, 2, 0, 1, 0],
      baseFret: 1,
      barres: [],
      midi: [60, 64, 67, 72, 76]
    };
    
    const unified = createUnifiedChord(legacyFormat, 'Cmajor', 'guitar');
    expect(isValidUnifiedChord(unified)).toBe(true);
  });

  it('devrait gérer les différents formats de cordes muettes', () => {
    const formats = [
      ['x', 3, 2, 0, 1, 0],
      ['X', 3, 2, 0, 1, 0],
      [-1, 3, 2, 0, 1, 0]
    ];
    
    formats.forEach((frets, index) => {
      const unified = createUnifiedChord(
        { frets, baseFret: 1 },
        'Cmajor',
        'guitar'
      );
      expect(isValidUnifiedChord(unified)).toBe(true);
    });
  });
});
