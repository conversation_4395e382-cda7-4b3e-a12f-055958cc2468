"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { User<PERSON><PERSON>, UserCheck, Loader2, Rss } from 'lucide-react'; // Rss can be used for bands too
import { useUser } from '@/contexts/user-context';
import { getSupabaseClient } from '@/lib/supabase/client';
import { toast } from '@/hooks/use-toast';

interface FollowBandButtonProps {
  bandId: string;
  initialIsFollowed: boolean;
  initialFollowerCount: number;
  onFollowToggle?: (isFollowing: boolean, newFollowerCount: number) => void;
  size?: "sm" | "default" | "lg" | "icon";
  className?: string;
}

export function FollowBandButton({
  bandId,
  initialIsFollowed,
  initialFollowerCount,
  onFollowToggle,
  size = "default",
  className,
}: FollowBandButtonProps) {
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [isFollowed, setIsFollowed] = useState(initialIsFollowed);
  const [followerCount, setFollowerCount] = useState(initialFollowerCount);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsFollowed(initialIsFollowed);
    setFollowerCount(initialFollowerCount);
  }, [initialIsFollowed, initialFollowerCount]);

  const handleFollow = async () => {
    if (!user) {
      toast({ title: "Connexion requise", description: "Vous devez être connecté pour suivre un groupe.", variant: "destructive" });
      return;
    }

    // A user might be the creator of the band, in which case "following" might not make sense
    // This logic can be added if needed, for now, allowing follow.

    setIsLoading(true);
    try {
      const { data, error } = await supabase.rpc('toggle_band_follow', {
        p_band_id: bandId,
        p_user_id: user.id,
      });

      if (error) throw error;

      if (data && data.length > 0) {
        const result = data[0];
        setIsFollowed(result.is_following);
        setFollowerCount(result.new_follower_count);
        if (onFollowToggle) {
          onFollowToggle(result.is_following, result.new_follower_count);
        }
        toast({
          title: result.is_following ? "Suivi !" : "Ne plus suivi",
          description: result.is_following ? `Vous suivez maintenant ce groupe.` : `Vous ne suivez plus ce groupe.`,
        });
      }
    } catch (error: any) {
      console.error("Error toggling band follow:", error);
      toast({ title: "Erreur", description: error.message || "Une erreur est survenue.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!user) { // Only show if user is logged in. Creator can also follow/unfollow their band.
    return null;
  }

  return (
    <Button
      variant={isFollowed ? "secondary" : "outline"}
      size={size}
      onClick={handleFollow}
      disabled={isLoading}
      className={className}
      title={isFollowed ? "Ne plus suivre ce groupe" : "Suivre ce groupe"}
    >
      {isLoading ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : isFollowed ? (
        <Rss className="mr-2 h-4 w-4" /> // Using Rss as a general follow icon
      ) : (
        <Rss className="mr-2 h-4 w-4 opacity-70" /> // Slightly different for not followed
      )}
      {isFollowed ? 'Suivi' : 'Suivre'}
    </Button>
  );
}
