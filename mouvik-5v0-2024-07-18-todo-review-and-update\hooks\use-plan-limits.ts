"use client";

import { useState, useEffect } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context'; // Assuming user-context.tsx is in contexts/

// Define the structure of plan limits based on the plan_limits table
export interface PlanLimitDetails {
  tier: 'free' | 'pro' | 'studio' | string; // string for flexibility if enum values change
  uploads_per_month: number | null;
  vault_space_gb: number | null;
  ia_credits_month: number | null;
  coins_month: number | null;
  page_customisation: boolean;
  analytics_level: 'basic' | 'extended' | 'pro' | string;
  max_playlists: number | null;
  max_friends: number | null;
  vault_max_files: number | null; // Added vault_max_files
}

// Store all plan limits fetched to avoid re-fetching on every hook usage
let allPlanLimits: PlanLimitDetails[] | null = null;
let fetchPromise: Promise<void> | null = null;

async function fetchAllPlanLimits(supabase: ReturnType<typeof getSupabaseClient>) {
  if (allPlanLimits) return; // Already fetched
  if (fetchPromise) return fetchPromise; // Fetch in progress

  fetchPromise = (async () => {
    try {
      const { data, error } = await supabase.from('plan_limits').select('*');
      if (error) {
        console.error("Error fetching plan limits:", error);
        throw error;
      }
      allPlanLimits = data as PlanLimitDetails[];
    } catch (err) {
      // Handle error, maybe set allPlanLimits to an empty array or default
      allPlanLimits = []; // Fallback
    } finally {
      fetchPromise = null;
    }
  })();
  return fetchPromise;
}


export function usePlanLimits(): { limits: PlanLimitDetails | null; isLoading: boolean } {
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [limits, setLimits] = useState<PlanLimitDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user?.subscription_tier) {
      setIsLoading(false);
      // Optionally set to 'free' tier limits if user/tier is unknown but a default is desired
      // For now, returns null if no tier.
      setLimits(null); 
      return;
    }

    const getLimits = async () => {
      setIsLoading(true);
      if (!allPlanLimits && !fetchPromise) {
        await fetchAllPlanLimits(supabase);
      } else if (fetchPromise) {
        await fetchPromise; // Wait for ongoing fetch
      }

      if (allPlanLimits) {
        const userTierLimits = allPlanLimits.find(limit => limit.tier === user.subscription_tier);
        setLimits(userTierLimits || null); // Set to null if specific tier not found
      } else {
        // Fallback if fetch failed or no limits defined
        setLimits(null); 
      }
      setIsLoading(false);
    };

    getLimits();
  }, [user?.subscription_tier, supabase]);

  return { limits, isLoading };
}
