"use client";

import React, { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Input } from "../ui/input";
import { Loader2, Brain, Wand2, RefreshCcw, Languages, CheckCircle2, XCircle } from "lucide-react";

interface AiQuickActionsProps {
  onGenerate: (prompt?: string) => void;
  onCorrect: () => void;
  onTranslate: (lang: string) => void;
  aiConfig: {
    provider: string;
    model: string;
    temperature: number;
  };
  setAiConfig: (cfg: Partial<AiQuickActionsProps["aiConfig"]>) => void;
  loading?: boolean;
  lastResult?: string;
  error?: string;
}

const languageOptions = [
  { value: "fr", label: "Français" },
  { value: "en", label: "English" },
  { value: "es", label: "Espagnol" },
  { value: "de", label: "Deutsch" },
  { value: "it", label: "Italiano" },
];

export function AiQuickActions({
  onGenerate,
  onCorrect,
  onTranslate,
  aiConfig,
  setAiConfig,
  loading,
  lastResult,
  error,
}: AiQuickActionsProps) {
  const [prompt, setPrompt] = useState("");
  const [selectedLang, setSelectedLang] = useState(aiConfig?.model === "fr" ? "fr" : "en");

  return (
    <div className="space-y-3 w-[320px]">
      <div className="flex items-center gap-2 mb-2">
        <Brain className="w-5 h-5 text-primary" />
        <span className="font-semibold text-base">Assistance IA</span>
      </div>
      <div className="space-y-2">
        <Input
          value={prompt}
          onChange={e => setPrompt(e.target.value)}
          placeholder="Prompt personnalisé (optionnel)"
          className="text-sm"
        />
        <div className="flex gap-2">
          <Button size="sm" variant="outline" className="flex-1" onClick={() => onGenerate(prompt)} disabled={loading}>
            <Wand2 className="w-4 h-4 mr-1" /> Générer
          </Button>
          <Button size="sm" variant="outline" className="flex-1" onClick={onCorrect} disabled={loading}>
            <RefreshCcw className="w-4 h-4 mr-1" /> Corriger
          </Button>
        </div>
        <div className="flex gap-2 items-center">
          <Select value={selectedLang} onValueChange={val => setSelectedLang(val)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Langue" />
            </SelectTrigger>
            <SelectContent>
              {languageOptions.map(opt => (
                <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button size="sm" variant="outline" onClick={() => onTranslate(selectedLang)} disabled={loading}>
            <Languages className="w-4 h-4 mr-1" /> Traduire
          </Button>
        </div>
      </div>
      <div className="mt-2 space-y-1 border-t pt-2">
        <div className="flex items-center gap-2">
          <span className="text-xs text-zinc-500">Provider</span>
          <Select value={aiConfig.provider} onValueChange={val => setAiConfig({ provider: val })}>
            <SelectTrigger className="w-[100px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="ollama">Ollama</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-xs text-zinc-500 ml-2">Modèle</span>
          <Input
            value={aiConfig.model}
            onChange={e => setAiConfig({ model: e.target.value })}
            className="w-[80px] text-xs"
            placeholder="gpt-3.5"
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-zinc-500">Température</span>
          <Input
            type="number"
            min={0}
            max={2}
            step={0.1}
            value={aiConfig.temperature}
            onChange={e => setAiConfig({ temperature: parseFloat(e.target.value) })}
            className="w-[60px] text-xs"
          />
        </div>
      </div>
      {loading && (
        <div className="flex items-center gap-2 text-primary mt-2">
          <Loader2 className="animate-spin w-4 h-4" />
          <span className="text-xs">Traitement IA…</span>
        </div>
      )}
      {lastResult && (
        <div className="bg-zinc-100 dark:bg-zinc-800 rounded p-2 mt-2 text-xs flex items-start gap-2">
          <CheckCircle2 className="w-4 h-4 text-green-500 mt-0.5" />
          <span>{lastResult}</span>
        </div>
      )}
      {error && (
        <div className="bg-red-100 dark:bg-red-900 rounded p-2 mt-2 text-xs flex items-start gap-2">
          <XCircle className="w-4 h-4 text-red-500 mt-0.5" />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
}
