"use client"

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getSupabaseClient } from "@/lib/supabase/client";
import { SongForm, SongFormValues, Album } from "@/components/songs/SongForm"; // Import the main SongForm
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Save, Loader2 as LoadingSpinner } from "lucide-react";

export default function CreateSongPage() {
  const router = useRouter();
  const { toast } = useToast();
  const supabase = getSupabaseClient(); // Get Supabase client instance

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [albums, setAlbums] = useState<Album[]>([]);
  const [isLoadingAlbums, setIsLoadingAlbums] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [currentUserName, setCurrentUserName] = useState<string>("");


  useEffect(() => {
    const fetchInitialData = async () => {
      setIsLoadingAlbums(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setCurrentUserId(user.id);
        // Fetch user's albums for the album selector in SongForm
        const { data: albumData, error: albumError } = await supabase
          .from("albums")
          .select("id, title")
          .eq("user_id", user.id)
          .order("title", { ascending: true });

        if (albumError) {
          toast({ title: "Erreur", description: "Impossible de charger vos albums.", variant: "destructive" });
        } else {
          setAlbums(albumData || []);
        }
        
        // Fetch user's profile to get artist_name
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('display_name, username')
          .eq('id', user.id)
          .single();
        
        if (profileError) {
            console.warn("Could not fetch profile for default artist name:", profileError.message);
        } else if (profileData) {
            setCurrentUserName(profileData.display_name || profileData.username || "");
        }

      } else {
        // Handle case where user is not authenticated, though route protection should prevent this
        toast({ title: "Erreur d'authentification", description: "Utilisateur non trouvé.", variant: "destructive" });
        router.push("/login"); // Or appropriate auth page
      }
      setIsLoadingAlbums(false);
    };
    fetchInitialData();
  }, [supabase, toast, router]);

  const handleFormSubmit = async (data: SongFormValues, status: 'draft' | 'published' = 'draft') => {
    if (!currentUserId) {
      toast({ title: "Erreur", description: "Utilisateur non identifié.", variant: "destructive" });
      return;
    }
    setIsSubmitting(true);

    // Ensure status is part of the data to be submitted
    const dataToSubmit = { ...data, status };
    
    // Convert comma-separated strings from form back to arrays for DB if necessary
    // For SongForm, featured_artists, writers, producers are handled as strings by the form
    // but the DB schema expects TEXT[]. This needs alignment.
    // For now, assuming DB schema for these is TEXT. If TEXT[], conversion is needed.
    // The SongForm schema already defines genres, moods, instrumentation, tags, lyrics_language as array(string).

    const songPayload = {
      user_id: currentUserId,
      title: dataToSubmit.title,
      artist_name: dataToSubmit.artist_name,
      featured_artists: dataToSubmit.featured_artists?.split(',').map(s => s.trim()).filter(Boolean) || [],
      genres: dataToSubmit.genres || [],
      moods: dataToSubmit.moods || [],
      instrumentation: dataToSubmit.instrumentation || [],
      key: dataToSubmit.key || null,
      bpm: dataToSubmit.bpm,
      time_signature: dataToSubmit.time_signature || null,
      duration: dataToSubmit.duration,
      capo: dataToSubmit.capo,
      tuning_frequency: dataToSubmit.tuning_frequency,
      description: dataToSubmit.description || null,
      audio_url: dataToSubmit.audio_url || null,
      cover_url: dataToSubmit.cover_url || null,
      album_id: dataToSubmit.album_id === "__NO_ALBUM__" ? null : dataToSubmit.album_id,
      composer_name: dataToSubmit.composer_name || null,
      writers: dataToSubmit.writers?.split(',').map(s => s.trim()).filter(Boolean) || [],
      producers: dataToSubmit.producers?.split(',').map(s => s.trim()).filter(Boolean) || [],
      release_date: dataToSubmit.release_date ? dataToSubmit.release_date.toISOString() : null,
      recording_date: dataToSubmit.recording_date ? dataToSubmit.recording_date.toISOString() : null,
      record_label: dataToSubmit.record_label || null,
      distributor: dataToSubmit.distributor || null,
      isrc: dataToSubmit.isrc || null,
      upc: dataToSubmit.upc || null,
      lyrics_language: dataToSubmit.lyrics_language || [],
      is_explicit: dataToSubmit.is_explicit || false,
      is_ai_generated: dataToSubmit.is_ai_generated || false,
      stems_available: dataToSubmit.stems_available || false,
      allow_downloads: dataToSubmit.allow_downloads || false,
      allow_comments: dataToSubmit.allow_comments === null || dataToSubmit.allow_comments === undefined ? true : dataToSubmit.allow_comments,
      lyrics: dataToSubmit.lyrics || null,
      bloc_note: dataToSubmit.bloc_note || null,
      right_column_notepad: dataToSubmit.right_column_notepad || null,
      status: dataToSubmit.status,
      progress_data: dataToSubmit.progress_data || {},
    };

    try {
      const { data: songData, error: songError } = await supabase
        .from("songs")
        .insert(songPayload)
        .select()
        .single();

      if (songError) throw songError;
      if (!songData) throw new Error("La création du morceau a échoué.");

      const songId = songData.id;

      // Handle tags
      if (dataToSubmit.tags && dataToSubmit.tags.length > 0) {
        for (const tagName of dataToSubmit.tags) {
          const { data: existingTag } = await supabase.from("tags").select("id").eq("name", tagName).single();
          let tagId = existingTag?.id;
          if (!existingTag) {
            const { data: newTag, error: tagError } = await supabase.from("tags").insert({ name: tagName }).select().single();
            if (tagError) { console.error("Erreur création tag:", tagError); continue; }
            tagId = newTag?.id;
          }
          if (tagId) {
            await supabase.from("resource_tags").insert({ tag_id: tagId, resource_type: "song", resource_id: songId });
          }
        }
      }

      // Handle activity log
      await supabase.from("activities").insert({
        user_id: currentUserId,
        type: "song_created", // Corrected column name
        target_type: "song",    // Corrected column name
        target_id: songId,      // Corrected column name
        content: `a créé un nouveau morceau: ${songPayload.title}`,
      });

      toast({ title: status === "published" ? "Morceau Publié" : "Brouillon Enregistré", description: "Votre morceau a été sauvegardé." });
      router.push(`/dashboard/songs/${songId}/edit`); // Redirect to edit page after creation

    } catch (error: any) {
      console.error("Erreur sauvegarde morceau:", error);
      toast({ title: "Erreur", description: error.message || "Une erreur est survenue.", variant: "destructive" });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const initialSongFormValues: Partial<SongFormValues> = {
    artist_name: currentUserName, // Pre-fill artist name
    // Set other sensible defaults for create mode if needed
    bpm: 120,
    tuning_frequency: 440,
    allow_comments: true,
    status: 'draft',
    progress_data: { /* initial progress structure */ }
  };


  if (isLoadingAlbums || !currentUserId) {
    return <div className="flex justify-center items-center h-screen"><LoadingSpinner className="h-8 w-8 animate-spin" /></div>;
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8 h-full flex flex-col">
      <SongForm
        mode="create"
        onFormSubmit={handleFormSubmit}
        isSubmitting={isSubmitting}
        albums={albums}
        isLoadingAlbums={isLoadingAlbums}
        supabaseClient={supabase}
        initialValues={initialSongFormValues}
      />
    </div>
  );
}
