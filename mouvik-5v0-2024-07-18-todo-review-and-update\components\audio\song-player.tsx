"use client"

import { useState, useRef, useEffect } from "react"
import { Play, Pause } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { usePlaySong } from "@/hooks/use-play-song"
import { useAudio } from "@/contexts/audio-context"
import type { Song } from "@/types"

interface SongPlayerProps {
  song: Song & { artist?: string }
  waveformUrl?: string
  onPlay?: () => void
  className?: string
  showCover?: boolean
}

export function SongPlayer({ song, waveformUrl, onPlay, className, showCover = false }: SongPlayerProps) {
  const { currentSong, isPlaying } = useAudio()
  const { play } = usePlaySong()
  const [localIsPlaying, setLocalIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(0.8)
  const [isMuted, setIsMuted] = useState(false)
  const audioRef = useRef<HTMLAudioElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>(0)

  // Check if this song is the current playing song
  const isCurrentSong = currentSong?.id === song.id

  useEffect(() => {
    if (isCurrentSong) {
      setLocalIsPlaying(isPlaying)
    } else {
      setLocalIsPlaying(false)
    }
  }, [isCurrentSong, isPlaying])

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume
    }
  }, [volume])

  useEffect(() => {
    if (audioRef.current) {
      const handleLoadedMetadata = () => {
        setDuration(audioRef.current?.duration || 0)
      }

      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata)

      return () => {
        audioRef.current?.removeEventListener("loadedmetadata", handleLoadedMetadata)
      }
    }
  }, [song.audio_url])

  const togglePlayPause = () => {
    if (isCurrentSong) {
      // If this is the current song, toggle global player
      if (isPlaying) {
        // Global context will handle pausing
      } else {
        // Global context will handle resuming
      }
    } else {
      // If this is not the current song, start playing it
      play(song)
      if (onPlay) onPlay()
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  // This component now acts as a controller for the global player
  // It doesn't actually play audio itself anymore
  return (
    <div className={`border rounded-lg p-4 bg-card ${className}`}>
      <div className="flex items-center gap-4">
        {showCover && song.cover_url && (
          <div className="w-12 h-12 rounded overflow-hidden">
            <img src={song.cover_url || "/placeholder.svg"} alt={song.title} className="w-full h-full object-cover" />
          </div>
        )}
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={togglePlayPause} className="h-10 w-10">
            {isCurrentSong && isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
          </Button>
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-1">
            <div className="text-sm font-medium">{song.title}</div>
            <div className="text-xs text-muted-foreground">
              {formatTime(isCurrentSong ? currentTime : 0)} / {formatTime(song.duration || 0)}
            </div>
          </div>
          <div className="relative">
            <Slider
              value={[isCurrentSong ? currentTime : 0]}
              max={song.duration || 100}
              step={0.1}
              className="cursor-pointer"
              disabled={!isCurrentSong}
            />
            {waveformUrl && (
              <div className="absolute inset-0 pointer-events-none">
                <img
                  src={waveformUrl || "/placeholder.svg"}
                  alt="Waveform"
                  className="w-full h-full object-cover opacity-30"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
