# MOUVIK - Comprehensive Music Platform

## Project Overview

MOUVIK is a comprehensive music platform designed for artists and listeners. It enables music creation, sharing, discovery (including external sources), and monetization, featuring AI assistance and community interaction.

## Core Features

- **Music Creation & AI**: Integrated studio & AI composition tools (Details TBD).
- **Discover & Playlists**: Multi-platform music hub (import URLs, manage resources, advanced tagging, mixed playlists).
- **Community**: Social features (activity feed, profiles, follows, posts, comments, likes, groups).
- **Monetization & Analytics**: Premium features and performance stats (Details TBD).

## Project Structure (Next.js App Router)

```plaintext
/app                    # Application routes and pages
  /(app)/[user]/...    # Main authenticated user sections (Dashboard, Profile, Settings)
  /(public)             # Publicly accessible pages (Homepage, Explore)
  /api                  # API Routes (e.g., /api/discover, /api/community)
  /auth                 # Authentication pages
/components             # Reusable UI components (atomic design)
  /audio                # Audio-related components
  /community            # Community feature components
  /discover             # Discovery feature components
  /ui                   # General UI elements (buttons, inputs, etc.)
/lib                    # Shared utilities, hooks, Supabase client
/prisma                 # Prisma schema and migrations (If using Prisma)
/public                 # Static assets (images, fonts)
/styles                 # Global styles
.env.local              # Local environment variables (DO NOT COMMIT)
```
*Note: This structure is indicative and may evolve.* Refer to [ARCHITECTURE.md](./ARCHITECTURE.md) for detailed component placement.

## Key Workflows

- **Multi-Platform Import**: URL input -> Platform detection -> Metadata extraction -> UI Preview -> User validation -> Database insertion (`music_resources`, `tags`).
- **Playlist Management**: Create/edit playlists with mixed internal/external tracks.
- **Community Feed**: Post creation, comments, likes, hashtag detection, filtered feed.

## Documentation & Guidelines

- **AI Agent Briefing**: [PROMPT.md](./PROMPT.md) - **Start here for AI-driven development.**
- **Architecture**: [ARCHITECTURE.md](./ARCHITECTURE.md) - Technical design, data flows, API structure.
- **Database**: [DATABASE_STRUCTURE.md](./DATABASE_STRUCTURE.md) - Detailed schema (Note: Currently requires updates for Discover/Community modules).
- **Development Guidelines**: [GUIDELINES.md](./GUIDELINES.md) - Coding standards, security, UI/UX practices.
- **Tasks & Roadmap**: [TASKS.md](./TASKS.md) & [ROADMAP.md](./ROADMAP.md) - Development planning.

## Tech Stack

- **Framework**: Next.js 14 (App Router)
- **UI**: React, Tailwind CSS
- **Backend Logic**: Next.js API Routes, Server Actions
- **Database/Backend Services**: Supabase (PostgreSQL, Auth, Storage)
- **Deployment**: Vercel

## Environment Setup

Refer to [GUIDELINES.md](./GUIDELINES.md) for comprehensive setup details.

### Prerequisites

- Node.js 18+
- npm/yarn/pnpm
- Supabase Account

### Environment Variables

Create a `.env.local` file in the project root:

```bash
# Public Supabase Keys (Safe for client-side)
NEXT_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY

# Secret Supabase Key (Server-side ONLY - NEVER expose in browser)
SUPABASE_SERVICE_ROLE_KEY=YOUR_SUPABASE_SERVICE_ROLE_KEY

# Site URL (For NextAuth, redirects, etc.)
NEXT_PUBLIC_SITE_URL=http://localhost:3000 # Or your deployment URL

# Add other necessary variables (OAuth providers, external API keys, etc.)
# GITHUB_CLIENT_ID=...
# GITHUB_CLIENT_SECRET=...
```

**IMPORTANT**: The `SUPABASE_SERVICE_ROLE_KEY` grants full access to your database, bypassing Row Level Security. Use it **only** in server-side code (API Routes, Server Actions) where necessary. Store it securely.

### Installation & Running

1.  Clone the repository.
2.  Install dependencies: `npm install` (or yarn/pnpm equivalent).
3.  Set up your `.env.local` file with Supabase credentials.
4.  Initialize/Migrate database (if applicable, e.g., `npx prisma migrate dev` or Supabase SQL scripts).
5.  Run development server: `npm run dev`.
6.  Open [http://localhost:3000](http://localhost:3000) in your browser.

## Contributing

Follow the Git workflow and coding standards outlined in [GUIDELINES.md](./GUIDELINES.md).

1.  Create a feature branch.
2.  Implement changes and add tests.
3.  Ensure code follows guidelines.
4.  Submit a Pull Request for review.

## License

All rights reserved MOUVIK 2025
