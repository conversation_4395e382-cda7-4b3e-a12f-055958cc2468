"use client";

import React from "react";
import dynamic from "next/dynamic";
import "react-quill/dist/quill.snow.css";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

interface RichLyricsEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  quillRef?: React.RefObject<any>;
  onSelectionChange?: (range: any, source: any, editor: any) => void;
  className?: string;
}

export function RichLyricsEditor({ value, onChange, placeholder, label, quillRef, onSelectionChange, className }: RichLyricsEditorProps) {
  return (
    <div className={`space-y-2 w-full ${className || ''}`}>
      {label && <label className="block font-semibold mb-1">{label}</label>}
      <ReactQuill
        // ref removed, use innerRef or forwardRef if needed
        theme="snow"
        value={value}
        onChange={onChange}
        onChangeSelection={onSelectionChange}
        placeholder={placeholder || "<PERSON><PERSON><PERSON> ou colle tes paroles ici..."}
        modules={{
          toolbar: [
            [{ header: [1, 2, 3, false] }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            ["bold", "italic", "underline"],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'align': [] }],
            [{ list: "ordered" }, { list: "bullet" }],
            ["blockquote"],
            ["link"],
            ["clean"],
          ],
        }}
        className="bg-white dark:bg-zinc-900 rounded shadow min-h-[300px]"
      />
    </div>
  );
}
