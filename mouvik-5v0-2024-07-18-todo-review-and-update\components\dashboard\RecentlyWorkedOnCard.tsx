"use client"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Clock, Music, Library, Pencil, Calendar } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { Badge } from "@/components/ui/badge"

interface Song {
  id: string
  title: string
  cover_art_url: string | null
  updated_at: string
  created_at: string
  genres: string[] | null
  moods: string[] | null
  duration_ms: number | null
  plays_count: number | null
  bpm: number | null
  musical_key: string | null
  visibility: string | null
}

interface Album {
  id: string
  title: string
  image_url: string | null
  updated_at: string
  created_at: string
  status: string
}

interface RecentlyWorkedOnCardProps {
  recentSongs: Song[]
  recentAlbums: Album[]
}

export function RecentlyWorkedOnCard({ recentSongs, recentAlbums }: RecentlyWorkedOnCardProps) {
  const [activeTab, setActiveTab] = useState<"songs" | "albums">("songs")

  // Fonction pour formater la date en "il y a X jours/heures"
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: fr
      })
    } catch (error) {
      return "Date inconnue"
    }
  }

  // Local duration formatter (ideally move to a shared utils file)
  const formatDurationFromMsLocal = (duration: number | null | undefined): string => {
    if (duration === null || duration === undefined || duration === 0) {
      return '0:00';
    }
    const totalSeconds = Math.floor(duration / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  // Fonction pour déterminer si un élément a été créé ou mis à jour récemment
  const getActionType = (item: Song | Album) => {
    const createdAt = new Date(item.created_at)
    const updatedAt = new Date(item.updated_at)

    // Si la date de création et de mise à jour sont proches (moins de 5 minutes d'écart)
    // on considère que c'est une création
    const isCreation = Math.abs(updatedAt.getTime() - createdAt.getTime()) < 5 * 60 * 1000

    return isCreation ? "Créé" : "Modifié"
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-md font-medium">Projets récents</CardTitle>
          <CardDescription>Vos dernières créations et modifications</CardDescription>
        </div>
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as "songs" | "albums")}
          className="w-full"
        >
          <div className="flex justify-between items-center">
            <div></div>
            <TabsList className="grid w-[180px] grid-cols-2">
              <TabsTrigger value="songs" className="text-xs">
                <Music className="h-3 w-3 mr-1" />
                Morceaux
              </TabsTrigger>
              <TabsTrigger value="albums" className="text-xs">
                <Library className="h-3 w-3 mr-1" />
                Albums
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="songs" className="mt-2">
            {recentSongs && recentSongs.length > 0 ? (
              <div className="space-y-4">
                {recentSongs.map((song) => (
                  <div key={song.id} className="flex items-center space-x-4">
                    <div className="h-10 w-10 rounded-md overflow-hidden bg-muted flex-shrink-0">
                      {song.cover_art_url ? (
                        <img
                          src={song.cover_art_url}
                          alt={song.title}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center bg-secondary">
                          <Music className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <p className="text-sm font-medium truncate mr-2">{song.title}</p>
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <span className="flex items-center">
                          {getActionType(song) === "Créé" ? (
                            <Calendar className="h-3 w-3 mr-1" />
                          ) : (
                            <Pencil className="h-3 w-3 mr-1" />
                          )}
                          {getActionType(song)}
                        </span>
                        <span className="mx-1">•</span>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDate(song.updated_at)}
                        </span>
                      </div>
                      <div className="mt-1 text-xs text-muted-foreground space-y-1">
                        <div><span className="font-medium">Genre(s):</span> {song.genres && song.genres.length > 0 ? song.genres.join(', ') : "N/A"}</div>
                        <div><span className="font-medium">Durée:</span> {formatDurationFromMsLocal(song.duration_ms)}</div>
                        <div><span className="font-medium">Écoutes:</span> {song.plays_count || 0}</div>
                        <div><span className="font-medium">BPM:</span> {song.bpm || "N/A"}</div>
                        <div><span className="font-medium">Clé:</span> {song.musical_key || "N/A"}</div>
                        {song.moods && song.moods.length > 0 && (
                          <div className="flex flex-wrap gap-1 pt-1">
                            <span className="font-medium">Moods:</span>
                            {song.moods.map((mood, index) => (
                              <Badge key={index} variant="secondary" className="text-xs px-1.5 py-0.5">{mood}</Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                    <Button variant="outline" size="sm" asChild className="flex-shrink-0">
                      <Link href={`/songs/${song.id}/edit`}>
                        Éditer
                      </Link>
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                <p>Aucun morceau récent</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="albums" className="mt-2">
            {recentAlbums && recentAlbums.length > 0 ? (
              <div className="space-y-4">
                {recentAlbums.map((album) => (
                  <div key={album.id} className="flex items-center space-x-4">
                    <div className="h-10 w-10 rounded-md overflow-hidden bg-muted flex-shrink-0">
                      {album.image_url ? (
                        <img
                          src={album.image_url}
                          alt={album.title}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center bg-secondary">
                          <Library className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <p className="text-sm font-medium truncate mr-2">{album.title}</p>
                        <span className={`text-xs px-1.5 py-0.5 rounded-full ${album.status === 'published' ? 'bg-green-500/20 text-green-500' : 'bg-amber-500/20 text-amber-500'}`}>
                          {album.status === 'published' ? 'Publié' : 'Brouillon'}
                        </span>
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <span className="flex items-center">
                          {getActionType(album) === "Créé" ? (
                            <Calendar className="h-3 w-3 mr-1" />
                          ) : (
                            <Pencil className="h-3 w-3 mr-1" />
                          )}
                          {getActionType(album)}
                        </span>
                        <span className="mx-1">•</span>
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDate(album.updated_at)}
                        </span>
                      </div>
                    </div>
                    <Button variant="outline" size="sm" asChild className="flex-shrink-0">
                      <Link href={`/albums/${album.id}/edit`}>
                        Éditer
                      </Link>
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                <p>Aucun album récent</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardHeader>
      <CardContent>
      </CardContent>
    </Card>
  )
}
