-- Création de la table des likes si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('song', 'album', 'playlist')),
  resource_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, resource_type, resource_id)
);

-- Index pour les recherches rapides de likes
CREATE INDEX IF NOT EXISTS likes_resource_idx ON likes(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS likes_user_idx ON likes(user_id);

-- Création de la table des follows si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS follows (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  following_id UUID NOT NULL,
  following_type VARCHAR(20) NOT NULL CHECK (following_type IN ('user', 'band')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(follower_id, following_id, following_type)
);

-- Index pour les recherches rapides de follows
CREATE INDEX IF NOT EXISTS follows_following_idx ON follows(following_type, following_id);
CREATE INDEX IF NOT EXISTS follows_follower_idx ON follows(follower_id);

-- Création de la table des vues si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS views (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('song', 'album', 'profile', 'band')),
  resource_id UUID NOT NULL,
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les recherches rapides de vues
CREATE INDEX IF NOT EXISTS views_resource_idx ON views(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS views_user_idx ON views(user_id);

-- Création de la table des commentaires de groupe si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS band_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  band_id UUID NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  parent_id UUID REFERENCES band_comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les recherches rapides de commentaires de groupe
CREATE INDEX IF NOT EXISTS band_comments_band_idx ON band_comments(band_id);
CREATE INDEX IF NOT EXISTS band_comments_user_idx ON band_comments(user_id);
CREATE INDEX IF NOT EXISTS band_comments_parent_idx ON band_comments(parent_id);

-- Création de la table des conversations si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Création de la table des participants aux conversations si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS conversation_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(conversation_id, user_id)
);

-- Index pour les recherches rapides de participants aux conversations
CREATE INDEX IF NOT EXISTS conversation_participants_conversation_idx ON conversation_participants(conversation_id);
CREATE INDEX IF NOT EXISTS conversation_participants_user_idx ON conversation_participants(user_id);

-- Création de la table des messages si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  read_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les recherches rapides de messages
CREATE INDEX IF NOT EXISTS messages_conversation_idx ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS messages_sender_idx ON messages(sender_id);
