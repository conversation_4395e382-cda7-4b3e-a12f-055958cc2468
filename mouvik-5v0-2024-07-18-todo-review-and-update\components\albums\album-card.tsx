"use client";

import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit3, Trash2, PlayCircle, Disc, UserCircle, Copy, Eye, ThumbsUp, Headphones, Loader2 } from 'lucide-react'; // Added Loader2
import type { Album, UserProfile } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useState } from 'react'; // Added useState
import { useUser } from '@/contexts/user-context'; // Added useUser
import { getSupabaseClient } from '@/lib/supabase/client'; // Added Supabase client
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils'; // Added cn

// Extend Album type for card display if it includes counts or other specific fields not in base type
interface AlbumForCard extends Album {
  songs_count?: number;
  // Add other stats if fetched, e.g.:
  // view_count?: number;
  // like_count?: number;
  // plays?: number; 
  // is_public?: boolean; // Assuming albums have a public status
  // slug?: string | null; // For public links
}

interface AlbumCardProps {
  album: AlbumForCard;
  onDelete?: (albumId: string) => void;
  onUpdateStatus?: (albumId: string, newStatus: { is_public: boolean; slug: string | null }) => void;
}

export function AlbumCard({ album, onDelete, onUpdateStatus }: AlbumCardProps) {
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);

  // Determine public link logic for albums
  const viewPageUrl = album.is_public && album.slug 
    ? `/album/${album.slug}` // Public page uses singular 'album' and slug
    : `/albums/${album.id}`; // Authenticated page uses plural 'albums' and ID

  const handleDelete = () => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'album "${album.title}" ?`)) {
      if (onDelete) {
        onDelete(album.id);
      } else {
        toast({ title: "Suppression (Placeholder)", description: `Album ${album.id} serait supprimé.` });
      }
    }
  };

  const handlePlay = () => {
    toast({ title: "Lecture (Placeholder)", description: `Lancer la lecture de l'album "${album.title}".` });
  };

  const handleDuplicate = () => {
    toast({ title: "Duplication (Placeholder)", description: `L'album "${album.title}" serait dupliqué.` });
  };

  const artistProfile = album.profiles as UserProfile | null;

  const togglePublicStatus = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!user || !album.user_id || user.id !== album.user_id || isTogglingStatus) { // Ensure owner
      if (user?.id !== album.user_id) {
        toast({ title: "Action non autorisée", description: "Seul le créateur peut changer la visibilité.", variant: "destructive" });
      }
      return;
    }

    setIsTogglingStatus(true);
    try {
      const { data, error } = await supabase.rpc('toggle_album_public_status', {
        p_album_id: album.id,
        p_user_id: user.id,
      });

      if (error) throw error;

      if (data && data.length > 0) {
        const { new_is_public, new_slug } = data[0]; // Expect new_slug as well
        toast({
          title: "Statut de l'album mis à jour",
          description: `L'album "${album.title}" est maintenant ${new_is_public ? 'public' : 'privé'}.`,
        });
        if (onUpdateStatus) {
          onUpdateStatus(album.id, { is_public: new_is_public, slug: new_slug });
        }
      } else {
        throw new Error("Réponse invalide de la fonction RPC.");
      }
    } catch (err: any) {
      console.error("Error toggling album status:", err);
      toast({
        title: "Erreur",
        description: err.message || "Impossible de changer le statut de l'album.",
        variant: "destructive",
      });
    } finally {
      setIsTogglingStatus(false);
    }
  };

  return (
    <Card className="overflow-hidden flex flex-col h-full group/card">
      <CardHeader className="p-0 relative">
        {/* LED Indicator for Public/Private Status */}
        {album.is_public !== undefined && ( // Only show if is_public is defined
            <div 
                title={album.is_public ? "Album public (cliquer pour changer)" : "Album privé (cliquer pour changer)"}
                onClick={togglePublicStatus}
                className={cn(
                    "absolute top-2 left-2 z-10 w-4 h-4 rounded-full cursor-pointer flex items-center justify-center transition-all",
                    "ring-2 ring-offset-2 ring-offset-card", // Base ring for halo effect
                    isTogglingStatus 
                      ? "bg-yellow-500 ring-yellow-500/50 animate-pulse" // Yellow for loading, with halo
                      : album.is_public 
                        ? "bg-green-500 ring-green-500/50 hover:bg-green-400" // Green filled, green halo
                        : "bg-red-500 ring-red-500/50 hover:bg-red-400" // Red filled, red halo
                )}
            >
                {isTogglingStatus && <Loader2 className="h-2.5 w-2.5 animate-spin text-white" />}
            </div>
        )}
        <Link href={viewPageUrl} className="block aspect-square relative group">
          {album.cover_url ? (
            <Image
              src={album.cover_url}
              alt={album.title}
              width={300}
              height={300}
              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <Disc className="w-16 h-16 text-muted-foreground" />
            </div>
          )}
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
            <PlayCircle className="w-12 h-12 text-white" />
          </div>
        </Link>
      </CardHeader>
      <CardContent className="p-4 flex-grow">
        <Link href={viewPageUrl}>
          <CardTitle className="text-lg font-semibold hover:underline mb-1 truncate" title={album.title}>
            {album.title}
          </CardTitle>
        </Link>
        <div className="text-xs text-muted-foreground mb-2">
          {artistProfile ? (
            <Link href={`/artists/${artistProfile.username}`} className="hover:underline flex items-center gap-1">
              {artistProfile.avatar_url ? (
                <Image src={artistProfile.avatar_url} alt={artistProfile.display_name || artistProfile.username || 'avatar'} width={16} height={16} className="rounded-full" />
              ) : (
                <UserCircle className="w-4 h-4" />
              )}
              {artistProfile.display_name || artistProfile.username}
            </Link>
          ) : (
            <span>{album.artist_name || "Artiste inconnu"}</span>
          )}
          {album.songs_count !== undefined && (
            <>
              <span className="mx-1">•</span>
              <span>{album.songs_count} morceau{album.songs_count === 1 ? '' : 'x'}</span>
            </>
          )}
        </div>
        {album.release_date && (
            <p className="text-xs text-muted-foreground">Sorti le {format(new Date(album.release_date), 'd MMM yyyy', { locale: fr })}</p>
        )}

        {/* Placeholder for stats - TODO: Add these to AlbumForCard and fetch them */}
        {/* <div className="mt-2 flex items-center gap-x-3 gap-y-1 flex-wrap text-xs text-muted-foreground">
          {album.view_count !== undefined && album.view_count > 0 && (
            <span className="flex items-center gap-1" title="Vues"><Eye className="w-3 h-3" /> {album.view_count}</span>
          )}
          {album.like_count !== undefined && album.like_count > 0 && (
            <span className="flex items-center gap-1" title="Likes"><ThumbsUp className="w-3 h-3" /> {album.like_count}</span>
          )}
        </div> */}
      </CardContent>
      <CardFooter className="p-4 pt-2 flex justify-between items-center">
        <Button onClick={handlePlay} size="sm" variant="outline" className="flex-grow mr-2">
          <PlayCircle className="mr-2 h-4 w-4" /> Écouter
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Options</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => router.push(`/albums/${album.id}/edit`)}>
              <Edit3 className="mr-2 h-4 w-4" /> Modifier
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}>
              <Copy className="mr-2 h-4 w-4" /> Dupliquer
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10">
              <Trash2 className="mr-2 h-4 w-4" /> Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );
}
