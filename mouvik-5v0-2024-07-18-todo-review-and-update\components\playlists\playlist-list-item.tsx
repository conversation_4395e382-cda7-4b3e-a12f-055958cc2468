"use client";

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit3, Trash2, PlayCircle, ListMusic, UserCircle, Eye, ThumbsUp, Users, Headphones, Copy, GripVertical, Globe, Lock, Loader2 } from 'lucide-react'; // Added Loader2
import type { Playlist } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useState } from 'react'; // Added useState
import { useUser } from '@/contexts/user-context'; // Added useUser
import { getSupabaseClient } from '@/lib/supabase/client'; // Added Supabase client
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils'; 

interface PlaylistListItemProps {
  playlist: Playlist;
  onDelete: (playlistId: string) => void; 
  onUpdateStatus?: (playlistId: string, newStatus: { is_public: boolean; slug: string | null }) => void;
  density?: number; // 0: compact, 1: default, 2: spacious
}

export function PlaylistListItem({ playlist, onDelete, onUpdateStatus, density = 1 }: PlaylistListItemProps) {
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);
  
  const viewPageUrl = playlist.is_public && playlist.slug ? `/playlist/${playlist.slug}` : `/playlists/${playlist.id}`;

  const handleDelete = () => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la playlist "${playlist.name}" ?`)) {
      onDelete(playlist.id);
    }
  };

  const handlePlay = () => {
    toast({ title: "Lecture (Placeholder)", description: `Lancer la lecture de la playlist "${playlist.name}".` });
  };

  const handleDuplicate = () => {
    toast({ title: "Duplication (Placeholder)", description: `La playlist "${playlist.name}" serait dupliquée.` });
  };
  
  const togglePublicStatus = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!user || isTogglingStatus) return;

    setIsTogglingStatus(true);
    try {
      const { data, error } = await supabase.rpc('toggle_playlist_public_status', {
        p_playlist_id: playlist.id,
        p_user_id: user.id,
      });

      if (error) throw error;

      if (data && data.length > 0) {
        const { new_is_public, new_slug } = data[0];
        toast({
          title: "Statut de la playlist mis à jour",
          description: `La playlist "${playlist.name}" est maintenant ${new_is_public ? 'publique' : 'privée'}.`,
        });
        if (onUpdateStatus) {
          onUpdateStatus(playlist.id, { is_public: new_is_public, slug: new_slug });
        }
      } else {
        throw new Error("Réponse invalide de la fonction RPC.");
      }
    } catch (err: any) {
      console.error("Error toggling playlist status:", err);
      toast({
        title: "Erreur",
        description: err.message || "Impossible de changer le statut de la playlist.",
        variant: "destructive",
      });
    } finally {
      setIsTogglingStatus(false);
    }
  };
  
  const paddingClasses = {
    0: 'p-2', // compact
    1: 'p-3', // default
    2: 'p-4', // spacious
  };

  return (
    <div className={cn("flex items-center gap-4 hover:bg-muted/50 rounded-md border", paddingClasses[density as keyof typeof paddingClasses] || 'p-3')}>
      
      <div className="flex-shrink-0 relative">
        <div 
          title={playlist.is_public ? "Playlist publique (cliquer pour changer)" : "Playlist privée (cliquer pour changer)"}
          onClick={togglePublicStatus}
          className={cn(
            "absolute top-0.5 left-0.5 z-10 w-3.5 h-3.5 rounded-full border-2 border-white shadow-lg cursor-pointer flex items-center justify-center", // Enhanced visibility
            playlist.is_public ? "bg-green-500 hover:bg-green-600" : "bg-red-500 hover:bg-red-600"
          )}
        >
          {isTogglingStatus && <Loader2 className="h-2 w-2 animate-spin text-white" />}
        </div>
        <Link href={viewPageUrl}>
          {playlist.cover_url ? (
            <Image
              src={playlist.cover_url}
            alt={playlist.name}
            width={48} // Smaller for list view
            height={48}
            className="object-cover w-12 h-12 rounded-md"
          />
        ) : (
          <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
            <ListMusic className="w-6 h-6 text-muted-foreground" />
          </div>
        )}
        </Link>
      </div>

      <div className="flex-1 min-w-0"> 
        <Link href={viewPageUrl} className="hover:underline">
          <p className="text-sm font-medium truncate" title={playlist.name}>{playlist.name}</p>
        </Link>
        <div className="text-xs text-muted-foreground flex items-center gap-1.5 truncate">
          {playlist.profiles ? (
            <Link href={`/artists/${playlist.profiles.username}`} className="hover:underline">
              {playlist.profiles.display_name || playlist.profiles.username}
            </Link>
          ) : (
            <span>Créateur inconnu</span>
          )}
          <span>•</span>
          <span>{playlist.songs_count ?? 0} morceau{playlist.songs_count === 1 || playlist.songs_count === 0 ? '' : 's'}</span>
          {/* Public/Private Badge removed from here, now an LED on cover */}
        </div>
        {/* Display Genres if available */}
        {playlist.genres && playlist.genres.length > 0 && (
          <div className="mt-1 flex flex-wrap gap-1 max-h-5 overflow-hidden">
            {playlist.genres.slice(0, 3).map(genre => ( // Show up to 3 genres
              <Badge key={genre} variant="secondary" className="text-xs px-1 py-0">{genre}</Badge>
            ))}
            {playlist.genres.length > 3 && <span className="text-xs text-muted-foreground self-center">...</span>}
          </div>
        )}
      </div>

      {/* Optional: Display a few key stats, could be icons only for compactness */}
      <div className="hidden md:flex items-center gap-3 text-xs text-muted-foreground flex-shrink-0">
        {playlist.plays !== undefined && playlist.plays > 0 && (
          <span className="flex items-center gap-1" title="Écoutes"><Headphones className="w-3 h-3" /> {playlist.plays}</span>
        )}
        {playlist.follower_count !== undefined && playlist.follower_count > 0 && (
          <span className="flex items-center gap-1" title="Followers"><Users className="w-3 h-3" /> {playlist.follower_count}</span>
        )}
        <span title={`Créée ${new Date(playlist.created_at).toLocaleDateString()}`}>
          {formatDistanceToNow(new Date(playlist.created_at), { locale: fr, addSuffix: true })}
        </span>
      </div>
      
      <div className="flex items-center gap-1 flex-shrink-0">
        <Button onClick={handlePlay} size="sm" variant="ghost" className="h-8 px-2">
            <PlayCircle className="mr-1.5 h-4 w-4" /> Écouter
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Options</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => router.push(`/playlists/${playlist.id}/edit`)}>
              <Edit3 className="mr-2 h-4 w-4" /> Modifier
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleDuplicate}>
              <Copy className="mr-2 h-4 w-4" /> Dupliquer
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleDelete} className="text-destructive focus:text-destructive focus:bg-destructive/10">
              <Trash2 className="mr-2 h-4 w-4" /> Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
