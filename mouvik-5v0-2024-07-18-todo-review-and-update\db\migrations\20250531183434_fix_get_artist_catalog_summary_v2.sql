DROP FUNCTION IF EXISTS get_artist_catalog_summary_v2(uuid);

CREATE OR REPLACE FUNCTION get_artist_catalog_summary_v2(p_artist_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT jsonb_build_object(
    'total_songs', COALESCE(COUNT(DISTINCT s.id), 0),
    'total_albums', COALESCE(COUNT(DISTINCT alb.id), 0),
    'total_duration_ms', COALESCE(SUM(s.duration_ms), 0),
    'total_plays', COALESCE(SUM(s.plays), 0),
    'total_likes', COALESCE(SUM(s.like_count), 0) -- Assumant que like_count est sur la table songs
  )
  INTO result
  FROM profiles p
  LEFT JOIN songs s ON s.creator_user_id = p.id  -- Jointure correcte pour songs
  LEFT JOIN albums alb ON alb.user_id = p.id     -- Jointure correcte pour albums
  WHERE p.id = p_artist_id;

  RETURN result;
END;
$$;
