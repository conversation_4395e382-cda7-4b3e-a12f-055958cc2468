-- Fonction pour obtenir les chansons les plus populaires d'un utilisateur pour une période donnée
CREATE OR REPLACE FUNCTION get_top_songs_by_user(p_user_id UUID, p_time_range TEXT, p_limit INT DEFAULT 5)
RETURNS TABLE (
  id UUID,
  title TEXT,
  artist_name TEXT, -- Peut être le display_name de l'utilisateur
  cover_url TEXT,
  plays BIGINT,
  likes BIGINT -- Ajouté pour plus de détails
)
LANGUAGE plpgsql
AS $$
DECLARE
  v_start_date TIMESTAMP;
  v_end_date TIMESTAMP;
BEGIN
  v_end_date := NOW();
  IF p_time_range = '7d' THEN
    v_start_date := v_end_date - INTERVAL '7 days';
  ELSIF p_time_range = '30d' THEN
    v_start_date := v_end_date - INTERVAL '30 days';
  ELSIF p_time_range = '90d' THEN
    v_start_date := v_end_date - INTERVAL '90 days';
  ELSIF p_time_range = '1y' THEN
    v_start_date := v_end_date - INTERVAL '1 year';
  ELSE
    v_start_date := '1970-01-01'::TIMESTAMP;
  END IF;

  RETURN QUERY
  SELECT
    s.id,
    s.title::TEXT,
    COALESCE(s.artist, pr.display_name, pr.username)::TEXT AS artist,
    s.cover_art_url::TEXT,
    COALESCE(SUM(p.count_plays), 0)::BIGINT AS plays,
    COALESCE(SUM(l.count_likes), 0)::BIGINT AS likes
  FROM
    songs s
  JOIN
    profiles pr ON s.creator_user_id = pr.id
  LEFT JOIN (
    SELECT song_id, COUNT(*) AS count_plays
    FROM plays
    WHERE created_at BETWEEN v_start_date AND v_end_date
    GROUP BY song_id
  ) p ON s.id = p.song_id
  LEFT JOIN (
    SELECT resource_id, COUNT(*) AS count_likes
    FROM likes
    WHERE resource_type = 'song' AND created_at BETWEEN v_start_date AND v_end_date
    GROUP BY resource_id
  ) l ON s.id = l.resource_id
  WHERE
    s.creator_user_id = p_user_id
    AND s.visibility = 'public' -- Uniquement les chansons publiées
  GROUP BY
    s.id, pr.display_name, pr.username
  ORDER BY
    plays DESC, likes DESC, s.created_at DESC
  LIMIT p_limit;

END;
$$;

COMMENT ON FUNCTION get_top_songs_by_user(UUID, TEXT, INT) IS 'Récupère les chansons les plus populaires (basé sur les écoutes et likes) d''un utilisateur pour une période donnée.';

-- Exemple d'appel :
-- SELECT * FROM get_top_songs_by_user('VOTRE_USER_ID_ICI', '30d', 5);
