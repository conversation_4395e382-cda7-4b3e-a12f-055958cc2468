// components/songs/SongFormCoverArtCard.tsx
import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ImagePlus, Trash2, UploadCloud } from 'lucide-react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import type { LocalFileState } from './hooks/useLocalFileManagement'; // Assuming this path

interface SongFormCoverArtCardProps {
  localCoverArtFile: LocalFileState;
  onCoverArtSelect: (event: React.ChangeEvent<HTMLInputElement>) => void; // Changed from onSelect to onCoverArtSelect for clarity
  onClearCoverArt: () => void;
  coverArtInputRef: React.RefObject<HTMLInputElement>;
  // Add any other props needed from SongForm, e.g., form control for specific fields
}

export const SongFormCoverArtCard: React.FC<SongFormCoverArtCardProps> = ({
  localCoverArtFile,
  onCoverArtSelect,
  onClearCoverArt,
  coverArtInputRef,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Pochette</CardTitle>
        <CardDescription>Ajoutez ou modifiez la pochette de votre morceau.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div 
          className="aspect-square w-full rounded-md overflow-hidden border border-dashed flex items-center justify-center relative bg-muted/20 hover:bg-muted/30 transition-colors cursor-pointer"
          onClick={() => coverArtInputRef.current?.click()} // Make the whole area clickable
        >
          {localCoverArtFile.previewUrl ? (
            <Image
              src={localCoverArtFile.previewUrl}
              alt={localCoverArtFile.file?.name || 'Aperçu de la pochette'}
              layout="fill"
              objectFit="cover"
            />
          ) : (
            <div className="text-center text-muted-foreground p-4">
              <ImagePlus size={48} className="mx-auto mb-2" />
              <p className="text-sm">Glissez-déposez une image ici, ou cliquez pour sélectionner.</p>
            </div>
          )}
        </div>

        <Input
          ref={coverArtInputRef}
          type="file"
          accept="image/png, image/jpeg, image/webp, image/gif"
          onChange={onCoverArtSelect} // Corrected prop name
          className="hidden"
          id="cover-art-input"
        />

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => coverArtInputRef.current?.click()}
            className="w-full"
            type="button"
            disabled={localCoverArtFile.isUploading} // Disable if uploading
          >
            <UploadCloud className="mr-2 h-4 w-4" />
            {localCoverArtFile.file ? 'Changer' : 'Télécharger'}
          </Button>
          {localCoverArtFile.file && (
            <Button
              variant="destructive"
              onClick={onClearCoverArt}
              className="w-full"
              type="button"
              disabled={localCoverArtFile.isUploading} // Disable if uploading
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Retirer
            </Button>
          )}
        </div>
        {localCoverArtFile.error && (
          <p className="text-sm text-destructive mt-1">{localCoverArtFile.error}</p>
        )}
        {/* Placeholder for upload progress if needed */}
        {/* {localCoverArtFile.isUploading && <Progress value={localCoverArtFile.uploadProgress} className="mt-2" />} */}
      </CardContent>
    </Card>
  );
};