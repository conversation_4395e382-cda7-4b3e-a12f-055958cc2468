"use client"; 

import { useEffect, useState } from "react"; 
import { getSupabaseClient } from "@/lib/supabase/client"; 
import { notFound, redirect } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card"; 
import { ListMusic, Music, Play, Edit, Share2, Heart, Clock, UserCircle2, ThumbsDown, PlayCircle, GripVertical } from "lucide-react"; 
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { cn } from "@/lib/utils"; 
import { DragDropContext, Droppable, Draggable, type DropResult } from "@hello-pangea/dnd";
import { PlayButton } from "@/components/audio/play-button"; 
import { ResourceViewTracker } from "@/components/stats/resource-view-tracker";
import { LikeButton } from "@/components/social/like-button"; 
import { DislikeButton } from "@/components/social/dislike-button"; 
import { FollowPlaylistButton } from "@/components/social/follow-playlist-button"; 
import { PlayPlaylistButton } from "@/components/playlists/play-playlist-button";
import { ResourceStatsDisplay } from "@/components/shared/ResourceStatsDisplay"; 
import { SharePopover } from "@/components/shared/share-popover"; // Added SharePopover
import type { Song } from "@/types"; 

interface PlaylistSongForDisplay {
  id: string; 
  title: string;
  duration: number | null;
  cover_url: string | null;
  profiles: { 
    username: string | null;
    display_name: string | null;
  } | null;
  audio_url?: string; 
}

interface PlaylistDetails {
  id: string;
  name: string;
  description: string | null;
  is_public: boolean;
  cover_url: string | null;
  user_id: string;
  created_at: string;
  updated_at: string;
  like_count?: number; 
  dislike_count?: number; 
  follower_count?: number; 
  plays?: number; 
  view_count?: number; // Added for stats
  banner_url?: string | null; 
  genres?: string[] | null; 
  moods?: string[] | null; 
  instrumentation?: string[] | null;
  slug?: string | null; // Added slug for SharePopover
  profiles: { 
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
  } | null;
  playlist_songs: { songs: (PlaylistSongForDisplay & Song) | null }[]; 
}

export default function PlaylistViewPage({ params }: { params: { id: string } }) {
  const supabase = getSupabaseClient(); 
  const [playlistData, setPlaylistData] = useState<PlaylistDetails | null>(null);
  const [songsInPlaylist, setSongsInPlaylist] = useState<Song[]>([]); 
  const [currentUser, setCurrentUser] = useState<any>(null); 
  const [isLikedByCurrentUser, setIsLikedByCurrentUser] = useState(false);
  const [isDislikedByCurrentUser, setIsDislikedByCurrentUser] = useState(false);
  const [isFollowedByCurrentUser, setIsFollowedByCurrentUser] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSavingOrder, setIsSavingOrder] = useState(false); 

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);

      const { data: rpcData, error: rpcError } = await supabase.rpc(
        'get_playlist_details_for_view', 
        {
          p_playlist_id: params.id,
          p_requesting_user_id: user?.id || null,
        }
      );

      if (rpcError) {
        console.error("Full PlaylistViewPage - RPC Error fetching playlist:", rpcError);
        setPlaylistData(null); 
        setIsLoading(false);
        notFound(); 
        return;
      }
      
      let fetchedPlaylistData = rpcData as PlaylistDetails | null;

      if (fetchedPlaylistData) {
        // Fetch view_count separately if not included in RPC
        // For now, assuming it might be part of fetchedPlaylistData or we add it
        // If your RPC 'get_playlist_details_for_view' doesn't return view_count, you'd fetch it here:
        const { data: viewCountData } = await supabase
          .rpc('get_view_count', { resource_id_param: fetchedPlaylistData.id, resource_type_param: 'playlist' });
        if (typeof viewCountData === 'number') {
          fetchedPlaylistData.view_count = viewCountData;
        }
      }
      
      setPlaylistData(fetchedPlaylistData);


      if (!fetchedPlaylistData) {
        console.error(`Full PlaylistViewPage - Playlist not found or access denied for ID: ${params.id}`);
        setIsLoading(false);
        notFound();
        return;
      }

      if (!fetchedPlaylistData.is_public && fetchedPlaylistData.user_id !== user?.id) {
        redirect("/playlists"); 
      }
      
      const mappedSongs: Song[] = (fetchedPlaylistData.playlist_songs || [])
        .map(ps => ps.songs)
        .filter((song): song is (PlaylistSongForDisplay & Song) => song !== null)
        .map(song => ({
          ...song,
          artist_name: song.profiles?.display_name || song.profiles?.username || "Artiste Inconnu",
        }));
      setSongsInPlaylist(mappedSongs);

      if (user && fetchedPlaylistData) {
        const { data: likeData } = await supabase.from('likes').select('id').eq('resource_id', fetchedPlaylistData.id).eq('resource_type', 'playlist').eq('user_id', user.id).maybeSingle();
        setIsLikedByCurrentUser(!!likeData);
        const { data: dislikeData } = await supabase.from('dislikes').select('id').eq('resource_id', fetchedPlaylistData.id).eq('resource_type', 'playlist').eq('user_id', user.id).maybeSingle();
        setIsDislikedByCurrentUser(!!dislikeData);
        const { data: followData } = await supabase.from('playlist_followers').select('id').eq('playlist_id', fetchedPlaylistData.id).eq('user_id', user.id).maybeSingle();
        setIsFollowedByCurrentUser(!!followData);
      }
      setIsLoading(false);
    };

    fetchData();
  }, [params.id, supabase]);

  if (isLoading) {
    return <div className="container py-8 text-center">Chargement de la playlist...</div>;
  }

  if (!playlistData) {
    return <div className="container py-8 text-center">Playlist non trouvée ou accès refusé.</div>;
  }

  const totalDurationSeconds = songsInPlaylist.reduce((acc, song) => acc + (song.duration || 0), 0);
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes} min ${remainingSeconds.toString().padStart(2, "0")} sec`;
  };

  const playlistCreator = playlistData.profiles;

  console.log("PlaylistViewPage - Final playlistData before render:", playlistData); // DEBUG
  console.log("PlaylistViewPage - Props for ResourceStatsDisplay:", {
    likeCount: playlistData.like_count,
    dislikeCount: playlistData.dislike_count,
    playCount: playlistData.plays,
    followerCount: playlistData.follower_count,
    viewCount: playlistData.view_count 
  }); // DEBUG

  const onDragEnd = async (result: DropResult) => {
    const { source, destination } = result;
    if (!destination || destination.index === source.index) return;
    const reorderedSongs = Array.from(songsInPlaylist);
    const [removed] = reorderedSongs.splice(source.index, 1);
    reorderedSongs.splice(destination.index, 0, removed);
    setSongsInPlaylist(reorderedSongs);
    if (!playlistData || !currentUser) return;
    setIsSavingOrder(true);
    try {
      const songIdsInNewOrder = reorderedSongs.map(song => song.id);
      const { error: rpcError } = await supabase.rpc('update_playlist_song_positions', {
        p_playlist_id: playlistData.id,
        p_song_ids: songIdsInNewOrder,
        p_requesting_user_id: currentUser.id
      });
      if (rpcError) {
        console.error("Error saving new song order:", rpcError);
        alert(`Erreur lors de la sauvegarde de l'ordre : ${rpcError.message}`);
      }
    } catch (error) {
      console.error("Exception saving new song order:", error);
      alert("Une exception s'est produite lors de la sauvegarde de l'ordre.");
    } finally {
      setIsSavingOrder(false);
    }
  };

  return (
    <div className="container py-8">
      {!playlistData.is_public && (
        <Badge variant="secondary" className="mb-4">Playlist Privée</Badge>
      )}
      <ResourceViewTracker resourceId={playlistData.id} resourceType="playlist" /> 

      {playlistData.banner_url && (
        <div className="mb-8 h-48 md:h-64 lg:h-80 rounded-lg overflow-hidden relative shadow-lg">
          <img src={playlistData.banner_url} alt={`${playlistData.name} banner`} className="w-full h-full object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent"></div>
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-8 mb-8">
        <div className="w-full md:w-1/3 lg:w-1/4 flex-shrink-0 md:-mt-24 relative z-10"> 
          {playlistData.cover_url ? (
            <img src={playlistData.cover_url} alt={playlistData.name} className="w-full aspect-square object-cover rounded-lg shadow-xl border-4 border-background" />
          ) : (
            <div className="w-full aspect-square bg-muted rounded-lg flex items-center justify-center shadow-xl border-4 border-background">
              <ListMusic className="h-24 w-24 text-muted-foreground" />
            </div>
          )}
        </div>
        <div className={`flex-1 ${playlistData.banner_url ? 'pt-4 md:pt-0' : ''}`}> 
          <p className="text-sm text-muted-foreground mb-1">Playlist {playlistData.is_public ? "Publique" : "Privée"}</p>
          <h1 className="text-4xl font-bold mb-3">{playlistData.name}</h1>
          {playlistData.description && (
            <p className="text-muted-foreground mb-4">{playlistData.description}</p>
          )}
          <div className="flex items-center gap-2 mb-2 text-sm"> {/* Reduced mb here */}
            {playlistCreator ? (
              <Link href={`/artists/${playlistCreator.username}`} className="flex items-center gap-2 hover:underline">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={playlistCreator.avatar_url || undefined} />
                  <AvatarFallback>
                    {playlistCreator.display_name?.charAt(0) || playlistCreator.username?.charAt(0) || <UserCircle2 size={16}/>}
                  </AvatarFallback>
                </Avatar>
                <span>{playlistCreator.display_name || playlistCreator.username}</span>
              </Link>
            ) : (
              <span>Créateur inconnu</span>
            )}
          </div>
          {/* Combined Details and Stats Section */}
          <div className="text-sm text-muted-foreground flex flex-col gap-y-1 mb-4"> {/* Reduced mb here */}
            <div className="flex items-center gap-x-3 gap-y-1 flex-wrap">
              <span>{songsInPlaylist.length} morceaux</span>
              {totalDurationSeconds > 0 && (
                <>
                  <span>•</span>
                  <span className="flex items-center gap-1"><Clock className="h-4 w-4" /> {formatDuration(totalDurationSeconds)}</span>
                </>
              )}
              <span>•</span>
              <span>Créée {formatDistanceToNow(new Date(playlistData.created_at), { locale: fr, addSuffix: true })}</span>
            </div>
            <ResourceStatsDisplay
                resourceType="playlist"
                likeCount={playlistData.like_count}
                dislikeCount={playlistData.dislike_count}
                playCount={playlistData.plays}
                followerCount={playlistData.follower_count}
                viewCount={playlistData.view_count} 
            />
          </div>


          <div className="flex flex-wrap gap-2 mb-6">
            {playlistData.genres?.map(genre => <Badge key={genre} variant="secondary">{genre}</Badge>)}
            {playlistData.moods?.map(mood => <Badge key={mood} variant="outline" className="border-blue-500 text-blue-500">{mood}</Badge>)}
            {playlistData.instrumentation?.map(inst => <Badge key={inst} variant="outline" className="border-green-500 text-green-500">{inst}</Badge>)}
          </div>

          <div className="flex items-center gap-2">
            <PlayPlaylistButton
              playlistId={playlistData.id}
              playlistName={playlistData.name}
              songs={songsInPlaylist} 
              initialPlayCount={playlistData.plays || 0}
            />
            {currentUser?.id !== playlistData.user_id && ( 
              <>
                <LikeButton
                  resourceId={playlistData.id}
                  resourceType="playlist"
                  initialLikes={playlistData.like_count || 0}
                  initialIsLiked={isLikedByCurrentUser}
                  userId={currentUser?.id}
                  size="default"
                />
                <DislikeButton
                  resourceId={playlistData.id}
                  resourceType="playlist"
                  initialDislikes={playlistData.dislike_count || 0}
                  initialIsDisliked={isDislikedByCurrentUser}
                  userId={currentUser?.id}
                  size="default"
                />
              </>
            )}
            {currentUser?.id !== playlistData.user_id && playlistData.is_public && ( 
               <FollowPlaylistButton
                  playlistId={playlistData.id}
                  userId={currentUser?.id}
                  initialFollowerCount={playlistData.follower_count || 0}
                  initialIsFollowed={isFollowedByCurrentUser}
                  size="default" 
                />
            )}
            <SharePopover
              resourceType="playlist"
              resourceSlug={playlistData.slug || playlistData.id} // Use slug if available, else ID
              resourceTitle={playlistData.name}
              triggerButton={
                <Button variant="outline" size="icon" title="Partager la playlist">
                  <Share2 className="h-4 w-4" />
                </Button>
              }
            />
            {currentUser?.id === playlistData.user_id && (
              <Button variant="outline" size="icon" asChild>
                <Link href={`/playlists/${playlistData.id}/edit`}><Edit className="h-4 w-4" /></Link>
              </Button>
            )}
          </div>
        </div>
      </div>

      <DragDropContext onDragEnd={onDragEnd}>
        <div>
          <h2 className="text-2xl font-semibold mb-4">Morceaux dans la playlist</h2>
          {songsInPlaylist.length > 0 ? (
            <Droppable droppableId="playlistSongs">
              {(provided) => (
                <div 
                  {...provided.droppableProps} 
                  ref={provided.innerRef} 
                  className="space-y-2"
                >
                  {songsInPlaylist.map((song, index) => (
                    <Draggable key={song.id} draggableId={song.id} index={index}>
                      {(providedDraggable, snapshot) => (
                        <Card 
                          ref={providedDraggable.innerRef}
                          {...providedDraggable.draggableProps}
                          className={cn(
                            "flex items-center p-3 gap-3 hover:bg-muted/50", 
                            snapshot.isDragging && "shadow-lg bg-card ring-2 ring-primary",
                            isSavingOrder && "opacity-70 cursor-not-allowed" 
                          )}
                        >
                          <div 
                            {...providedDraggable.dragHandleProps} 
                            className={cn(
                              "cursor-grab p-1 -ml-1", 
                              isSavingOrder && "cursor-not-allowed"
                            )}
                            aria-label="Réorganiser le morceau"
                          >
                             <GripVertical className="h-5 w-5 text-muted-foreground/70" />
                          </div>
                          <span className="text-sm text-muted-foreground w-5 text-center flex-shrink-0">{index + 1}</span>
                          {song.cover_url ? (
                            <img src={song.cover_url} alt={song.title} className="h-12 w-12 object-cover rounded-md flex-shrink-0" />
                          ) : (
                            <div className="h-12 w-12 bg-muted rounded-md flex items-center justify-center flex-shrink-0">
                              <Music className="h-6 w-6 text-muted-foreground" />
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <Link href={`/songs/${song.id}`} className="font-medium hover:underline truncate block" title={song.title}>{song.title}</Link>
                            <p className="text-xs text-muted-foreground truncate" title={song.artist_name || "Artiste inconnu"}>
                              {song.artist_name || "Artiste inconnu"}
                            </p>
                            <div className="mt-1 flex flex-wrap gap-1">
                              {song.tags?.map((tag: string) => <Badge key={tag} variant="outline" className="text-xs px-1 py-0">{tag}</Badge>)}
                              {song.genres?.map((genre: string) => <Badge key={genre} variant="secondary" className="text-xs px-1 py-0">{genre}</Badge>)}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {song.duration && (
                              <span className="text-sm text-muted-foreground hidden sm:inline">{Math.floor(song.duration / 60)}:{String(song.duration % 60).padStart(2, '0')}</span>
                            )}
                            {song.audio_url && (
                              <PlayButton song={song} size="sm" variant="ghost" className="h-8 w-8" />
                            )}
                          </div>
                        </Card>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          ) : (
            <p className="text-muted-foreground text-center py-8">Cette playlist est vide pour le moment.</p>
          )}
        </div>
      </DragDropContext>
    </div>
  );
}
