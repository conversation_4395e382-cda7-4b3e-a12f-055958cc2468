"use client"; 

import { useState, useEffect } from 'react'; 
import { getSupabaseClient } from '@/lib/supabase/client'; 
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Disc, Music2, UserCircle, Clock, ListMusic, Heart, MessageCircle, Share2, Edit3, PlayCircle as PlayAlbumIcon, ThumbsDown, Eye, X, Link2, Mail, Code } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { PlayButton } from '@/components/audio/play-button'; 
import { Label } from "@/components/ui/label"; 
import { Input } from "@/components/ui/input"; 
import { formatDuration as formatDurationUtil } from '@/lib/utils'; // Renamed to avoid conflict with local formatDuration

import { useUser } from '@/contexts/user-context'; 
import { LikeButton } from '@/components/social/like-button';
import { DislikeButton } from '@/components/social/dislike-button';
import { ResourceViewTracker } from '@/components/stats/resource-view-tracker'; 
import { PlayPlaylistButton } from '@/components/playlists/play-playlist-button'; 
import type { Song, Album as AlbumType } from '@/types'; 
import { SimilarAlbums } from '@/components/albums/similar-albums'; 
import { ArtistCardDisplay } from '@/components/artists/artist-card-display'; 
import { CommentSection } from '@/components/comments/comment-section'; 
import { SharePopover } from '@/components/shared/share-popover';
import { ResourceHeaderBanner } from '@/components/shared/resource-header-banner';
import { ResourceStatsDisplay } from '@/components/shared/ResourceStatsDisplay'; // Added ResourceStatsDisplay
import { AudioSliderPlayer } from '@/components/audio-slider-player'; // Added AudioSliderPlayer
import { useAudio } from '@/contexts/audio-context'; // Added useAudio
import { usePlaySong } from '@/hooks/use-play-song'; // Added usePlaySong

interface PublicAlbumData extends Omit<AlbumType, 'genre' | 'instrumentation'> {
  genre?: string[] | null; 
  instrumentation?: string[] | null; 
  profiles: {
    id: string; 
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
    bio?: string | null;
    website?: string | null;
    location_city?: string | null;
    location_country?: string | null;
  } | null;
  songs: Array<Song & { 
    track_number: number | null; 
    artist_name?: string; 
  }>;
  album_type?: string | null;
  slug?: string | null; 
  like_count?: number;
  dislike_count?: number;
  view_count?: number;
  are_comments_public?: boolean; 
  gallery_image_urls?: string[] | null; 
  is_gallery_public?: boolean; 
  plays?: number; // Added for consistency, though might not be fetched yet
}

interface AlbumDetailsFromDB {
  id: string;
  title: string;
  description?: string | null;
  cover_url?: string | null;
  release_date?: string | null;
  user_id: string;
  genre?: string[] | null; 
  moods?: string[] | null;
  instrumentation?: string[] | null; 
  album_type?: string | null;
  like_count?: number;
  dislike_count?: number;
  view_count?: number;
  status?: string;
  is_public: boolean;
  slug: string | null;
  profiles: {
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
  } | null;
  gallery_image_urls?: string[] | null; 
  is_gallery_public?: boolean;
  are_comments_public?: boolean;
}

type SongDetailFromDB = Pick<Song, 'id' | 'title' | 'duration' | 'audio_url' | 'cover_url' | 'user_id' | 'slug'> & {
  genres?: string[]; 
  moods?: string[];  
  status?: Song['status'];
};

async function getAlbumBySlug(slug: string, supabaseClient: any): Promise<PublicAlbumData | null> {
  const supabase = supabaseClient; 

  const { data: albumDetails, error: albumError } = await supabase
    .from('albums')
    .select(`
      id, title, description, cover_url, release_date, user_id, genre, moods, instrumentation, album_type,
      dislike_count, status, is_public, slug, are_comments_public, gallery_image_urls, is_gallery_public,
      profiles:user_id (id, username, display_name, avatar_url, bio, website, location_city, location_country)
    `) 
    .eq('slug', slug)
    .eq('is_public', true) 
    .single();

  if (albumError || !albumDetails) {
    console.error('Error fetching public album details by slug:', slug, albumError);
    return null;
  }

  let likeCount = 0;
  let viewCount = 0;

  const { data: likeCountData } = await supabase
    .rpc('get_like_count', { resource_id_param: albumDetails.id, resource_type_param: 'album' });
  if (typeof likeCountData === 'number') likeCount = likeCountData;

  const { data: viewCountData } = await supabase
    .rpc('get_view_count', { resource_id_param: albumDetails.id, resource_type_param: 'album' });
  if (typeof viewCountData === 'number') viewCount = viewCountData;

  const { data: albumSongsEntries, error: albumSongsError } = await supabase
    .from('album_songs')
    .select('song_id, track_number')
    .eq('album_id', albumDetails.id)
    .order('track_number', { ascending: true });

  if (albumSongsError) {
    console.error('Error fetching album_songs for album:', albumDetails.id, albumSongsError);
    return { 
      ...(albumDetails as unknown as PublicAlbumData), 
      songs: [], 
      like_count: likeCount, 
      view_count: viewCount, 
      dislike_count: albumDetails.dislike_count || 0,
      slug: albumDetails.slug,
    };
  }

  let songsData: PublicAlbumData['songs'] = [];
  if (albumSongsEntries && albumSongsEntries.length > 0) {
    const songIds = albumSongsEntries.map((as: { song_id: string }) => as.song_id);
    
    const { data: songDetailsList, error: songsError } = await supabase
      .from('songs')
      .select('id, title, duration, audio_url, cover_url, user_id, genres, moods, instrumentation, status, slug') // Added instrumentation for songs
      .in('id', songIds);

    if (songsError) {
      console.error('Error fetching song details for album:', albumDetails.id, songsError);
    } else if (songDetailsList) {
      const typedSongDetailsList = songDetailsList as Array<SongDetailFromDB & { instrumentation?: string[]}>;
      const songDetailsMap = new Map(
        typedSongDetailsList.map((s) => [s.id, s])
      );
      songsData = albumSongsEntries.map(
        (as: { song_id: string; track_number: number | null }): PublicAlbumData['songs'][0] => {
          const detail = songDetailsMap.get(as.song_id);
          const albumArtistName = (albumDetails.profiles?.display_name || albumDetails.profiles?.username) || '';
          
          return {
            id: as.song_id,
            title: detail?.title || 'Titre inconnu',
            duration: detail?.duration || 0,
            audio_url: detail?.audio_url || null,
            track_number: as.track_number,
            cover_url: detail?.cover_url || albumDetails?.cover_url || null,
            user_id: detail?.user_id || albumDetails?.user_id || '',
            artist_name: albumArtistName,
            is_public: albumDetails?.is_public || false, 
            slug: detail?.slug || null, 
            genres: detail?.genres || [], 
            moods: detail?.moods || [],  
            instrumentation: detail?.instrumentation || [],
            lyrics: null,
            bpm: null,
            key: null,
            ai_content_origin: null,
            is_explicit: false, 
            status: detail?.status || 'draft', 
            created_at: new Date().toISOString(), 
            updated_at: new Date().toISOString(), 
            album_id: albumDetails.id,
          };
        }
      );
    }
  }
  
  const finalAlbumData: PublicAlbumData = {
    id: albumDetails.id,
    title: albumDetails.title,
    description: albumDetails.description || null,
    cover_url: albumDetails.cover_url || null,
    release_date: albumDetails.release_date || null,
    user_id: albumDetails.user_id,
    profiles: albumDetails.profiles || null,
    songs: songsData,
    genre: albumDetails.genre || [], 
    moods: albumDetails.moods || [],
    instrumentation: albumDetails.instrumentation || [], 
    album_type: albumDetails.album_type || null,
    like_count: likeCount, 
    dislike_count: albumDetails.dislike_count || 0,
    view_count: viewCount,
    slug: albumDetails.slug, 
    gallery_image_urls: albumDetails.gallery_image_urls || [], 
    is_gallery_public: albumDetails.is_gallery_public === undefined ? true : albumDetails.is_gallery_public,
    are_comments_public: albumDetails.are_comments_public === undefined ? false : albumDetails.are_comments_public,
  };
  
  return finalAlbumData;
}


export default function PublicAlbumPage({ params }: { params: { slug: string } }) {
  const supabase = getSupabaseClient();
  const { user: currentUser } = useUser(); 
  const { currentSong, isPlaying } = useAudio(); // For AudioSliderPlayer
  const { play } = usePlaySong(); // For AudioSliderPlayer onPlayPause

  const [album, setAlbum] = useState<PublicAlbumData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const [isLikedByCurrentUser, setIsLikedByCurrentUser] = useState(false);
  const [isDislikedByCurrentUser, setIsDislikedByCurrentUser] = useState(false);

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);

  const openLightbox = (imageUrl: string) => {
    setSelectedImage(imageUrl);
    setIsLightboxOpen(true);
  };

  const closeLightbox = () => {
    setIsLightboxOpen(false);
    setSelectedImage(null);
  };
  
  useEffect(() => {
    const fetchAlbumData = async () => {
      setIsLoading(true);
      const fetchedAlbum = await getAlbumBySlug(params.slug, supabase);
      
      if (!fetchedAlbum) {
        notFound();
      } else {
        setAlbum(fetchedAlbum);
        if (currentUser && fetchedAlbum.id) {
          const { data: likeData } = await supabase
            .from('likes')
            .select('id')
            .eq('resource_id', fetchedAlbum.id)
            .eq('resource_type', 'album')
            .eq('user_id', currentUser.id)
            .maybeSingle();
          setIsLikedByCurrentUser(!!likeData);

          const { data: dislikeData } = await supabase
            .from('dislikes')
            .select('id')
            .eq('resource_id', fetchedAlbum.id)
            .eq('resource_type', 'album')
            .eq('user_id', currentUser.id)
            .maybeSingle();
          setIsDislikedByCurrentUser(!!dislikeData);
        } else {
          setIsLikedByCurrentUser(false);
          setIsDislikedByCurrentUser(false);
        }
      }
      setIsLoading(false);
    };
    fetchAlbumData();
  }, [params.slug, supabase, currentUser]);

  if (isLoading) {
    return <div className="container mx-auto max-w-4xl py-8 px-4 text-center">Chargement de l'album...</div>;
  }

  if (!album) {
    return <div className="container mx-auto max-w-4xl py-8 px-4 text-center">Album non trouvé.</div>;
  }

  const artistDisplayName = album.profiles?.display_name || album.profiles?.username || 'Artiste inconnu';
  const totalDuration = album.songs.reduce((sum, song) => sum + (song.duration || 0), 0);

  const displayedSongs = currentUser?.id === album.user_id 
    ? album.songs 
    : album.songs.filter(song => song.status === 'published');

  const handleAlbumStatsUpdate = (newIsLiked: boolean, newLikeCount: number, newDislikeCount?: number) => {
    setAlbum(prev => {
      if (!prev) return null;
      const updatedAlbum = {
        ...prev,
        like_count: newLikeCount,
        dislike_count: newDislikeCount !== undefined ? newDislikeCount : prev.dislike_count,
      };
      setIsLikedByCurrentUser(newIsLiked);
      if (newDislikeCount !== undefined) setIsDislikedByCurrentUser(false);
      return updatedAlbum;
    });
  };

   const handleAlbumDislikeUpdate = (newIsDisliked: boolean, newDislikeCount: number, newLikeCount?: number) => {
    setAlbum(prev => {
      if (!prev) return null;
      const updatedAlbum = {
        ...prev,
        dislike_count: newDislikeCount,
        like_count: newLikeCount !== undefined ? newLikeCount : prev.like_count,
      };
      setIsDislikedByCurrentUser(newIsDisliked);
      if (newLikeCount !== undefined) setIsLikedByCurrentUser(false);
      return updatedAlbum;
    });
  };

  const songsForPlayer: Song[] = displayedSongs.map(s => ({ 
    ...s, 
    artist_name: s.artist_name || artistDisplayName, 
  }));


  return (
    <div className="pb-8"> 
      <ResourceViewTracker resourceId={album.id} resourceType="album" />

      <ResourceHeaderBanner
        coverUrl={album.cover_url}
        defaultIcon={<Disc className="w-20 h-20 text-muted-foreground" />}
        resourceTypeLabel={album.album_type || 'Album'}
        title={album.title}
        artistName={artistDisplayName}
        artistLink={album.profiles?.username ? `/artists/${album.profiles.username}` : (album.user_id ? `/profile/${album.user_id}`: undefined)}
        description={album.description}
        details={
          <>
            {album.release_date && (
              <span>Sorti le {format(new Date(album.release_date), 'd MMM yyyy', { locale: fr })}</span>
            )}
            <span>• {album.songs.length} morceau{album.songs.length === 1 ? '' : 'x'}</span>
            {totalDuration > 0 && (
              <span>• {formatDurationUtil(totalDuration)}</span>
            )}
          </>
        }
        actionButtons={
          <>
            <PlayPlaylistButton playlistId={album.id} songs={songsForPlayer} playlistName={album.title} />
            {currentUser && (
              <LikeButton 
              resourceId={album.id} 
              resourceType="album" 
              initialLikes={album.like_count || 0} 
              initialIsLiked={isLikedByCurrentUser} 
              userId={currentUser.id}
              onLikeToggle={handleAlbumStatsUpdate}
              size="lg"
              className="px-4 py-2 text-sm"
            />
            )}
            {currentUser && (
              <DislikeButton
                resourceId={album.id}
                resourceType="album"
                initialDislikes={album.dislike_count || 0}
                initialIsDisliked={isDislikedByCurrentUser}
                userId={currentUser.id}
                onDislikeToggle={handleAlbumDislikeUpdate} // Assumes this handler exists or will be added
                size="lg"
                className="px-4 py-2 text-sm"
              />
            )}
            <SharePopover 
              resourceType="album"
              resourceSlug={album.slug || album.id}
              resourceTitle={album.title}
              triggerButton={
                <Button variant="outline" size="lg" title="Partager">
                  <Share2 className="mr-2 h-4 w-4" /> Partager
                </Button>
              }
            />
            {currentUser?.id === album.user_id && (
              <Button variant="outline" asChild title="Modifier l'album" className="px-3">
                <Link href={`/albums/${album.id}/edit`}><Edit3 className="mr-2 h-4 w-4" /> Éditer</Link>
              </Button>
            )}
          </>
        }
        stats={
          <ResourceStatsDisplay
            resourceType="album"
            likeCount={album.like_count}
            dislikeCount={album.dislike_count}
            viewCount={album.view_count}
            // playCount={album.plays} // Add if/when plays is available for albums
          />
        }
      />

      <div className="lg:grid lg:grid-cols-3 lg:gap-x-8 px-4 sm:px-6 lg:px-8">
        <div className="lg:col-span-2">
          <section className="mb-12">
            <h2 className="text-2xl font-semibold mb-4 border-b pb-2">Morceaux</h2>
            {displayedSongs && displayedSongs.length > 0 ? (
              <ol className="space-y-3">
                {displayedSongs.map((song, index) => (
                  <li key={song.id} className="flex items-start gap-4 p-4 bg-card rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <span className="text-sm font-medium text-muted-foreground pt-2 w-6 text-right">{song.track_number || index + 1}.</span>
                    
                    <div className="flex-shrink-0 w-16 h-16">
                      {song.cover_url ? (
                        <Image src={song.cover_url} alt={`Pochette de ${song.title}`} width={64} height={64} className="rounded object-cover aspect-square" />
                      ) : (
                        <div className="w-full h-full bg-muted rounded flex items-center justify-center">
                          <Music2 className="w-8 h-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    <div className="flex-grow">
                      <Link href={`/song/${song.slug || song.id}`} className="hover:underline">
                        <h3 className="font-semibold text-lg">{song.title}</h3>
                      </Link>
                      <p className="text-sm text-muted-foreground">{song.artist_name || artistDisplayName}</p>
                      
                      {(song.genres && song.genres.length > 0 || song.moods && song.moods.length > 0) && (
                        <div className="mt-1.5 flex flex-wrap gap-1.5">
                          {song.genres?.map((g: string) => <Badge key={`genre-${song.id}-${g}`} variant="secondary" className="text-xs">{g}</Badge>)}
                          {song.moods?.map((m: string) => <Badge key={`mood-${song.id}-${m}`} variant="outline" className="text-xs">{m}</Badge>)}
                        </div>
                      )}
                       {song.audio_url && (
                        <div className="mt-2">
                          <AudioSliderPlayer
                            audioSrc={song.audio_url}
                            isPlaying={currentSong?.id === song.id && isPlaying}
                            onPlayPause={() => {
                              const songToPlay = { ...song, artist: song.artist_name || artistDisplayName };
                              play(songToPlay as Song);
                            }}
                          />
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col items-end gap-2 ml-auto pl-2">
                      {song.audio_url && (
                        <PlayButton 
                          song={song} // song is already compatible with Song type here
                          size="sm" 
                          variant="ghost" 
                        />
                      )}
                      {song.duration && (
                        <span className="text-xs text-muted-foreground flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {formatDurationUtil(song.duration)}
                        </span>
                      )}
                    </div>
                  </li>
                ))}
              </ol>
            ) : (
              <p className="text-muted-foreground">Aucun morceau dans cet album pour le moment.</p>
            )}
          </section>

          {album.is_gallery_public && album.gallery_image_urls && album.gallery_image_urls.length > 0 && (
            <section className="mb-12">
              <h2 className="text-2xl font-semibold mb-4 border-b pb-2">Galerie</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {album.gallery_image_urls.map((url, index) => (
                  <div 
                    key={index} 
                    className="aspect-square bg-muted rounded-lg overflow-hidden cursor-pointer group relative"
                    onClick={() => openLightbox(url)}
                  >
                    <img src={url} alt={`Galerie image ${index + 1}`} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
                    <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <Eye className="w-8 h-8 text-white" />
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {isLightboxOpen && selectedImage && (
            <div 
              className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4 transition-opacity duration-300"
              onClick={closeLightbox}
              style={{ opacity: 1 }} 
            >
              <div 
                className="relative max-w-3xl max-h-[90vh] bg-background rounded-lg shadow-xl overflow-hidden"
                onClick={(e) => e.stopPropagation()} 
              >
                <img src={selectedImage} alt="Image agrandie" className="block max-w-full max-h-[80vh] object-contain" />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 text-white bg-black/30 hover:bg-black/50 h-8 w-8"
                  onClick={closeLightbox}
                  aria-label="Fermer l'aperçu"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          )}

          <CommentSection 
            resourceId={album.id} 
            resourceType="album" 
            resourceCreatorId={album.user_id}
            isModeratorView={currentUser?.id === album.user_id} 
            areCommentsPublic={album.are_comments_public ?? false} 
          />
        </div>
        
        <aside className="lg:col-span-1 space-y-8">
          {album.description && (
            <Card>
              <CardHeader>
                <CardTitle>À propos de l'album</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">{album.description}</p>
                {(album.genre && album.genre.length > 0 || album.moods && album.moods.length > 0) && (
                  <div>
                    <h4 className="font-medium text-sm mb-1">Tags</h4>
                    <div className="flex flex-wrap gap-1.5">
                      {album.genre?.map((g: string) => <Badge key={g} variant="secondary" className="text-xs">{g}</Badge>)}
                      {album.moods?.map((mood: string) => <Badge key={mood} variant="outline" className="text-xs">{mood}</Badge>)}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
          
          {album.profiles && <ArtistCardDisplay artist={{...album.profiles, id: album.user_id }} />}

          <SimilarAlbums 
            currentAlbumId={album.id} 
            currentAlbumGenres={album.genre} 
            currentAlbumArtistId={album.user_id} 
          />
        </aside>
      </div>
    </div>
  );
}
