<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MOUVIK - Cauchemar - Dreams of Neon</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-bg: #0c1821;
            --secondary-bg: #1a2634;
            --accent-teal: #20e3b2;
            --accent-blue: #0ea5e9;
            --accent-red: #e11d48;
            --text-primary: #ffffff;
            --text-secondary: #94a3b8;
        }
        
        body {
            background-color: var(--primary-bg);
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
        }
        
        .bg-gradient {
            background: linear-gradient(135deg, #0f172a 0%, #0c4a6e 100%);
        }
        
        .cover-gradient {
            background: linear-gradient(135deg, rgba(219, 39, 119, 0.7) 0%, rgba(14, 165, 233, 0.7) 100%);
        }
        
        .sidebar {
            background-color: rgba(10, 20, 30, 0.8);
        }
        
        .player-container {
            background-color: rgba(15, 23, 42, 0.8);
        }
        
        .progress-bar {
            height: 4px;
            background-color: var(--text-secondary);
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--accent-teal);
        }
        
        .chord {
            color: var(--accent-teal);
            font-weight: 600;
        }
        
        .comment-card {
            background-color: rgba(30, 41, 59, 0.6);
        }
        
        .track-card {
            background-color: rgba(30, 41, 59, 0.6);
            transition: all 0.3s ease;
        }
        
        .track-card:hover {
            background-color: rgba(51, 65, 85, 0.6);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background-color: var(--accent-teal);
            color: #0f172a;
        }
        
        .btn-outline {
            border: 1px solid var(--accent-teal);
            color: var(--accent-teal);
        }
        
        .tab-active {
            color: var(--accent-teal);
            border-bottom: 2px solid var(--accent-teal);
        }
        
        .volume-slider {
            accent-color: var(--accent-teal);
        }
        
        .audio-visualizer {
            display: flex;
            align-items: flex-end;
            height: 40px;
        }
        
        .visualizer-bar {
            width: 3px;
            margin-right: 2px;
            background-color: var(--accent-teal);
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="flex flex-col md:flex-row">
        <!-- Sidebar Navigation -->
        <div class="sidebar w-16 fixed left-0 top-0 h-full flex flex-col items-center py-6 space-y-8 z-10">
            <div class="logo">
                <i class="fas fa-waveform text-2xl text-teal-400"></i>
            </div>
            <nav class="flex flex-col space-y-6">
                <a href="#" class="p-2 rounded-full bg-opacity-20 bg-teal-400 text-teal-400">
                    <i class="fas fa-home"></i>
                </a>
                <a href="#" class="p-2 rounded-full hover:bg-opacity-20 hover:bg-gray-400 text-gray-400">
                    <i class="fas fa-search"></i>
                </a>
                <a href="#" class="p-2 rounded-full hover:bg-opacity-20 hover:bg-gray-400 text-gray-400">
                    <i class="fas fa-music"></i>
                </a>
                <a href="#" class="p-2 rounded-full hover:bg-opacity-20 hover:bg-gray-400 text-gray-400">
                    <i class="fas fa-heart"></i>
                </a>
                <a href="#" class="p-2 rounded-full hover:bg-opacity-20 hover:bg-gray-400 text-gray-400">
                    <i class="fas fa-user"></i>
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 ml-16">
            <!-- Hero Section with Cover Image -->
            <div class="relative bg-gradient min-h-96 p-8 pb-24">
                <div class="max-w-7xl mx-auto">
                    <div class="flex flex-col md:flex-row items-center gap-8">
                        <!-- Album Cover -->
                        <div class="w-64 h-64 rounded-lg overflow-hidden shadow-2xl relative">
                            <div class="absolute inset-0 cover-gradient opacity-30"></div>
                            <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Cauchemar" class="w-full h-full object-cover">
                        </div>
                        
                        <!-- Track Info -->
                        <div class="flex flex-col">
                            <span class="text-sm font-medium text-gray-400">SINGLE • ELECTRO</span>
                            <h1 class="text-4xl font-bold mt-2">Cauchemar</h1>
                            <div class="flex items-center mt-2">
                                <div class="w-8 h-8 rounded-full overflow-hidden bg-gray-700">
                                    <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Artist" class="w-full h-full object-cover">
                                </div>
                                <span class="ml-2">Dreams of Neon</span>
                            </div>
                            
                            <div class="flex items-center mt-4 text-sm text-gray-400">
                                <span><i class="fas fa-calendar-alt mr-1"></i> 2023</span>
                                <span class="ml-4"><i class="fas fa-clock mr-1"></i> 04:32</span>
                                <span class="ml-4"><i class="fas fa-play mr-1"></i> 1,214 écoutes</span>
                            </div>
                            
                            <div class="flex mt-6 gap-3">
                                <button class="btn-primary px-6 py-2 rounded-full flex items-center">
                                    <i class="fas fa-play mr-2"></i> Écouter
                                </button>
                                <button class="btn-outline px-4 py-2 rounded-full">
                                    <i class="fas fa-heart"></i>
                                </button>
                                <button class="btn-outline px-4 py-2 rounded-full">
                                    <i class="fas fa-share-alt"></i>
                                </button>
                                <button class="btn-outline px-4 py-2 rounded-full">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Player Controls - Fixed at bottom -->
            <div class="player-container fixed bottom-0 left-0 right-0 z-10 p-4">
                <div class="progress-bar w-full">
                    <div class="progress-fill" style="width: 35%;"></div>
                </div>
                
                <div class="flex justify-between items-center pt-2">
                    <!-- Currently Playing -->
                    <div class="flex items-center">
                        <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Now Playing" class="w-12 h-12 rounded">
                        <div class="ml-3">
                            <div class="text-sm font-medium">Cauchemar</div>
                            <div class="text-xs text-gray-400">Dreams of Neon</div>
                        </div>
                    </div>
                    
                    <!-- Play Controls -->
                    <div class="flex items-center space-x-6">
                        <button class="text-gray-400 hover:text-white">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button class="w-10 h-10 bg-teal-400 rounded-full flex items-center justify-center text-gray-900">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="text-gray-400 hover:text-white">
                            <i class="fas fa-step-forward"></i>
                        </button>
                    </div>
                    
                    <!-- Volume & Additional Controls -->
                    <div class="flex items-center space-x-4">
                        <div class="audio-visualizer hidden md:flex">
                            <div class="visualizer-bar" style="height: 60%;"></div>
                            <div class="visualizer-bar" style="height: 30%;"></div>
                            <div class="visualizer-bar" style="height: 80%;"></div>
                            <div class="visualizer-bar" style="height: 40%;"></div>
                            <div class="visualizer-bar" style="height: 70%;"></div>
                        </div>
                        <button class="text-gray-400 hover:text-white">
                            <i class="fas fa-volume-up"></i>
                        </button>
                        <input type="range" class="volume-slider w-20 hidden md:block" min="0" max="100" value="80">
                        <span class="text-xs text-gray-400">-1:48</span>
                    </div>
                </div>
            </div>
            
            <!-- Main Content Tabs -->
            <div class="max-w-7xl mx-auto px-6 py-8 pb-32">
                <!-- Tabs -->
                <div class="border-b border-gray-700 flex space-x-8">
                    <button class="tab-active pb-4 px-2">Paroles & Accords</button>
                    <button class="text-gray-400 hover:text-white pb-4 px-2">Commentaires</button>
                    <button class="text-gray-400 hover:text-white pb-4 px-2">Informations</button>
                </div>
                
                <!-- Lyrics with Chords -->
                <div class="mt-8">
                    <h3 class="text-xl font-medium mb-4">Couplet 1</h3>
                    <div class="mb-1">
                        <span class="chord">Am</span>
                        <span> Je me ré</span>
                        <span class="chord">Am</span>
                        <span>veille sous un ciel</span>
                    </div>
                    <div class="mb-1">
                        <span>Qui n'a </span>
                        <span class="chord">F</span>
                        <span> plus de cou</span>
                        <span class="chord">C</span>
                        <span>leur</span>
                    </div>
                    <div class="mb-1">
                        <span class="chord">C</span>
                        <span> Les souve</span>
                        <span class="chord">Am</span>
                        <span>nirs comme des </span>
                        <span class="chord">C</span>
                        <span>ailes</span>
                    </div>
                    <div class="mb-1">
                        <span>S'envo</span>
                        <span class="chord">C</span>
                        <span>lent de mon </span>
                        <span class="chord">G</span>
                        <span>cœur</span>
                    </div>
                    
                    <h3 class="text-xl font-medium mt-8 mb-4">Refrain</h3>
                    <div class="mb-1">
                        <span class="chord">F</span>
                        <span> Rêve, </span>
                        <span class="chord">C</span>
                        <span>cours, je </span>
                        <span class="chord">G</span>
                        <span>fuis</span>
                    </div>
                    <div class="mb-1">
                        <span class="chord">F</span>
                        <span> Sans sa</span>
                        <span class="chord">C</span>
                        <span>voir où </span>
                        <span class="chord">G</span>
                        <span>va</span>
                    </div>
                    <div class="mb-1">
                        <span class="chord">F</span>
                        <span> Être </span>
                        <span class="chord">C</span>
                        <span>temps sus</span>
                        <span class="chord">G</span>
                        <span>pendu</span>
                    </div>
                    <div class="mb-1">
                        <span class="chord">F</span>
                        <span> Quand tu </span>
                        <span class="chord">G</span>
                        <span>viens là</span>
                    </div>
                    
                    <h3 class="text-xl font-medium mt-8 mb-4">Couplet 2</h3>
                    <div class="mb-1">
                        <span class="chord">C</span>
                        <span> Dans ce </span>
                        <span class="chord">Am</span>
                        <span>monde sans retour</span>
                    </div>
                    <div class="mb-1">
                        <span>Je cher</span>
                        <span class="chord">F</span>
                        <span>che un che</span>
                        <span class="chord">G</span>
                        <span>min</span>
                    </div>
                    <div class="mb-1">
                        <span class="chord">C</span>
                        <span> Parmi les </span>
                        <span class="chord">Am</span>
                        <span>pages oubliées</span>
                    </div>
                    
                    <h3 class="text-xl font-medium mt-8 mb-4">Pont</h3>
                    <div class="mb-1">
                        <span class="chord">G</span>
                        <span> On se perd par</span>
                        <span class="chord">Em</span>
                        <span>fois</span>
                    </div>
                    <div class="mb-1">
                        <span class="chord">F</span>
                        <span> Dans cet océan de </span>
                        <span class="chord">C</span>
                        <span>froid</span>
                    </div>
                    <div class="mb-1">
                        <span class="chord">G</span>
                        <span> Sous mes </span>
                        <span class="chord">Am</span>
                        <span>pieds le monde </span>
                        <span class="chord">F</span>
                        <span>fuit</span>
                    </div>
                </div>
                
                <!-- Comments Section -->
                <div class="mt-16">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold">Commentaires</h2>
                        <span class="text-sm text-gray-400">25 commentaires</span>
                    </div>
                    
                    <div class="flex mb-8">
                        <div class="w-10 h-10 rounded-full overflow-hidden bg-gray-700 flex-shrink-0">
                            <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="User" class="w-full h-full object-cover">
                        </div>
                        <div class="ml-4 flex-1">
                            <textarea placeholder="Ajouter un commentaire..." class="w-full bg-gray-800 rounded-lg p-3 text-sm focus:outline-none focus:ring-2 focus:ring-teal-400"></textarea>
                        </div>
                    </div>
                    
                    <!-- Comment 1 -->
                    <div class="comment-card rounded-lg p-4 mb-4">
                        <div class="flex">
                            <div class="w-10 h-10 rounded-full overflow-hidden bg-gray-700 flex-shrink-0">
                                <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Sophie Dubois" class="w-full h-full object-cover">
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <h4 class="font-medium">Sophie Dubois</h4>
                                    <span class="text-xs text-gray-400">2h</span>
                                </div>
                                <p class="mt-2 text-sm">Cette mélodie est incroyable, j'adore les sons qui s'entremêlent au refrain. Le bassline est tellement hypnotique.</p>
                                <div class="mt-3 flex items-center text-xs text-gray-400">
                                    <button class="flex items-center hover:text-teal-400">
                                        <i class="far fa-heart mr-1"></i> 14
                                    </button>
                                    <button class="ml-4 hover:text-teal-400">Répondre</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Nested Reply -->
                        <div class="ml-12 mt-3 pl-4 border-l border-gray-700">
                            <div class="flex">
                                <div class="w-8 h-8 rounded-full overflow-hidden bg-gray-700 flex-shrink-0">
                                    <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Dreams of Neon" class="w-full h-full object-cover">
                                </div>
                                <div class="ml-3">
                                    <div class="flex justify-between">
                                        <h4 class="font-medium text-teal-400">Dreams of Neon</h4>
                                        <span class="text-xs text-gray-400">1h</span>
                                    </div>
                                    <p class="mt-1 text-sm">Merci beaucoup ! Le bassline a été créé avec un synthé Moog vintage.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Comment 2 -->
                    <div class="comment-card rounded-lg p-4 mb-4">
                        <div class="flex">
                            <div class="w-10 h-10 rounded-full overflow-hidden bg-gray-700 flex-shrink-0">
                                <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Alex Lowy" class="w-full h-full object-cover">
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <h4 class="font-medium">Alex Lowy</h4>
                                    <span class="text-xs text-gray-400">1j</span>
                                </div>
                                <p class="mt-2 text-sm">Les paroles touchent au plus profond, je me retrouve dans ces mots qui évoquent cette étrange sensation de rêve éveillé, entre deux mondes.</p>
                                <div class="mt-3 flex items-center text-xs text-gray-400">
                                    <button class="flex items-center hover:text-teal-400">
                                        <i class="far fa-heart mr-1"></i> 9
                                    </button>
                                    <button class="ml-4 hover:text-teal-400">Répondre</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button class="text-center w-full text-teal-400 py-2 hover:underline">
                        Voir plus de commentaires
                    </button>
                </div>
                
                <!-- Recommended Tracks -->
                <div class="mt-16">
                    <h2 class="text-2xl font-bold mb-6">Vous pourriez aussi aimer</h2>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <!-- Track Card 1 -->
                        <div class="track-card rounded-lg overflow-hidden">
                            <div class="relative">
                                <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Track 1" class="w-full aspect-square object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-30 opacity-0 hover:opacity-100 flex items-center justify-center transition-opacity">
                                    <button class="w-12 h-12 bg-teal-400 rounded-full flex items-center justify-center">
                                        <i class="fas fa-play text-gray-900"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-3">
                                <h4 class="font-medium">Midnight Drive</h4>
                                <p class="text-sm text-gray-400">Synthwave Dreams</p>
                            </div>
                        </div>
                        
                        <!-- Track Card 2 -->
                        <div class="track-card rounded-lg overflow-hidden">
                            <div class="relative">
                                <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Track 2" class="w-full aspect-square object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-30 opacity-0 hover:opacity-100 flex items-center justify-center transition-opacity">
                                    <button class="w-12 h-12 bg-teal-400 rounded-full flex items-center justify-center">
                                        <i class="fas fa-play text-gray-900"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-3">
                                <h4 class="font-medium">Cyberdelica</h4>
                                <p class="text-sm text-gray-400">Neon Binary</p>
                            </div>
                        </div>
                        
                        <!-- Track Card 3 -->
                        <div class="track-card rounded-lg overflow-hidden">
                            <div class="relative">
                                <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Track 3" class="w-full aspect-square object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-30 opacity-0 hover:opacity-100 flex items-center justify-center transition-opacity">
                                    <button class="w-12 h-12 bg-teal-400 rounded-full flex items-center justify-center">
                                        <i class="fas fa-play text-gray-900"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-3">
                                <h4 class="font-medium">Mirror Lights</h4>
                                <p class="text-sm text-gray-400">Electric Ocean</p>
                            </div>
                        </div>
                        
                        <!-- Track Card 4 -->
                        <div class="track-card rounded-lg overflow-hidden">
                            <div class="relative">
                                <img src="https://page.genspark.site/v1/base64_upload/1809e088c685d6e0174ebb1590d55052" alt="Track 4" class="w-full aspect-square object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-30 opacity-0 hover:opacity-100 flex items-center justify-center transition-opacity">
                                    <button class="w-12 h-12 bg-teal-400 rounded-full flex items-center justify-center">
                                        <i class="fas fa-play text-gray-900"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-3">
                                <h4 class="font-medium">Spectrum of Neon</h4>
                                <p class="text-sm text-gray-400">Digital Sunset</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Music Info Section -->
                <div class="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4">Détails du titre</h3>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-400">Artiste</span>
                                <span>Dreams of Neon</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Genre</span>
                                <span>Synthwave / Electro</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Date de sortie</span>
                                <span>14 mars 2023</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">BPM</span>
                                <span>120</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Tonalité</span>
                                <span>Am (La mineur)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Langue</span>
                                <span>Français</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">ISRC</span>
                                <span>FR-S10-23-00123</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-xl font-bold mb-4">Crédits</h3>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-400">Compositeur</span>
                                <span>Sarah Nightshade</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Parolier</span>
                                <span>Michel Laurent, Léa Dubois</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Producteur</span>
                                <span>Quantum Studios</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Enregistré à</span>
                                <span>Paris, France</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Date d'enregistrement</span>
                                <span>5 janvier 2023</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Label</span>
                                <span>MOUVIK Records</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <footer class="mt-24 border-t border-gray-800 pt-8 pb-16">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <i class="fas fa-waveform text-2xl text-teal-400 mr-2"></i>
                            <span class="font-semibold">MOUVIK</span>
                        </div>
                        <div class="flex space-x-4 text-gray-400">
                            <a href="#" class="hover:text-teal-400">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="hover:text-teal-400">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="hover:text-teal-400">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="hover:text-teal-400">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                    </div>
                    <div class="mt-6 flex flex-wrap justify-center md:justify-between text-sm text-gray-500">
                        <div class="flex space-x-4 mb-4 md:mb-0">
                            <a href="#" class="hover:text-teal-400">À propos</a>
                            <a href="#" class="hover:text-teal-400">Conditions</a>
                            <a href="#" class="hover:text-teal-400">Confidentialité</a>
                            <a href="#" class="hover:text-teal-400">Contactez-nous</a>
                        </div>
                        <div>
                            © 2023 MOUVIK. Tous droits réservés.
                        </div>
                    </div>
                </footer>
            </div>
        </div>
    </div>

    <script>
        // Simple audio visualizer effect simulation
        document.addEventListener('DOMContentLoaded', function() {
            const visualizerBars = document.querySelectorAll('.visualizer-bar');
            
            function animateVisualizer() {
                visualizerBars.forEach(bar => {
                    const height = Math.floor(Math.random() * 100) + 20;
                    bar.style.height = `${height}%`;
                });
                
                requestAnimationFrame(animateVisualizer);
            }
            
            animateVisualizer();
        });
    </script>
</body>
</html>