'use client';

import React, { useState, useCallback } from 'react';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  PanelLeftClose, PanelLeftOpen, PanelRightClose, PanelRightOpen,
  Maximize2, Minimize2, Settings, Palette, Layout, Grid3X3,
  FileText, Guitar, Music, Brain, BarChart3, Sparkles
} from 'lucide-react';

// Import des composants unifiés
import { LyricsEditorUnified } from './LyricsEditorUnified';
import { AIAssistantMegaPro } from './AIAssistantMegaPro';
import { UnifiedSongStructureTimeline } from '../UnifiedSongStructureTimeline';
import AIChordIntegration from '../AIChordIntegration';
import { StyleThemeConfig } from '../StyleThemeConfig';

interface AIComposerLayoutMegaProProps {
  // États principaux
  activeTab: string;
  setActiveTab: (tab: string) => void;
  lyricsContent: string;
  setLyricsContent: (content: string) => void;
  songSections: any[];
  setSongSections: (sections: any[]) => void;
  selectedSection: string;
  setSelectedSection: (section: string) => void;
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  currentSong: any;
  setCurrentSong: (song: any) => void;
  
  // Configuration IA
  isConfigured: boolean;
  aiHistory: any[];
  setAiHistory: (history: any[]) => void;
  lastAiResult: string;
  setLastAiResult: (result: string) => void;
  aiLoading: boolean;
  aiError: string | null;
  
  // Fonctions
  handleAIGenerate: (prompt: string, type: string) => Promise<void>;
  handleLyricsChange: (content: string) => void;
  handleChordAdd: (sectionId: string, chord: any) => void;
  handleChordRemove: (sectionId: string, chordId: string) => void;
  handleChordUpdate: (sectionId: string, chordId: string, updates: any) => void;
  
  // Instruments disponibles
  availableInstruments: any[];
}

type LayoutMode = 'standard' | 'focus' | 'split' | 'minimal';
type PanelVisibility = {
  leftPanel: boolean;
  rightPanel: boolean;
  timeline: boolean;
};

export const AIComposerLayoutMegaPro: React.FC<AIComposerLayoutMegaProProps> = ({
  activeTab,
  setActiveTab,
  lyricsContent,
  setLyricsContent,
  songSections,
  setSongSections,
  selectedSection,
  setSelectedSection,
  styleConfig,
  setStyleConfig,
  currentSong,
  setCurrentSong,
  isConfigured,
  aiHistory,
  setAiHistory,
  lastAiResult,
  setLastAiResult,
  aiLoading,
  aiError,
  handleAIGenerate,
  handleLyricsChange,
  handleChordAdd,
  handleChordRemove,
  handleChordUpdate,
  availableInstruments
}) => {
  
  // États pour le layout
  const [layoutMode, setLayoutMode] = useState<LayoutMode>('standard');
  const [panelVisibility, setPanelVisibility] = useState<PanelVisibility>({
    leftPanel: true,
    rightPanel: true,
    timeline: true
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chordPlacements, setChordPlacements] = useState<any[]>([]);

  // Gestionnaires pour les panneaux
  const togglePanel = useCallback((panel: keyof PanelVisibility) => {
    setPanelVisibility(prev => ({
      ...prev,
      [panel]: !prev[panel]
    }));
  }, []);

  // Gestionnaires pour les modes de layout
  const handleLayoutModeChange = useCallback((mode: LayoutMode) => {
    setLayoutMode(mode);
    
    switch (mode) {
      case 'focus':
        setPanelVisibility({ leftPanel: false, rightPanel: false, timeline: false });
        break;
      case 'split':
        setPanelVisibility({ leftPanel: true, rightPanel: true, timeline: false });
        break;
      case 'minimal':
        setPanelVisibility({ leftPanel: false, rightPanel: true, timeline: false });
        break;
      default:
        setPanelVisibility({ leftPanel: true, rightPanel: true, timeline: true });
    }
  }, []);

  // Actions IA spécialisées
  const handleGeneralSuggestions = useCallback(async () => {
    const prompt = `Analysez cette structure de chanson et donnez des suggestions d'amélioration :\n${JSON.stringify(songSections, null, 2)}`;
    await handleAIGenerate(prompt, 'structure');
  }, [handleAIGenerate, songSections]);

  const handleMelodySuggestion = useCallback(async () => {
    const currentChords = songSections.map(s => s.chords).flat().map(c => c.chord).join(', ');
    const prompt = `Suggérez une mélodie pour ces accords: ${currentChords}. Tonalité: ${styleConfig.key || 'C'}`;
    await handleAIGenerate(prompt, 'melody');
  }, [handleAIGenerate, songSections, styleConfig.key]);

  const handleArrangementAdvice = useCallback(async () => {
    const prompt = `Donnez des conseils d'arrangement pour cette structure: ${JSON.stringify(songSections, null, 2)}`;
    await handleAIGenerate(prompt, 'arrangement');
  }, [handleAIGenerate, songSections]);

  // Configuration des onglets avec icônes et couleurs
  const tabs = [
    {
      id: 'compose',
      label: 'Composer',
      icon: FileText,
      color: 'text-blue-600',
      description: 'Éditeur de paroles avancé'
    },
    {
      id: 'chords',
      label: 'Accords',
      icon: Guitar,
      color: 'text-green-600',
      description: 'Bibliothèque et création d\'accords'
    },
    {
      id: 'style',
      label: 'Style',
      icon: Palette,
      color: 'text-purple-600',
      description: 'Configuration style et thème'
    },
    {
      id: 'timeline',
      label: 'Timeline',
      icon: BarChart3,
      color: 'text-orange-600',
      description: 'Structure et timeline'
    }
  ];

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-950">
      {/* Barre d'outils supérieure */}
      <div className="border-b bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm p-3">
        <div className="flex items-center justify-between">
          {/* Contrôles de layout */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
              {(['standard', 'focus', 'split', 'minimal'] as LayoutMode[]).map((mode) => (
                <Button
                  key={mode}
                  variant={layoutMode === mode ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handleLayoutModeChange(mode)}
                  className="text-xs"
                >
                  {mode === 'standard' && <Layout className="h-3 w-3" />}
                  {mode === 'focus' && <Maximize2 className="h-3 w-3" />}
                  {mode === 'split' && <Grid3X3 className="h-3 w-3" />}
                  {mode === 'minimal' && <Minimize2 className="h-3 w-3" />}
                  <span className="ml-1 capitalize">{mode}</span>
                </Button>
              ))}
            </div>
            
            {/* Contrôles de panneaux */}
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => togglePanel('leftPanel')}
                className="gap-1"
              >
                {panelVisibility.leftPanel ? <PanelLeftClose className="h-3 w-3" /> : <PanelLeftOpen className="h-3 w-3" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => togglePanel('rightPanel')}
                className="gap-1"
              >
                {panelVisibility.rightPanel ? <PanelRightClose className="h-3 w-3" /> : <PanelRightOpen className="h-3 w-3" />}
              </Button>
            </div>
          </div>

          {/* Informations contextuelles */}
          <div className="flex items-center gap-3">
            <Badge variant="outline" className="gap-1">
              <Music className="h-3 w-3" />
              {currentSong.key} • {currentSong.tempo} BPM
            </Badge>
            <Badge variant="outline" className="gap-1">
              <FileText className="h-3 w-3" />
              {lyricsContent.trim().split(/\s+/).filter(Boolean).length} mots
            </Badge>
            {isConfigured && (
              <Badge variant="default" className="gap-1 bg-green-500">
                <Brain className="h-3 w-3" />
                IA Active
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Layout principal */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Panneau gauche - Timeline/Structure */}
          {panelVisibility.leftPanel && (
            <>
              <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                <div className="h-full border-r bg-white/50 dark:bg-gray-900/50">
                  <div className="p-3 border-b">
                    <h3 className="font-medium text-sm flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Structure & Timeline
                    </h3>
                  </div>
                  <UnifiedSongStructureTimeline
                    structure={{
                      sections: songSections.map(s => ({
                        id: s.id,
                        type: s.type,
                        title: s.title,
                        duration: s.duration || 16,
                        startTime: s.startTime || 0,
                        key: styleConfig.key,
                        tempo: styleConfig.bpm
                      })),
                      totalDuration: songSections.reduce((total, section) => total + (section.duration || 16), 0),
                      key: styleConfig.key,
                      tempo: styleConfig.bpm,
                      timeSignature: styleConfig.timeSignature
                    }}
                    onStructureChange={(structure) => {
                      const newSections = structure.sections.map(s => {
                        const existing = songSections.find(existing => existing.id === s.id);
                        return existing ? { 
                          ...existing, 
                          type: s.type, 
                          title: s.title,
                          duration: s.duration,
                          startTime: s.startTime
                        } : {
                          id: s.id,
                          type: s.type,
                          title: s.title,
                          duration: s.duration,
                          startTime: s.startTime,
                          content: '',
                          chords: []
                        };
                      });
                      setSongSections(newSections);
                    }}
                    onSectionSelect={setSelectedSection}
                    selectedSectionId={selectedSection}
                    viewMode="list"
                  />
                </div>
              </ResizablePanel>
              <ResizableHandle withHandle />
            </>
          )}

          {/* Panneau central - Éditeur principal */}
          <ResizablePanel defaultSize={panelVisibility.leftPanel && panelVisibility.rightPanel ? 50 : 70} minSize={40}>
            <div className="h-full flex flex-col bg-white/30 dark:bg-gray-900/30">
              {/* Navigation des onglets */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
                <div className="border-b bg-white/50 dark:bg-gray-900/50 px-4 py-2">
                  <TabsList className="grid w-full grid-cols-4">
                    {tabs.map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <TabsTrigger key={tab.id} value={tab.id} className="gap-2">
                          <Icon className={`h-4 w-4 ${tab.color}`} />
                          {tab.label}
                        </TabsTrigger>
                      );
                    })}
                  </TabsList>
                </div>
                
                <div className="flex-1 overflow-hidden">
                  <TabsContent value="compose" className="h-full m-0 p-0">
                    <LyricsEditorUnified
                      content={lyricsContent}
                      onContentChange={handleLyricsChange}
                      selectedSection={selectedSection}
                      sections={songSections}
                      onAIGenerate={handleAIGenerate}
                    />
                  </TabsContent>
                  
                  <TabsContent value="chords" className="h-full m-0 p-0">
                    <AIChordIntegration
                      sections={songSections}
                      selectedSection={selectedSection}
                      onSectionUpdate={(sectionId: string, updates: any) => {
                        setSongSections(prev => prev.map(section => 
                          section.id === sectionId 
                            ? { ...section, ...updates }
                            : section
                        ));
                      }}
                      onChordAdd={handleChordAdd}
                      onChordRemove={handleChordRemove}
                      onChordUpdate={handleChordUpdate}
                      availableInstruments={availableInstruments}
                      onAIGenerateChords={async (prompt: string, sectionId: string) => {
                        await handleAIGenerate(prompt, 'chords');
                      }}
                    />
                  </TabsContent>
                  
                  <TabsContent value="style" className="h-full m-0 p-0">
                    <div className="h-full p-4">
                      <StyleThemeConfig
                        initialConfig={styleConfig}
                        onConfigChange={(config) => {
                          setStyleConfig(config);
                          setCurrentSong(prev => ({
                            ...prev,
                            key: config.key,
                            tempo: config.bpm,
                            timeSignature: config.timeSignature
                          }));
                        }}
                      />
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="timeline" className="h-full m-0 p-0">
                    <div className="h-full p-4">
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <p className="text-blue-800 text-sm">
                          🎵 Timeline intégrée dans le panneau gauche pour une meilleure ergonomie
                        </p>
                      </div>
                    </div>
                  </TabsContent>
                </div>
              </Tabs>
            </div>
          </ResizablePanel>

          {/* Panneau droit - Assistant IA Mega Pro */}
          {panelVisibility.rightPanel && (
            <>
              <ResizableHandle withHandle />
              <ResizablePanel defaultSize={30} minSize={25} maxSize={40}>
                <AIAssistantMegaPro
                  isConfigured={isConfigured}
                  aiLoading={aiLoading}
                  aiError={aiError}
                  currentSection={selectedSection}
                  songSections={songSections}
                  styleConfig={styleConfig}
                  lyricsContent={lyricsContent}
                  chordPlacements={chordPlacements}
                  aiHistory={aiHistory}
                  lastAiResult={lastAiResult}
                  setAiHistory={setAiHistory}
                  setLastAiResult={setLastAiResult}
                  onAIGenerate={handleAIGenerate}
                  onGeneralSuggestions={handleGeneralSuggestions}
                  onMelodySuggestion={handleMelodySuggestion}
                  onArrangementAdvice={handleArrangementAdvice}
                  onInsertToLyrics={(text) => {
                    const newContent = lyricsContent + '\n' + text;
                    setLyricsContent(newContent);
                  }}
                  onApplyChordSuggestion={(chord) => {
                    // Logique pour appliquer une suggestion d'accord
                    console.log('Applying chord suggestion:', chord);
                  }}
                  onApplyStructureSuggestion={(structure) => {
                    // Logique pour appliquer une suggestion de structure
                    console.log('Applying structure suggestion:', structure);
                  }}
                />
              </ResizablePanel>
            </>
          )}
        </ResizablePanelGroup>
      </div>
    </div>
  );
};
