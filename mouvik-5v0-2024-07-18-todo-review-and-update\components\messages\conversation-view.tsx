"use client"

import { useState, useEffect, useRef } from "react"
import { createBrowserClient } from '@/lib/supabase/client'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { useToast } from "@/hooks/use-toast"

interface ConversationViewProps {
  conversationId: string
  currentUserId: string
}

export function ConversationView({
  conversationId,
  currentUserId
}: ConversationViewProps) {
  const [messages, setMessages] = useState<any[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [participants, setParticipants] = useState<any[]>([])
  const [otherUser, setOtherUser] = useState<any>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const supabase = createBrowserClient()
  const { toast } = useToast()

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const { data, error } = await supabase
          .from('messages')
          .select(`
            *,
            sender:sender_id (
              id,
              email,
              user_metadata
            )
          `)
          .eq('conversation_id', conversationId)
          .order('created_at', { ascending: true })

        if (error) {
          console.error('Erreur lors de la récupération des messages:', error)
          return
        }

        setMessages(data || [])
      } catch (error) {
        console.error('Erreur lors de la récupération des messages:', error)
      }
    }

    const fetchParticipants = async () => {
      try {
        const { data, error } = await supabase
          .from('conversation_participants')
          .select(`
            user_id,
            users:user_id (
              id,
              email,
              user_metadata
            )
          `)
          .eq('conversation_id', conversationId)

        if (error) {
          console.error('Erreur lors de la récupération des participants:', error)
          return
        }

        setParticipants(data || [])
        
        // Trouver l'autre utilisateur
        const other = data?.find(p => p.user_id !== currentUserId)?.users
        setOtherUser(other)
      } catch (error) {
        console.error('Erreur lors de la récupération des participants:', error)
      }
    }

    fetchMessages()
    fetchParticipants()

    // Mettre à jour les messages non lus
    const updateUnreadMessages = async () => {
      try {
        await supabase
          .from('messages')
          .update({ read_at: new Date().toISOString() })
          .eq('conversation_id', conversationId)
          .neq('sender_id', currentUserId)
          .is('read_at', null)
      } catch (error) {
        console.error('Erreur lors de la mise à jour des messages non lus:', error)
      }
    }

    updateUnreadMessages()

    // Abonnement aux nouveaux messages
    const channel = supabase
      .channel(`conversation:${conversationId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: `conversation_id=eq.${conversationId}`
      }, async (payload) => {
        // Récupérer les informations de l'expéditeur
        const { data: sender } = await supabase
          .from('auth.users')
          .select('id, email, user_metadata')
          .eq('id', payload.new.sender_id)
          .single()

        const newMessage = {
          ...payload.new,
          sender
        }

        setMessages(prev => [...prev, newMessage])
        
        // Marquer comme lu si ce n'est pas notre message
        if (payload.new.sender_id !== currentUserId) {
          await supabase
            .from('messages')
            .update({ read_at: new Date().toISOString() })
            .eq('id', payload.new.id)
        }
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [conversationId, currentUserId, supabase])

  useEffect(() => {
    // Faire défiler vers le bas lorsque de nouveaux messages arrivent
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return

    try {
      setIsLoading(true)

      const { error } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: currentUserId,
          content: newMessage
        })

      if (error) {
        console.error('Erreur lors de l\'envoi du message:', error)
        toast({
          title: "Erreur",
          description: "Impossible d'envoyer le message",
          variant: "destructive"
        })
        return
      }

      // Mettre à jour la date de la conversation
      await supabase
        .from('conversations')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', conversationId)

      setNewMessage('')
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const otherUserName = otherUser?.user_metadata?.full_name || otherUser?.email?.split('@')[0] || 'Utilisateur'
  const otherUserAvatar = otherUser?.user_metadata?.avatar_url

  return (
    <div className="flex h-[500px] flex-col rounded-lg border bg-card">
      {/* En-tête de la conversation */}
      <div className="border-b p-4">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={otherUserAvatar || ''} alt={otherUserName} />
            <AvatarFallback>{otherUserName.substring(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">{otherUserName}</p>
            <p className="text-sm text-gray-500">{otherUser?.email}</p>
          </div>
        </div>
      </div>
      
      {/* Corps de la conversation */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {messages.map(message => {
            const isSentByMe = message.sender_id === currentUserId
            const senderName = isSentByMe 
              ? 'Vous' 
              : message.sender?.user_metadata?.full_name || message.sender?.email?.split('@')[0] || 'Utilisateur'
            const senderAvatar = message.sender?.user_metadata?.avatar_url
            
            return (
              <div
                key={message.id}
                className={`flex ${isSentByMe ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex max-w-[70%] items-start gap-2 ${isSentByMe ? 'flex-row-reverse' : ''}`}>
                  {!isSentByMe && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={senderAvatar || ''} alt={senderName} />
                      <AvatarFallback>{senderName.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                  )}
                  <div>
                    <div
                      className={`rounded-lg p-3 ${
                        isSentByMe
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                    </div>
                    <p className="mt-1 text-xs text-gray-500">
                      {formatDistanceToNow(new Date(message.created_at), { addSuffix: true, locale: fr })}
                    </p>
                  </div>
                </div>
              </div>
            )
          })}
          <div ref={messagesEndRef} />
        </div>
      </div>
      
      {/* Formulaire d'envoi de message */}
      <div className="border-t p-4">
        <form
          onSubmit={(e) => {
            e.preventDefault()
            handleSendMessage()
          }}
          className="flex items-center gap-2"
        >
          <Input
            placeholder="Écrire un message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            className="flex-1"
          />
          <Button type="submit" size="icon" disabled={isLoading || !newMessage.trim()} >
            Envoyer
          </Button>
        </form>
      </div>
    </div>
  )
}
