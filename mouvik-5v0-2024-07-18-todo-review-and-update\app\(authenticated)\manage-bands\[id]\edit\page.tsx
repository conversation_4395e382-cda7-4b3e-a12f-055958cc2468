"use client"
import { useState, useEffect } from "react"
import type React from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"; // Added Link import
import { getSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { MultiSelect } from "@/components/ui/multi-select" 
import { genreOptions, moodOptions, instrumentationOptions, countryOptions } from '@/lib/constants/song-options'; // Added countryOptions
import { useToast } from "@/hooks/use-toast"
import { ImageUploader } from "@/components/ui/image-uploader"
import { slugify } from "@/lib/utils"; 
import { Eye } from "lucide-react"; 
import { Switch } from "@/components/ui/switch"; // Added Switch import

interface EditBandPageProps {
  params: { id: string }; // Band ID from URL
}

export default function EditBandPage({ params }: EditBandPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const supabase = getSupabaseClient()
  const bandId = params.id;

  const [isLoading, setIsLoading] = useState(false)
  const [isFetching, setIsFetching] = useState(true)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    genres: [] as string[],
    moods: [] as string[],
    instrumentation: [] as string[],
    countries: [] as string[], 
    location: "",
    avatar_url: "",
    cover_url: "",
    slug: "", 
    is_public: true, // This is for the band's own public status
    are_comments_public: false, // For comment visibility, default private as per user
  })

  useEffect(() => {
    const fetchBandData = async () => {
      if (!bandId) return;
      setIsFetching(true);
      try {
        const { data: bandData, error } = await supabase
          .from("bands")
          .select("*, are_comments_public") // Ensure are_comments_public is fetched
          .eq("id", bandId)
          .single();

        if (error) throw error;
        if (bandData) {
          setFormData({
            name: bandData.name || "",
            description: bandData.description || "",
            genres: Array.isArray(bandData.genres) ? bandData.genres : [],
            moods: Array.isArray(bandData.moods) ? bandData.moods : [],
            instrumentation: Array.isArray(bandData.instrumentation) ? bandData.instrumentation : [],
            countries: Array.isArray(bandData.countries) ? bandData.countries : [],
            location: bandData.location || "",
            avatar_url: bandData.avatar_url || "",
            cover_url: bandData.cover_url || "",
            slug: bandData.slug || "", 
            is_public: typeof bandData.is_public === 'boolean' ? bandData.is_public : true, 
            are_comments_public: typeof bandData.are_comments_public === 'boolean' ? bandData.are_comments_public : false, // Populate are_comments_public
          });
        }
      } catch (error) {
        console.error("Error fetching band data:", error);
        toast({ title: "Erreur", description: "Impossible de charger les données du groupe.", variant: "destructive" });
        router.push("/bands"); // Redirect if band not found or error
      } finally {
        setIsFetching(false);
      }
    };
    fetchBandData();
  }, [bandId, supabase, toast, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleImageUpload = (type: "avatar" | "cover", url: string) => {
    setFormData((prev) => ({
      ...prev,
      [type === "avatar" ? "avatar_url" : "cover_url"]: url,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const { data: userData, error: userError } = await supabase.auth.getUser()
      if (userError || !userData.user) throw userError || new Error("User not found");

      // Update the band
      const { error: bandUpdateError } = await supabase
        .from("bands")
        .update({
          name: formData.name,
          description: formData.description,
          genres: formData.genres.length > 0 ? formData.genres : null,
          moods: formData.moods.length > 0 ? formData.moods : null,
          instrumentation: formData.instrumentation.length > 0 ? formData.instrumentation : null,
          countries: formData.countries.length > 0 ? formData.countries : null, 
          location: formData.location,
          avatar_url: formData.avatar_url,
          cover_url: formData.cover_url,
          slug: slugify(formData.name), 
          is_public: formData.is_public, // This saves the band's public status
          are_comments_public: formData.are_comments_public, // Save comment visibility status
          updated_at: new Date().toISOString(), 
        })
        .eq("id", bandId)
        // .eq("creator_id", userData.user.id); // RLS should handle permission for update
        // For now, allow if user is creator or co-admin (handled by RLS policy ideally)

      if (bandUpdateError) throw bandUpdateError;

      toast({
        title: "Groupe mis à jour avec succès",
        description: `Le groupe "${formData.name}" a été mis à jour.`,
      })

      router.push(`/manage-bands/${bandId}`) // Navigate to band view page (updated path)
    } catch (error: any) {
      console.error("Error updating band:", error)
      toast({
        title: "Erreur",
        description: error.message || "Une erreur est survenue lors de la mise à jour du groupe.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isFetching) {
    return <div className="container max-w-3xl py-8 text-center">Chargement des données du groupe...</div>;
  }

  return (
    <div className="container max-w-3xl py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Modifier le groupe</h1>
        <p className="text-muted-foreground">Mettez à jour les informations de votre groupe</p>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informations du groupe</CardTitle>
            <CardDescription>Modifiez les informations de base de votre groupe</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Nom du groupe</Label>
              <Input id="name" name="name" placeholder="Entrez le nom du groupe" value={formData.name} onChange={handleChange} required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" name="description" placeholder="Décrivez votre groupe et son style musical" value={formData.description} onChange={handleChange} rows={4} />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="genres">Genres</Label>
                <MultiSelect options={genreOptions} selected={formData.genres} onChange={(selected) => setFormData((prev) => ({ ...prev, genres: selected }))} placeholder="Sélectionnez des genres" className="w-full" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="moods">Ambiances / Moods</Label>
                <MultiSelect options={moodOptions} selected={formData.moods} onChange={(selected) => setFormData((prev) => ({ ...prev, moods: selected }))} placeholder="Sélectionnez des ambiances" className="w-full" />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="instrumentation">Instrumentation</Label>
                <MultiSelect options={instrumentationOptions} selected={formData.instrumentation} onChange={(selected) => setFormData((prev) => ({ ...prev, instrumentation: selected }))} placeholder="Sélectionnez des instruments" className="w-full" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="countries">Pays</Label>
                <MultiSelect
                  options={countryOptions}
                  selected={formData.countries}
                  onChange={(selected) => setFormData((prev) => ({ ...prev, countries: selected }))}
                  placeholder="Sélectionnez des pays"
                  className="w-full"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Localisation (Ville/Région)</Label>
              <Input id="location" name="location" placeholder="Ex: Paris, Île-de-France" value={formData.location} onChange={handleChange} />
            </div>
            <div className="space-y-2">
              <Label>Avatar du groupe</Label>
              <ImageUploader 
                onImageUploaded={(url) => handleImageUpload("avatar", url)} 
                existingImageUrl={formData.avatar_url} 
                aspectRatio="free" // Changed from "square" to "free"
                maxWidth={300} 
                maxHeight={300} // Added maxHeight
                bucketName="band-avatars" 
              />
              <p className="text-xs text-muted-foreground">Recommandé: image carrée, minimum 300x300px</p>
            </div>
            <div className="space-y-2">
              <Label>Image de couverture</Label>
              <ImageUploader 
                onImageUploaded={(url) => handleImageUpload("cover", url)} 
                existingImageUrl={formData.cover_url} 
                aspectRatio="free" // Changed from "landscape" to "free"
                maxWidth={1200} 
                maxHeight={1200} // Added maxHeight
                bucketName="band-covers" 
              />
              <p className="text-xs text-muted-foreground">Recommandé: image au format 16:9, minimum 1200x675px</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="are_comments_public"
                  checked={formData.are_comments_public} // Correctly bind to are_comments_public
                  onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, are_comments_public: checked }))}
                />
                <Label htmlFor="are_comments_public" className="cursor-pointer">
                  Rendre les commentaires publics pour ce groupe ?
                </Label>
              </div>
              <p className="text-xs text-muted-foreground">
                Si coché, les commentaires seront visibles par tous les utilisateurs connectés (selon les RLS des commentaires). Sinon, ils ne seront visibles que par les membres du groupe.
              </p>
            </div>
            
            {/* TODO: Add a switch for formData.is_public (band's own public status) if it should be editable here */}
            {/* For now, band's public status is toggled from BandCardActions */}

          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              Annuler
            </Button>
            <div className="flex items-center gap-2">
              {formData.slug && (
                <Button variant="ghost" asChild>
                  <Link href={`/bands/${formData.slug}`} target="_blank" rel="noopener noreferrer">
                    <>
                      <Eye className="mr-2 h-4 w-4" />
                      Prévisualiser
                    </>
                  </Link>
                </Button>
              )}
              <Button type="submit" disabled={isLoading || isFetching}>
                {isLoading ? "Mise à jour..." : "Enregistrer les modifications"}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
