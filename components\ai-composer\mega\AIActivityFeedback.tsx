'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Brain, Sparkles, CheckCircle2, AlertCircle, Clock, 
  Loader2, Zap, MessageSquare, BarChart3, X, Copy,
  ArrowRight, Lightbulb, Target, Wand2
} from 'lucide-react';

interface AIActivityFeedbackProps {
  aiLoading: boolean;
  aiError: string | null;
  lastAiResult: string;
  aiHistory: any[];
  onClearHistory: () => void;
  onCopyResult: (result: string) => void;
}

interface AIActivity {
  id: string;
  type: string;
  prompt: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  result?: string;
  error?: string;
  timestamp: Date;
  duration?: number;
  progress?: number;
}

export const AIActivityFeedback: React.FC<AIActivityFeedbackProps> = ({
  aiLoading,
  aiError,
  lastAiResult,
  aiHistory,
  onClearHistory,
  onCopyResult
}) => {
  
  const [activities, setActivities] = useState<AIActivity[]>([]);
  const [currentActivity, setCurrentActivity] = useState<AIActivity | null>(null);
  const [progress, setProgress] = useState(0);

  // Simuler le progress pour les activités IA
  useEffect(() => {
    if (aiLoading && !currentActivity) {
      const newActivity: AIActivity = {
        id: `activity-${Date.now()}`,
        type: 'analysis',
        prompt: 'Analyse en cours...',
        status: 'processing',
        timestamp: new Date(),
        progress: 0
      };
      
      setCurrentActivity(newActivity);
      setProgress(0);
      
      // Simuler la progression
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90; // Rester à 90% jusqu'à la vraie réponse
          }
          return prev + Math.random() * 15;
        });
      }, 200);
      
      return () => clearInterval(progressInterval);
    }
  }, [aiLoading, currentActivity]);

  // Gérer la fin de l'activité IA
  useEffect(() => {
    if (!aiLoading && currentActivity) {
      const completedActivity: AIActivity = {
        ...currentActivity,
        status: aiError ? 'error' : 'completed',
        result: lastAiResult,
        error: aiError,
        duration: Date.now() - currentActivity.timestamp.getTime(),
        progress: 100
      };
      
      setActivities(prev => [completedActivity, ...prev.slice(0, 9)]); // Garder 10 activités max
      setCurrentActivity(null);
      setProgress(100);
      
      // Reset progress après 2 secondes
      setTimeout(() => setProgress(0), 2000);
    }
  }, [aiLoading, aiError, lastAiResult, currentActivity]);

  // Types d'activités avec icônes et couleurs
  const getActivityConfig = (type: string) => {
    const configs = {
      'analysis': { icon: BarChart3, color: 'text-blue-400', bg: 'bg-blue-500/10' },
      'suggestion': { icon: Lightbulb, color: 'text-yellow-400', bg: 'bg-yellow-500/10' },
      'chat': { icon: MessageSquare, color: 'text-green-400', bg: 'bg-green-500/10' },
      'chords': { icon: Target, color: 'text-purple-400', bg: 'bg-purple-500/10' },
      'lyrics': { icon: Wand2, color: 'text-pink-400', bg: 'bg-pink-500/10' },
      'default': { icon: Brain, color: 'text-slate-400', bg: 'bg-slate-500/10' }
    };
    
    return configs[type] || configs.default;
  };

  // Formater la durée
  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  // Tronquer le texte
  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className="h-full flex flex-col bg-slate-900/30">
      {/* En-tête */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg">
              <Brain className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-medium text-white">Activité IA</h3>
              <p className="text-sm text-slate-400">
                {activities.length} interactions • {activities.filter(a => a.status === 'completed').length} réussies
              </p>
            </div>
          </div>
          
          {activities.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearHistory}
              className="gap-1"
            >
              <X className="h-4 w-4" />
              Effacer
            </Button>
          )}
        </div>
      </div>

      {/* Activité en cours */}
      {(currentActivity || aiLoading) && (
        <div className="border-b border-slate-700 bg-slate-800/30 p-3">
          <Card className="bg-blue-500/10 border-blue-500/20">
            <CardContent className="p-3">
              <div className="flex items-center gap-3">
                <Loader2 className="h-5 w-5 text-blue-400 animate-spin" />
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-white font-medium">IA en réflexion...</span>
                    <Badge variant="outline" className="text-xs text-blue-400 border-blue-400">
                      En cours
                    </Badge>
                  </div>
                  
                  {/* Barre de progression */}
                  <div className="space-y-1">
                    <Progress value={progress} className="h-2" />
                    <div className="flex justify-between text-xs text-slate-400">
                      <span>Analyse et génération...</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                  </div>
                  
                  {/* Messages de progression */}
                  <div className="mt-2 text-xs text-blue-300">
                    {progress < 30 && "🧠 Analyse du contexte..."}
                    {progress >= 30 && progress < 60 && "💭 Génération des idées..."}
                    {progress >= 60 && progress < 90 && "✨ Optimisation des suggestions..."}
                    {progress >= 90 && "🎯 Finalisation de la réponse..."}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Dernier résultat */}
      {lastAiResult && !aiLoading && (
        <div className="border-b border-slate-700 bg-slate-800/20 p-3">
          <Card className="bg-green-500/10 border-green-500/20">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm text-white flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-green-400" />
                  Dernière réponse IA
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onCopyResult(lastAiResult)}
                  className="h-6 w-6 p-0"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-sm text-slate-300 bg-slate-800/50 rounded p-2 max-h-20 overflow-y-auto">
                {truncateText(lastAiResult, 200)}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Erreur IA */}
      {aiError && !aiLoading && (
        <div className="border-b border-slate-700 bg-slate-800/20 p-3">
          <Card className="bg-red-500/10 border-red-500/20">
            <CardContent className="p-3">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-400" />
                <div className="flex-1">
                  <div className="text-white font-medium mb-1">Erreur IA</div>
                  <div className="text-sm text-red-300">{aiError}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Historique des activités */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-3 space-y-2">
            {activities.length === 0 ? (
              <div className="text-center text-slate-400 py-8">
                <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Aucune activité IA pour le moment</p>
                <p className="text-xs mt-1">Les interactions apparaîtront ici</p>
              </div>
            ) : (
              activities.map((activity) => {
                const config = getActivityConfig(activity.type);
                const Icon = config.icon;
                
                return (
                  <Card 
                    key={activity.id} 
                    className={`${config.bg} border-slate-600 hover:border-slate-500 transition-all`}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-start gap-3">
                        <div className={`p-1 rounded ${config.bg}`}>
                          <Icon className={`h-4 w-4 ${config.color}`} />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-white text-sm font-medium">
                              {activity.type === 'analysis' && 'Analyse'}
                              {activity.type === 'suggestion' && 'Suggestion'}
                              {activity.type === 'chat' && 'Chat'}
                              {activity.type === 'chords' && 'Accords'}
                              {activity.type === 'lyrics' && 'Paroles'}
                            </span>
                            
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${
                                activity.status === 'completed' ? 'text-green-400 border-green-400' :
                                activity.status === 'error' ? 'text-red-400 border-red-400' :
                                'text-yellow-400 border-yellow-400'
                              }`}
                            >
                              {activity.status === 'completed' && 'Terminé'}
                              {activity.status === 'error' && 'Erreur'}
                              {activity.status === 'processing' && 'En cours'}
                            </Badge>
                            
                            <span className="text-xs text-slate-400">
                              {activity.timestamp.toLocaleTimeString()}
                            </span>
                            
                            {activity.duration && (
                              <span className="text-xs text-slate-400">
                                {formatDuration(activity.duration)}
                              </span>
                            )}
                          </div>
                          
                          {activity.result && (
                            <div className="text-sm text-slate-300 bg-slate-800/30 rounded p-2 mt-2">
                              {truncateText(activity.result, 150)}
                            </div>
                          )}
                          
                          {activity.error && (
                            <div className="text-sm text-red-300 bg-red-500/10 rounded p-2 mt-2">
                              {activity.error}
                            </div>
                          )}
                        </div>
                        
                        {activity.result && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onCopyResult(activity.result!)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Statistiques */}
      <div className="border-t border-slate-700 bg-slate-800/50 p-3">
        <div className="flex items-center justify-between text-sm text-slate-400">
          <div className="flex items-center gap-4">
            <span>Total: {activities.length}</span>
            <span>•</span>
            <span className="text-green-400">
              Réussies: {activities.filter(a => a.status === 'completed').length}
            </span>
            <span>•</span>
            <span className="text-red-400">
              Erreurs: {activities.filter(a => a.status === 'error').length}
            </span>
          </div>
          
          {activities.length > 0 && (
            <div className="text-xs">
              Moyenne: {formatDuration(
                activities
                  .filter(a => a.duration)
                  .reduce((sum, a) => sum + (a.duration || 0), 0) / 
                activities.filter(a => a.duration).length || 0
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
