"use client";

// This component was created to wrap ViewRecorder for use in Server Components.
// However, ViewRecorder itself is a client component with useEffect.
// It can be directly imported and used in Server Components if it's at the top level
// or passed to a Client Component child.
// For simplicity, if ViewRecorder can be directly used, this wrapper might be redundant.
// Let's assume ViewRecorder is designed to be self-contained for now.
// This file might not be strictly necessary if ViewRecorder is used directly.
// For now, creating it as per the thought process.

import { ViewRecorder, ViewRecorderProps } from '@/components/stats/view-recorder'; // Import ViewRecorderProps

// Use ViewRecorderProps directly to ensure types are synced
export function ResourceViewTracker({ resourceId, resourceType }: ViewRecorderProps) { // Use ViewRecorderProps
  return <ViewRecorder resourceId={resourceId} resourceType={resourceType} />;
}
