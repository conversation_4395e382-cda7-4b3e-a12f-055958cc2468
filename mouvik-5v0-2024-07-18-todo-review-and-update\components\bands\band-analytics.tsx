import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"

interface BandAnalyticsProps {
  bandId: string
}

export function BandAnalytics({ bandId }: BandAnalyticsProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Analytics</CardTitle>
        <CardDescription>Performance des 30 derniers jours</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="listens">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="listens">Écoutes</TabsTrigger>
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
          </TabsList>
          <TabsContent value="listens" className="pt-4">
            <div className="text-center mb-2">
              <p className="text-3xl font-bold">12,548</p>
              <p className="text-xs text-muted-foreground">+12.5% vs mois précédent</p>
            </div>
            <div className="h-[150px] w-full">
              <svg viewBox="0 0 100 30" className="w-full h-full">
                <path
                  d="M0,25 L5,23 L10,24 L15,22 L20,20 L25,18 L30,15 L35,16 L40,14 L45,13 L50,10 L55,8 L60,9 L65,7 L70,5 L75,6 L80,4 L85,3 L90,2 L95,1 L100,0"
                  fill="none"
                  stroke="hsl(var(--primary))"
                  strokeWidth="1"
                />
                <path
                  d="M0,25 L5,23 L10,24 L15,22 L20,20 L25,18 L30,15 L35,16 L40,14 L45,13 L50,10 L55,8 L60,9 L65,7 L70,5 L75,6 L80,4 L85,3 L90,2 L95,1 L100,0"
                  fill="hsl(var(--primary) / 0.1)"
                  strokeWidth="0"
                />
              </svg>
            </div>
          </TabsContent>
          <TabsContent value="engagement" className="pt-4">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <p className="text-2xl font-bold">845</p>
                <p className="text-xs text-muted-foreground">Likes</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">143</p>
                <p className="text-xs text-muted-foreground">Partages</p>
              </div>
            </div>
            <div className="h-[120px] w-full">
              <svg viewBox="0 0 100 30" className="w-full h-full">
                <rect x="0" y="5" width="10" height="20" fill="hsl(var(--primary) / 0.5)" />
                <rect x="15" y="10" width="10" height="15" fill="hsl(var(--primary) / 0.5)" />
                <rect x="30" y="8" width="10" height="17" fill="hsl(var(--primary) / 0.5)" />
                <rect x="45" y="12" width="10" height="13" fill="hsl(var(--primary) / 0.5)" />
                <rect x="60" y="7" width="10" height="18" fill="hsl(var(--primary) / 0.5)" />
                <rect x="75" y="15" width="10" height="10" fill="hsl(var(--primary) / 0.5)" />
                <rect x="90" y="9" width="10" height="16" fill="hsl(var(--primary) / 0.5)" />
              </svg>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Top Morceaux</h3>
          <div className="space-y-2">
            {[
              { title: "Cosmic Journey", plays: "5.2k" },
              { title: "Lunar Eclipse", plays: "3.8k" },
              { title: "Stellar Convergence", plays: "2.1k" },
              { title: "Quantum Waves", plays: "1.4k" },
            ].map((track, i) => (
              <div key={i} className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">{i + 1}</span>
                  <span className="text-sm">{track.title}</span>
                </div>
                <span className="text-xs text-muted-foreground">{track.plays}</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
