import React from 'react';

/**
 * ChordDiagram
 * Affiche un diagramme d'accord pour guitare, piano, etc. (support Vexchords et React-Piano)
 * TODO: Support mandoline, banjo, ukulélé (librairies à intégrer)
 */

export type ChordInstrument = 'guitar' | 'piano' | 'mandolin' | 'banjo' | 'ukulele';

export interface ChordDiagramProps {
  key?: React.Key;
  instrument: ChordInstrument;
  chord: string;
  positions?: Array<[number, number | 'x', (string | number)?]> | number[]; // For stringed instruments or MIDI notes for piano
  actualTuning?: string[]; // e.g., ['E2', 'A2', 'D3', 'G3', 'B3', 'E4'] (low to high)
  numStrings?: number;
}

import { useEffect, useRef } from 'react';
import { ChordBox } from 'vexchords';
import { Piano } from 'react-piano';
import 'react-piano/dist/styles.css';

export const ChordDiagram: React.FC<ChordDiagramProps> = ({ instrument, chord, positions, actualTuning, numStrings }) => {
  const svgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if ((instrument === 'guitar' || instrument === 'ukulele' || instrument === 'banjo' || instrument === 'mandolin') && svgRef.current && positions && positions.length > 0) {
      svgRef.current.innerHTML = ''; 
      try {
        const numActualStrings = numStrings || (instrument === 'guitar' ? 6 : instrument === 'ukulele' ? 4 : (instrument === 'banjo' ? 5 : (instrument === 'mandolin' ? 4 : 6)));
        
        let vexTuning: string[] | undefined = undefined;
        if (actualTuning) {
          vexTuning = [...actualTuning].reverse(); // Vexchords expects tuning from high to low string
        } else {
          // Default tunings (high to low for VexChords)
          if (instrument === 'guitar') vexTuning = ['E4', 'B3', 'G3', 'D3', 'A2', 'E2'];
          else if (instrument === 'ukulele') vexTuning = ['A4', 'E4', 'C4', 'G4']; // GCEA tuning
          else if (instrument === 'banjo') vexTuning = ['D4', 'B3', 'G3', 'D3', 'G2']; // Standard G tuning for 5-string banjo (gDGBD -> D B G D G)
          else if (instrument === 'mandolin') vexTuning = ['E5', 'A4', 'D4', 'G3']; // GDAE tuning
          else vexTuning = ['E4', 'B3', 'G3', 'D3', 'A2', 'E2']; // Default to guitar if unknown
        }

        const chordBox = new ChordBox(svgRef.current, {
          width: 90,
          height: 120,
          numStrings: numActualStrings,
          tuning: vexTuning,
        });
        
        const vexChordPositions = (positions as Array<[number, number | 'x', (string | number)?]>)
          .map(p => {
            const stringIndexZeroBasedFromLow = p[0]; 
            const fret = p[1];
            const finger = p[2];
            const vexStringNumOneBasedFromHigh = numActualStrings - stringIndexZeroBasedFromLow;
            if (vexStringNumOneBasedFromHigh < 1 || vexStringNumOneBasedFromHigh > numActualStrings) {
              return null; 
            }
            return [vexStringNumOneBasedFromHigh, fret, finger];
          })
          .filter(p => p !== null) as Array<[number, number | 'x', (string | number)?]>;

        if (vexChordPositions.length > 0) {
           chordBox.draw({
            chord: vexChordPositions,
           });
        } else {
           if(svgRef.current) svgRef.current.innerHTML = `<div style='font-size:0.7em;text-align:center;'>Positions<br/>invalides</div>`;
        }

        if (svgRef.current) {
          const existingLabel = svgRef.current.querySelector('.chord-label');
          if (existingLabel) existingLabel.remove();
          const label = document.createElement('div');
          label.className = 'chord-label';
          label.style.textAlign = 'center';
          label.style.fontSize = '0.8em';
          label.style.marginTop = '4px';
          label.textContent = chord;
          svgRef.current.appendChild(label);
        }
      } catch (err) {
        if (svgRef.current) svgRef.current.innerHTML = `<div style='color:red; font-size:0.7em;'>Erreur Vexchords</div>`;
        console.error('Vexchords error:', err, { instrument, chord, positions, actualTuning, numStrings });
      }
    }
  }, [instrument, chord, positions, actualTuning, numStrings]);

  if (instrument === 'guitar' || instrument === 'ukulele' || instrument === 'banjo' || instrument === 'mandolin') {
    return <div ref={svgRef} className="chord-diagram-svg" style={{ width: 90, height: 120 }} />;
  }
  if (instrument === 'piano' && positions && positions.length) {
    // Piano: React-Piano
    // positions = notes MIDI (ex: [60, 64, 67])
    return (
      <div style={{ width: 180 }}>
        <Piano
          noteRange={{ first: 48, last: 72 }}
          playNote={() => {}}
          stopNote={() => {}}
          activeNotes={positions as number[]}
          disabled
          width={180}
        />
        <div className="text-xs text-center mt-1">{chord}</div>
      </div>
    );
  }
  return <div className="chord-diagram-placeholder">[Diagramme: {chord}]</div>;
};

export default ChordDiagram;
