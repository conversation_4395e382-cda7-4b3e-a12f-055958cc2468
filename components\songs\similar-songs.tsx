"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { getSupabaseClient } from '@/lib/supabase/client';
import { Card, CardContent } from "@/components/ui/card";
import { Music2 } from 'lucide-react'; // Icon for placeholder

interface SimilarSong {
  id: string;
  title: string;
  slug: string | null;
  cover_url: string | null; 
  albums: Array<{ // Album info - Supabase join returns an array
    cover_url: string | null;
  }> | null;
  profiles: Array<{ // Artist info - Supabase join returns an array
    display_name: string | null;
    username: string | null;
    id: string; 
  }> | null;
}

interface SimilarSongsProps {
  currentSongId: string;
  currentSongGenres: string[] | null | undefined;
  currentSongArtistId: string | null | undefined;
}

export function SimilarSongs({ currentSongId, currentSongGenres, currentSongArtistId }: SimilarSongsProps) {
  const [similarSongs, setSimilarSongs] = useState<SimilarSong[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = getSupabaseClient();

  useEffect(() => {
    const fetchSimilarSongs = async () => {
      if (!currentSongId) {
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      let fetchedSongs: SimilarSong[] = [];

      // Phase 1: Songs by the same artist in the same genres (if artist and genres are available)
      if (currentSongArtistId && currentSongGenres && currentSongGenres.length > 0) {
        const { data: artistGenreSongs, error: artistGenreError } = await supabase
          .from('songs')
          .select(`
            id, title, slug, cover_url,
            albums (cover_url),
            profiles:creator_user_id (id, display_name, username)
          `)
          .eq('creator_user_id', currentSongArtistId)
          .overlaps('genres', currentSongGenres)
          .neq('id', currentSongId)
          .eq('is_public', true)
          .limit(5);
        
        if (artistGenreError) console.error("Error fetching similar songs (artist+genre):", artistGenreError);
        else if (artistGenreSongs) fetchedSongs = [...fetchedSongs, ...(artistGenreSongs as SimilarSong[])];
      }

      // Phase 2: If not enough songs, fetch songs by any artist in the same genres
      if (fetchedSongs.length < 5 && currentSongGenres && currentSongGenres.length > 0) {
        const existingIds = fetchedSongs.map(s => s.id);
        const needed = 5 - fetchedSongs.length;

        const { data: genreSongs, error: genreError } = await supabase
          .from('songs')
          .select(`
            id, title, slug, cover_url,
            albums (cover_url),
            profiles:creator_user_id (id, display_name, username)
          `)
          .overlaps('genres', currentSongGenres)
          .neq('id', currentSongId)
          // Optionally filter out songs by the same artist if already fetched, or let limit handle it
          // .not('id', 'in', `(${existingIds.join(',')})`) // Ensure we don't re-fetch
          .eq('is_public', true)
          .limit(needed);
        
        if (genreError) console.error("Error fetching similar songs (genre only):", genreError);
        else if (genreSongs) {
          // Add only new songs
          genreSongs.forEach(gs => {
            if (!fetchedSongs.find(fs => fs.id === (gs as SimilarSong).id)) {
              fetchedSongs.push(gs as SimilarSong);
            }
          });
        }
      }
      
      // Deduplicate and limit to 5, in case phases overlap or return more than needed
      const uniqueSongs = Array.from(new Map(fetchedSongs.map(song => [song.id, song])).values());
      setSimilarSongs(uniqueSongs.slice(0, 5));
      setIsLoading(false);
    };

    fetchSimilarSongs();
  }, [currentSongId, currentSongGenres, currentSongArtistId, supabase]);

  if (isLoading) {
    return (
      <div>
        <h3 className="text-xl font-semibold mb-3">Morceaux Similaires</h3>
        <p className="text-sm text-muted-foreground">Chargement...</p>
      </div>
    );
  }

  if (similarSongs.length === 0) {
    return (
       <div>
        <h3 className="text-xl font-semibold mb-3">Morceaux Similaires</h3>
        <p className="text-sm text-muted-foreground">Aucun morceau similaire trouvé.</p>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-xl font-semibold mb-4">Morceaux Similaires</h3>
      <div className="space-y-3">
        {similarSongs.map(song => {
          const albumData = song.albums && song.albums.length > 0 ? song.albums[0] : null;
          const profileData = song.profiles && song.profiles.length > 0 ? song.profiles[0] : null;
          const coverArt = song.cover_url || albumData?.cover_url;
          const artistName = profileData?.display_name || profileData?.username || 'Artiste inconnu';
          return (
            <Link key={song.id} href={song.slug ? `/song/${song.slug}` : `/songs/${song.id}`} className="block group">
              <Card className="hover:bg-muted/50 transition-colors">
                <CardContent className="p-3 flex items-center gap-3">
                  <div className="w-12 h-12 relative flex-shrink-0 bg-muted rounded">
                    {coverArt ? (
                      <Image src={coverArt} alt={song.title} layout="fill" objectFit="cover" className="rounded" />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Music2 className="w-6 h-6 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <div className="min-w-0">
                    <p className="text-sm font-semibold truncate group-hover:underline" title={song.title}>{song.title}</p>
                    <p className="text-xs text-muted-foreground truncate" title={artistName}>{artistName}</p>
                  </div>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
