"use client"

import { useEffect, useRef } from "react"

interface WaveformVisualizerProps {
  audioUrl: string
}

export function WaveformVisualizer({ audioUrl }: WaveformVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    if (!canvasRef.current || !audioUrl) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Définir les dimensions du canvas
    const dpr = window.devicePixelRatio || 1
    const rect = canvas.getBoundingClientRect()
    canvas.width = rect.width * dpr
    canvas.height = rect.height * dpr
    ctx.scale(dpr, dpr)

    // Créer un contexte audio
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    const analyser = audioContext.createAnalyser()
    analyser.fftSize = 256
    const bufferLength = analyser.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)

    // Charger l'audio
    const audio = new Audio(audioUrl)
    audio.crossOrigin = "anonymous"
    const source = audioContext.createMediaElementSource(audio)
    source.connect(analyser)
    analyser.connect(audioContext.destination)

    // Jouer l'audio pour générer la forme d'onde
    audio.play()
    audio.volume = 0.01 // Volume très bas pour ne pas déranger l'utilisateur

    // Dessiner la forme d'onde
    let animationFrameId: number
    const draw = () => {
      animationFrameId = requestAnimationFrame(draw)

      analyser.getByteFrequencyData(dataArray)

      // Clear the canvas for a transparent/classy background
      ctx.clearRect(0, 0, rect.width, rect.height)

      // Optionally, add a subtle shadow or border effect
      // ctx.save()
      // ctx.shadowColor = 'rgba(0,0,0,0.15)'
      // ctx.shadowBlur = 8
      // ctx.restore()

      const barWidth = (rect.width / bufferLength) * 2.5
      let x = 0

      for (let i = 0; i < bufferLength; i++) {
        const barHeight = (dataArray[i] / 255) * rect.height
        // Use a modern blue gradient for the bars
        ctx.fillStyle = `linear-gradient(180deg, #38bdf8 0%, #0ea5e9 100%)` // fallback in case gradient doesn't work
        ctx.fillStyle = `rgba(56,189,248,0.8)` // cyan-400, fallback
        ctx.fillRect(x, rect.height - barHeight, barWidth, barHeight)
        x += barWidth + 1
      }
    }

    draw()

    // Do NOT stop after 2 seconds; keep waveform visible
    // Clean up on unmount
    return () => {
      cancelAnimationFrame(animationFrameId)
      audio.pause()
      audioContext.close()
    }
  }, [audioUrl])

  // Add modern CSS for transparency, border-radius, and shadow
  return <canvas ref={canvasRef} className="w-full h-full bg-transparent rounded-lg shadow-md" />
}
