CREATE TABLE public.audience_demographics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  age_group TEXT,
  gender TEXT,
  country TEXT,
  device_type TEXT,
  count INTEGER NOT NULL DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(artist_id, age_group, gender, country, device_type)
);

CREATE INDEX audience_demographics_artist_id_idx ON public.audience_demographics(artist_id);
