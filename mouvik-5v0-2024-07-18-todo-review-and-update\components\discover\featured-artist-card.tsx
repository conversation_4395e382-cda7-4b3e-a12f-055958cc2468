import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface FeaturedArtistCardProps {
  artist: any
}

export function FeaturedArtistCard({ artist }: FeaturedArtistCardProps) {
  const songCount = artist.songs?.length || 0
  const initials =
    artist.name
      ?.split(" ")
      .map((n: string) => n[0])
      .join("") || "U"

  return (
    <Link href={`/artists/${artist.id}`} className="flex flex-col items-center group">
      <Avatar className="h-24 w-24 mb-2 group-hover:ring-2 ring-primary transition-all">
        <AvatarImage
          src={artist.avatar_url || "/placeholder.svg?height=96&width=96&query=musician"}
          alt={artist.name}
        />
        <AvatarFallback>{initials}</AvatarFallback>
      </Avatar>
      <h3 className="font-medium text-center">{artist.name || "Artiste"}</h3>
      <p className="text-sm text-muted-foreground text-center">
        {artist.type === "group" ? "Groupe" : "Artiste"} • {songCount} titres
      </p>
      <Button variant="outline" size="sm" className="mt-2">
        Suivre
      </Button>
    </Link>
  )
}
