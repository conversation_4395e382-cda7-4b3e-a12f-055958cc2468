"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Card, CardContent } from "@/components/ui/card"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Music, Eye, Heart, Users, TrendingUp, TrendingDown } from "lucide-react"

interface AnalyticsOverviewProps {
  userId: string;
  timeRange: '7d' | '30d' | '90d' | '1y' | 'all';
}

// This interface should match the JSON structure returned by the RPC 'get_user_overview_stats'
// This interface should match the JSON structure returned by the RPC 'get_user_overview_stats'
interface OverviewStats {
  total_plays: number;
  total_songs: number;
  total_albums: number;
  total_followers: number;
  total_views: number;
  total_likes: number;
  total_comments: number;
  total_duration_seconds: number;
  // Fields for percentage change and engagement are not currently returned by the RPC
  // plays_change?: number; 
  // unique_listeners?: number; 
  // listeners_change?: number;
  // comments_change?: number;
  // likes_change?: number;
  // engagement_rate?: number;
  // engagement_change?: number;
}

// This interface should match the structure of items in 'daily_metrics' array from RPC
interface ChartData {
  date: string; // 'YYYY-MM-DD' format from RPC
  plays: number;
  likes: number;
  comments: number;
}

export function AnalyticsOverview({ userId, timeRange }: AnalyticsOverviewProps) {
  const [stats, setStats] = useState<OverviewStats | null>(null);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      if (!userId) {
        setIsLoading(false);
        setError("User ID is not available.");
        return;
      }
      setIsLoading(true);
      setError(null);
      const supabase = createBrowserClient();
      
      const { data: rpcData, error: rpcError } = await supabase.rpc('get_user_overview_stats', {
        p_user_id: userId,
        p_time_range: timeRange
      });

      if (rpcError) {
        console.error('Error fetching overview stats:', rpcError);
        setError(rpcError.message);
        setStats(null);
        setChartData([]);
      } else if (rpcData && rpcData.length > 0) {
        const result = rpcData[0]; // RPC returns an array with one object
        const overviewData = result; // result is the single object from rpcData[0]
        setStats(overviewData as OverviewStats);
        // daily_metrics are not currently returned by get_user_overview_stats
        // If another RPC provides them, fetch and setChartData here.
        setChartData([]); // Assuming no daily_metrics for now
      } else if (rpcData && rpcData.length === 0) {
        // Handle case where RPC returns empty array (e.g., no data for user/timerange)
        console.warn('get_user_overview_stats returned no data.');
        setStats(null); // Or set to default zeroed stats
        setChartData([]);
      }
      setIsLoading(false);
    };
    
    fetchStats();
  }, [userId, timeRange]);

  // Add an error display state
  if (error) {
    return (
      <Card className="mb-8">
        <CardContent className="p-6">
          <p className="text-red-500">Erreur lors du chargement des statistiques d'aperçu: {error}</p>
        </CardContent>
      </Card>
    );
  }
  
  const formatNumber = (num?: number | null): string => {
    if (num == null) return '0'; // Handles undefined or null, displays '0'
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k';
    return num.toString();
  }
  
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return new Intl.DateTimeFormat('fr-FR', { 
      day: 'numeric', 
      month: 'short'
    }).format(date)
  }
  
  if (isLoading || !stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 w-24 bg-muted rounded mb-4"></div>
              <div className="h-8 w-16 bg-muted rounded mb-2"></div>
              <div className="h-4 w-32 bg-muted rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }
  
  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <StatCard 
          title="Écoutes totales" 
          value={formatNumber(stats.total_plays)} 
          // change={stats.plays_change ?? 0} // No change data available 
          icon={<Music className="h-5 w-5" />} 
          color="cyan" 
        />
        <StatCard 
          title="Total Abonnés" 
          value={formatNumber(stats.total_followers)} 
          // change={0} // No change data available
          icon={<Users className="h-5 w-5" />} 
          color="indigo" 
        />
        <StatCard 
          title="Likes" 
          value={formatNumber(stats.total_likes)} 
          // change={stats.likes_change ?? 0} // No change data available 
          icon={<Heart className="h-5 w-5" />} 
          color="pink" 
        />
        <StatCard 
          title="Total Vues" 
          value={formatNumber(stats.total_views)} 
          // change={0} // No change data available
          icon={<Eye className="h-5 w-5" />} 
          color="amber" 
        />
      </div>
      
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <div>
              <h3 className="text-lg font-medium">Tendances d'activité</h3>
              <p className="text-sm text-muted-foreground">Évolution sur la période sélectionnée</p>
            </div>
            <div className="flex items-center gap-4 mt-2 sm:mt-0">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-cyan-500"></div>
                <span className="text-xs text-muted-foreground">Écoutes</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-pink-500"></div>
                <span className="text-xs text-muted-foreground">Likes</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                <span className="text-xs text-muted-foreground">Commentaires</span>
              </div>
            </div>
          </div>
          
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={formatDate} 
                  interval={timeRange === '7d' ? 0 : 'preserveEnd'} 
                  minTickGap={30}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(label) => formatDate(label)}
                  formatter={(value, name) => [
                    value, 
                    name === 'plays' ? 'Écoutes' : 
                    name === 'likes' ? 'Likes' : 
                    'Commentaires'
                  ]}
                />
                <Line 
                  type="monotone" 
                  dataKey="plays" 
                  stroke="#06b6d4" 
                  strokeWidth={2} 
                  dot={false} 
                  activeDot={{ r: 6 }} 
                />
                <Line 
                  type="monotone" 
                  dataKey="likes" 
                  stroke="#ec4899" 
                  strokeWidth={2} 
                  dot={false} 
                  activeDot={{ r: 6 }} 
                />
                <Line 
                  type="monotone" 
                  dataKey="comments" 
                  stroke="#f59e0b" 
                  strokeWidth={2} 
                  dot={false} 
                  activeDot={{ r: 6 }} 
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

interface StatCardProps {
  title: string;
  value: string;
  change?: number;
  icon: React.ReactNode;
  color: 'cyan' | 'indigo' | 'pink' | 'amber';
}

function StatCard({ title, value, change, icon, color }: StatCardProps) {
  const colorMap = {
    cyan: {
      bg: 'bg-cyan-50 dark:bg-cyan-950',
      text: 'text-cyan-600 dark:text-cyan-400',
      icon: 'text-cyan-500'
    },
    indigo: {
      bg: 'bg-indigo-50 dark:bg-indigo-950',
      text: 'text-indigo-600 dark:text-indigo-400',
      icon: 'text-indigo-500'
    },
    pink: {
      bg: 'bg-pink-50 dark:bg-pink-950',
      text: 'text-pink-600 dark:text-pink-400',
      icon: 'text-pink-500'
    },
    amber: {
      bg: 'bg-amber-50 dark:bg-amber-950',
      text: 'text-amber-600 dark:text-amber-400',
      icon: 'text-amber-500'
    }
  }
  
  const changeIsDefined = typeof change === 'number';
  const changeColor = changeIsDefined && change >= 0 ? 'text-green-500' : 'text-red-500';
  const ChangeIcon = changeIsDefined && change >= 0 ? TrendingUp : TrendingDown;
  
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">{title}</p>
            <h3 className="text-2xl font-bold">{value}</h3>
          </div>
          <div className={`p-2 rounded-full ${colorMap[color].bg}`}>
            <div className={colorMap[color].icon}>{icon}</div>
          </div>
        </div>
        {changeIsDefined && (
          <div className="mt-4 flex items-center">
            <ChangeIcon className="h-4 w-4 mr-1" />
            <span className={changeColor}>
              {change >= 0 ? '+' : ''}{change.toFixed(1)}%
            </span>
            <span className="text-xs text-muted-foreground ml-1">vs période précédente</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
