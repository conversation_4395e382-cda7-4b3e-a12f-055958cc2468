import React, { useRef, useEffect, useState } from 'react';
import { Play, Pause, AlertCircle } from 'lucide-react'; // Added AlertCircle for error display

interface AudioSliderPlayerProps {
  file?: File; // Make file optional
  audioSrc?: string; // Add audioSrc for direct URLs
  isPlaying: boolean;
  error?: string;
  onPlayPause: () => void;
  color?: string;
}

export const AudioSliderPlayer: React.FC<AudioSliderPlayerProps> = ({ file, audioSrc, isPlaying, error, onPlayPause, color }) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [sourceUrl, setSourceUrl] = useState<string | null>(null);
  const [internalPlaying, setInternalPlaying] = useState(false);
  const [internalError, setInternalError] = useState<string | null>(error || null);

  useEffect(() => {
    setInternalError(error || null); // Sync external error prop
  }, [error]);

  useEffect(() => {
    let objectUrl: string | null = null;
    if (file) {
      objectUrl = URL.createObjectURL(file);
      setSourceUrl(objectUrl);
      setInternalError(null); // Clear previous errors if new file is provided
    } else if (audioSrc) {
      setSourceUrl(audioSrc);
      setInternalError(null); // Clear previous errors if new src is provided
    } else {
      setSourceUrl(null);
      // Don't set an error here, let the parent component decide if no source is an error
    }

    return () => {
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [file, audioSrc]);

  // Sync React -> audio (play/pause)
  useEffect(() => {
    if (!audioRef.current) return;
    if (isPlaying) {
      audioRef.current.play();
      setInternalPlaying(true);
    } else {
      audioRef.current.pause();
      setInternalPlaying(false);
    }
  }, [isPlaying]);

  // Sync audio -> React (slider, fin de lecture)
  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
      setInternalPlaying(!audioRef.current.paused && !audioRef.current.ended);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
      setInternalError(null); // Clear error on successful load
    }
  };

  const handleAudioError = (e: React.SyntheticEvent<HTMLAudioElement, Event>) => {
    console.error("Audio Element Error:", e);
    setInternalError("Erreur de lecture audio.");
    setInternalPlaying(false);
    // if (isPlaying) onPlayPause(); // Optionally notify parent to reset play state
  };

  const handleEnded = () => {
    setInternalPlaying(false);
    setCurrentTime(0); // Reset time to beginning
    if (isPlaying) { // If it was playing, call onPlayPause to toggle parent state
        onPlayPause(); 
    }
  };

  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = Number(e.target.value);
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const formatTime = (t: number) => {
    const min = Math.floor(t / 60);
    const sec = Math.floor(t % 60).toString().padStart(2, '0');
    return `${min}:${sec}`;
  };

  return (
    <div className="flex items-center gap-2 w-full group">
      <button
        type="button"
        className={`rounded-full p-1 border hover:bg-muted flex items-center justify-center 
                    ${internalError ? 'border-destructive text-destructive' : 'border-muted-foreground'}`}
        onClick={onPlayPause}
        disabled={!!internalError || !sourceUrl}
        style={{ width: 28, height: 28 }}
        aria-label={isPlaying && internalPlaying ? "Pause" : "Play"}
      >
        {internalError ? <AlertCircle className="h-4 w-4" /> : (isPlaying && internalPlaying) ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
      </button>
      <input
        type="range"
        min={0}
        max={duration || 0} // Use 0 if duration is not yet known
        value={currentTime}
        onChange={handleSliderChange}
        className={`flex-1 h-1 rounded-full appearance-none cursor-pointer ${internalError || !sourceUrl ? 'bg-muted' : 'bg-muted group-hover:bg-muted/80'}`}
        disabled={!!internalError || !sourceUrl}
        style={{ 
          minWidth: 50,
          accentColor: internalError ? 'hsl(var(--destructive))' : color || 'hsl(var(--primary))',
          // Custom track styling might be needed for full color control if accent-color is not enough
          // This is a basic approach. For a fully colored track based on `color` prop,
          // you might need a more complex slider or CSS variables.
        }}
      />
      <span className={`text-xs tabular-nums w-12 text-right ${internalError ? 'text-destructive' : 'text-muted-foreground'}`}>
        {internalError ? "Erreur" : `${formatTime(currentTime)} / ${formatTime(duration)}`}
      </span>
      {sourceUrl && (
        <audio
          ref={audioRef}
          src={sourceUrl}
          onLoadedMetadata={handleLoadedMetadata}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleEnded}
          onError={handleAudioError}
          controls={false}
          style={{ display: 'none' }}
          preload="metadata"
        />
      )}
    </div>
  );
}


// Génère une couleur pseudo-aléatoire à partir d'une string
export function randomColorFromString(str: string) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) hash = str.charCodeAt(i) + ((hash << 5) - hash);
  const h = Math.abs(hash) % 360;
  return `hsl(${h},70%,60%)`;
}
