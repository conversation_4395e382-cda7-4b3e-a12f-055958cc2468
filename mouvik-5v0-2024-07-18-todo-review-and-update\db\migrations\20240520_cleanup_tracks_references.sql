-- Migration pour nettoyer les références restantes à 'tracks' et standardiser sur 'songs'
-- C<PERSON><PERSON> le: 2024-05-20
-- Auteur: <PERSON><PERSON><PERSON>

-- Début de la transaction
BEGIN;

-- 1. Supprimer la vue de compatibilité si elle existe
DROP VIEW IF EXISTS public.tracks;

-- 2. Mettre à jour les fonctions du tableau de bord
-- Note: Ces mises à jour doivent être effectuées dans les fichiers de fonctions respectifs
-- car ils sont gérés séparément

-- 3. Vérifier et supprimer les contraintes ou index obsolètes
DO $$
BEGIN
    -- Supprimer les index liés à tracks s'ils existent
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tracks_project_id') THEN
        DROP INDEX idx_tracks_project_id;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_tracks_created_at') THEN
        DROP INDEX idx_tracks_created_at;
    END IF;
END
$$;

-- 4. Mettre à jour les vues qui pourraient référencer tracks
-- Exemple: recréer la vue project_songs_view si nécessaire
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.views WHERE table_schema = 'public' AND table_name = 'project_songs_view') THEN
        DROP VIEW public.project_songs_view;
        CREATE VIEW public.project_songs_view AS 
        SELECT s.*, p.name as project_name
        FROM public.songs s
        JOIN public.projects p ON s.project_id = p.id;
    END IF;
END
$$;

-- Fin de la transaction
COMMIT;

-- Instructions post-migration:
-- 1. Tester soigneusement toutes les fonctionnalités liées aux chansons
-- 2. Vérifier que toutes les requêtes fonctionnent comme prévu
-- 3. Mettre à jour la documentation pour refléter les changements
