CREATE TABLE public.audio_analysis (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  song_id UUID REFERENCES public.songs(id) ON DELETE CASCADE UNIQUE NOT NULL,
  energy FLOAT CHECK (energy BETWEEN 0 AND 1),
  danceability FLOAT CHECK (danceability BETWEEN 0 AND 1),
  valence FLOAT CHECK (valence BETWEEN 0 AND 1),
  acousticness FLOAT CHECK (acousticness BETWEEN 0 AND 1),
  instrumentalness FLOAT CHECK (instrumentalness BETWEEN 0 AND 1),
  speechiness FLOAT CHECK (speechiness BETWEEN 0 AND 1),
  liveness FLOAT CHECK (liveness BETWEEN 0 AND 1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX audio_analysis_song_id_idx ON public.audio_analysis(song_id);

COMMENT ON TABLE public.audio_analysis IS 'Stores audio characteristics of songs for analysis and visualization.';
COMMENT ON COLUMN public.audio_analysis.song_id IS 'Reference to the song (foreign key to public.songs(id)). Must be unique as each song has one set of audio features.';
