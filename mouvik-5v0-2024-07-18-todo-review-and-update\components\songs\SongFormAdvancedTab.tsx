import { Control, FieldErrors } from 'react-hook-form';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { RHFSelect, RHFSwitch, RHFTextarea } from '@/components/hook-form'; 
import { SelectItem } from '@/components/ui/select';
import { SongFormValues } from './song-schema';
import { SONG_STATUS_OPTIONS, ATTRIBUTION_TYPE_OPTIONS } from './song-options'; // Assuming these are now in song-options

interface SongFormAdvancedTabProps {
  control: Control<SongFormValues>;
  errors: FieldErrors<SongFormValues>;
  // Add any other props needed from the parent SongForm
}

export const SongFormAdvancedTab: React.FC<SongFormAdvancedTabProps> = ({ 
  control,
  // errors 
}) => {
  return (
    <Card>
      <CardHeader><CardTitle>Paramètres Avancés</CardTitle></CardHeader>
      <CardContent className="space-y-6">
        <RHFSelect name="visibility" label="Visibilité" control={control}>
          <SelectItem value="public">Public</SelectItem>
          <SelectItem value="unlisted">Non répertorié</SelectItem>
          <SelectItem value="private">Privé</SelectItem>
          {/* <SelectItem value="band_only">Membres du groupe seulement</SelectItem> */}
        </RHFSelect>

        <RHFSelect name="status" label="Statut du morceau" control={control}>
          {SONG_STATUS_OPTIONS && SONG_STATUS_OPTIONS.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
        </RHFSelect>

        <RHFSwitch name="is_explicit" label="Contenu explicite" control={control} />
        <RHFSwitch name="is_archived" label="Archiver le morceau" control={control} description="Les morceaux archivés n'apparaissent pas dans les listes publiques." />
        
        <div className="space-y-3 pt-4 border-t">
          <h4 className="text-md font-medium">Permissions</h4>
          <RHFSwitch name="allow_comments" label="Autoriser les commentaires" control={control} />
          <RHFSwitch name="allow_downloads" label="Autoriser les téléchargements directs" control={control} />
        </div>

        <RHFSelect name="attribution_type" label="Type d'attribution / Licence" control={control}>
          {ATTRIBUTION_TYPE_OPTIONS && ATTRIBUTION_TYPE_OPTIONS.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
        </RHFSelect>

        <RHFTextarea
          name="custom_css"
          control={control}
          label="CSS Personnalisé"
          placeholder="Ajoutez votre CSS personnalisé ici..."
          rows={10}
        />
        {/* Placeholder for other advanced fields if any */}
        {/* <RHFTextField name="slug" label="Slug URL (Optionnel)" control={control} description="Personnalisez le lien du morceau. Laissez vide pour auto-génération."/> */}
        {/* <RHFTextField name="project_id" label="ID Projet (Optionnel)" control={control} /> */}
      </CardContent>
    </Card>
  );
};