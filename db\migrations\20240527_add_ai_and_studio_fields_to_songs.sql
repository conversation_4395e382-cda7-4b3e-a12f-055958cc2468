-- Migration: Ajout des champs IA, studio et nettoyage crédits à la table songs
-- Date: 2024-05-27

BEGIN;

-- Enum pour l'origine de création (si non existant déjà)
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ai_content_origin_enum') THEN
        CREATE TYPE public.ai_content_origin_enum AS ENUM ('100%_ia', 'hybrid', 'full_human');
    END IF;
END $$;

ALTER TABLE public.songs
    ADD COLUMN IF NOT EXISTS ai_content_origin public.ai_content_origin_enum,
    ADD COLUMN IF NOT EXISTS ai_assist_percentage INTEGER CHECK (ai_assist_percentage >= 0 AND ai_assist_percentage <= 100),
    ADD COLUMN IF NOT EXISTS studio_name TEXT;

-- Optionnel: supprimer les champs JSON inutiles
-- ALTER TABLE public.songs DROP COLUMN IF EXISTS credits;
-- ALTER TABLE public.songs DROP COLUMN IF EXISTS editor_data;

COMMIT;
