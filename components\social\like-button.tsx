"use client"

import { useState, useEffect } from "react"
import { Heart } from "lucide-react"
import { Button, type ButtonProps } from '@/components/ui/button';
import { useToast } from "@/hooks/use-toast"
import { useResourceInteractionStore, getResourceInteractionStoreState, type ResourceInteractionStoreState, type ToggleLikeResult, type ResourceType, DEFAULT_RESOURCE_STATE, getResourceKey } from "@/lib/stores/resource-interaction-store";

import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';

interface LikeButtonProps extends Omit<ButtonProps, 'onClick'> {
  resourceId: string;
  resourceType: ResourceType;
  initialLikes?: number;
  initialIsLiked?: boolean;
  userId?: string;
  onLikeToggle?: (liked: boolean, newLikeCount: number, newDislikeCount?: number) => void;
  size?: ButtonProps['size'];
  variant?: ButtonProps['variant'];
  className?: string;
}

export function LikeButton({ 
  resourceId, 
  resourceType, 
  initialLikes,
  initialIsLiked,
  userId,
  onLikeToggle,     
  size = "default", 
  variant = "ghost", 
  className, 
  ...rest 
}: LikeButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user: currentUser } = useUser(); // Get current user

  const storeActions = getResourceInteractionStoreState();
  const {
    isLiked: displayIsLiked, 
    likeCount: displayLikeCount 
  } = useResourceInteractionStore(
    (state: ResourceInteractionStoreState) => state.resourceStates[getResourceKey(resourceType, resourceId)] || DEFAULT_RESOURCE_STATE
  );



  const handleLike = async () => {
    if (!currentUser) {
      toast({ title: "Connexion requise", description: "Vous devez être connecté pour liker.", variant: "default" });
      return;
    }
    if (isLoading) return;
    setIsLoading(true);

    const actualApiCall = async (): Promise<ToggleLikeResult> => {
      if (!currentUser) throw new Error('User not authenticated for API call'); // Should be caught by earlier check
      const supabase = getSupabaseClient();
      const { data, error } = await supabase.rpc('toggle_like', { p_resource_id: resourceId, p_resource_type: resourceType, p_user_id: currentUser.id });
      if (error) {
        console.error('Error toggling like:', error);
        throw error;
      }
      // Assuming the RPC returns an object compatible with LikeDislikeApiResult
      // { new_like_count, new_is_liked, new_is_disliked, new_dislike_count }
      // We need to map these snake_case fields to camelCase for LikeDislikeApiResult
      // For songs, the RPC might return dislike info too
      if (resourceType === 'song') {
        return {
          newLikeCount: data.new_like_count,
          newIsLiked: data.new_is_liked,
          newDislikeCount: data.new_dislike_count, // Assuming RPC returns this for songs
          newIsDisliked: data.new_is_disliked,   // Assuming RPC returns this for songs
        };
      } else {
        // For albums/playlists, expect only like info
        return {
          newLikeCount: data.new_like_count,
          newIsLiked: data.new_is_liked,
        };
      }
    };

    try {
      await storeActions.toggleLike(resourceType, resourceId, actualApiCall);
    } catch (error: any) {
      // Error handling is mostly done within the store (reversion)
      // Toast for unexpected errors or if API call itself fails before store logic
      if (!error.message?.includes("Supabase RPC Error")) { // Avoid double toasting if store already handled it
        toast({
          title: "Erreur",
          description: error.message || `Une erreur est survenue en likant ${resourceType}.`,
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Size classes are handled by the underlying Button component if size prop is passed directly.
  // No need for sizeClasses map here if we pass the size prop directly to Button.

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleLike}
      disabled={isLoading || !currentUser} 
      className={`transition-colors ${displayIsLiked ? 'text-red-500 border-red-500 hover:bg-red-500/10' : ''} ${className || ''}`} 
      aria-pressed={displayIsLiked}
      {...rest}
    >
      <Heart className={`mr-2 h-4 w-4 ${displayIsLiked ? "fill-current" : ""}`} /> 
      {displayLikeCount} 
    </Button>
  )
}
