"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Music, Library, ListMusic, BarChart2 } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

interface TopContentProps {
  userId: string
  timeRange: '7d' | '30d' | '90d' | '1y' | 'all'
}

interface TopSong {
  id: string
  title: string
  plays: number
  likes: number // Ajouté pour cohérence, même si la RPC simulée ne l'utilise pas
  cover_url: string | null
  artist_name: string | null // Peut être le display_name du user
}

interface TopAlbum {
  id: string
  title: string
  plays: number // Somme des plays des chansons de l'album
  likes: number // Somme des likes des chansons de l'album
  cover_url: string | null
  song_count: number
}

interface TopPlaylist {
  id: string
  title: string
  plays: number // Somme des plays des chansons de la playlist
  likes: number // Somme des likes des chansons de la playlist
  cover_url: string | null
  song_count: number
}

export function TopContent({ userId, timeRange }: TopContentProps) {
  const [topSongs, setTopSongs] = useState<TopSong[]>([])
  const [topAlbums, setTopAlbums] = useState<TopAlbum[]>([])
  const [topPlaylists, setTopPlaylists] = useState<TopPlaylist[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchTopContent = async () => {
      if (!userId) {
        setIsLoading(false);
        return;
      }
      try {
        setIsLoading(true)
        setError(null)
        const supabase = createBrowserClient()
        
        // Appels RPC réels
        const songsPromise = supabase.rpc('get_top_songs_by_user', {
          p_user_id: userId,
          p_time_range: timeRange,
          p_limit: 5
        })
        
        const albumsPromise = supabase.rpc('get_top_albums_by_user', {
          p_user_id: userId,
          p_time_range: timeRange,
          p_limit: 5
        })

        const playlistsPromise = supabase.rpc('get_top_playlists_by_user', {
          p_user_id: userId,
          p_time_range: timeRange,
          p_limit: 5
        })

        const [
          { data: songsData, error: songsError },
          { data: albumsData, error: albumsError },
          { data: playlistsData, error: playlistsError }
        ] = await Promise.all([songsPromise, albumsPromise, playlistsPromise]);

        if (songsError) {
          console.error("Erreur RPC get_top_songs_by_user:", songsError);
          // Ne pas jeter d'erreur ici pour permettre aux autres de charger
          setError(prev => prev ? `${prev}\nErreur chansons: ${songsError.message}` : `Erreur chansons: ${songsError.message}`);
        } else {
          setTopSongs(songsData || []);
        }
        
        if (albumsError) {
          console.error("Erreur RPC get_top_albums_by_user:", albumsError);
          setError(prev => prev ? `${prev}\nErreur albums: ${albumsError.message}` : `Erreur albums: ${albumsError.message}`);
        } else {
          setTopAlbums(albumsData || []);
        }

        if (playlistsError) {
          console.error("Erreur RPC get_top_playlists_by_user:", playlistsError);
          setError(prev => prev ? `${prev}\nErreur playlists: ${playlistsError.message}` : `Erreur playlists: ${playlistsError.message}`);
        } else {
          setTopPlaylists(playlistsData || []);
        }

      } catch (err) {
        console.error("Erreur lors de la récupération du contenu populaire:", err)
        setError(err instanceof Error ? err.message : "Une erreur globale est survenue")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchTopContent()
  }, [userId, timeRange])
  
  const formatNumber = (num?: number): string => {
    if (num === undefined || num === null || isNaN(num)) return '-';
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k';
    return num.toString();
  }
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Contenu populaire</CardTitle>
          <CardDescription>Chargement des données...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    )
  }
  
  if (error && (!topSongs.length && !topAlbums.length && !topPlaylists.length)) { // Afficher l'erreur seulement si tout a échoué
    return (
      <Card>
        <CardHeader>
          <CardTitle>Contenu populaire</CardTitle>
          <CardDescription>Erreur lors du chargement des données</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-destructive whitespace-pre-line">{error}</p>
          <p className="text-destructive mt-2">Veuillez vérifier que les fonctions RPC 'get_top_songs_by_user', 'get_top_albums_by_user', et 'get_top_playlists_by_user' existent et fonctionnent.</p>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <BarChart2 className="h-4 w-4 mr-2" />
          Contenu populaire
        </CardTitle>
        <CardDescription>Votre contenu le plus performant sur la période sélectionnée</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="songs" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="songs" className="flex items-center">
              <Music className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Morceaux</span>
            </TabsTrigger>
            <TabsTrigger value="albums" className="flex items-center">
              <Library className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Albums</span>
            </TabsTrigger>
            <TabsTrigger value="playlists" className="flex items-center">
              <ListMusic className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Playlists</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="songs">
            <div className="space-y-4">
              {topSongs.map((song, index) => (
                <div key={song.id} className="flex items-center gap-4">
                  <div className="flex-shrink-0 w-10 text-center font-medium text-muted-foreground">
                    #{index + 1}
                  </div>
                  <div className="flex-shrink-0 w-12 h-12 bg-muted rounded-md overflow-hidden">
                    {song.cover_url ? (
                      <Image 
                        src={song.cover_url} 
                        alt={song.title} 
                        width={48} 
                        height={48} 
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-primary/10">
                        <Music className="h-6 w-6 text-primary/40" />
                      </div>
                    )}
                  </div>
                  <div className="flex-grow min-w-0">
                    <Link 
                      href={`/songs/${song.id}`} 
                      className="font-medium hover:underline truncate block"
                    >
                      {song.title}
                    </Link>
                    <p className="text-sm text-muted-foreground truncate">
                      {song.artist_name || "Artiste inconnu"}
                    </p>
                  </div>
                  <div className="flex-shrink-0 text-right">
                    <p className="font-medium">{formatNumber(song.plays)}</p>
                    <p className="text-sm text-muted-foreground">écoutes</p>
                  </div>
                </div>
              ))}
              
              {topSongs.length === 0 && !isLoading && (
                <p className="text-center text-muted-foreground py-8">
                  Aucun morceau trouvé pour cette période
                </p>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="albums">
            <div className="space-y-4">
              {topAlbums.map((album, index) => (
                <div key={album.id} className="flex items-center gap-4">
                  <div className="flex-shrink-0 w-10 text-center font-medium text-muted-foreground">
                    #{index + 1}
                  </div>
                  <div className="flex-shrink-0 w-12 h-12 bg-muted rounded-md overflow-hidden">
                    {album.cover_url ? (
                      <Image 
                        src={album.cover_url} 
                        alt={album.title} 
                        width={48} 
                        height={48} 
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-primary/10">
                        <Library className="h-6 w-6 text-primary/40" />
                      </div>
                    )}
                  </div>
                  <div className="flex-grow min-w-0">
                    <Link 
                      href={`/albums/${album.id}`} 
                      className="font-medium hover:underline truncate block"
                    >
                      {album.title}
                    </Link>
                    <p className="text-sm text-muted-foreground truncate">
                      {album.song_count} morceau{album.song_count > 1 ? 'x' : ''}
                    </p>
                  </div>
                  <div className="flex-shrink-0 text-right">
                    <p className="font-medium">{formatNumber(album.plays)}</p>
                    <p className="text-sm text-muted-foreground">écoutes</p>
                  </div>
                </div>
              ))}
              
              {topAlbums.length === 0 && !isLoading && (
                <p className="text-center text-muted-foreground py-8">
                  Aucun album trouvé pour cette période
                </p>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="playlists">
            <div className="space-y-4">
              {topPlaylists.map((playlist, index) => (
                <div key={playlist.id} className="flex items-center gap-4">
                  <div className="flex-shrink-0 w-10 text-center font-medium text-muted-foreground">
                    #{index + 1}
                  </div>
                  <div className="flex-shrink-0 w-12 h-12 bg-muted rounded-md overflow-hidden">
                    {playlist.cover_url ? (
                      <Image 
                        src={playlist.cover_url} 
                        alt={playlist.title} 
                        width={48} 
                        height={48} 
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-primary/10">
                        <ListMusic className="h-6 w-6 text-primary/40" />
                      </div>
                    )}
                  </div>
                  <div className="flex-grow min-w-0">
                    <Link 
                      href={`/playlists/${playlist.id}`} 
                      className="font-medium hover:underline truncate block"
                    >
                      {playlist.title}
                    </Link>
                    <p className="text-sm text-muted-foreground truncate">
                      {playlist.song_count} morceau{playlist.song_count > 1 ? 'x' : ''}
                    </p>
                  </div>
                  <div className="flex-shrink-0 text-right">
                    <p className="font-medium">{formatNumber(playlist.plays)}</p>
                    <p className="text-sm text-muted-foreground">écoutes</p>
                  </div>
                </div>
              ))}
              
              {topPlaylists.length === 0 && !isLoading && (
                <p className="text-center text-muted-foreground py-8">
                  Aucune playlist trouvée pour cette période
                </p>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
