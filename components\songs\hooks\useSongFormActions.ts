// components/songs/hooks/useSongFormActions.ts
import { useState, useCallback } from 'react';
import { type UseFormReturn, type FieldErrors } from 'react-hook-form';
import { type SongFormValues } from '../song-schema'; // Adjust path as necessary
import { toast } from 'sonner';

interface UseSongFormActionsProps {
  onFormSubmit: (data: SongFormValues, coverArtFile?: File | null, audioFile?: File | null) => Promise<void>;
  initialIsSubmitting?: boolean;
}

interface UseSongFormActionsReturn {
  isProcessingSubmit: boolean;
  handleFormSubmit: (data: SongFormValues, coverArtFile?: File | null, audioFile?: File | null) => Promise<void>;
  handleFormError: (errors: FieldErrors<SongFormValues>) => void;
}

export function useSongFormActions({
  onFormSubmit,
  initialIsSubmitting = false,
}: UseSongFormActionsProps): UseSongFormActionsReturn {
  const [isProcessingSubmit, setIsProcessingSubmit] = useState<boolean>(initialIsSubmitting);

  const handleFormSubmit = useCallback(async (data: SongFormValues, coverArtFile?: File | null, audioFile?: File | null) => {
    setIsProcessingSubmit(true);
    try {
      await onFormSubmit(data, coverArtFile, audioFile);
      // Success is usually handled by the onFormSubmit callback (e.g., navigation, toast)
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Une erreur est survenue lors de la soumission du formulaire.");
      // Optionally, re-throw or handle specific error types
    } finally {
      setIsProcessingSubmit(false);
    }
  }, [onFormSubmit]);

  const handleFormError = useCallback((errors: FieldErrors<SongFormValues>) => {
    console.error("Form validation errors:", errors);
    // Example: Focus on the first error field or show a generic error toast
    // This could be more sophisticated, e.g., iterating errors and showing specific messages
    toast.error("Veuillez corriger les erreurs dans le formulaire.");
  }, []);

  return {
    isProcessingSubmit,
    handleFormSubmit,
    handleFormError,
  };
}