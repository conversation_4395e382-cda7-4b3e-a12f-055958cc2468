"use client";

import React from 'react';

interface CatalogPerformanceTabProps {
  userId?: string;
  timeRange?: any;
}

import { useEffect, useState } from "react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { STATS_DEFINITIONS, StatDefinition } from "@/components/stats/stats-definitions";
import { getArtistStats } from "@/lib/actions/stats";

export function CatalogPerformanceTab({ userId, timeRange }: CatalogPerformanceTabProps) {
  const [catalogStats, setCatalogStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId) return;
    setIsLoading(true);
    setError(null);
    getArtistStats(userId)
      .then((stats) => {
        setCatalogStats(stats);
        setIsLoading(false);
      })
      .catch((err) => {
        setError(err instanceof Error ? err.message : "Erreur inconnue");
        setIsLoading(false);
      });
  }, [userId]);

  // Filtrer les stats catalogue à afficher (issus du référentiel central)
  const catalogStatDefs = STATS_DEFINITIONS.filter(
    (def) => def.category === "catalogue" && def.visualizations.includes("card")
  );

  if (!userId) return <div>Veuillez vous connecter.</div>;
  if (isLoading) return <div>Chargement des statistiques catalogue...</div>;
  if (error) return <div className="text-red-500">Erreur: {error}</div>;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
      {catalogStatDefs.map((def: StatDefinition) => {
        let value = null;
        // Mapping simple basé sur la convention des ids et la structure de retour de getArtistStats
        switch (def.id) {
          case "songs_count":
            value = catalogStats?.totalSongs ?? 0;
            break;
          case "total_followers":
            value = catalogStats?.totalFollowers ?? 0;
            break;
          case "albums_count":
            value = catalogStats?.totalAlbums ?? 0;
            break;
          case "total_plays":
            value = catalogStats?.totalPlays ?? 0;
            break;
          // Ajoutez ici d'autres mappings selon la structure de vos stats/actions
          default:
            value = "-";
        }
        return (
          <Card key={def.id}>
            <CardHeader>
              <CardTitle>{def.label}</CardTitle>
            </CardHeader>
            <CardContent>
              <span className="text-2xl font-bold">{value}</span>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}