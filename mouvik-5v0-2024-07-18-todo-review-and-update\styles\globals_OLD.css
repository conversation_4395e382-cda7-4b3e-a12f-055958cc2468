@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
  /* Ajout du fond global sur toutes les pages */
  background: url('/bg1.png') center center/cover no-repeat fixed;
  min-height: 100vh;
}

@layer utilities {
  /* --- PLAYER OVERRIDES --- */
  .player-bg {
    background: #000 !important;
    background-color: #000 !important;
    box-shadow: 0 0 32px 0 #000d;
    border-top: 1px solid rgba(255,255,255,0.10);
    z-index: 99;
  }
  .player-slider {
    background: #181818 !important;
    border-radius: 9999px;
    height: 1.2rem !important;
  }
  .player-slider .slider-thumb {
    background: #00fff0 !important;
    border: 3px solid #fff !important;
    box-shadow: 0 0 12px 2px #00fff088 !important;
    width: 1.6rem !important;
    height: 1.6rem !important;
    transition: transform 0.18s;
  }
  .player-slider .slider-thumb:hover {
    transform: scale(1.18);
  }
  .player-slider .slider-track {
    background: #222 !important;
  }

  .text-balance {
    text-wrap: balance;
  }
  /* Classe utilitaire pour effet frosted glass */
  .frosted {
    background: rgba(255, 255, 255, 0.18); /* Ajuste selon la palette extraite */
    border-radius: 1rem;
    border: 1px solid rgba(255,255,255,0.25);
    box-shadow: 0 4px 32px 0 rgba(31, 38, 135, 0.10);
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    /* Optionnel : transition douce */
    transition: background 0.3s, box-shadow 0.3s;
  }
  /* --- MOUVIK DESIGN UTILITIES --- */
  :root {
    --primary-teal: #00CED1;
    --dark-teal: #004a4b;
    --darker-teal: #002a2b;
    --darkest-bg: #001214;
    --glow-effect: 0 0 8px rgba(0, 206, 209, 0.5);
    /* Variables de la Sidebar ajoutées */
    --sidebar-width: 16rem; /* valeur par défaut pour la sidebar étendue */
    --sidebar-width-icon: 3rem; /* valeur pour la sidebar réduite (icônes) */
  }
  .gradient-text {
    background: linear-gradient(90deg, var(--primary-teal), #4FFFB0);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .teal-glow {
    text-shadow: var(--glow-effect);
  }
  .teal-glow-box {
    box-shadow: var(--glow-effect);
  }
  .bg-glass {
    background: rgba(0, 18, 24, 0.7);
    border: 1px solid rgba(0, 206, 209, 0.2);
    backdrop-filter: blur(8px);
  }
  .bg-glass-strong {
    background: rgba(0, 18, 24, 0.85);
    border: 1px solid rgba(0, 206, 209, 0.3);
    backdrop-filter: blur(12px);
  }
  .btn-gradient {
    background: linear-gradient(90deg, var(--primary-teal), #4FFFB0);
    color: #001214;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  .btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 206, 209, 0.3);
  }
  .btn-outline-teal {
    background: transparent;
    border: 1px solid var(--primary-teal);
    color: var(--primary-teal);
    font-weight: 500;
    transition: all 0.3s ease;
  }
  .btn-outline-teal:hover {
    background: rgba(0, 206, 209, 0.1);
  }
  /* Responsive helpers */
  .container-center {
    margin-left: auto;
    margin-right: auto;
    padding-left: 1.5rem; /* plus aéré */
    padding-right: 1.5rem;
    max-width: 1200px;
  }
  @media (min-width: 640px) {
    .container-center {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
  @media (min-width: 1024px) {
    .container-center {
      padding-left: 3rem;
      padding-right: 3rem;
    }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    /* Assurons-nous que ces variables ne sont pas redéfinies ici si elles sont déjà dans :root au-dessus */
    /* --sidebar-width: 16rem; */ /* Déjà défini plus haut */
    /* --sidebar-width-icon: 3rem; */ /* Déjà défini plus haut */
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

#auth-dialog-content {
  background: #13181e !important;
  background-color: #13181e !important;
  box-shadow: 0 8px 40px 0 #000C !important;
  border: 2px solid #14b8a6 !important;
  opacity: 1 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  background-image: none !important;
}
