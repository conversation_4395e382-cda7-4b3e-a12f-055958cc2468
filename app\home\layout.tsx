import type React from 'react'
import { createSupabaseServerClient } from '@/lib/supabase/server'; 
import { redirect } from 'next/navigation';

// TODO: Consider if this layout is strictly necessary or can be merged
// It currently seems redundant with app/(authenticated)/layout.tsx

export default async function HomeLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = createSupabaseServerClient(); 

  const {
    data: { session }, 
    error
  } = await supabase.auth.getSession(); 

  if (error || !session) {
    redirect('/');
  }

  // If specific user data IS needed here later, use getUser:
  // const { data: { user }, error } = await supabase.auth.getUser();
  // if (error || !user) {
  //   redirect('/');
  // }

  return <main className="flex-1 p-4 md:p-6 lg:p-8">{children}</main>;
}
