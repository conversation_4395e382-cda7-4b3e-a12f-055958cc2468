# MOUVIK Project - AI Agent Briefing (Manus/Genspark)

## 1. Project Goal

MOUVIK aims to be a comprehensive music platform enabling artists to create, share, discover, and monetize their music, featuring AI-assisted composition, social networking, and multi-platform music aggregation.

**Core Concepts:**
- Seamless integration of user-generated content, AI-composed tracks, and external music sources (Spotify, YouTube, Suno, etc.).
- Rich community interaction around music discovery and creation.
- Professional tools for artists combined with an engaging experience for listeners.

## 2. Key Features & Modules

- **Music Creation**: Integrated tools (details TBD).
- **AI-Assisted Composition:** Tools to help users generate musical ideas, melodies, harmonies, and potentially lyrics or arrangements.
- **Pro Tools Integration:** Seamless workflow for importing/exporting projects with professional Digital Audio Workstations (DAWs).
- **Multi-Source Music Library:** Access to user uploads, creations within Mouvik, and integration with external streaming platforms (e.g., Spotify, YouTube Music) for a unified library.
- **Community & Collaboration:** Features for users to connect, share work, collaborate on projects (defining collaborator roles), form bands *and* user-created interest groups, provide feedback (including timestamped comments on waveforms), and engage in direct messaging.
- **Discovery Engine:** Recommendations for music, artists, and collaborators based on user preferences and activity.
- **Monetization Options:** Tools for artists to monetize their work (details TBD).
- **Editing Suite:** Robust editors for song details, lyrics, chords, album arrangement, and detailed user/artist profiles (including bio, social links, privacy settings, etc.).
- **Track Versioning:** An "Audio Vault" feature to manage different versions or stems of a musical resource.
- **Follow System:** Users can follow other users or artists.

## 3. Tech Stack

- **Frontend**: Next.js 14 (App Router), React, Tailwind CSS
- **Backend**: Next.js API Routes, Server Actions
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Deployment**: Vercel

## 4. Architecture Overview

- **Client**: Next.js Frontend (React components, client-side routing).
- **Server**: Next.js Backend (API Routes for data fetching/mutations, Server Actions for specific mutations).
- **Database**: Supabase handles data persistence, auth, storage.

Refer to [ARCHITECTURE.md](./ARCHITECTURE.md) for a detailed diagram and data flow descriptions.

## 5. Supabase Integration

- **Database Name**: MOUVIK (Note: The connection string connects to the default `postgres` DB, ensure operations target the correct schemas if applicable.)
- **Project URL**: `https://wwvfnwjcsshhekfnmogt.supabase.co`
- **Anon Key (Public)**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.np6ZxIa8yU0WwQlSK5ef5ZQcbW4vANRRW0AISclfM_o`
    - Use this key for client-side Supabase interactions (e.g., fetching public data, user authentication UI).
- **Service Role Key (Secret)**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind3dmZud2pjc3NoaGVrZm5tb2d0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjIxNzgxOCwiZXhwIjoyMDYxNzkzODE4fQ.yGEi4hvrXvqadWa24t-eQE5PDJa7NsEmHk3xaTDNUOk`
    - **CRITICAL**: Use this key ONLY for backend operations (API Routes, Server Actions) where elevated privileges are required (e.g., bypassing RLS for administrative tasks, complex data manipulation). **NEVER EXPOSE THIS KEY IN CLIENT-SIDE CODE.** Store it securely as an environment variable (`SUPABASE_SERVICE_ROLE_KEY`).
- **Postgres Connection String**: `postgresql://postgres:[YOUR-PASSWORD]@db.wwvfnwjcsshhekfnmogt.supabase.co:5432/postgres`
    - Primarily for direct database management or specific backend tasks if the Supabase client library is insufficient. Requires the database password.

**Task Example:** If implementing a Server Action to add a resource to `music_resources` and associate tags, use the Service Role Key to initialize the Supabase client within that server-side function.

## 6. Development Workflow & Guidelines

- **Code Style**: Follow conventions in [GUIDELINES.md](./GUIDELINES.md) (TypeScript, React Hooks, Tailwind CSS, naming conventions).
- **Git**: Feature branches, atomic commits, descriptive messages, Pull Requests for review.
- **Testing**: Write relevant tests (unit, component, integration).
- **Documentation**: Keep related documentation ([README.md](./README.md), [ARCHITECTURE.md](./ARCHITECTURE.md), [DATABASE_STRUCTURE.md](./DATABASE_STRUCTURE.md)) updated.
- **Design Consistency**: 
    - Adhere to the visual style presented in the `_DESIGN_*.html` mockups and images in the `/visuels` directory (Logos, background `bg1.png`, general layout from `home.png`, `dashboard.png`).
    - Implement UI components matching these designs using Tailwind CSS.

## 7. How AI Agents Should Contribute

- **Understand the Context**: Review this prompt and linked documentation before starting a task.
- **Clarify Ambiguities**: Ask questions if requirements are unclear.
- **Focus on Specific Tasks**: Work on tasks defined in [TASKS.md](./TASKS.md) or assigned by the project lead.
- **Implement Features**: Write clean, maintainable, and tested code following the guidelines.
- **Update Documentation**: If your changes affect architecture, database, or core logic, update the relevant documentation.
- **Communicate Progress**: Provide clear updates on your work.

## 8. Important Links

- [README.md](./README.md): Project Overview & Setup
- [ARCHITECTURE.md](./ARCHITECTURE.md): Technical Architecture & Data Flows
- [DATABASE_STRUCTURE.md](./DATABASE_STRUCTURE.md): Database Schema (Needs update for new modules)
- [GUIDELINES.md](./GUIDELINES.md): Development Conventions & Best Practices
- [TASKS.md](./TASKS.md): Current Development Tasks
- [ROADMAP.md](./ROADMAP.md): Project Milestones
- `_DESIGN_*.html` files: HTML Mockups for UI reference.
- `/visuels` directory: Logos, backgrounds, and reference images.

---
*This document serves as a starting point. Refer to the specific files for detailed information.*

**Target Audience**

- **Database:** Supabase (PostgreSQL) - See `DATABASE_STRUCTURE.md` for the detailed schema, which includes tables for users, profiles, music resources (with versioning), albums, lyrics, chords, playlists, tags, bands, community groups, activity feeds, comments (with timestamps), likes, follows, notifications, direct messaging, and collaboration links.
- **Authentication:** Supabase Auth.
- **Storage:** Supabase Storage (for user uploads like audio files, project files, images).

**Project Structure**

- **`INFOS_and_DOCS/`:** Contains documentation, design mockups (HTML), database structure (`DATABASE_STRUCTURE.md`), and this prompt file.
    *   Review the `_DESIGN_*.html` files for UI/UX context.
    *   Consult `DATABASE_STRUCTURE.md` for precise table and field definitions.
- **`APP_CODEBASE/`:** Will contain the main application code (Frontend, Backend, specific modules). *(Directory structure within this TBD)*
