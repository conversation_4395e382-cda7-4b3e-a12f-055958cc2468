// components/hook-form/rhf-textarea.tsx
import React from 'react';
import { Control, FieldValues, FieldPath, useFormContext } from 'react-hook-form';
import { Textarea } from '@/components/ui/textarea';
import type { ComponentProps } from 'react';
import { FormField, FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

interface RHFTextareaProps<TFieldValues extends FieldValues = FieldValues> extends Omit<ComponentProps<'textarea'>, 'name' | 'value' | 'onChange' | 'onBlur'> {
  control?: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
  label: string;
  description?: string;
  className?: string;
}

export const RHFTextarea = <TFieldValues extends FieldValues = FieldValues>({
  control: controlProp,
  name,
  label,
  description,
  className,
  ...rest
}: RHFTextareaProps<TFieldValues>) => {
  const context = useFormContext<TFieldValues>();
  const control = controlProp || context?.control;

  const { control: contextControl } = useFormContext<TFieldValues>() || {};
  const finalControl = controlProp || contextControl;

  if (!finalControl) {
    console.error('RHFTextarea requires control prop or to be used within a FormProvider.');
    return <FormItem className={className}><FormLabel>{label}</FormLabel><FormMessage>Control not found or FormProvider is missing.</FormMessage></FormItem>;
  }

  return (
    <FormField
      control={finalControl}
      name={name}
      render={({ field, fieldState: { error } }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Textarea {...field} value={field.value === null ? '' : field.value} {...rest} />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormItem>
      )}
    />
  );
};