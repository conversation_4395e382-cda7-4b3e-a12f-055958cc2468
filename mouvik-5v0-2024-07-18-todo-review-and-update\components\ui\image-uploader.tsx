"use client"

import type React from "react"

import { useState, useRef } from "react"
import { Upload, X } from "lucide-react"
import { getSupabaseClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"

interface ImageUploaderProps {
  onImageUploaded: (url: string) => void
  existingImageUrl?: string
  aspectRatio?: "square" | "landscape" | "portrait" | "free"
  maxWidth?: number
  maxHeight?: number
  maxFileSizeMB?: number // New prop for max file size in MB
  bucketName?: string
  className?: string
}

export function ImageUploader({
  onImageUploaded,
  existingImageUrl,
  aspectRatio = "free",
  maxWidth = 2000,
  maxHeight = 2000,
  maxFileSizeMB = 5, // Default to 5MB
  bucketName = "covers",
  className = "",
}: ImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [imageUrl, setImageUrl] = useState(existingImageUrl || "")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Vérifier le type de fichier
    if (!file.type.startsWith("image/")) {
      toast({
        title: "Type de fichier non pris en charge",
        description: "Veuillez sélectionner une image (JPG, PNG, etc.)",
        variant: "destructive",
      })
      return
    }

    // Vérifier la taille du fichier
    if (file.size > maxFileSizeMB * 1024 * 1024) {
      toast({
        title: "Fichier trop volumineux",
        description: `La taille de l'image ne doit pas dépasser ${maxFileSizeMB}MB`,
        variant: "destructive",
      })
      if (fileInputRef.current) fileInputRef.current.value = ""; // Reset file input
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Vérifier les dimensions de l'image
      const img = new Image()
      img.src = URL.createObjectURL(file)

      img.onload = async () => {
        let imageFileToUpload = file;
        let sx = 0, sy = 0, sWidth = img.width, sHeight = img.height;
        let dWidth = img.width, dHeight = img.height;

        // Determine target aspect ratio for cropping (if not "free")
        let targetAspectRatioValue = img.width / img.height;
        if (aspectRatio === "square") targetAspectRatioValue = 1;
        // For landscape/portrait, we might need specific ratios, e.g., 16/9 or 9/16.
        // For now, "landscape" implies wider than tall, "portrait" implies taller than wide.
        // The user wants auto-center cropping, so we'll crop to fit the maxWidth/maxHeight box with the target aspect ratio.

        if (aspectRatio !== "free") {
          const currentRatio = img.width / img.height;
          if (aspectRatio === "square") {
            if (currentRatio > 1) { // Wider than square
              sWidth = img.height;
              sx = (img.width - sWidth) / 2;
            } else { // Taller than square or already square
              sHeight = img.width;
              sy = (img.height - sHeight) / 2;
            }
          } else if (aspectRatio === "landscape") { // e.g., target 16:9
            targetAspectRatioValue = 16 / 9;
            if (currentRatio > targetAspectRatioValue) { // Image is wider than target landscape
              sWidth = img.height * targetAspectRatioValue;
              sx = (img.width - sWidth) / 2;
            } else { // Image is taller or less wide than target landscape
              sHeight = img.width / targetAspectRatioValue;
              sy = (img.height - sHeight) / 2;
            }
          } else if (aspectRatio === "portrait") { // e.g., target 9:16
            targetAspectRatioValue = 9 / 16;
             if (currentRatio > targetAspectRatioValue) { // Image is wider or less tall than target portrait
              sWidth = img.height * targetAspectRatioValue;
              sx = (img.width - sWidth) / 2;
            } else { // Image is taller than target portrait
              sHeight = img.width / targetAspectRatioValue;
              sy = (img.height - sHeight) / 2;
            }
          }
        }
        
        // Now, sWidth and sHeight define the source crop box.
        // Next, scale this crop box down if it exceeds maxWidth/maxHeight.
        dWidth = sWidth;
        dHeight = sHeight;

        if (dWidth > maxWidth || dHeight > maxHeight) {
          const scaleRatioWidth = maxWidth / dWidth;
          const scaleRatioHeight = maxHeight / dHeight;
          const scale = Math.min(scaleRatioWidth, scaleRatioHeight);
          dWidth = Math.round(dWidth * scale);
          dHeight = Math.round(dHeight * scale);
        }
        
        // If cropping or resizing occurred, process with canvas
        if (sWidth !== img.width || sHeight !== img.height || dWidth !== img.width || dHeight !== img.height) {
           toast({
            title: "Traitement de l'image",
            description: `Image recadrée/redimensionnée à ${dWidth}x${dHeight}px.`,
            variant: "default",
          });
          const canvas = document.createElement('canvas');
          canvas.width = dWidth;
          canvas.height = dHeight;
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            toast({ title: "Erreur de traitement", description: "Impossible d'initialiser le canvas.", variant: "destructive" });
            setIsUploading(false);
            return;
          }
          // Draw the cropped and resized image onto the canvas
          // sx, sy, sWidth, sHeight define the source rectangle (crop)
          // 0, 0, dWidth, dHeight define the destination rectangle (scaled output)
          ctx.drawImage(img, sx, sy, sWidth, sHeight, 0, 0, dWidth, dHeight);
          
          // Convert canvas to Blob, then to File
          const blob = await new Promise<Blob | null>((resolve) => canvas.toBlob(resolve, file.type, 0.90)); // 0.90 quality
          if (!blob) {
            toast({ title: "Erreur de redimensionnement", description: "Impossible de convertir l'image redimensionnée.", variant: "destructive" });
            setIsUploading(false);
            return;
          }
          imageFileToUpload = new File([blob], file.name, { type: file.type, lastModified: Date.now() });
          
           // Check new file size after resize (optional, as resize might increase size of highly compressed small images)
          if (imageFileToUpload.size > maxFileSizeMB * 1024 * 1024) {
            toast({
              title: "Fichier encore trop volumineux après redimensionnement",
              description: `La taille de l'image (${(imageFileToUpload.size / 1024 / 1024).toFixed(2)}MB) dépasse toujours ${maxFileSizeMB}MB. Essayez une image moins complexe ou plus compressée.`,
              variant: "destructive",
            });
            setIsUploading(false);
            return;
          }
        }

        const supabase = getSupabaseClient()
        const fileExt = imageFileToUpload.name.split(".").pop()
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`
        const filePath = `${fileName}`

        const { error } = await supabase.storage
          .from(bucketName)
          .upload(filePath, imageFileToUpload, { // Use imageFileToUpload
            cacheControl: "3600",
            upsert: true,
            // @ts-ignore // Supabase JS v2 might have different type for event on progress
            onProgress: (event) => {
              if (event.lengthComputable) {
                const percentLoaded = Math.round((event.loaded / event.total) * 100);
                setUploadProgress(percentLoaded);
              }
            }
          });
        
        // Ensure progress is 100% at the end if upload didn't error
        if (!error) {
            setUploadProgress(100);
        }


        if (error) {
          throw error
        }

        const { data: publicUrlData } = supabase.storage.from(bucketName).getPublicUrl(filePath)
        const imageUrl = publicUrlData.publicUrl

        setImageUrl(imageUrl)
        onImageUploaded(imageUrl)

        toast({
          title: "Image téléchargée",
          description: "Votre image a été téléchargée avec succès",
        })
      }

      img.onerror = () => {
        toast({
          title: "Erreur de lecture de l'image",
          description: "Impossible de lire le fichier image",
          variant: "destructive",
        })
        setIsUploading(false)
      }
    } catch (error: any) {
      toast({
        title: "Erreur de téléchargement",
        description: error.message || "Une erreur s'est produite lors du téléchargement de l'image",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveImage = () => {
    setImageUrl("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
    onImageUploaded("")
  }

  return (
    <div className={className}>
      {!imageUrl ? (
        <div
          className={`border-2 border-dashed rounded-md p-6 text-center ${
            isUploading ? "border-primary" : "border-muted-foreground/25"
          }`}
        >
          <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" accept="image/*" />
          <div className="flex flex-col items-center justify-center space-y-2">
            <div className="rounded-full bg-primary/10 p-3">
              <Upload className="h-6 w-6 text-primary" />
            </div>
            <div className="text-sm font-medium">
              {isUploading ? (
                <div className="flex flex-col items-center">
                  <div className="mb-2">Téléchargement en cours...</div>
                  <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-primary" style={{ width: `${uploadProgress}%` }}></div>
                  </div>
                  <div className="mt-1 text-xs text-muted-foreground">{uploadProgress}%</div>
                </div>
              ) : (
                <>
                  <span className="text-primary">Cliquez pour télécharger</span> ou glissez-déposez
                  <p className="text-xs text-muted-foreground mt-1">JPG, PNG, GIF (max 5MB)</p>
                </>
              )}
            </div>
          </div>
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="mt-4"
          >
            Sélectionner une image
          </Button>
        </div>
      ) : (
        <div className="relative">
          <img src={imageUrl || "/placeholder.svg"} alt="Uploaded" className="w-full h-auto rounded-md object-cover" />
          <Button
            variant="outline"
            size="icon"
            onClick={handleRemoveImage}
            className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}
