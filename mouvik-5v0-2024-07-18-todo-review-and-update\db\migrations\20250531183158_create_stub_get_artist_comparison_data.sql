DROP FUNCTION IF EXISTS get_artist_comparison_data(uuid);

CREATE OR REPLACE FUNCTION get_artist_comparison_data(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_monthly_plays BIGINT;
    v_monthly_new_followers BIGINT;
    v_monthly_song_likes BIGINT;
    v_monthly_song_comments BIGINT;
    v_start_date DATE := CURRENT_DATE - INTERVAL '30 days';
    v_end_date DATE := CURRENT_DATE + INTERVAL '1 day'; -- To include today fully
BEGIN
    -- Calculate monthly plays for the user's songs
    SELECT COUNT(sp.id)
    INTO v_monthly_plays
    FROM song_plays sp
    JOIN my_songs ms ON sp.song_id = ms.id
    WHERE ms.user_id = p_user_id
      AND sp.played_at >= v_start_date
      AND sp.played_at < v_end_date;

    -- Calculate monthly new followers for the user
    SELECT COUNT(pf.follower_id)
    INTO v_monthly_new_followers
    FROM profile_followers pf
    WHERE pf.followed_profile_id = p_user_id
      AND pf.created_at >= v_start_date
      AND pf.created_at < v_end_date;

    -- Calculate monthly likes for the user's songs
    SELECT COUNT(sl.id)
    INTO v_monthly_song_likes
    FROM song_likes sl
    JOIN my_songs ms ON sl.song_id = ms.id
    WHERE ms.user_id = p_user_id
      AND sl.created_at >= v_start_date
      AND sl.created_at < v_end_date;

    -- Calculate monthly comments for the user's songs
    SELECT COUNT(sc.id)
    INTO v_monthly_song_comments
    FROM song_comments sc
    JOIN my_songs ms ON sc.song_id = ms.id
    WHERE ms.user_id = p_user_id
      AND sc.created_at >= v_start_date
      AND sc.created_at < v_end_date;

  RETURN jsonb_build_object(
    'rank', 0, -- Placeholder
    'growth_percentile', 0, -- Placeholder
    'engagement_percentile', 0, -- Placeholder
    'metrics', jsonb_build_array(
      jsonb_build_object('metric', 'Écoutes mensuelles', 'value', COALESCE(v_monthly_plays, 0), 'average', 0, 'percentile', 0), -- Averages/percentiles are placeholders
      jsonb_build_object('metric', 'Nouveaux followers mensuels', 'value', COALESCE(v_monthly_new_followers, 0), 'average', 0, 'percentile', 0),
      jsonb_build_object('metric', 'Likes mensuels sur chansons', 'value', COALESCE(v_monthly_song_likes, 0), 'average', 0, 'percentile', 0),
      jsonb_build_object('metric', 'Commentaires mensuels sur chansons', 'value', COALESCE(v_monthly_song_comments, 0), 'average', 0, 'percentile', 0)
    )
  );
END;
$$;
