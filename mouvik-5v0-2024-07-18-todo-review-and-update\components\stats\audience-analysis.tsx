"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, Legend, BarChart, Bar, XAxis, YAxis, CartesianGrid } from "recharts"
import { Loader2, Globe, Users, Clock } from "lucide-react"

interface AudienceAnalysisProps {
  userId: string
  // timeRange?: '7d' | '30d' | '90d' | '1y'; // Optionnel, si la RPC le prend en compte
}

interface AudienceData {
  age_groups: { group: string; count: number; percentage: number }[]
  countries: { country: string; count: number; percentage: number }[]
  devices: { device: string; count: number; percentage: number }[]
  times: { hour: number; count: number; percentage: number }[] // Heures d'écoute agrégées
  gender: { gender: string; count: number; percentage: number }[]
}

// Type pour les données retournées par la RPC get_audience_demographics
// La table audience_demographics stocke déjà des données agrégées.
// La RPC pourrait simplement sélectionner et formater ces données.
interface AudienceDemographicsRpcData {
  age_group: string;
  gender: string;
  country: string;
  device_type: string;
  count: number;
}


const COLORS = ["#4ECDC4", "#FF6B6B", "#C44D58", "#556270", "#1A535C", "#FFE66D", "#6B5B95", "#88B04B"]

export function AudienceAnalysis({ userId }: AudienceAnalysisProps) {
  const [audienceData, setAudienceData] = useState<AudienceData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAudienceData = async () => {
      if(!userId) {
        setIsLoading(false);
        return;
      }
      setIsLoading(true)
      setError(null)
      const supabase = createBrowserClient()
      
      // Appel réel à la fonction RPC Supabase
      // La RPC 'get_audience_demographics_summary' doit être créée pour agréger les données
      // de la table 'audience_demographics' et les formater comme attendu par 'AudienceData'.
      // Ou, interroger directement la table 'audience_demographics' et faire l'agrégation/formatage côté client.
      // Pour l'instant, nous allons interroger la table directement.
      
      const { data, error: rpcError } = await supabase
        .from('audience_demographics')
        .select('age_group, gender, country, device_type, count')
        .eq('artist_id', userId);
      
      if (rpcError) {
        console.error("Erreur récupération audience_demographics:", rpcError)
        setError(rpcError.message)
        setAudienceData(null)
      } else if (data) {
        const rawData = data as AudienceDemographicsRpcData[];

        // Agréger et calculer les pourcentages
        const totalListeners = rawData.reduce((sum, item) => sum + item.count, 0);

        const processGroup = (key: keyof AudienceDemographicsRpcData, nameField: string) => {
          const groupMap = new Map<string, number>();
          rawData.forEach(item => {
            const groupName = item[key] as string || 'Inconnu';
            groupMap.set(groupName, (groupMap.get(groupName) || 0) + item.count);
          });
          return Array.from(groupMap.entries()).map(([group, count]) => ({
            [nameField]: group,
            count,
            percentage: totalListeners > 0 ? (count / totalListeners) * 100 : 0
          })).sort((a,b) => b.count - a.count);
        };
        
        // Pour les heures, la table 'audience_demographics' ne semble pas avoir cette info.
        // Cela viendrait plutôt de l'analyse de la table 'plays'.
        // Pour l'instant, on simule cette partie.
        const simulatedTimes = Array.from({length: 24}, (_, i) => ({
            hour: i,
            count: Math.floor(Math.random() * (totalListeners / 10 || 50)), // Simulé
            percentage: Math.random() * 10 // Simulé
        }));


        const processedData: AudienceData = {
          age_groups: processGroup('age_group', 'group') as AudienceData['age_groups'],
          countries: processGroup('country', 'country') as AudienceData['countries'],
          devices: processGroup('device_type', 'device') as AudienceData['devices'],
          gender: processGroup('gender', 'gender') as AudienceData['gender'],
          times: simulatedTimes // Garder la simulation pour les heures pour l'instant
        };
        
        setAudienceData(processedData);
      } else {
        setAudienceData(null)
      }
      setIsLoading(false)
    }
    
    fetchAudienceData()
  }, [userId])
  
  if (isLoading) { /* ... Loader ... */ 
    return (
      <Card>
        <CardHeader><CardTitle>Analyse d'audience</CardTitle><CardDescription>Chargement...</CardDescription></CardHeader>
        <CardContent className="flex justify-center items-center h-[300px]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></CardContent>
      </Card>
    );
  }
  
  if (error) { /* ... Error ... */ 
     return (
      <Card>
        <CardHeader><CardTitle>Analyse d'audience</CardTitle><CardDescription>Erreur: {error}</CardDescription></CardHeader>
        <CardContent><p className="text-destructive">Impossible de charger les données d'audience.</p></CardContent>
      </Card>
    );
  }
  
  if (!audienceData || 
      (!audienceData.age_groups.length && !audienceData.countries.length && 
       !audienceData.devices.length && !audienceData.gender.length)
     ) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Analyse d'audience</CardTitle>
          <CardDescription>Aucune donnée disponible</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Les données démographiques ne sont pas encore disponibles.</p>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Analyse d'audience</CardTitle>
        <CardDescription>Comprendre qui écoute votre musique</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="demographics" className="space-y-4">
          <TabsList>
            <TabsTrigger value="demographics">Démographie</TabsTrigger>
            <TabsTrigger value="geography">Géographie</TabsTrigger>
            <TabsTrigger value="devices">Appareils</TabsTrigger>
            <TabsTrigger value="times">Heures d'écoute</TabsTrigger>
          </TabsList>
          
          <TabsContent value="demographics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium mb-2 flex items-center"><Users className="h-4 w-4 mr-2" />Répartition par âge</h3>
                <ResponsiveContainer width="100%" height={200}>
                  <BarChart data={audienceData.age_groups} layout="vertical">
                    <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                    <XAxis type="number" domain={[0, 100]} tickFormatter={(val) => `${val}%`} />
                    <YAxis dataKey="group" type="category" width={50} />
                    <Tooltip formatter={(value) => [`${(typeof value === 'number' ? value : 0).toFixed(1)}%`, 'Pourcentage']} />
                    <Bar dataKey="percentage" fill="#4ECDC4" radius={[0, 4, 4, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-2 flex items-center"><Users className="h-4 w-4 mr-2" />Répartition par genre</h3>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie data={audienceData.gender} cx="50%" cy="50%" labelLine={false} outerRadius={80} fill="#8884d8" dataKey="percentage" nameKey="gender" label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}>
                      {audienceData.gender.map((entry, index) => (<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${(typeof value === 'number' ? value : 0).toFixed(1)}%`, 'Pourcentage']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="geography">
            <h3 className="text-sm font-medium mb-2 flex items-center"><Globe className="h-4 w-4 mr-2" />Répartition géographique</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie data={audienceData.countries} cx="50%" cy="50%" labelLine={true} outerRadius={100} fill="#8884d8" dataKey="percentage" nameKey="country" label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}>
                  {audienceData.countries.map((entry, index) => (<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />))}
                </Pie>
                <Tooltip formatter={(value) => [`${(typeof value === 'number' ? value : 0).toFixed(1)}%`, 'Pourcentage']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </TabsContent>
          
          <TabsContent value="devices">
            <h3 className="text-sm font-medium mb-2">Répartition par appareil</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie data={audienceData.devices} cx="50%" cy="50%" labelLine={false} outerRadius={100} fill="#8884d8" dataKey="percentage" nameKey="device" label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}>
                  {audienceData.devices.map((entry, index) => (<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />))}
                </Pie>
                <Tooltip formatter={(value) => [`${(typeof value === 'number' ? value : 0).toFixed(1)}%`, 'Pourcentage']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </TabsContent>
          
          <TabsContent value="times">
            <h3 className="text-sm font-medium mb-2 flex items-center"><Clock className="h-4 w-4 mr-2" />Heures d'écoute (UTC)</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={audienceData.times}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="hour" />
                <YAxis domain={[0, 100]} tickFormatter={(val) => `${val}%`} />
                <Tooltip formatter={(value) => [`${(typeof value === 'number' ? value : 0).toFixed(1)}%`, 'Pourcentage']} />
                <Bar dataKey="percentage" fill="#4ECDC4" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
