import React, { useState, useEffect, useMemo } from 'react';
import { Control, FieldErrors } from 'react-hook-form'; // Keep existing RHF imports
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LyricsEditorWithAI } from '@/components/songs/LyricsEditorWithAI';
import type Quill from 'quill';
import { SongFormValues, AiConfig, AiHistoryItem } from './song-schema';
import AddChordDiagramForm from './AddChordDiagramForm';
import { ChordDiagram, ChordDiagramProps as ExternalChordDiagramProps, ChordInstrument } from './ChordDiagram'; // Renamed to avoid conflict
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

// --- START OF RECONSTRUCTED LOGIC ---

// Interface for parsed chord components
interface ParsedChord {
  root: string | null;
  suffix: string | null;
  bass: string | null;
}

// Function to parse chord names into root, suffix, and bass
const parseChordNameDetailed = (chordString: string): ParsedChord => {
  if (!chordString) {
    return { root: null, suffix: null, bass: null };
  }
  const slashChordRegex = /^([A-G][#b]?)(\S*?)\/([A-G][#b]?)$/;
  let match = chordString.match(slashChordRegex);
  if (match) {
    return { root: match[1], suffix: match[2] || null, bass: match[3] };
  }
  const regularChordRegex = /^([A-G][#b]?)(\S*)$/;
  match = chordString.match(regularChordRegex);
  if (match) {
    return { root: match[1], suffix: match[2] || null, bass: null };
  }
  const basicRootRegex = /^([A-G][#b]?)$/;
  match = chordString.match(basicRootRegex);
  if (match) {
    return { root: match[1], suffix: null, bass: null };
  }
  // console.warn(`Could not parse chord string: ${chordString}`); // Keep commented for now
  return { root: null, suffix: null, bass: null };
};

// Map for normalizing chord suffixes to match JSON keys
const SUFFIX_NORMALIZATION_MAP: { [key: string]: string } = {
  "M": "", "maj": "", "Major": "", "": "",
  "m": "m", "min": "m", "Minor": "m",
  "7": "7", "dom7": "7",
  "maj7": "maj7", "Maj7": "maj7", "M7": "maj7",
  "m7": "m7", "min7": "m7",
  "m(maj7)": "m(maj7)", "mM7": "m(maj7)",
  "sus": "sus4", "sus4": "sus4", "sus2": "sus2",
  "aug": "aug", "Aug": "aug", "+": "aug",
  "dim": "dim", "Dim": "dim", "°": "dim", "o": "dim",
  "halfdim": "m7b5", "ø": "m7b5",
  "add9": "add9", "add2": "add9",
  "add11": "add11", "add4": "add11",
  "5": "5",
};

// Helper to get plain text from Quill's HTML content
const getPlainTextFromHtml = (html: string): string => {
  if (typeof document !== 'undefined') {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || "";
  }
  return html.replace(/<[^>]+>/g, '').replace(/&nbsp;/g, ' ');
};

// Helper Interfaces for Chord Definitions from JSON
interface ChordJsonPosition {
  frets: (number | string)[]; // Allow string for 'x'
  fingers?: (number | string)[];
  barres?: Array<{ fret: number; fromString: number; toString: number }>;
  midi?: number[];
  baseFret?: number;
}
interface ChordJsonVariation {
  suffix: string;
  name: string;
  positions: ChordJsonPosition[];
}
interface ChordDefinition {
  instrument: string;
  tuning: string[];
  strings: number;
  chords: { [rootNoteKey: string]: ChordJsonVariation[] };
  keys?: string[];
  suffixes?: string[];
}

// Interface for props required by our ChordDiagram component wrapper
interface InternalChordDiagramProps {
  instrument: ChordInstrument;
  chord: string;
  positions: Array<[number, number | 'x', (string | number)?]>;
  actualTuning?: string[];
  numStrings?: number;
  baseFret?: number; // Add baseFret if ChordDiagram supports it or for display
}

// Interface for the data structure returned by getChordData
interface ChordDataWithVoicings {
  instrument: ChordInstrument;
  chordDisplayName: string;
  parsedChordName: string;
  actualTuning?: string[];
  numStrings?: number;
  availableVoicings: Array<{
    positions: Array<[number, number | 'x', (string | number)?]>;
    baseFret?: number;
    fingers?: (number | string)[];
    barres?: Array<{ fret: number; fromString: number; toString: number }>;
    midi?: number[];
  }>;
}

// Instrument and Tuning constants
const INSTRUMENTS = [
  { value: 'guitar', label: 'Guitare' },
  { value: 'ukulele', label: 'Ukulélé' },
  { value: 'piano', label: 'Piano' },
];
const TUNINGS: { [instrument: string]: { value: string; label: string }[] } = {
  guitar: [ { value: 'standard', label: 'Standard (EADGBe)' } ],
  ukulele: [ { value: 'standard_gcea', label: 'Standard (GCEA)' } ],
  piano: [ { value: 'standard', label: 'Standard' } ],
};


// Props for SongFormLyricsChordTab (ensure it matches the actual usage)
interface SongFormLyricsChordTabProps {
  control: Control<SongFormValues>;
  errors: FieldErrors<SongFormValues>;
  lyricsContent: string;
  onLyricsChange: (value: string, editorContent?: any) => void;
  quillRef: React.RefObject<Quill | null>;
  aiConfig: AiConfig;
  aiGeneralPrompt: string;
  addAiHistory: (userPrompt: string, assistantResponse: string) => void;
  aiHistory: AiHistoryItem[];
  showAiHistory: boolean;
  setShowAiHistory: (show: boolean) => void;
  formControl: Control<SongFormValues>;
}

export const SongFormLyricsChordTab: React.FC<SongFormLyricsChordTabProps> = ({
  control, errors, lyricsContent, onLyricsChange, quillRef,
  aiConfig, aiGeneralPrompt, addAiHistory, aiHistory, showAiHistory, setShowAiHistory, formControl
}) => {
  const [addedChordDiagrams, setAddedChordDiagrams] = useState<ExternalChordDiagramProps[]>([]);
  const [selectedInstrument, setSelectedInstrument] = useState<string>(INSTRUMENTS[0]?.value || 'guitar');
  const [selectedTuning, setSelectedTuning] = useState<string>(TUNINGS[selectedInstrument]?.[0]?.value || 'standard');
  const [currentChordDefs, setCurrentChordDefs] = useState<ChordDefinition | null>(null);
  const [selectedVoicings, setSelectedVoicings] = useState<{ [key: string]: number }>({});

  const getChordDefinitionFilename = (instrument: string, tuning: string): string | null => {
    const instrumentLower = instrument.toLowerCase();
    const tuningLower = tuning.toLowerCase();
    if (instrumentLower === 'guitar') {
      if (tuningLower === 'standard') return 'guitar_standard_tuning.json';
    } else if (instrumentLower === 'ukulele') {
      if (tuningLower === 'standard_gcea') return 'ukulele_standard_gcea_tuning.json'; // Example, actual file might differ
    }
    // console.warn(`No chord definition file mapping for instrument: '${instrument}' and tuning: '${tuning}'`);
    return null;
  };

  const loadChordDefinitionFile = async (instrument: string, tuning: string): Promise<ChordDefinition | null> => {
    const filename = getChordDefinitionFilename(instrument, tuning);
    if (!filename) {
      setCurrentChordDefs(null);
      return null;
    }
    try {
      const module = await import(`@/lib/chords/${filename}`);
      return (module.default || module) as ChordDefinition;
    } catch (error) {
      // console.error(`Error loading chord definition file ${filename}:`, error);
      setCurrentChordDefs(null);
      return null;
    }
  };

  useEffect(() => {
    loadChordDefinitionFile(selectedInstrument, selectedTuning)
      .then(data => setCurrentChordDefs(data))
      .catch(error => {
        // console.error("Failed to set chord definitions:", error);
        setCurrentChordDefs(null);
      });
  }, [selectedInstrument, selectedTuning]);

  useEffect(() => {
    const availableTunings = TUNINGS[selectedInstrument] || [];
    if (!availableTunings.find(t => t.value === selectedTuning)) {
      setSelectedTuning(availableTunings[0]?.value || '');
    }
  }, [selectedInstrument]);

  const parseLyricsWithChords = (text: string) => {
    const segments = [];
    const chordRegex = /\[([^\]]+)\]/g;
    let lastIndex = 0;
    let match;

    while ((match = chordRegex.exec(text)) !== null) {
      if (match.index > lastIndex) {
        segments.push({ type: 'text', content: text.substring(lastIndex, match.index) });
      }
      segments.push({ type: 'chord', content: match[1].trim(), startIndex: match.index });
      lastIndex = match.index + match[0].length;
    }

    if (lastIndex < text.length) {
      segments.push({ type: 'text', content: text.substring(lastIndex) });
    }
    return segments;
  };

  const processedLyricsSegments = useMemo(() => {
    const plainTextLyrics = getPlainTextFromHtml(lyricsContent);
    return parseLyricsWithChords(plainTextLyrics);
  }, [lyricsContent]);

  const getChordData = (
    chordNameToParse: string,
    instrumentValue: string,
    tuningValue: string,
    chordDefs: ChordDefinition | null
  ): ChordDataWithVoicings | null => {
    if (!chordDefs || !chordDefs.chords || chordDefs.instrument.toLowerCase() !== instrumentValue.toLowerCase()) {
      // console.warn("Chord definitions not loaded, or instrument mismatch.");
      return null;
    }

    const parsedDetails = parseChordNameDetailed(chordNameToParse);
    if (!parsedDetails.root) {
      // console.warn(`Could not parse root for chord: ${chordNameToParse}`);
      return null;
    }

    let rootNoteKey = parsedDetails.root.charAt(0).toUpperCase();
    if (parsedDetails.root.length > 1) {
        if (parsedDetails.root.charAt(1) === '#') rootNoteKey += 'sharp';
        else if (parsedDetails.root.charAt(1) === 'b') {
            // Simplified flat mapping, ensure your JSON keys match this (e.g., "Db", "Eb")
            const flatMap: { [key: string]: string } = { 'A': 'Ab', 'B': 'Bb', 'D': 'Db', 'E': 'Eb', 'G': 'Gb' };
            rootNoteKey = flatMap[rootNoteKey.charAt(0)] || rootNoteKey; // Handles single letter roots like 'A' becoming 'Ab'
        }
    }

    let suffixForLookup = parsedDetails.suffix || ""; // Default to empty string for major
    if (SUFFIX_NORMALIZATION_MAP.hasOwnProperty(suffixForLookup)) {
        suffixForLookup = SUFFIX_NORMALIZATION_MAP[suffixForLookup];
    }

    const variationsForRoot = chordDefs.chords[rootNoteKey];
    if (!variationsForRoot) {
      // console.warn(`No variations found for root: ${rootNoteKey} (from ${chordNameToParse})`);
      return null;
    }

    let targetSuffixString = suffixForLookup;
    if(parsedDetails.bass) {
        targetSuffixString += "/" + parsedDetails.bass;
    }

    let chordVariation = variationsForRoot.find(v => v.suffix === targetSuffixString);

    // Fallback logic for major/minor if specific suffix not found
    if (!chordVariation) {
        if (suffixForLookup === "") { // Trying to find "major"
            chordVariation = variationsForRoot.find(v => v.suffix === "major");
        } else if (suffixForLookup === "m") { // Trying to find "minor"
            chordVariation = variationsForRoot.find(v => v.suffix === "minor");
        }
    }

    if (!chordVariation || !chordVariation.positions || chordVariation.positions.length === 0) {
      // console.warn(`Chord variation not found for: '${chordNameToParse}'. Root: ${rootNoteKey}, Suffix: ${targetSuffixString}`);
      return null;
    }

    const allVoicings = chordVariation.positions.map(pos => {
      const vexPositions: Array<[number, number | 'x', (string | number)?]> = [];
      pos.frets.forEach((fret, index) => {
        vexPositions.push([index + 1, fret === -1 ? 'x' : fret, pos.fingers ? pos.fingers[index] : undefined]);
      });
      return {
        positions: vexPositions,
        baseFret: pos.baseFret,
        fingers: pos.fingers,
        barres: pos.barres || [],
        midi: pos.midi
      };
    });

    if (allVoicings.length === 0) return null;

    return {
      instrument: instrumentValue as ChordInstrument,
      chordDisplayName: chordNameToParse,
      parsedChordName: chordVariation.name || chordNameToParse, // Use the name from JSON if available
      actualTuning: chordDefs.tuning,
      numStrings: chordDefs.strings,
      availableVoicings: allVoicings,
    };
  };

  const handleVoicingChange = (chordKey: string, direction: number, totalVoicings: number) => {
    setSelectedVoicings(prev => {
      const currentIdx = prev[chordKey] || 0;
      let newIdx = currentIdx + direction;
      if (newIdx < 0) newIdx = totalVoicings - 1;
      else if (newIdx >= totalVoicings) newIdx = 0;
      return { ...prev, [chordKey]: newIdx };
    });
  };

  const handleAddNewDiagram = (diagram: Omit<ExternalChordDiagramProps, 'key'>) => {
    const newDiagramWithKey: ExternalChordDiagramProps = { ...diagram, key: `diagram-${Date.now()}-${Math.random()}` };
    setAddedChordDiagrams(prev => [...prev, newDiagramWithKey]);
  };

  return (
    <Card>
      <CardHeader><CardTitle>Paroles et Accords</CardTitle></CardHeader>
      <CardContent>
        <Accordion type="single" collapsible defaultValue="lyrics-ai-item" className="w-full">
          <AccordionItem value="lyrics-ai-item">
            <AccordionTrigger className="text-lg font-semibold">Paroles & Outils IA</AccordionTrigger>
            <AccordionContent className="pt-4">
              <LyricsEditorWithAI
                lyricsContent={lyricsContent} handleLyricsChange={onLyricsChange} quillRef={quillRef}
                formControl={formControl} aiConfig={aiConfig} aiGeneralPrompt={aiGeneralPrompt}
                addAiHistory={addAiHistory} aiHistory={aiHistory} showAiHistory={showAiHistory}
                setShowAiHistory={setShowAiHistory}
              />
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="chord-tools-item">
            <AccordionTrigger className="text-lg font-semibold">Outils & Diagrammes d'Accords</AccordionTrigger>
            <AccordionContent className="pt-4">
              <div className="space-y-6">
                <h3 className="text-lg font-semibold mb-4">Visualiseur d'accords dynamiques</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <Label htmlFor="instrument-select-lyrics">Instrument</Label>
                    <Select value={selectedInstrument} onValueChange={setSelectedInstrument}>
                      <SelectTrigger id="instrument-select-lyrics"><SelectValue placeholder="Choisir instrument" /></SelectTrigger>
                      <SelectContent>{INSTRUMENTS.map(ins => <SelectItem key={ins.value} value={ins.value}>{ins.label}</SelectItem>)}</SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="tuning-select-lyrics">Accordage</Label>
                    <Select value={selectedTuning} onValueChange={setSelectedTuning} disabled={!TUNINGS[selectedInstrument] || TUNINGS[selectedInstrument].length === 0}>
                      <SelectTrigger id="tuning-select-lyrics"><SelectValue placeholder="Choisir accordage" /></SelectTrigger>
                      <SelectContent>{(TUNINGS[selectedInstrument] || []).map(tun => <SelectItem key={tun.value} value={tun.value}>{tun.label}</SelectItem>)}</SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="mt-4">
                  <h4 className="text-md font-semibold mb-2">Accords Détectés:</h4>
                  {!currentChordDefs && <p className="text-sm text-orange-500">Définitions d'accords non chargées pour l'instrument/accordage sélectionné.</p>}
                  {processedLyricsSegments.length === 0 && currentChordDefs && <p className="text-sm text-muted-foreground">Aucun texte ou accord.</p>}

                  <div className="flex flex-wrap items-end gap-x-1 gap-y-8"> {/* Increased gap-y */}
                    {currentChordDefs && processedLyricsSegments.map((segment, segIndex) => {
                      if (segment.type === 'text') {
                        return <span key={`text-${segIndex}`} style={{ whiteSpace: 'pre-wrap' }}>{segment.content}</span>;
                      } else if (segment.type === 'chord') {
                        const chordData = getChordData(segment.content, selectedInstrument, selectedTuning, currentChordDefs);
                        if (chordData && chordData.availableVoicings.length > 0) {
                          const voicingStateKey = `${segment.startIndex}_${chordData.parsedChordName}`;
                          const currentVoicingIndex = selectedVoicings[voicingStateKey] || 0;
                          const actualVoicing = chordData.availableVoicings[currentVoicingIndex];
                          const numStrings = chordData.numStrings || 6;

                          if (actualVoicing) {
                            return (
                              <div key={`chord-container-${segment.startIndex}-${chordData.parsedChordName}`} className="inline-flex flex-col items-center mx-1 align-bottom">
                                <ChordDiagram
                                  key={`chord-diag-${segment.startIndex}-${currentVoicingIndex}`}
                                  instrument={chordData.instrument}
                                  chord={chordData.parsedChordName} // Display name from JSON
                                  positions={actualVoicing.positions}
                                  actualTuning={chordData.actualTuning}
                                  numStrings={numStrings}
                                  // baseFret={actualVoicing.baseFret} // Uncomment if ChordDiagram supports baseFret
                                />
                                {chordData.availableVoicings.length > 1 && (
                                  <div className="voicing-controls flex items-center justify-center text-xs mt-1">
                                    <button type="button" onClick={() => handleVoicingChange(voicingStateKey, -1, chordData.availableVoicings.length)} className="px-1.5 py-0.5 border rounded-l-md bg-gray-100 hover:bg-gray-200">&lt;</button>
                                    <span className="px-1 py-0.5 border-t border-b bg-white tabular-nums">{currentVoicingIndex + 1}/{chordData.availableVoicings.length}</span>
                                    <button type="button" onClick={() => handleVoicingChange(voicingStateKey, 1, chordData.availableVoicings.length)} className="px-1.5 py-0.5 border rounded-r-md bg-gray-100 hover:bg-gray-200">&gt;</button>
                                  </div>
                                )}
                              </div>
                            );
                          }
                        }
                        return <span key={`chord-fail-${segment.startIndex}`} className="text-red-500 font-semibold mx-1 align-bottom">[{segment.content}]</span>;
                      }
                      return null;
                    })}
                  </div>
                </div>
                <div className="mt-6 pt-6 border-t">
                  <h3 className="text-lg font-semibold mb-3">Ajouter un diagramme manuellement</h3>
                  <AddChordDiagramForm onAdd={handleAddNewDiagram} />
                  {addedChordDiagrams.length > 0 && (
                    <div className="mt-4"><h4 className="text-md font-semibold mb-2">Accords ajoutés :</h4>
                      <div className="flex flex-wrap gap-4">
                        {addedChordDiagrams.map((diag) => <ChordDiagram {...diag} />)}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  );
};
// --- END OF RECONSTRUCTED LOGIC ---