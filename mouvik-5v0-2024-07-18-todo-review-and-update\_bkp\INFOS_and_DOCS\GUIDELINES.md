# Directives de Développement - MOUVIK

Ce document définit les règles et bonnes pratiques à suivre lors du développement de la plateforme MOUVIK.

## 1. Conventions de Code

### Nommage

- **Fichiers**: Utiliser le kebab-case pour les noms de fichiers (`user-profile.tsx`)
- **Composants React**: Utiliser le PascalCase (`UserProfile`)
- **Fonctions**: Utiliser le camelCase (`getUserData`)
- **Variables**: Utiliser le camelCase (`userData`)
- **Constantes**: Utiliser le SNAKE_CASE en majuscules (`MAX_FILE_SIZE`)
- **Types/Interfaces**: Utiliser le PascalCase avec préfixe I pour les interfaces (`IUserData`)

### Structure des Fichiers

- Un composant par fichier
- Exports nommés pour les utilitaires, hooks, etc.
- Export par défaut pour les composants principaux et les pages

### Style de Code

- Utiliser TypeScript pour tout le code
- Préférer les fonctions fléchées pour les composants React
- Utiliser les hooks React plutôt que les classes
- Éviter les any, préférer les types explicites
- Utiliser les commentaires JSDoc pour documenter les fonctions importantes

## 2. Architecture Frontend

### Organisation des Composants

- **Atomique**: Diviser les composants en atomes, molécules, organismes
- **Réutilisabilité**: Concevoir les composants pour être réutilisables
- **Props**: Utiliser des interfaces TypeScript pour définir les props
- **État**: Minimiser l'état local, préférer les props

### Gestion d'État

- Utiliser React Context pour l'état global (auth, player, etc.)
- Utiliser les hooks useState/useReducer pour l'état local
- Éviter la prop drilling en utilisant des contextes appropriés
- Utiliser Server Actions pour les mutations de données

### Styling

- Utiliser Tailwind CSS pour le styling
- Suivre le système de design défini
- Utiliser les variables CSS pour les couleurs et espacement
- Assurer la responsivité pour tous les écrans

## 3. Architecture Backend

### API Routes

- Organiser par domaine fonctionnel
- Valider les entrées avec zod ou similar
- Retourner des réponses JSON structurées
- Gérer les erreurs de manière cohérente

### Server Actions

- Utiliser pour les mutations de données
- Valider les données d'entrée
- Retourner des résultats typés
- Gérer les erreurs avec try/catch

### Base de Données

- Suivre les conventions de nommage PostgreSQL (snake_case)
- Utiliser des clés étrangères pour maintenir l'intégrité référentielle
- Implémenter des politiques RLS pour la sécurité
- Indexer les colonnes fréquemment recherchées

## 4. Sécurité

### Authentification

- Toujours vérifier l'authentification pour les routes protégées
- Ne jamais exposer les clés secrètes dans le code client
- Utiliser des tokens à courte durée de vie
- Implémenter la déconnexion sur tous les appareils

### Données Utilisateur

- Valider toutes les entrées utilisateur
- Échapper les sorties HTML pour prévenir les XSS
- Ne jamais stocker de données sensibles en clair
- Limiter les informations exposées dans les API

## 5. Performance

### Optimisation Frontend

- Utiliser l'image optimization de Next.js
- Lazy loading pour les composants non critiques
- Minimiser les re-renders inutiles
- Optimiser les bundles avec le code splitting

### Optimisation Backend

- Mettre en cache les résultats coûteux
- Optimiser les requêtes de base de données
- Utiliser des limites et pagination pour les grandes collections
- Implémenter des timeouts pour les opérations longues

### Optimisation Audio

- Utiliser le streaming pour les fichiers audio
- Générer des versions à différentes qualités
- Précharger les métadonnées avant le contenu audio
- Optimiser les waveforms pour un rendu rapide

## 6. Tests

### Types de Tests

- **Tests Unitaires**: Pour les fonctions et hooks isolés
- **Tests de Composants**: Pour les composants UI
- **Tests d'Intégration**: Pour les flux complets
- **Tests E2E**: Pour les parcours utilisateur critiques

### Bonnes Pratiques

- Écrire des tests pour toute nouvelle fonctionnalité
- Maintenir une couverture de test > 70%
- Utiliser des mocks pour les dépendances externes
- Tester les cas limites et les scénarios d'erreur

## 7. Accessibilité

- Suivre les standards WCAG 2.1 AA
- Utiliser des attributs ARIA appropriés
- Assurer la navigation au clavier
- Maintenir un contraste suffisant pour le texte
- Fournir des alternatives textuelles pour les médias

## 8. Processus de Développement

### Git Workflow

- Une branche par fonctionnalité
- Commits atomiques avec messages descriptifs
- Pull requests pour toutes les modifications
- Code review obligatoire avant merge

### Déploiement

- CI/CD via GitHub Actions et Vercel
- Tests automatisés avant déploiement
- Environnements de staging et production
- Rollback automatique en cas d'échec

## 9. Documentation

- Documenter toutes les API et composants majeurs
- Maintenir un changelog des modifications
- Documenter les décisions d'architecture importantes
- Créer des guides pour les nouveaux développeurs

## 10. Spécificités Audio

- Supporter les formats audio courants (MP3, WAV, FLAC)
- Gérer correctement les métadonnées ID3
- Implémenter des contrôles de volume non-linéaires
- Assurer la compatibilité cross-browser pour l'API Web Audio
- Optimiser la consommation de batterie sur mobile

## 11. Directives Spécifiques - Module "Découvrir & Playlists"

### 11.1. Gestion des Sources Externes
- **Prioriser les API Officielles**: Utiliser les API publiques (Spotify, YouTube, etc.) quand elles existent. Gérer l'authentification (OAuth) et les tokens de manière sécurisée.
- **Scraping (En dernier recours)**: Pour les plateformes sans API (ex: potentiellement Suno, Udio), implémenter des scrapers robustes mais respectueux. Inclure des mécanismes de `User-Agent` clairs, gestion des `robots.txt`, délais entre les requêtes, et gestion élégante des erreurs (changements de structure HTML, blocages).
- **Gestion des Erreurs & Limitations**: Implémenter des stratégies de retry exponentiel pour les erreurs API/Scraping. Respecter les limitations de taux (rate limits) des API externes.
- **Stockage Métadonnées Brutes**: Conserver les données brutes retournées par l'API/Scraper dans le champ `platform_data` (JSONB) pour référence et débogage futur.
- **Extensibilité**: Concevoir le système d'extraction de manière modulaire pour faciliter l'ajout de nouvelles plateformes.

### 11.2. Taxonomie & Tags
- **Centralisation**: La table `tags` est la source unique de vérité. Utiliser les types définis ('genre', 'style', 'mood', 'platform', 'custom').
- **Normalisation**: Les labels des tags doivent être stockés en minuscules et sans espaces superflus.
- **Tags de Plateforme**: Ajouter automatiquement un tag `platform:[nom_plateforme]` (ex: `platform:spotify`) lors de l'import.
- **Suggestion Intelligente**: Baser les suggestions de tags sur les genres/infos extraites, mais toujours permettre à l'utilisateur de valider/modifier/ajouter.
- **Auto-complétion**: L'UI d'ajout de tags doit proposer une auto-complétion basée sur la table `tags` existante.
- **Création de Tags**: Permettre la création de tags `custom` par les utilisateurs, mais prévoir un mécanisme de modération/validation (potentiellement par des admins ou via un seuil d'utilisation) pour éviter la prolifération et maintenir la cohérence.

### 11.3. Gestion des Playlists
- **Playlists Mixtes**: Le modèle (`playlist_resources`) doit lier une playlist à des `music_resources`, qu'elles soient internes (`type='ai_composer'`) ou externes.
- **Permissions**: Gérer les droits de lecture/écriture sur les playlists (privées, publiques, partagées avec groupe/utilisateurs spécifiques).
- **Ordre**: Maintenir l'ordre des pistes dans une playlist via le champ `order`.

### 11.4. UI/UX
- **Provenance Claire**: Utiliser des badges/logos distinctifs pour indiquer la source de chaque `music_resource` (MOUVIK, Spotify, Suno...).
- **Player Adapté**: Utiliser le player interne pour les morceaux MOUVIK et des players embarqués (embed) appropriés pour les sources externes (ex: Spotify Web Playback SDK, iframe YouTube).
- **Preview Import**: Toujours montrer une preview des métadonnées extraites avant de finaliser l'import.

## 12. Directives Spécifiques - Module "Communauté"

### 12.1. Gestion des Activités
- **Types d'Activité Clairs**: Utiliser l'enum `type` de la table `activity` de manière cohérente.
- **Granularité**: Créer des activités pour les actions significatives (ajout ressource, post, commentaire, like, création groupe/album). Éviter de surcharger le flux avec des actions mineures.
- **Liens Contextuels**: Utiliser `resource_id`, `album_id`, `band_id`, `parent_activity_id` pour lier les activités aux entités concernées.
- **Contenu**: Stocker le texte des posts/commentaires dans `content`. Utiliser Markdown ou un format enrichi simple si nécessaire.

### 12.2. Hashtags
- **Détection & Normalisation**: Détecter les `#hashtags` dans les contenus (`activity.content`, descriptions...). Normaliser les labels (minuscules, sans '#') avant stockage dans `hashtags` et `activity_hashtags`.
- **Unicité**: Assurer l'unicité du `label` dans la table `hashtags`.

### 12.3. Interactions Sociales
- **Likes/Commentaires**: Implémenter via la table `likes` et les activités de type `comment` liées par `parent_activity_id`.
- **Server Actions**: Privilégier les Server Actions pour les actions rapides comme liker/unliker pour une meilleure UX (optimistic UI).
- **Notifications**: Prévoir l'architecture pour un futur système de notifications (mentions, likes, commentaires, invitations).

### 12.4. Modération
- **Signalement**: Permettre aux utilisateurs de signaler des contenus inappropriés (posts, commentaires, ressources, tags).
- **Rôles & Permissions**: Utiliser les rôles (`band_members`, potentiellement des rôles globaux admin/moderator) pour définir qui peut modérer/supprimer du contenu.
- **Historique**: Conserver un historique des actions de modération (soft delete plutôt que hard delete si possible).

### 12.5. UI/UX
- **Flux Clair**: Le `ActivityFeed` doit être facile à lire et à filtrer.
- **Affichage Contextuel**: Adapter l'`ActivityCard` au type d'activité (preview ressource, texte du post, etc.).
- **Performance du Flux**: Implémenter une pagination efficace (curseur ou offset) pour le chargement des activités.
