-- Migration pour mettre à jour les fonctions du tableau de bord
-- <PERSON><PERSON><PERSON> le: 2024-05-20
-- Auteur: <PERSON><PERSON><PERSON>

-- Début de la transaction
BEGIN;

-- 1. Mettre à jour la fonction get_dashboard_data
-- Note: Cette fonction doit être remplacée par la version la plus récente qui utilise 'songs'
-- au lieu de 'tracks'. Le contenu exact dépend de votre implémentation actuelle.

-- 2. Supprimer les anciennes versions des fonctions dupliquées
DROP FUNCTION IF EXISTS public.get_dashboard_data_with_metrics;
DROP FUNCTION IF EXISTS public.get_dashboard_data_updated;
DROP FUNCTION IF EXISTS public.get_dashboard_data_fixed;
DROP FUNCTION IF EXISTS public.get_dashboard_data_final;
DROP FUNCTION IF EXISTS public.get_dashboard_data_corrected;

-- 3. Créer une nouvelle version unifiée de la fonction
-- Remplacez ceci par votre implémentation actuelle qui utilise 'songs'
CREATE OR REPLACE FUNCTION public.get_dashboard_data(p_user_id UUID)
RETURNS TABLE (
    total_songs BIGINT,
    total_plays BIGINT,
    total_likes BIGINT,
    total_comments BIGINT,
    recent_songs JSONB,
    top_songs JSONB
) AS $$
BEGIN
    -- Implémentation de la fonction
    -- Assurez-vous que toutes les références à 'tracks' sont remplacées par 'songs'
    -- et que la structure de retour correspond à ce qui est attendu par l'application
    
    -- Exemple simplifié:
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM public.songs WHERE user_id = p_user_id)::BIGINT,
        (SELECT COALESCE(SUM(plays), 0) FROM public.songs WHERE user_id = p_user_id)::BIGINT,
        (SELECT COUNT(*) FROM public.likes 
         WHERE resource_type = 'song' 
         AND resource_id IN (SELECT id FROM public.songs WHERE user_id = p_user_id))::BIGINT,
        (SELECT COUNT(*) FROM public.comments 
         WHERE resource_type = 'song' 
         AND resource_id IN (SELECT id FROM public.songs WHERE user_id = p_user_id))::BIGINT,
        (SELECT COALESCE(jsonb_agg(to_jsonb(s)), '[]')
         FROM (
             SELECT id, title, plays, created_at
             FROM public.songs
             WHERE user_id = p_user_id
             ORDER BY created_at DESC
             LIMIT 5
         ) s)::JSONB,
        (SELECT COALESCE(jsonb_agg(to_jsonb(s)), '[]')
         FROM (
             SELECT id, title, plays, created_at
             FROM public.songs
             WHERE user_id = p_user_id
             ORDER BY plays DESC
             LIMIT 5
         ) s)::JSONB;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fin de la transaction
COMMIT;

-- Instructions post-migration:
-- 1. Tester la fonction get_dashboard_data avec différents utilisateurs
-- 2. Vérifier que les statistiques affichées sont correctes
-- 3. Mettre à jour la documentation si nécessaire
