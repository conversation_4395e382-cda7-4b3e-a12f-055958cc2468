// Fichier central de définition des statistiques et visualisations (pour ISIGHT et la page stats)
// À compléter/valider selon les besoins métiers et UX

export type StatCategory =
  | 'overview'
  | 'catalogue'
  | 'engagement'
  | 'audience'
  | 'social'
  | 'music'
  | 'performance'
  | 'other';

export type StatVisualization =
  | 'card'
  | 'line_chart'
  | 'bar_chart'
  | 'pie_chart'
  | 'radar_chart'
  | 'table'
  | 'scatter_plot'
  | 'custom';

export interface StatDefinition {
  id: string;
  label: string;
  category: StatCategory;
  visualizations: StatVisualization[];
  access: Array<'admin' | 'artist' | 'user'>;
  backend: 'rpc' | 'direct_query' | 'aggregate' | 'external';
  query?: string; // nom de la RPC ou table
  description?: string;
}

export const STATS_DEFINITIONS: StatDefinition[] = [
  // === OVERVIEW ===
  {
    id: 'total_plays',
    label: 'Écoutes totales',
    category: 'overview',
    visualizations: ['card', 'line_chart'],
    access: ['artist', 'admin'],
    backend: 'rpc',
    query: 'get_user_overview_stats',
    description: 'Nombre total d\'écoutes sur la période.'
  },
  {
    id: 'unique_listeners',
    label: 'Auditeurs uniques',
    category: 'overview',
    visualizations: ['card', 'line_chart'],
    access: ['artist', 'admin'],
    backend: 'rpc',
    query: 'get_user_overview_stats',
  },
  {
    id: 'total_likes',
    label: 'Likes reçus',
    category: 'engagement',
    visualizations: ['card', 'line_chart'],
    access: ['artist', 'admin'],
    backend: 'rpc',
    query: 'get_user_overview_stats',
  },
  {
    id: 'total_comments',
    label: 'Commentaires reçus',
    category: 'engagement',
    visualizations: ['card', 'line_chart'],
    access: ['artist', 'admin'],
    backend: 'rpc',
    query: 'get_user_overview_stats',
  },
  {
    id: 'engagement_rate',
    label: 'Taux d\'engagement',
    category: 'engagement',
    visualizations: ['card'],
    access: ['artist', 'admin'],
    backend: 'rpc',
    query: 'get_user_overview_stats',
  },
  // === CATALOGUE ===
  {
    id: 'songs_count',
    label: 'Nombre de morceaux publiés',
    category: 'catalogue',
    visualizations: ['card'],
    access: ['artist', 'admin'],
    backend: 'direct_query',
    query: 'songs',
    description: 'Nombre total de morceaux publiés par l\'utilisateur.'
  },
  {
    id: 'liked_songs_count',
    label: 'Nombre de morceaux likés',
    category: 'catalogue',
    visualizations: ['card'],
    access: ['artist', 'admin'],
    backend: 'direct_query',
    query: 'song_likes',
    description: 'Nombre de morceaux de l\'utilisateur ayant reçu au moins un like.'
  },
  {
    id: 'songs_played_count',
    label: 'Nombre de morceaux écoutés',
    category: 'catalogue',
    visualizations: ['card'],
    access: ['artist', 'admin'],
    backend: 'aggregate',
    query: 'song_plays',
    description: 'Nombre total de morceaux différents écoutés.'
  },
  {
    id: 'total_followers',
    label: 'Nombre de followers',
    category: 'social',
    visualizations: ['card', 'line_chart'],
    access: ['artist', 'admin'],
    backend: 'direct_query',
    query: 'follows',
    description: 'Nombre total de followers de l\'utilisateur.'
  },
  // === VISUALISATIONS ===
  {
    id: 'plays_timeline',
    label: 'Évolution des écoutes',
    category: 'performance',
    visualizations: ['line_chart'],
    access: ['artist', 'admin'],
    backend: 'rpc',
    query: 'get_activity_timeline',
  },
  {
    id: 'genre_performance',
    label: 'Performance par genre',
    category: 'performance',
    visualizations: ['bar_chart'],
    access: ['artist', 'admin'],
    backend: 'rpc',
    query: 'get_genre_performance',
  },
  {
    id: 'audience_demographics',
    label: 'Démographie de l\'audience',
    category: 'audience',
    visualizations: ['pie_chart', 'table'],
    access: ['artist', 'admin'],
    backend: 'rpc',
    query: 'get_audience_demographics_summary',
  },
  // === AUTRES (à compléter selon besoin) ===
];
