<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MOUVIK DISCOVER ULTIMATE - Exploration Musicale Révolutionnaire</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/three@0.136.0/build/three.min.js">
    <script src="https://cdn.jsdelivr.net/npm/three@0.136.0/build/three.min.js"></script>
    <style>
        :root {
            --primary-dark: #041a1c;
            --primary: #04383c;
            --primary-light: #06565c;
            --accent: #1be8f3;
            --accent-soft: rgba(27, 232, 243, 0.2);
            --gradient-main: linear-gradient(135deg, #041a1c 0%, #04383c 100%);
            --gradient-accent: linear-gradient(135deg, #04383c 0%, #1be8f3 100%);
        }
        
        body {
            background: var(--primary-dark);
            color: white;
            font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--primary-dark);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--primary-light);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--accent);
        }
        
        .gradient-border {
            position: relative;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .gradient-border::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 0.5rem;
            padding: 2px;
            background: var(--gradient-accent);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
        }
        
        .card {
            background: rgba(4, 56, 60, 0.5);
            border-radius: 0.5rem;
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        
        .universe-container {
            perspective: 1000px;
            height: 600px;
            position: relative;
            overflow: hidden;
            margin-bottom: 2rem;
            border-radius: 0.75rem;
            background: linear-gradient(to bottom, rgba(4, 26, 28, 0.9), rgba(4, 56, 60, 0.7));
        }
        
        .universe {
            transform-style: preserve-3d;
            position: absolute;
            width: 100%;
            height: 100%;
            transform: rotateX(20deg);
            transition: transform 1s ease;
        }
        
        .orbit {
            position: absolute;
            top: 50%;
            left: 50%;
            border: 1px solid rgba(27, 232, 243, 0.3);
            border-radius: 50%;
            transform-style: preserve-3d;
            animation: rotate 60s linear infinite;
        }
        
        .genre-node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--gradient-accent);
            transform-style: preserve-3d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 15px rgba(27, 232, 243, 0.6);
        }
        
        .genre-node:hover {
            transform: scale(1.2);
            box-shadow: 0 0 25px rgba(27, 232, 243, 0.9);
        }
        
        .connection-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--accent), transparent);
            transform-origin: left center;
            opacity: 0.6;
        }
        
        @keyframes rotate {
            from { transform: translate(-50%, -50%) rotateZ(0deg); }
            to { transform: translate(-50%, -50%) rotateZ(360deg); }
        }
        
        .time-machine {
            height: 200px;
            background: linear-gradient(to right, rgba(4, 26, 28, 0.9), rgba(6, 86, 92, 0.7));
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
        }
        
        .time-line {
            position: absolute;
            top: 50%;
            left: 0;
            height: 4px;
            width: 100%;
            background: var(--accent);
            transform: translateY(-50%);
            z-index: 1;
        }
        
        .time-node {
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary);
            border: 2px solid var(--accent);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 2;
            transform: translateY(-50%);
            top: 50%;
        }
        
        .time-node:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 0 15px var(--accent-soft);
        }
        
        .mood-map {
            height: 300px;
            background: linear-gradient(135deg, rgba(4, 26, 28, 0.9), rgba(6, 86, 92, 0.7));
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
        }
        
        .mood-point {
            position: absolute;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
        }
        
        .mood-point:hover {
            transform: scale(1.5);
            box-shadow: 0 0 15px white;
        }
        
        .global-listeners {
            height: 350px;
            background: linear-gradient(135deg, rgba(4, 26, 28, 0.9), rgba(6, 86, 92, 0.7));
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
        }
        
        .globe {
            position: absolute;
            width: 300px;
            height: 300px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, var(--primary-light), var(--primary-dark));
            box-shadow: 0 0 50px rgba(27, 232, 243, 0.3);
        }
        
        .listener-point {
            position: absolute;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: var(--accent);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(2); opacity: 0.5; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .waveform {
            height: 60px;
            background: linear-gradient(90deg, var(--primary-dark), var(--primary-light), var(--primary-dark));
            position: relative;
            overflow: hidden;
            border-radius: 0.25rem;
        }
        
        .wave {
            position: absolute;
            height: 100%;
            width: 100%;
            background: linear-gradient(to bottom, transparent 40%, var(--accent) 40%, var(--accent) 60%, transparent 60%);
            mask: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1200 60" xmlns="http://www.w3.org/2000/svg"><path d="M0,30 Q300,0 600,30 T1200,30" fill="none" stroke="white" stroke-width="2"/></svg>') repeat-x;
            animation: wave 10s linear infinite;
        }
        
        @keyframes wave {
            0% { transform: translateX(0); }
            100% { transform: translateX(-100%); }
        }
        
        .playlist-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 1.5rem;
        }
        
        .track-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            background: rgba(4, 56, 60, 0.3);
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
        }
        
        .track-item:hover {
            background: rgba(4, 56, 60, 0.6);
            transform: translateX(5px);
        }
        
        .player {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(4, 26, 28, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem;
            z-index: 100;
            border-top: 1px solid rgba(27, 232, 243, 0.3);
        }
        
        .progress-bar {
            height: 4px;
            width: 100%;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
            cursor: pointer;
        }
        
        .progress {
            position: absolute;
            height: 100%;
            width: 30%;
            background: var(--accent);
            border-radius: 2px;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(4, 26, 28, 0.95);
            z-index: 200;
            display: none;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(10px);
        }
        
        .modal-content {
            background: var(--primary);
            border-radius: 0.5rem;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            position: relative;
        }
        
        .brain-wave {
            position: relative;
            height: 100px;
            margin-bottom: 1.5rem;
            background: rgba(4, 56, 60, 0.3);
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .wave-line {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: transparent;
        }
        
        .wave-line::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--accent);
            animation: brainwave 3s ease infinite;
        }
        
        @keyframes brainwave {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(30px) scaleY(2); opacity: 0.5; }
        }
        
        .ai-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            background: var(--accent-soft);
            color: var(--accent);
            border: 1px solid var(--accent);
        }
        
        /* Animations for content */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease forwards;
        }
        
        /* Dynamic album covers */
        .album-dynamic {
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
            transition: transform 0.3s ease;
        }
        
        .album-dynamic::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(27, 232, 243, 0.3), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .album-dynamic:hover {
            transform: scale(1.05);
        }
        
        .album-dynamic:hover::before {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 pb-32">
        <!-- Header -->
        <header class="py-6 mb-6">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <div class="mr-3">
                        <!-- Mouvik Logo -->
                        <svg viewBox="0 0 50 50" width="40" height="40">
                            <path fill="#1be8f3" d="M25,10 C15,10 10,20 15,30 C20,40 30,40 35,30 C40,20 35,10 25,10 Z"></path>
                            <path fill="none" stroke="#1be8f3" stroke-width="2" d="M35,30 C40,25 45,30 40,35"></path>
                            <path fill="none" stroke="#1be8f3" stroke-width="2" d="M40,25 L45,20 M45,25 L40,20"></path>
                        </svg>
                    </div>
                    <h1 class="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-teal-400">MOUVIK DISCOVER</h1>
                    <span class="ml-2 px-2 py-1 bg-gradient-to-r from-teal-500 to-teal-300 text-xs font-bold text-black rounded">ULTIMATE</span>
                </div>
                <div class="flex space-x-4">
                    <button class="bg-opacity-20 bg-white px-4 py-2 rounded-full hover:bg-opacity-30 transition-all flex items-center">
                        <i class="fas fa-search mr-2"></i> Recherche
                    </button>
                    <div class="w-10 h-10 rounded-full bg-gradient-to-r from-teal-500 to-teal-300 flex items-center justify-center">
                        <span class="font-bold text-black">JD</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Universe Navigation System -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i class="fas fa-galaxy mr-2 text-teal-400"></i>
                Univers Musical 3D
                <span class="ml-2 text-sm font-normal text-gray-400"><i class="fas fa-info-circle mr-1"></i> Navigation spatiale</span>
            </h2>
            
            <div class="universe-container gradient-border">
                <div class="universe">
                    <!-- Orbits and Genre Nodes will be created by JS -->
                </div>
                <div class="absolute bottom-4 left-4 right-4 px-4 py-3 bg-black bg-opacity-70 backdrop-filter backdrop-blur-sm rounded-lg">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="font-bold text-teal-300">Navigation 3D</h3>
                            <p class="text-sm text-gray-300">Explorez le cosmos musical par genre, influence et mood</p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-teal-800 bg-opacity-50 rounded hover:bg-opacity-70">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="px-3 py-1 bg-teal-800 bg-opacity-50 rounded hover:bg-opacity-70">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button class="px-3 py-1 bg-teal-800 bg-opacity-50 rounded hover:bg-opacity-70">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- IA Hyper-Contextuelle -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i class="fas fa-brain mr-2 text-teal-400"></i>
                Intelligence IA Hyper-Contextuelle
                <span class="ml-2 text-sm font-normal text-gray-400"><i class="fas fa-bolt mr-1"></i> Analyse en temps réel</span>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="card p-4 col-span-1">
                    <h3 class="text-lg font-semibold text-teal-300 mb-3">Votre Profil Musical</h3>
                    <div class="brain-wave mb-4">
                        <div class="wave-line" style="top: 30%;"></div>
                        <div class="wave-line" style="top: 50%;"></div>
                        <div class="wave-line" style="top: 70%;"></div>
                    </div>
                    <div class="mb-4">
                        <h4 class="text-sm text-gray-300 mb-2">Préférences détectées</h4>
                        <div class="flex flex-wrap">
                            <span class="ai-tag">Indie Folk 87%</span>
                            <span class="ai-tag">Post-Rock 76%</span>
                            <span class="ai-tag">Alternative 72%</span>
                            <span class="ai-tag">Jazz Fusion 65%</span>
                            <span class="ai-tag">Ambient 61%</span>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm text-gray-300 mb-2">Modèle d'écoute</h4>
                        <div class="bg-gray-800 bg-opacity-50 rounded-lg p-3">
                            <div class="flex justify-between mb-1">
                                <span class="text-xs">Matin</span>
                                <span class="text-xs text-teal-300">Ambiance calme</span>
                            </div>
                            <div class="flex justify-between mb-1">
                                <span class="text-xs">Après-midi</span>
                                <span class="text-xs text-teal-300">Rythmé & Énergique</span>
                            </div>
                            <div class="flex justify-between mb-1">
                                <span class="text-xs">Soir</span>
                                <span class="text-xs text-teal-300">Introspectif</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card p-4 col-span-1 md:col-span-2">
                    <h3 class="text-lg font-semibold text-teal-300 mb-3">Recommandations Hyper-Personnalisées</h3>
                    <p class="text-sm text-gray-300 mb-4">Notre IA analyse votre humeur, contexte et historique pour des suggestions parfaitement alignées.</p>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="bg-gray-800 bg-opacity-30 rounded-lg p-3 flex items-center">
                            <div class="w-16 h-16 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                <div class="w-full h-full bg-gradient-to-br from-purple-600 to-blue-400"></div>
                            </div>
                            <div>
                                <h4 class="font-medium">Morning Atmosphere</h4>
                                <p class="text-xs text-gray-400">Playlist matinale créée pour votre humeur actuelle</p>
                                <div class="flex items-center mt-1">
                                    <div class="text-xs px-2 py-0.5 bg-teal-900 bg-opacity-50 rounded text-teal-300 mr-2">98% match</div>
                                    <i class="fas fa-play-circle text-white"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-800 bg-opacity-30 rounded-lg p-3 flex items-center">
                            <div class="w-16 h-16 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                <div class="w-full h-full bg-gradient-to-br from-amber-600 to-orange-400"></div>
                            </div>
                            <div>
                                <h4 class="font-medium">Crystal Echoes</h4>
                                <p class="text-xs text-gray-400">Artiste correspondant à vos écoutes récentes</p>
                                <div class="flex items-center mt-1">
                                    <div class="text-xs px-2 py-0.5 bg-teal-900 bg-opacity-50 rounded text-teal-300 mr-2">96% match</div>
                                    <i class="fas fa-play-circle text-white"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-800 bg-opacity-30 rounded-lg p-3 flex items-center">
                            <div class="w-16 h-16 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                <div class="w-full h-full bg-gradient-to-br from-green-600 to-emerald-400"></div>
                            </div>
                            <div>
                                <h4 class="font-medium">Harmonic Transitions</h4>
                                <p class="text-xs text-gray-400">Album suggéré pour votre session créative</p>
                                <div class="flex items-center mt-1">
                                    <div class="text-xs px-2 py-0.5 bg-teal-900 bg-opacity-50 rounded text-teal-300 mr-2">94% match</div>
                                    <i class="fas fa-play-circle text-white"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-800 bg-opacity-30 rounded-lg p-3 flex items-center">
                            <div class="w-16 h-16 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                <div class="w-full h-full bg-gradient-to-br from-blue-600 to-indigo-400"></div>
                            </div>
                            <div>
                                <h4 class="font-medium">Sonic Exploration</h4>
                                <p class="text-xs text-gray-400">Mix basé sur votre évolution d'écoute</p>
                                <div class="flex items-center mt-1">
                                    <div class="text-xs px-2 py-0.5 bg-teal-900 bg-opacity-50 rounded text-teal-300 mr-2">91% match</div>
                                    <i class="fas fa-play-circle text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 bg-gradient-to-r from-teal-800 to-teal-700 bg-opacity-30 p-3 rounded-lg">
                        <div class="flex items-start">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center bg-teal-600 mr-3 flex-shrink-0">
                                <i class="fas fa-lightbulb text-black"></i>
                            </div>
                            <div>
                                <h4 class="font-medium">Suggestion IA • Moment musical</h4>
                                <p class="text-sm text-gray-300">Basé sur votre activité récente et l'heure actuelle, notre IA suggère d'explorer les créations de <span class="text-teal-300">Luminous Tide</span> — un artiste qui combine ambiances électroniques et structures mélodiques similaires à vos favoris récents.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Playlists Nouvelle Génération -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i class="fas fa-layer-group mr-2 text-teal-400"></i>
                Playlists Nouvelle Génération
                <span class="ml-2 text-sm font-normal text-gray-400"><i class="fas fa-magic mr-1"></i> Organisation intelligente</span>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="card p-4 col-span-1">
                    <h3 class="text-lg font-semibold text-teal-300 mb-4">Vos Collections</h3>
                    
                    <div class="mb-4">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-medium">Playlists Récentes</h4>
                            <button class="text-xs text-teal-300 hover:text-teal-100">Voir tout</button>
                        </div>
                        
                        <div class="space-y-2">
                            <div class="track-item">
                                <div class="w-10 h-10 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                    <div class="w-full h-full bg-gradient-to-br from-purple-600 to-pink-400"></div>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium">Ambiances Nocturnes</h5>
                                    <p class="text-xs text-gray-400">32 titres • Créée par vous</p>
                                </div>
                            </div>
                            
                            <div class="track-item">
                                <div class="w-10 h-10 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                    <div class="w-full h-full bg-gradient-to-br from-blue-600 to-cyan-400"></div>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium">Deep Focus</h5>
                                    <p class="text-xs text-gray-400">18 titres • Créée par vous</p>
                                </div>
                            </div>
                            
                            <div class="track-item">
                                <div class="w-10 h-10 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                    <div class="w-full h-full bg-gradient-to-br from-amber-600 to-red-400"></div>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium">Summer Vibes 2023</h5>
                                    <p class="text-xs text-gray-400">45 titres • Créée par vous</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button class="w-full bg-teal-700 bg-opacity-30 hover:bg-opacity-50 text-teal-300 py-2 rounded-lg transition-all flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i> Créer une playlist
                    </button>
                </div>
                
                <div class="card p-4 col-span-1 md:col-span-2">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-teal-300">Mood Mapping</h3>
                        <button class="text-xs text-teal-300 bg-teal-900 bg-opacity-50 hover:bg-opacity-70 px-3 py-1 rounded">
                            <i class="fas fa-sliders-h mr-1"></i> Ajuster
                        </button>
                    </div>
                    
                    <div class="mood-map mb-6">
                        <div class="mood-point" style="top: 30%; left: 20%; background: #f472b6;"></div>
                        <div class="mood-point" style="top: 50%; left: 35%; background: #60a5fa;"></div>
                        <div class="mood-point" style="top: 70%; left: 55%; background: #34d399;"></div>
                        <div class="mood-point" style="top: 25%; left: 65%; background: #fbbf24;"></div>
                        <div class="mood-point" style="top: 60%; left: 80%; background: #a78bfa;"></div>
                        
                        <div class="absolute bottom-3 left-3 right-3 bg-black bg-opacity-70 backdrop-filter backdrop-blur p-2 rounded text-xs">
                            <div class="flex items-center">
                                <span class="text-gray-400 mr-2">Axes:</span>
                                <span class="text-teal-300 mr-1">X</span> = Énergie,
                                <span class="text-teal-300 mx-1">Y</span> = Positivité
                            </div>
                        </div>
                    </div>
                    
                    <h4 class="font-medium mb-3">Transitions Parfaites</h4>
                    <p class="text-sm text-gray-300 mb-4">Notre algorithme analyse les BPM, tonalités et transitions harmoniques pour créer des enchaînements fluides.</p>
                    
                    <div class="bg-gray-800 bg-opacity-30 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h5 class="font-medium">Flow Dynamique</h5>
                            <div class="text-xs text-teal-300">
                                <i class="fas fa-random mr-1"></i> Auto-généré
                            </div>
                        </div>
                        
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-purple-700 flex items-center justify-center mr-2 text-xs">1</div>
                                <div class="flex-grow">
                                    <div class="text-sm">Morning Light</div>
                                    <div class="text-xs text-gray-400">Ambient • 68 BPM • Am</div>
                                </div>
                                <div class="text-gray-400 text-xs">3:45</div>
                            </div>
                            
                            <div class="flex items-center justify-center">
                                <div class="text-teal-300 text-xs">
                                    <i class="fas fa-arrow-down mr-1"></i> Transition parfaite (compatible tonalité & tempo)
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-blue-700 flex items-center justify-center mr-2 text-xs">2</div>
                                <div class="flex-grow">
                                    <div class="text-sm">Oceanic Depths</div>
                                    <div class="text-xs text-gray-400">Downtempo • 72 BPM • C</div>
                                </div>
                                <div class="text-gray-400 text-xs">4:18</div>
                            </div>
                            
                            <div class="flex items-center justify-center">
                                <div class="text-teal-300 text-xs">
                                    <i class="fas fa-arrow-down mr-1"></i> Transition harmonique (progression complémentaire)
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-teal-700 flex items-center justify-center mr-2 text-xs">3</div>
                                <div class="flex-grow">
                                    <div class="text-sm">Ethereal Journey</div>
                                    <div class="text-xs text-gray-400">Electronic • 80 BPM • Em</div>
                                </div>
                                <div class="text-gray-400 text-xs">5:22</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Visualisations Immersives -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i class="fas fa-project-diagram mr-2 text-teal-400"></i>
                Connections & Influences
                <span class="ml-2 text-sm font-normal text-gray-400"><i class="fas fa-network-wired mr-1"></i> Visualisations immersives</span>
            </h2>
            
            <div class="card p-5">
                <div class="flex flex-col md:flex-row gap-6">
                    <div class="w-full md:w-1/3">
                        <h3 class="text-lg font-semibold text-teal-300 mb-3">Cartographie d'Influence</h3>
                        <p class="text-sm text-gray-300 mb-4">Explorez les connections entre artistes et genres, découvrez les influences cachées et les similarités musicales.</p>
                        
                        <div class="space-y-4">
                            <div class="bg-gray-800 bg-opacity-30 rounded-lg p-3">
                                <h4 class="font-medium mb-2">Focus Actuel</h4>
                                <div class="flex items-center">
                                    <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-600 to-blue-400 flex-shrink-0 mr-3"></div>
                                    <div>
                                        <div class="font-medium">Luminous Tide</div>
                                        <div class="text-xs text-gray-400">Ambient Electronic • Post-Rock</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="font-medium mb-2">Filtres</h4>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm">Profondeur</span>
                                        <input type="range" class="w-24" min="1" max="5" value="3">
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm">Type</span>
                                        <select class="bg-gray-800 text-xs rounded px-2 py-1 border-0">
                                            <option>Tous</option>
                                            <option>Influences</option>
                                            <option>Similaires</option>
                                            <option>Collaborations</option>
                                        </select>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm">Temporalité</span>
                                        <select class="bg-gray-800 text-xs rounded px-2 py-1 border-0">
                                            <option>Tous</option>
                                            <option>Contemporains</option>
                                            <option>Historiques</option>
                                            <option>Émergents</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="w-full bg-teal-700 bg-opacity-30 hover:bg-opacity-50 text-teal-300 py-2 rounded-lg transition-all flex items-center justify-center">
                                <i class="fas fa-sync-alt mr-2"></i> Actualiser la vue
                            </button>
                        </div>
                    </div>
                    
                    <div class="w-full md:w-2/3 h-80 bg-gray-900 rounded-lg relative overflow-hidden">
                        <!-- Connection Map Visualization would be rendered here with D3.js or similar -->
                        <div class="h-full w-full bg-gradient-to-br from-gray-900 to-primary flex items-center justify-center">
                            <!-- This would be an interactive visualization -->
                            <div class="absolute" style="top: 40%; left: 50%; transform: translate(-50%, -50%);">
                                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-600 to-blue-400 border-2 border-white flex items-center justify-center z-10">
                                    <span class="text-xs font-bold">LT</span>
                                </div>
                            </div>
                            <div class="absolute" style="top: 25%; left: 35%;">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-600 to-cyan-400 flex items-center justify-center z-10">
                                    <span class="text-xs font-bold">AP</span>
                                </div>
                            </div>
                            <div class="absolute" style="top: 65%; left: 30%;">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-teal-600 to-green-400 flex items-center justify-center z-10">
                                    <span class="text-xs font-bold">ES</span>
                                </div>
                            </div>
                            <div class="absolute" style="top: 30%; left: 65%;">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-amber-600 to-red-400 flex items-center justify-center z-10">
                                    <span class="text-xs font-bold">MR</span>
                                </div>
                            </div>
                            <div class="absolute" style="top: 60%; left: 70%;">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-600 to-violet-400 flex items-center justify-center z-10">
                                    <span class="text-xs font-bold">SH</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Connection lines would be rendered dynamically -->
                        <div class="absolute" style="top: 40%; left: 50%; height: 2px; width: 15%; transform: rotate(-45deg); transform-origin: left center;">
                            <div class="w-full h-full bg-gradient-to-r from-purple-400 to-blue-400"></div>
                        </div>
                        <div class="absolute" style="top: 40%; left: 50%; height: 2px; width: 20%; transform: rotate(-165deg); transform-origin: left center;">
                            <div class="w-full h-full bg-gradient-to-r from-purple-400 to-teal-400"></div>
                        </div>
                        <div class="absolute" style="top: 40%; left: 50%; height: 2px; width: 15%; transform: rotate(35deg); transform-origin: left center;">
                            <div class="w-full h-full bg-gradient-to-r from-purple-400 to-amber-400"></div>
                        </div>
                        <div class="absolute" style="top: 40%; left: 50%; height: 2px; width: 20%; transform: rotate(135deg); transform-origin: left center;">
                            <div class="w-full h-full bg-gradient-to-r from-purple-400 to-indigo-400"></div>
                        </div>
                        
                        <div class="absolute top-3 right-3 bg-black bg-opacity-70 backdrop-filter backdrop-blur p-2 rounded">
                            <div class="flex items-center space-x-2">
                                <button class="text-xs text-white bg-gray-700 bg-opacity-50 hover:bg-opacity-70 p-1 rounded">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="text-xs text-white bg-gray-700 bg-opacity-50 hover:bg-opacity-70 p-1 rounded">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button class="text-xs text-white bg-gray-700 bg-opacity-50 hover:bg-opacity-70 p-1 rounded">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="absolute bottom-3 left-3 bg-black bg-opacity-70 backdrop-filter backdrop-blur p-2 rounded text-xs">
                            <div class="flex items-center">
                                <span class="inline-block w-3 h-3 rounded-full bg-purple-400 mr-1"></span>
                                <span class="mr-2">Focus principal</span>
                                <span class="inline-block w-3 h-3 rounded-full bg-teal-400 mr-1"></span>
                                <span class="mr-2">Influence directe</span>
                                <span class="inline-block w-3 h-3 rounded-full bg-indigo-400 mr-1"></span>
                                <span>Similaire</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Time Machine -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i class="fas fa-clock mr-2 text-teal-400"></i>
                Time Machine
                <span class="ml-2 text-sm font-normal text-gray-400"><i class="fas fa-history mr-1"></i> Voyage musical temporel</span>
            </h2>
            
            <div class="card p-5">
                <h3 class="text-lg font-semibold text-teal-300 mb-4">Explorez l'Évolution Musicale</h3>
                <p class="text-sm text-gray-300 mb-6">Voyagez à travers les époques pour découvrir comment la musique a évolué et s'est transformée au fil du temps.</p>
                
                <div class="time-machine mb-6">
                    <div class="time-line"></div>
                    
                    <div class="time-node" style="left: 5%;">
                        <span>1960s</span>
                    </div>
                    <div class="time-node" style="left: 25%;">
                        <span>1980s</span>
                    </div>
                    <div class="time-node" style="left: 45%;">
                        <span>2000s</span>
                    </div>
                    <div class="time-node" style="left: 65%;">
                        <span>2010s</span>
                    </div>
                    <div class="time-node" style="left: 85%;">
                        <span>2020s</span>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div class="bg-gray-800 bg-opacity-30 p-3 rounded-lg">
                        <h4 class="font-medium text-sm mb-2">Post-Punk Revival</h4>
                        <div class="text-xs text-gray-400 mb-3">Début 2000s</div>
                        <div class="mb-2">
                            <div class="album-dynamic w-full h-24 bg-gradient-to-br from-red-600 to-gray-800"></div>
                        </div>
                        <button class="w-full bg-teal-800 bg-opacity-30 hover:bg-opacity-50 text-teal-300 py-1 text-xs rounded transition-all">
                            Explorer
                        </button>
                    </div>
                    
                    <div class="bg-gray-800 bg-opacity-30 p-3 rounded-lg">
                        <h4 class="font-medium text-sm mb-2">Chillwave</h4>
                        <div class="text-xs text-gray-400 mb-3">Fin 2000s</div>
                        <div class="mb-2">
                            <div class="album-dynamic w-full h-24 bg-gradient-to-br from-purple-600 to-pink-800"></div>
                        </div>
                        <button class="w-full bg-teal-800 bg-opacity-30 hover:bg-opacity-50 text-teal-300 py-1 text-xs rounded transition-all">
                            Explorer
                        </button>
                    </div>
                    
                    <div class="bg-gray-800 bg-opacity-30 p-3 rounded-lg">
                        <h4 class="font-medium text-sm mb-2">Alt-R&B</h4>
                        <div class="text-xs text-gray-400 mb-3">Début 2010s</div>
                        <div class="mb-2">
                            <div class="album-dynamic w-full h-24 bg-gradient-to-br from-blue-600 to-indigo-800"></div>
                        </div>
                        <button class="w-full bg-teal-800 bg-opacity-30 hover:bg-opacity-50 text-teal-300 py-1 text-xs rounded transition-all">
                            Explorer
                        </button>
                    </div>
                    
                    <div class="bg-gray-800 bg-opacity-30 p-3 rounded-lg">
                        <h4 class="font-medium text-sm mb-2">Bedroom Pop</h4>
                        <div class="text-xs text-gray-400 mb-3">Mi 2010s</div>
                        <div class="mb-2">
                            <div class="album-dynamic w-full h-24 bg-gradient-to-br from-teal-600 to-green-800"></div>
                        </div>
                        <button class="w-full bg-teal-800 bg-opacity-30 hover:bg-opacity-50 text-teal-300 py-1 text-xs rounded transition-all">
                            Explorer
                        </button>
                    </div>
                    
                    <div class="bg-gray-800 bg-opacity-30 p-3 rounded-lg">
                        <h4 class="font-medium text-sm mb-2">Hyperpop</h4>
                        <div class="text-xs text-gray-400 mb-3">Début 2020s</div>
                        <div class="mb-2">
                            <div class="album-dynamic w-full h-24 bg-gradient-to-br from-yellow-600 to-red-800"></div>
                        </div>
                        <button class="w-full bg-teal-800 bg-opacity-30 hover:bg-opacity-50 text-teal-300 py-1 text-xs rounded transition-all">
                            Explorer
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Global Listeners -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i class="fas fa-globe-americas mr-2 text-teal-400"></i>
                Communauté Mondiale
                <span class="ml-2 text-sm font-normal text-gray-400"><i class="fas fa-users mr-1"></i> Écoute en temps réel</span>
            </h2>
            
            <div class="card p-5">
                <div class="flex flex-col md:flex-row gap-6">
                    <div class="w-full md:w-2/3">
                        <h3 class="text-lg font-semibold text-teal-300 mb-3">Écoutant Actuellement</h3>
                        <div class="global-listeners">
                            <div class="globe">
                                <!-- Listener points would be generated dynamically -->
                                <div class="listener-point" style="top: 30%; left: 20%;"></div>
                                <div class="listener-point" style="top: 45%; left: 35%;"></div>
                                <div class="listener-point" style="top: 60%; left: 50%;"></div>
                                <div class="listener-point" style="top: 40%; left: 65%;"></div>
                                <div class="listener-point" style="top: 55%; left: 80%;"></div>
                            </div>
                            
                            <div class="absolute bottom-3 left-3 right-3 bg-black bg-opacity-70 backdrop-filter backdrop-blur p-2 rounded text-xs">
                                <div class="text-center">
                                    <span class="text-teal-300 font-medium">5,283</span>
                                    <span class="text-gray-300"> personnes écoutent de la musique sur Mouvik en ce moment</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="w-full md:w-1/3">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-lg font-semibold text-teal-300">Live Updates</h3>
                            <span class="text-xs bg-teal-900 text-teal-300 px-2 py-0.5 rounded-full">En direct</span>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="bg-gray-800 bg-opacity-30 p-3 rounded-lg">
                                <div class="flex items-start mb-2">
                                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-purple-600 to-pink-400 flex-shrink-0 mr-2"></div>
                                    <div>
                                        <div class="text-sm font-medium">Alejandro P.</div>
                                        <div class="text-xs text-gray-400">Tokyo, Japon</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-300">
                                    Écoute <span class="text-teal-300">Moonlight Sonata (Remix)</span> par Crystal Waves
                                </div>
                            </div>
                            
                            <div class="bg-gray-800 bg-opacity-30 p-3 rounded-lg">
                                <div class="flex items-start mb-2">
                                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-600 to-cyan-400 flex-shrink-0 mr-2"></div>
                                    <div>
                                        <div class="text-sm font-medium">Marie L.</div>
                                        <div class="text-xs text-gray-400">Paris, France</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-300">
                                    A ajouté <span class="text-teal-300">Atmospheric Journey</span> à sa playlist "Evening Vibes"
                                </div>
                            </div>
                            
                            <div class="bg-gray-800 bg-opacity-30 p-3 rounded-lg">
                                <div class="flex items-start mb-2">
                                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-amber-600 to-red-400 flex-shrink-0 mr-2"></div>
                                    <div>
                                        <div class="text-sm font-medium">John D.</div>
                                        <div class="text-xs text-gray-400">New York, USA</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-300">
                                    Vient de découvrir l'artiste <span class="text-teal-300">Luminous Tide</span>
                                </div>
                            </div>
                        </div>
                        
                        <button class="w-full bg-teal-700 bg-opacity-30 hover:bg-opacity-50 text-teal-300 py-2 rounded-lg transition-all">
                            Explorer la communauté
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- For You Section -->
        <section class="mb-16">
            <h2 class="text-2xl font-bold mb-4 flex items-center">
                <i class="fas fa-star mr-2 text-teal-400"></i>
                Conçu Pour Vous
                <span class="ml-2 text-sm font-normal text-gray-400"><i class="fas fa-sparkles mr-1"></i> Sélection personnalisée</span>
            </h2>
            
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
                <div class="card p-3">
                    <div class="album-dynamic w-full aspect-square bg-gradient-to-br from-purple-600 to-blue-800 mb-3"></div>
                    <h3 class="font-medium text-sm">Ethereal Dreams</h3>
                    <p class="text-xs text-gray-400">Luminous Tide</p>
                </div>
                
                <div class="card p-3">
                    <div class="album-dynamic w-full aspect-square bg-gradient-to-br from-teal-600 to-green-800 mb-3"></div>
                    <h3 class="font-medium text-sm">Crystal Visions</h3>
                    <p class="text-xs text-gray-400">Emerald Skies</p>
                </div>
                
                <div class="card p-3">
                    <div class="album-dynamic w-full aspect-square bg-gradient-to-br from-pink-600 to-purple-800 mb-3"></div>
                    <h3 class="font-medium text-sm">Night Echoes</h3>
                    <p class="text-xs text-gray-400">Midnight Runners</p>
                </div>
                
                <div class="card p-3">
                    <div class="album-dynamic w-full aspect-square bg-gradient-to-br from-amber-600 to-red-800 mb-3"></div>
                    <h3 class="font-medium text-sm">Solar Flares</h3>
                    <p class="text-xs text-gray-400">Cosmic Drift</p>
                </div>
                
                <div class="card p-3">
                    <div class="album-dynamic w-full aspect-square bg-gradient-to-br from-blue-600 to-indigo-800 mb-3"></div>
                    <h3 class="font-medium text-sm">Deep Blue</h3>
                    <p class="text-xs text-gray-400">Ocean Pulse</p>
                </div>
                
                <div class="card p-3">
                    <div class="album-dynamic w-full aspect-square bg-gradient-to-br from-green-600 to-teal-800 mb-3"></div>
                    <h3 class="font-medium text-sm">Forest Whispers</h3>
                    <p class="text-xs text-gray-400">Nature's Echo</p>
                </div>
            </div>
            
            <div class="card p-5">
                <h3 class="text-lg font-semibold text-teal-300 mb-4">Expérience Multisensorielle</h3>
                <p class="text-sm text-gray-300 mb-6">Notre technologie synchronise visuels, couleurs et retour haptique avec votre musique pour une immersion complète.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-gray-800 bg-opacity-30 p-4 rounded-lg">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-teal-700 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-eye text-teal-300"></i>
                            </div>
                            <h4 class="font-medium">Visualisation Adaptative</h4>
                        </div>
                        <p class="text-sm text-gray-300">Éléments visuels qui réagissent et évoluent en fonction des caractéristiques audio de votre musique.</p>
                    </div>
                    
                    <div class="bg-gray-800 bg-opacity-30 p-4 rounded-lg">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-teal-700 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-palette text-teal-300"></i>
                            </div>
                            <h4 class="font-medium">Chromesthésie Musicale</h4>
                        </div>
                        <p class="text-sm text-gray-300">Palette chromatique dynamique évoluant avec les tonalités et émotions de la musique.</p>
                    </div>
                    
                    <div class="bg-gray-800 bg-opacity-30 p-4 rounded-lg">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-teal-700 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-hand-sparkles text-teal-300"></i>
                            </div>
                            <h4 class="font-medium">Retour Haptique</h4>
                        </div>
                        <p class="text-sm text-gray-300">Sensation tactile synchronisée avec les battements et les moments forts de la musique.</p>
                    </div>
                </div>
            </div>
        </section>
    </div>
    
    <!-- Player -->
    <div class="player">
        <div class="flex items-center justify-between mb-2">
            <div class="flex items-center mr-4">
                <div class="w-12 h-12 rounded overflow-hidden mr-3">
                    <div class="w-full h-full bg-gradient-to-br from-purple-600 to-blue-400"></div>
                </div>
                <div>
                    <h5 class="font-medium leading-tight">Morning Atmosphere</h5>
                    <p class="text-sm text-gray-400">Luminous Tide</p>
                </div>
            </div>
            
            <div class="flex-grow px-4 hidden md:block">
                <div class="flex flex-col items-center">
                    <div class="flex items-center mb-1 space-x-4">
                        <button class="text-gray-400 hover:text-white">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button class="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center text-black">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="text-gray-400 hover:text-white">
                            <i class="fas fa-step-forward"></i>
                        </button>
                    </div>
                    <div class="w-full flex items-center text-xs">
                        <span class="text-gray-400 mr-2">1:45</span>
                        <div class="flex-grow">
                            <div class="progress-bar">
                                <div class="progress"></div>
                            </div>
                        </div>
                        <span class="text-gray-400 ml-2">5:30</span>
                    </div>
                </div>
            </div>
            
            <div class="flex items-center space-x-3">
                <button class="text-gray-400 hover:text-white">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button class="text-gray-400 hover:text-white">
                    <i class="fas fa-random"></i>
                </button>
                <button class="text-gray-400 hover:text-white">
                    <i class="fas fa-repeat"></i>
                </button>
                <button class="text-gray-400 hover:text-white">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>
        
        <div class="waveform">
            <div class="wave"></div>
        </div>
    </div>
    
    <script>
        // This would be a much more sophisticated implementation in a real app
        document.addEventListener('DOMContentLoaded', function() {
            // Create universe elements
            const universe = document.querySelector('.universe');
            const orbits = [300, 200, 400];
            const genres = [
                { name: 'Rock', color: '#f472b6' },
                { name: 'Jazz', color: '#60a5fa' },
                { name: 'Electronic', color: '#34d399' },
                { name: 'Classical', color: '#fbbf24' },
                { name: 'Hip-Hop', color: '#a78bfa' },
                { name: 'Folk', color: '#ef4444' },
                { name: 'R&B', color: '#2dd4bf' },
                { name: 'Metal', color: '#fb923c' }
            ];
            
            // Create orbits
            orbits.forEach((radius, i) => {
                const orbit = document.createElement('div');
                orbit.className = 'orbit';
                orbit.style.width = `${radius}px`;
                orbit.style.height = `${radius}px`;
                orbit.style.animationDelay = `${i * -4}s`;
                universe.appendChild(orbit);
                
                // Create genres on this orbit
                for (let j = 0; j < 3; j++) {
                    const genre = genres[(i * 3 + j) % genres.length];
                    const angle = (j / 3) * Math.PI * 2;
                    const x = Math.cos(angle) * (radius / 2);
                    const y = Math.sin(angle) * (radius / 2);
                    const z = (Math.random() - 0.5) * 50;
                    
                    const node = document.createElement('div');
                    node.className = 'genre-node';
                    node.style.background = genre.color;
                    node.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;
                    node.textContent = genre.name;
                    orbit.appendChild(node);
                }
            });
            
            // Add interactivity - this would be much more sophisticated in a real app
            const universeContainer = document.querySelector('.universe-container');
            let startX, startY, startRotX = 20, startRotY = 0;
            let isDragging = false;
            
            universeContainer.addEventListener('mousedown', function(e) {
                isDragging = true;
                startX = e.pageX;
                startY = e.pageY;
                e.preventDefault();
            });
            
            document.addEventListener('mousemove', function(e) {
                if (!isDragging) return;
                const dx = e.pageX - startX;
                const dy = e.pageY - startY;
                startRotY = startRotY + dx * 0.5;
                startRotX = Math.max(0, Math.min(45, startRotX + dy * 0.5));
                universe.style.transform = `rotateX(${startRotX}deg) rotateY(${startRotY}deg)`;
                startX = e.pageX;
                startY = e.pageY;
            });
            
            document.addEventListener('mouseup', function() {
                isDragging = false;
            });
            
            // Initialize animations for content sections
            const sections = document.querySelectorAll('section');
            const animateOnScroll = function() {
                sections.forEach(section => {
                    const sectionTop = section.getBoundingClientRect().top;
                    const sectionBottom = section.getBoundingClientRect().bottom;
                    if (sectionTop < window.innerHeight * 0.9 && sectionBottom > 0) {
                        section.classList.add('animate-fade-in-up');
                    }
                });
            };
            
            // Initial check and scroll event
            animateOnScroll();
            window.addEventListener('scroll', animateOnScroll);
        });
    </script>
</body>
</html>
    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDkkjqBueD9D3%2BYffh0iKdAXpd2YRGKNW3UfPjRmZFvqjqHpmrTzAJN2gt%2FQ%2Fuu3q64H%2FtGVXaj%2BGIrUW2Rg7uwIyI%2FpzURtR%2Bf7KJoYjJVbJKmtLqYa1YpVw0NbJ2i1oI4pmzuVTZImtvw0i2j7OMhlFkt5qNzbDhSnypZPyDUPtjoMyG4XtXh4%2BM85d00BXZhnQjcuszn%2FeipEcYJlrGnldrytJdqjJyTVlZ%2FcbWdvDNVvbHTggNEhoz1oUnQGSvqIdBNsCiDfXH6qLXq%2FIigkKEpGWKktfyZDbd6lURwn8ENbqmMyOhOUeQWaDNICiTIIu900Ed575A8a0oYDxwFk0XN1GIOdMriNoq7JrbKnoeiphTEsF2Z9NX5u%2FkHtEUnoVukkR4BqLeVfTWRGF%2BxHRaOIb8oD%2FVrjWm47fuNmjKePEs%2BZCbViebI1%2BEVhotX0yZbWFib9ClMBaiO1DTdzC09DCixw1WyRV7Ldm%2FzWKkVO0oKOMmbTpnSK5I9ZFwA%3D%3D";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDkkjqBueD9D3+Yffh0iKdAXpd2YRGKNW3UfPjRmZFvqjqHpmrTzAJN2gt/Q/uu3q64H/tGVXaj+GIrUW2Rg7uwIyI/pzURtR+f7KJoYjJVbJKmtLqYa1YpVw0NbJ2i1oI4pmzuVTZImtvw0i2j7OMhlFkt5qNzbDhSnypZPyDUPtjoMyG4XtXh4+M85d00BXZhnQjcuszn/eipEcYJlrGnldrytJdqjJyTVlZ/cbWdvDNVvbHTggNEhoz1oUnQGSvqIdBNsCiDfXH6qLXq/IigkKEpGWKktfyZDbd6lURwn8ENbqmMyOhOUeQWaDNICiTIIu900Ed575A8a0oYDxwFk0XN1GIOdMriNoq7JrbKnoeiphTEsF2Z9NX5u/kHtEUnoVukkR4BqLeVfTWRGF+xHRaOIb8oD/VrjWm47fuNmjKePEs+ZCbViebI1+EVhotX0yZbWFib9ClMBaiO1DTdzC09DCixw1WyRV7Ldm/zWKkVO0oKOMmbTpnSK5I9ZFwA==";
    </script>
    