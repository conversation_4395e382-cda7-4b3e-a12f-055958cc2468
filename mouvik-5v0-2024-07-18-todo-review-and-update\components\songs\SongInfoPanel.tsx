"use client";

import type { Song, Album } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Music2, Disc, CalendarDays, Users, Brain, Layers3, Info, Tag, GitBranch, FileText, Clock, BarChartHorizontalBig, Copyright, Fingerprint, ListMusic, Palette, Sparkles, Languages, Users2, UserSquare2, Building2, UserCheck2, ShieldCheck, ExternalLink, Link as LinkIcon, Guitar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SongInfoPanelProps {
  song: Partial<Song & { albums?: Pick<Album, 'id' | 'title' | 'slug'> | null }>;
  className?: string;
  showTopBadges?: boolean;
}

const DetailRow: React.FC<{ icon?: React.ElementType; label: string; value?: string | string[] | null | number | Date; isBadge?: boolean; isLink?: boolean; linkHref?: string; isList?: boolean }> = ({ icon: Icon, label, value, isBadge, isLink, linkHref, isList }) => {
  let displayContent: React.ReactNode;

  if (value === null || value === undefined || (typeof value === 'string' && value.trim() === '')) {
    displayContent = '—';
  } else if (isList && Array.isArray(value)) {
    displayContent = value.length > 0 ? value.map((item, index) => (
      <Badge key={`${item}-${index}`} variant="secondary" className="mr-1 mb-1 text-xs">{String(item)}</Badge>
    )) : '—';
  } else if (isBadge) {
    displayContent = <Badge variant="secondary" className="text-xs">{String(value)}</Badge>;
  } else if (isLink && typeof value === 'string' && linkHref) {
    displayContent = <a href={linkHref} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline flex items-center">{value} <ExternalLink className="ml-1 h-3 w-3" /></a>;
  } else if (value instanceof Date) {
    displayContent = format(value, 'PPP', { locale: fr });
  } else {
    displayContent = String(value);
  }

  return (
    <TableRow>
      <TableCell className="font-medium w-1/3">
        {Icon && <Icon className="inline-block mr-2 h-4 w-4 text-muted-foreground" />} 
        {label}
      </TableCell>
      <TableCell>{displayContent}</TableCell>
    </TableRow>
  );
};

export const SongInfoPanel: React.FC<SongInfoPanelProps> = ({ song, className, showTopBadges = true }) => {
  if (!song) return null;

  const renderBadge = (value: string | number | undefined | null, labelPrefix = '', icon?: React.ElementType) => {
    if (value === null || value === undefined || String(value).trim() === '') return null;
    const IconComponent = icon;
    return (
      <Badge variant="outline" className="mr-2 mb-2 flex items-center">
        {IconComponent && <IconComponent className="h-3.5 w-3.5 mr-1" />}
        {labelPrefix}{String(value)}
      </Badge>
    );
  };

  const genres = typeof song.genre === 'string' 
    ? song.genre.split(',').map(g => g.trim()).filter(g => g) 
    : (Array.isArray(song.genre) ? song.genre.filter(g => g) : []);

  const sections = [
    {
      title: 'Informations Générales',
      icon: Music2,
      details: [
        { label: 'Titre', value: song.title, icon: FileText },
        { label: 'ID du morceau', value: song.id, icon: Fingerprint },
        { label: 'Artiste(s)', value: song.artist_name, icon: Users2 },
        { label: 'Featuring', value: song.featured_artists, icon: UserSquare2 },
        { label: 'Album', value: song.albums?.title, icon: Disc, isLink: !!song.albums?.slug, linkHref: song.albums?.slug ? `/album/${song.albums.slug}` : undefined },
        { label: 'Description', value: song.description, icon: Info },
        { label: 'Durée', value: song.duration_ms ? `${Math.floor(song.duration_ms / 60000)}m ${String(Math.floor((song.duration_ms % 60000) / 1000)).padStart(2, '0')}s` : null, icon: Clock },
      ],
    },
    {
      title: 'Détails Musicaux',
      icon: Palette,
      details: [
        { label: 'Genre(s)', value: song.genres ? song.genres.map((g: string) => g.trim()) : null, icon: Tag, isList: true },
        { label: 'Ambiance(s)', value: song.moods, icon: Sparkles, isList: true },
        { label: 'Thème(s)', value: song.themes, icon: Brain, isList: true },
        { label: 'Instrumentation', value: song.instruments, icon: Guitar, isList: true },
        { label: 'Tonalité', value: song.key, icon: Music2 },
        { label: 'BPM', value: song.bpm, icon: BarChartHorizontalBig },
        { label: 'Signature Rythmique', value: song.time_signature, icon: GitBranch }, 
        { label: 'Capo', value: song.capo, icon: Layers3 },
        { label: 'Langue', value: song.lyrics_language, icon: Languages },
      ],
    },
    {
      title: 'Crédits & Publication',
      icon: Copyright,
      details: [
        { label: 'Compositeur(s)', value: song.composer_name, icon: UserCheck2 },
        { label: 'Auteur(s)', value: song.writers, icon: UserCheck2 },
        { label: 'Producteur(s)', value: song.producers, icon: UserCheck2 },
        { label: 'Label', value: song.record_label, icon: Building2 },
        { label: 'Date de sortie', value: song.release_date ? format(new Date(song.release_date), 'd MMMM yyyy', { locale: fr }) : null, icon: CalendarDays },
        { label: 'ISRC', value: song.isrc, icon: Fingerprint },
        { label: 'UPC/EAN', value: song.upc, icon: Fingerprint },
        { label: 'Statut', value: song.is_public ? 'Public' : 'Privé', icon: ShieldCheck, isBadge: true },
      ],
    },
    {
      title: 'Origine & Contribution IA',
      icon: Brain,
      details: [
        { label: 'Origine de la création', value: song.ai_content_origin, icon: Sparkles },
        { label: 'Outils IA utilisés', value: song.ai_tools_used, icon: Layers3 },
        { label: 'Prompts IA', value: song.ai_prompts, icon: FileText },
      ],
    },
  ];

  return (
    <div className={cn('space-y-6', className)}>
      {/* Badge section remains at the top, single column, conditionally rendered */}
      {showTopBadges && (genres.length > 0 || song.key || song.bpm || song.time_signature || typeof song.capo === 'number') && (
        <Card>
          <CardContent className="pt-4">
            <div className="flex flex-wrap items-center">
              {genres.map((g, index) => renderBadge(g, '', Tag))}
              {renderBadge(song.key, '', Music2)}
              {renderBadge(song.bpm, 'BPM: ', BarChartHorizontalBig)}
              {renderBadge(song.time_signature, '', GitBranch)}
              {renderBadge(song.capo, 'Capo: ', Layers3)}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sections below badges will be in a responsive grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {sections.map((section) => (
        <Card key={section.title}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <section.icon className="mr-2 h-5 w-5 text-primary" />
              {section.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableBody>
                {section.details.map((detail) => (
                  <DetailRow key={detail.label} {...detail} />
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
        ))}
      </div>
    </div>
  );
};
