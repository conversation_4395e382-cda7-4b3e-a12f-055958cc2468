import React, { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Brain, Music, Mic, Headphones, Sparkles, TrendingUp, CheckCircle2, Settings, Zap, AlertCircle, Wifi, WifiOff, Cloud, Server, Star, Crown, BarChart3, Target, Lightbulb, AlertTriangle, FileText, Layers } from 'lucide-react';
import {
  SongFormValues,
  AiAssistanceLevel
} from './song-schema';

type AiProvider = 'ollama' | 'openai' | 'gemini' | 'anthropic' | 'openrouter';

interface SongFormAiTabProps {
  control: UseFormReturn<SongFormValues>['control'];
  watch: UseFormReturn<SongFormValues>['watch'];
  setValue: UseFormReturn<SongFormValues>['setValue'];
}

interface OllamaModel {
  name: string;
  size: string;
  modified_at: string;
}

const SongFormAiTab: React.FC<SongFormAiTabProps> = ({ control, watch, setValue }) => {
  const [aiProvider, setAiProvider] = useState<AiProvider>('ollama');
  const [ollamaModels, setOllamaModels] = useState<OllamaModel[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [ollamaStatus, setOllamaStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking');
  const [ollamaUrl, setOllamaUrl] = useState('http://localhost:11434');
  const [selectedOllamaModel, setSelectedOllamaModel] = useState<string>('');
  const [selectedApiModel, setSelectedApiModel] = useState<string>('');
  const [analysisResult, setAnalysisResult] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);

  // AI Analysis Functions
  const executeAnalysis = async (prompt: string, analysisType: string) => {
    setIsAnalyzing(true);
    setAnalysisResult('');
    
    try {
      const songData = {
        title: watch('title') || 'Sans titre',
        duration: watch('duration_ms') || 'Non définie',
        progress: watch('progress_data') || {},
        lyrics: watch('lyrics') || '',
        chords: watch('chords') || '',
        genre: watch('genre') || '',
        mood: watch('moods') || [],
        tempo: watch('tempo') || '',
        key_signature: watch('key_signature') || '',
        time_signature: watch('time_signature') || '',
        structure: watch('structure') || '',
        instruments: watch('instruments') || '',
        recording_notes: watch('recording_notes') || ''
      };

      const contextualPrompt = `${prompt}

**DONNÉES DU MORCEAU:**
- Titre: ${songData.title}
- Durée: ${songData.duration}
- Progression: ${typeof songData.progress === 'object' ? JSON.stringify(songData.progress) : songData.progress}
- Genre: ${songData.genre}
- Humeur: ${Array.isArray(songData.mood) ? songData.mood.join(', ') : songData.mood}
- Tempo: ${songData.tempo}
- Tonalité: ${songData.key_signature}
- Signature temporelle: ${songData.time_signature}
- Structure: ${songData.structure}
- Instruments: ${songData.instruments}

**PAROLES:**
${songData.lyrics}

**ACCORDS:**
${songData.chords}

**NOTES D'ENREGISTREMENT:**
${songData.recording_notes}`;

      if (aiProvider === 'ollama' && selectedOllamaModel) {
        const response = await fetch('http://localhost:11434/api/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            model: selectedOllamaModel,
            prompt: contextualPrompt,
            stream: false,
            options: { temperature: 0.7, num_predict: 2000 }
          })
        });

        if (!response.ok) {
          throw new Error(`Erreur Ollama: ${response.statusText}`);
        }

        const data = await response.json();
        setAnalysisResult(data.response || 'Aucune réponse reçue');
      } else {
        // Placeholder pour les autres providers
        setAnalysisResult(`Analyse ${analysisType} simulée pour le morceau "${songData.title}". Configuration API requise pour ${aiProvider}.`);
      }
    } catch (error) {
      console.error('Erreur lors de l\'analyse:', error);
      setAnalysisResult(`Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Prompts spécialisés pour chaque type d'analyse
  const handleProInsightAnalysis = () => {
    const prompt = `Tu es un MEGA PRODUCTEUR/ARRANGEUR/COMPOSITEUR de renommée mondiale avec 30+ ans d'expérience dans l'industrie musicale. Tu as travaillé avec les plus grands artistes et produit des hits internationaux.

**MISSION:** Fournir une analyse COMPLÈTE et PROFESSIONNELLE de ce morceau avec l'œil expert d'un producteur de haut niveau.

**ANALYSE REQUISE:**
1. **VISION GLOBALE:** Potentiel commercial, originalité, force artistique
2. **STRUCTURE & ARRANGEMENT:** Efficacité de la structure, transitions, dynamiques
3. **HARMONIE & MÉLODIE:** Sophistication harmonique, mémorabilité mélodique
4. **PRODUCTION:** Suggestions d'instrumentation, effets, mix
5. **MARCHÉ:** Positionnement genre, audience cible, stratégie de sortie
6. **AMÉLIORATIONS PRIORITAIRES:** 3 points clés pour maximiser l'impact

**STYLE:** Professionnel mais accessible, avec des références concrètes et des conseils actionnables.`;
    
    executeAnalysis(prompt, 'Insight Pro Complet');
  };

  const handleCriticalAnalysis = () => {
    const prompt = `Tu es un CRITIQUE MUSICAL ACERBE et SANS CONCESSION, reconnu pour tes analyses impitoyables mais justes. Tu ne fais pas de cadeaux et tu identifies TOUS les problèmes.

**MISSION:** Décortiquer ce morceau avec un œil critique IMPLACABLE pour révéler TOUS ses défauts et faiblesses.

**ANALYSE CRITIQUE REQUISE:**
1. **DÉFAUTS STRUCTURELS:** Longueurs, répétitions, transitions ratées
2. **FAIBLESSES HARMONIQUES:** Progressions banales, erreurs théoriques
3. **PROBLÈMES MÉLODIQUES:** Manque d'originalité, difficultés de mémorisation
4. **LACUNES LYRIQUES:** Clichés, incohérences, manque de profondeur
5. **ERREURS DE PRODUCTION:** Choix d'instruments discutables, arrangements faibles
6. **VERDICT SANS APPEL:** Note sur 10 et justification brutale

**STYLE:** Direct, sans détour, constructif mais impitoyable. Pas de compliments gratuits.`;
    
    executeAnalysis(prompt, 'Critique Acerbe');
  };

  const handleGlobalStateAnalysis = () => {
    const prompt = `Analyse l'état global de ce morceau en évaluant sa complétude, sa cohérence et son potentiel. Examine tous les éléments disponibles (titre, durée, progression, paroles, accords, structure) et fournis un diagnostic complet avec des recommandations précises.`;
    executeAnalysis(prompt, 'État Global');
  };

  const handleStrengthsAnalysis = () => {
    const prompt = `Identifie et analyse les points forts de ce morceau. Mets en lumière ce qui fonctionne bien dans la composition, l'arrangement, les paroles, et la structure. Explique pourquoi ces éléments sont réussis et comment les valoriser davantage.`;
    executeAnalysis(prompt, 'Points Forts');
  };

  const handleMetricAnalysis = () => {
    const prompt = `Analyse les aspects métriques et temporels de ce morceau : durée, tempo, signature temporelle, structure rythmique. Évalue l'efficacité de ces choix et propose des optimisations si nécessaire.`;
    executeAnalysis(prompt, 'Analyse Métrique');
  };

  const handleHarmonyAnalysis = () => {
    const prompt = `Analyse en profondeur l'harmonie et les progressions d'accords de ce morceau. Évalue la sophistication harmonique, identifie les modulations, et propose des substitutions ou enrichissements harmoniques pour améliorer l'impact musical.`;
    executeAnalysis(prompt, 'Harmonie & Accords');
  };

  const handleLyricsAnalysis = () => {
    const prompt = `Analyse les paroles et la mélodie de ce morceau. Évalue la qualité littéraire, la cohérence narrative, l'adéquation mélodie-texte, et propose des améliorations pour renforcer l'impact émotionnel.`;
    executeAnalysis(prompt, 'Paroles & Mélodie');
  };

  const handleArrangementAnalysis = () => {
    const prompt = `Analyse l'arrangement et les aspects de production de ce morceau. Évalue les choix d'instrumentation, la structure, les dynamiques, et propose des améliorations pour optimiser l'impact sonore et commercial.`;
    executeAnalysis(prompt, 'Arrangement & Mix');
  };

  const handleCoherenceAnalysis = () => {
    const prompt = `Évalue la cohérence globale et l'unité artistique de ce morceau. Analyse comment tous les éléments (paroles, musique, arrangement, production) s'articulent pour créer une œuvre cohérente et impactante.`;
    executeAnalysis(prompt, 'Cohérence Globale');
  };

  const handleEmotionalAnalysis = () => {
    const prompt = `Analyse l'impact émotionnel de ce morceau. Évalue sa capacité à transmettre des émotions, à créer une connexion avec l'auditeur, et propose des moyens d'amplifier son pouvoir émotionnel.`;
    executeAnalysis(prompt, 'Impact Émotionnel');
  };

  const handleCommercialAnalysis = () => {
    const prompt = `Évalue le potentiel commercial de ce morceau. Analyse sa viabilité sur le marché actuel, son positionnement genre, son audience cible, et propose une stratégie de commercialisation adaptée.`;
    executeAnalysis(prompt, 'Potentiel Commercial');
  };
  const [apiKey, setApiKey] = useState<string>('');

  const aiAssistanceLevel = watch('ai_assistance_level');

  // Vérifier la connexion Ollama
  const checkOllamaConnection = async () => {
    setOllamaStatus('checking');
    try {
      const response = await fetch(`${ollamaUrl}/api/tags`);
      if (response.ok) {
        setOllamaStatus('connected');
        return true;
      } else {
        setOllamaStatus('disconnected');
        return false;
      }
    } catch (error) {
      setOllamaStatus('disconnected');
      return false;
    }
  };

  // Charger les modèles Ollama
  const loadOllamaModels = async () => {
    setIsLoadingModels(true);
    try {
      const response = await fetch(`${ollamaUrl}/api/tags`);
      if (response.ok) {
        const data = await response.json();
        setOllamaModels(data.models || []);
        setOllamaStatus('connected');
      } else {
        setOllamaModels([]);
        setOllamaStatus('disconnected');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des modèles Ollama:', error);
      setOllamaModels([]);
      setOllamaStatus('disconnected');
    } finally {
      setIsLoadingModels(false);
    }
  };

  // Effet pour vérifier la connexion au montage
  useEffect(() => {
    checkOllamaConnection();
  }, [ollamaUrl]);

  // Charger les modèles quand la connexion est établie
  useEffect(() => {
    if (ollamaStatus === 'connected') {
      loadOllamaModels();
    }
  }, [ollamaStatus]);

  return (
    <div className="space-y-6 p-1">
      {/* Sélection du Provider IA */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5 text-indigo-500" />
            Sélection du Provider IA
          </CardTitle>
          <CardDescription>
            Choisissez votre fournisseur d'intelligence artificielle préféré.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Provider IA</label>
                <Select value={aiProvider} onValueChange={(value: AiProvider) => setAiProvider(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ollama">
                      <div className="flex items-center gap-2">
                        <Server className="h-4 w-4 text-blue-500" />
                        <span>Ollama (Local)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="openai">
                      <div className="flex items-center gap-2">
                        <Cloud className="h-4 w-4 text-green-500" />
                        <span>OpenAI</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="gemini">
                      <div className="flex items-center gap-2">
                        <Cloud className="h-4 w-4 text-purple-500" />
                        <span>Google Gemini</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="anthropic">
                      <div className="flex items-center gap-2">
                        <Cloud className="h-4 w-4 text-amber-500" />
                        <span>Anthropic Claude</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="openrouter">
                      <div className="flex items-center gap-2">
                        <Cloud className="h-4 w-4 text-cyan-500" />
                        <span>OpenRouter</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {aiProvider !== 'ollama' && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Clé API</label>
                  <Input
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder={`Entrez votre clé API ${aiProvider === 'openai' ? 'OpenAI' : aiProvider === 'gemini' ? 'Google Gemini' : aiProvider === 'anthropic' ? 'Anthropic' : 'OpenRouter'}`}
                  />
                  <p className="text-xs text-muted-foreground">
                    Votre clé API est stockée localement et n'est jamais partagée.
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Ollama */}
      {aiProvider === 'ollama' && (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-500" />
            Configuration Ollama
          </CardTitle>
          <CardDescription>
            Configurez votre instance Ollama pour l'assistance IA.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            {ollamaStatus === 'connected' && <Wifi className="h-4 w-4 text-green-500" />}
            {ollamaStatus === 'disconnected' && <WifiOff className="h-4 w-4 text-red-500" />}
            {ollamaStatus === 'checking' && <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />}
            <Badge variant={ollamaStatus === 'connected' ? 'default' : 'destructive'}>
              {ollamaStatus === 'connected' ? 'Connecté' : ollamaStatus === 'disconnected' ? 'Déconnecté' : 'Vérification...'}
            </Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">URL Ollama</label>
              <div className="flex gap-2">
                <Input
                  value={ollamaUrl}
                  onChange={(e) => setOllamaUrl(e.target.value)}
                  placeholder="http://localhost:11434"
                />
                <Button 
                  onClick={checkOllamaConnection}
                  variant="outline"
                  size="sm"
                  disabled={ollamaStatus === 'checking'}
                >
                  {ollamaStatus === 'checking' ? 'Test...' : 'Tester'}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
                <label className="text-sm font-medium">Modèle IA</label>
                <div className="flex gap-2">
                  <Select value={selectedOllamaModel} onValueChange={setSelectedOllamaModel}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner un modèle" />
                    </SelectTrigger>
                    <SelectContent>
                      {ollamaModels.map((model) => (
                        <SelectItem key={model.name} value={model.name}>
                          <div className="flex items-center justify-between w-full">
                            <span>{model.name}</span>
                            <Badge variant="outline" className="ml-2">
                              {model.size}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                <Button 
                  onClick={loadOllamaModels}
                  variant="outline"
                  size="sm"
                  disabled={isLoadingModels || ollamaStatus !== 'connected'}
                >
                  {isLoadingModels ? 'Chargement...' : 'Actualiser'}
                </Button>
              </div>
            </div>
          </div>

          {ollamaStatus === 'disconnected' && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Impossible de se connecter à Ollama. Vérifiez que le service est démarré et accessible à l'URL configurée.
              </AlertDescription>
            </Alert>
          )}

          {ollamaModels.length === 0 && ollamaStatus === 'connected' && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Aucun modèle trouvé. Installez des modèles avec <code>ollama pull [nom-du-modèle]</code>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
      )}

      {/* Configuration API pour les autres providers */}
      {aiProvider !== 'ollama' && (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5 text-green-500" />
            Configuration {aiProvider === 'openai' ? 'OpenAI' : aiProvider === 'gemini' ? 'Google Gemini' : aiProvider === 'anthropic' ? 'Anthropic Claude' : 'OpenRouter'}
          </CardTitle>
          <CardDescription>
            Configuration pour l'API {aiProvider === 'openai' ? 'OpenAI' : aiProvider === 'gemini' ? 'Google Gemini' : aiProvider === 'anthropic' ? 'Anthropic Claude' : 'OpenRouter'}.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            {apiKey ? (
              <>
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <Badge variant="default">Clé API configurée</Badge>
              </>
            ) : (
              <>
                <AlertCircle className="h-4 w-4 text-amber-500" />
                <Badge variant="outline">Clé API requise</Badge>
              </>
            )}
          </div>

          {!apiKey && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Veuillez configurer votre clé API dans la section "Sélection du Provider IA" ci-dessus.
              </AlertDescription>
            </Alert>
          )}

          {apiKey && (
             <div className="space-y-2">
               <label className="text-sm font-medium">Modèle</label>
               <Select value={selectedApiModel} onValueChange={setSelectedApiModel}>
                 <SelectTrigger>
                   <SelectValue placeholder="Sélectionner un modèle" />
                 </SelectTrigger>
                 <SelectContent>
                  {aiProvider === 'openai' && (
                    <>
                      <SelectItem value="gpt-4">GPT-4</SelectItem>
                      <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
                      <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                    </>
                  )}
                  {aiProvider === 'gemini' && (
                    <>
                      <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                      <SelectItem value="gemini-pro-vision">Gemini Pro Vision</SelectItem>
                    </>
                  )}
                  {aiProvider === 'anthropic' && (
                    <>
                      <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                      <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                      <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                    </>
                  )}
                  {aiProvider === 'openrouter' && (
                    <>
                      <SelectItem value="openai/gpt-4">GPT-4 (via OpenRouter)</SelectItem>
                      <SelectItem value="anthropic/claude-3-opus">Claude 3 Opus (via OpenRouter)</SelectItem>
                      <SelectItem value="google/gemini-pro">Gemini Pro (via OpenRouter)</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>
          )}
        </CardContent>
      </Card>
      )}

      {/* Configuration de l'Assistant IA */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-500" />
            Configuration de l'Assistant IA
          </CardTitle>
          <CardDescription>
            Configurez le niveau d'aide de l'IA pour la composition.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FormField
            control={control}
            name="ai_assistance_level"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Niveau d'assistance</FormLabel>
                <Select 
                  onValueChange={(value) => field.onChange(value as AiAssistanceLevel)}
                  defaultValue={field.value || undefined}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionnez un niveau" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.values(AiAssistanceLevel).map((level) => (
                      <SelectItem key={level} value={level}>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${
                            level === AiAssistanceLevel.LOW ? 'bg-gray-400' :
                            level === AiAssistanceLevel.MEDIUM ? 'bg-blue-400' :
                            level === AiAssistanceLevel.HIGH ? 'bg-orange-400' :
                            'bg-purple-400'
                          }`}></div>
                          {level}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Définit le niveau d'intervention de l'IA dans le processus créatif.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Bloc d'analyse et conseils IA */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-amber-500" />
            Analyse Intelligente du Morceau
          </CardTitle>
          <CardDescription>
            Obtenez des conseils personnalisés et une analyse approfondie de votre composition.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Analyses Complètes Professionnelles */}
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-muted-foreground flex items-center gap-2">
              <Star className="h-4 w-4" />
              Analyses Professionnelles Complètes
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button 
                 variant="outline" 
                 className="w-full p-4 h-auto justify-start border-2 border-amber-200 hover:border-amber-300 bg-gradient-to-r from-amber-50 to-yellow-50"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleProInsightAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <Crown className="h-6 w-6 text-amber-600" />
                   <div className="text-left">
                     <div className="font-semibold text-amber-800">Insight Pro Complet</div>
                     <div className="text-xs text-amber-600">Vision producteur/arrangeur expert</div>
                     <div className="text-xs text-muted-foreground mt-1">Analyse titre, durée, progression, accords, paroles</div>
                   </div>
                 </div>
               </Button>
              
              <Button 
                 variant="outline" 
                 className="w-full p-4 h-auto justify-start border-2 border-red-200 hover:border-red-300 bg-gradient-to-r from-red-50 to-pink-50"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleCriticalAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <AlertTriangle className="h-6 w-6 text-red-600" />
                   <div className="text-left">
                     <div className="font-semibold text-red-800">Critique Acerbe</div>
                     <div className="text-xs text-red-600">Analyse critique sans concession</div>
                     <div className="text-xs text-muted-foreground mt-1">Points faibles et améliorations nécessaires</div>
                   </div>
                 </div>
               </Button>
            </div>
          </div>

          <Separator />

          {/* Analyses Spécialisées */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-sm text-muted-foreground">Analyse Générale</h4>
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleGlobalStateAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <TrendingUp className="h-5 w-5 text-blue-500" />
                   <div className="text-left">
                     <div className="font-medium">État Global</div>
                     <div className="text-sm text-muted-foreground">Analyse complète du morceau</div>
                   </div>
                 </div>
               </Button>
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleStrengthsAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <CheckCircle2 className="h-5 w-5 text-green-500" />
                   <div className="text-left">
                     <div className="font-medium">Points Forts</div>
                     <div className="text-sm text-muted-foreground">Éléments réussis de la composition</div>
                   </div>
                 </div>
               </Button>
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleMetricAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <BarChart3 className="h-5 w-5 text-indigo-500" />
                   <div className="text-left">
                     <div className="font-medium">Analyse Métrique</div>
                     <div className="text-sm text-muted-foreground">Durée, tempo, structure temporelle</div>
                   </div>
                 </div>
               </Button>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-sm text-muted-foreground">Conseils Spécialisés</h4>
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleHarmonyAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <Music className="h-5 w-5 text-purple-500" />
                   <div className="text-left">
                     <div className="font-medium">Harmonie & Accords</div>
                     <div className="text-sm text-muted-foreground">Progression harmonique et accords</div>
                   </div>
                 </div>
               </Button>
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleLyricsAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <FileText className="h-5 w-5 text-orange-500" />
                   <div className="text-left">
                     <div className="font-medium">Paroles & Mélodie</div>
                     <div className="text-sm text-muted-foreground">Analyse lyrique et mélodique</div>
                   </div>
                 </div>
               </Button>
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleArrangementAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <Layers className="h-5 w-5 text-teal-500" />
                   <div className="text-left">
                     <div className="font-medium">Arrangement & Mix</div>
                     <div className="text-sm text-muted-foreground">Structure et arrangement</div>
                   </div>
                 </div>
               </Button>
            </div>
          </div>

          {/* Analyses Avancées */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground">Analyses Avancées</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleCoherenceAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <Target className="h-5 w-5 text-slate-500" />
                   <div className="text-left">
                     <div className="font-medium">Cohérence Globale</div>
                     <div className="text-sm text-muted-foreground">Unité et cohésion du morceau</div>
                   </div>
                 </div>
               </Button>
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleEmotionalAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <Zap className="h-5 w-5 text-pink-500" />
                   <div className="text-left">
                     <div className="font-medium">Impact Émotionnel</div>
                     <div className="text-sm text-muted-foreground">Ressenti et émotion transmise</div>
                   </div>
                 </div>
               </Button>
              <Button 
                 variant="outline" 
                 className="w-full p-3 h-auto justify-start"
                 disabled={(aiProvider === 'ollama' && (ollamaStatus !== 'connected' || !selectedOllamaModel)) || (aiProvider !== 'ollama' && !apiKey) || isAnalyzing}
                 onClick={handleCommercialAnalysis}
               >
                 <div className="flex items-center gap-3">
                   <Lightbulb className="h-5 w-5 text-yellow-500" />
                   <div className="text-left">
                     <div className="font-medium">Potentiel Commercial</div>
                     <div className="text-sm text-muted-foreground">Viabilité et attractivité marché</div>
                   </div>
                 </div>
               </Button>
            </div>
          </div>
          
          {/* Résultats d'Analyse */}
          {analysisResult && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-500" />
                <h3 className="text-lg font-semibold">Résultat d'Analyse</h3>
              </div>
              
              <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                <div className="prose prose-sm max-w-none">
                  <div className="whitespace-pre-wrap text-sm text-gray-800">
                    {analysisResult}
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-blue-200">
                  <div className="flex items-center justify-between text-xs text-blue-600">
                    <span>Analyse générée par l'IA</span>
                    <span>Modèle : {aiProvider === 'ollama' ? selectedOllamaModel : selectedApiModel}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-6 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-start gap-3">
              <Brain className="h-5 w-5 text-purple-500 mt-0.5" />
              <div>
                <h4 className="font-medium mb-2">Conseil du Jour</h4>
                <p className="text-sm text-muted-foreground">
                  {((aiProvider === 'ollama' && ollamaStatus === 'connected' && selectedOllamaModel) || (aiProvider !== 'ollama' && apiKey && selectedApiModel)) ? (
                    `Modèle ${aiProvider === 'ollama' ? selectedOllamaModel : selectedApiModel} prêt ! Basé sur votre niveau d'assistance IA (${aiAssistanceLevel || 'Non défini'}), 
                    l'IA peut vous aider à développer les sections moins avancées de votre morceau. 
                    Commencez par les éléments fondamentaux comme l'harmonie avant de peaufiner les détails.`
                  ) : (
                    aiProvider === 'ollama' 
                      ? 'Configurez Ollama et sélectionnez un modèle pour activer l\'assistance IA intelligente.'
                      : 'Configurez votre clé API et sélectionnez un modèle pour activer l\'assistance IA intelligente.'
                  )}
                </p>
              </div>
            </div>
          </div>

          {((aiProvider === 'ollama' && ollamaStatus === 'connected' && selectedOllamaModel) || (aiProvider !== 'ollama' && apiKey && selectedApiModel)) && (
            <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Zap className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">
                IA activée avec {aiProvider === 'ollama' ? selectedOllamaModel : selectedApiModel}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SongFormAiTab;