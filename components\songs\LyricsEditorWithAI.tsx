"use client";

import { useState, useRef } from 'react';
import { Control } from 'react-hook-form';
import { FormField, FormItem, FormControl, FormMessage } from "@/components/ui/form"; // Keep FormControl import for potential future use if needed elsewhere, but remove it from around RichLyricsEditor
import { RichLyricsEditor } from "@/components/ui/rich-lyrics-editor";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// Import Select components
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; 
import { History, ChevronDown, ChevronUp, Wand2, RefreshCcw, Languages, AlignLeft, Lightbulb, Palette, Cog } from 'lucide-react';

// Define AI Action Types
const AiActionType = {
  SUGGESTION: 'SUGGESTION',
  ANALYSIS: 'ANALYSIS',
  CORRECTION: 'CORRECTION',
  TRANSLATION: 'TRANSLATION',
  FORMATTING: 'FORMATTING',
  GENERATION: 'GENERATION',
} as const;

type AiActionTypeValues = typeof AiActionType[keyof typeof AiActionType];
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import type Quill from 'quill';
import { toast } from "@/hooks/use-toast";

// Assuming types are exported from SongForm or a shared types file
// It might be better to move these to a dedicated types file later (e.g., types/ai.ts, types/song.ts)
import { SongFormValues, AiConfig, AiHistoryItem } from "./song-schema"; 

interface LyricsEditorWithAIProps {
  // Editor State & Handlers (from parent)
  lyricsContent: string;
  handleLyricsChange: (newContent: string) => void;
  quillRef: React.RefObject<any>; // Or specific Quill type
  
  // Form Control (from parent)
  formControl: Control<SongFormValues>; // Still needed for FormField

  // Shared AI State & Handlers (from parent)
  aiConfig: AiConfig;
  aiGeneralPrompt: string;
  addAiHistory: (userPrompt: string, assistantResponse: string) => void;
  aiHistory: AiHistoryItem[]; // For display in Collapsible
  showAiHistory: boolean; // For display in Collapsible
  setShowAiHistory: (show: boolean) => void; // For display in Collapsible
}

const AI_PROVIDERS = [
  { value: 'OpenAI', label: 'OpenAI' },
  { value: 'Anthropic', label: 'Anthropic' },
  { value: 'Ollama', label: 'Ollama (local)' },
  // Add other providers here
];

const AI_MODELS: { [key: string]: { value: string; label: string }[] } = {
  OpenAI: [
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
    { value: 'gpt-4', label: 'GPT-4' },
    { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
  ],
  Anthropic: [
    { value: 'claude-2', label: 'Claude 2' },
    { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
    { value: 'claude-3-opus', label: 'Claude 3 Opus' },
  ],
  Ollama: [
    { value: 'llama2', label: 'Llama 2' },
    { value: 'mistral', label: 'Mistral' },
    { value: 'codellama', label: 'Code Llama' },
    { value: 'custom', label: 'Custom Ollama Model' },
  ],
  // Add other models for other providers here
};

export function LyricsEditorWithAI({
  lyricsContent,
  handleLyricsChange,
  quillRef,
  formControl,
  aiConfig,
  aiGeneralPrompt,
  addAiHistory,
  aiHistory, // Receive for display
  showAiHistory, // Receive for display
  setShowAiHistory, // Receive for display
}: LyricsEditorWithAIProps) {
  const watchedAiProvider = formControl ? formControl._getWatch('ai_config.provider' as any) : aiConfig?.provider;
  const [isAiConfigOpen, setIsAiConfigOpen] = useState(false);
  // State specific to AI actions within this component
  const [aiLoading, setAiLoading] = useState(false);
  const [aiLastResult, setAiLastResult] = useState<string | undefined>(undefined);
  const [lastAiActionType, setLastAiActionType] = useState<AiActionTypeValues | null>(null);
  const [aiError, setAiError] = useState<string | undefined>(undefined);
  const [currentSelectionRange, setCurrentSelectionRange] = useState<any>(null); // State for selection

  // Utility to strip HTML tags (needed for prompts)
  const stripHtml = (html: string): string => {
    if (typeof document !== "undefined") {
      const doc = new DOMParser().parseFromString(html, 'text/html');
      return doc.body.textContent || "";
    }
    return html.replace(/<[^>]+>/g, ''); 
  };

  // Handler for editor selection changes (now local to this component)
  const handleEditorSelectionChange = (range: any, source: any, editor: any) => {
    setCurrentSelectionRange(range); 
  };

  // Generic AI Request Function
  const executeAiRequest = async (prompt: string, currentAiConfig: AiConfig, actionName: string): Promise<string> => {
    setAiLoading(true);
    setAiError(undefined);
    setAiLastResult(undefined);

    console.log(`Executing AI Request for: ${actionName}`, { prompt, currentAiConfig });

    try {
      if (currentAiConfig.provider === 'Ollama') {
        const ollamaModel = currentAiConfig.model || 'llama2'; // Default if not set
        const ollamaEndpoint = 'http://localhost:11434/api/generate';
        const requestBody: any = {
          model: ollamaModel,
          prompt: prompt,
          stream: false,
          options: {
            temperature: currentAiConfig.temperature !== undefined ? currentAiConfig.temperature : 0.7, // Default temperature
          }
        };
        if (currentAiConfig.max_tokens !== undefined && currentAiConfig.max_tokens > 0) {
          requestBody.options.num_predict = currentAiConfig.max_tokens;
        }

        const response = await fetch(ollamaEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: response.statusText }));
          throw new Error(`Ollama API error (${response.status}): ${errorData.error || 'Unknown error'}`);
        }
        const data = await response.json();
        if (!data.response) {
          throw new Error("Ollama API error: No response field in data.");
        }
        return data.response; // Ollama's response field
      } else if (currentAiConfig.provider === 'OpenAI' || currentAiConfig.provider === 'Anthropic') {
        // Placeholder for OpenAI/Anthropic or other API calls
        // In a real app, this would likely call a backend API route
        console.warn(`API call for ${currentAiConfig.provider} not fully implemented. Using simulation.`);
        await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API delay
        return `[Simulated AI Response for ${actionName} using ${currentAiConfig.provider} - ${currentAiConfig.model}]`;
      } else {
        throw new Error(`Provider ${currentAiConfig.provider} not supported for this action yet.`);
      }
    } catch (error: any) {
      console.error(`AI Request Error (${actionName} - ${currentAiConfig.provider}):`, error);
      setAiError(`Failed ${actionName} with ${currentAiConfig.provider}: ${error.message}`);
      toast({ title: `Erreur IA (${currentAiConfig.provider})`, description: error.message || `L'action '${actionName}' a échoué.`, variant: "destructive" });
      throw error; // Re-throw to be caught by the calling handler if needed
    } finally {
      // setAiLoading(false); // Loading state will be managed by the calling function
    }
  };

  // --- AI Action Handlers (Moved from SongForm) ---

  // --- AI Configuration UI --- 
  const aiConfigurationSection = (
    <Collapsible open={isAiConfigOpen} onOpenChange={setIsAiConfigOpen} className="mb-4 border rounded-md p-4">
      <CollapsibleTrigger asChild>
        <Button variant="ghost" className="w-full justify-between">
          <div className="flex items-center">
            <Cog className="h-5 w-5 mr-2" />
            Configuration IA
          </div>
          {isAiConfigOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="pt-4 space-y-4">
        <FormField
          control={formControl}
          name={'ai_config.provider' as any} // Use path for nested object
          render={({ field }) => (
            <FormItem>
              <Label>Fournisseur IA</Label>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger><SelectValue placeholder="Sélectionner un fournisseur" /></SelectTrigger>
                </FormControl>
                <SelectContent>
                  {AI_PROVIDERS.map(provider => (
                    <SelectItem key={provider.value} value={provider.value}>{provider.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={formControl}
          name={'ai_config.model' as any}
          render={({ field }) => (
            <FormItem>
              <Label>Modèle IA</Label>
              <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger><SelectValue placeholder="Sélectionner un modèle" /></SelectTrigger>
                </FormControl>
                <SelectContent>
                  {(AI_MODELS[watchedAiProvider as keyof typeof AI_MODELS] || []).map(model => (
                    <SelectItem key={model.value} value={model.value}>{model.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={formControl}
          name={'ai_config.temperature' as any}
          render={({ field }) => (
            <FormItem>
              <Label>Température ({field.value ?? 0})</Label>
              <FormControl>
                <Slider
                  defaultValue={[field.value ?? 0.7]}
                  onValueChange={(value) => field.onChange(value[0])}
                  min={0}
                  max={1} // OpenAI typically 0-2, Anthropic 0-1. Standardizing to 0-1 for now.
                  step={0.1}
                  className="py-2"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={formControl}
          name={'ai_config.max_tokens' as any}
          render={({ field }) => (
            <FormItem>
              <Label>Max Tokens</Label>
              <FormControl>
                <Input type="number" placeholder="Ex: 1024" {...field} onChange={event => field.onChange(+event.target.value)} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CollapsibleContent>
    </Collapsible>
  );

  // AI Action: Format Lyrics and Chords
  const handleAiFormatLayout = async () => {
    const actionName = "Mise en Forme Paroles/Accords";
    console.log(`Trigger ${actionName} (in LyricsEditorWithAI)`);
    setAiLoading(true);
    setAiError(undefined);
    setAiLastResult(undefined);
    setLastAiActionType(null);

    const currentHtmlContent = lyricsContent;
    const plainTextContent = stripHtml(currentHtmlContent);

    if (!plainTextContent.trim()) {
      toast({ title: actionName, description: "Aucun contenu à formater.", variant: "default" });
      setAiLoading(false);
      return;
    }

    const formattingPrompt = `Format the following song lyrics and chords. Ensure chords are placed appropriately above the lyrics. Preserve the structure (verses, chorus, bridge, etc.) if discernible. Raw content:\n\n${plainTextContent}`;
    
    const currentConfig = formControl._getWatch('ai_config' as any) as AiConfig;
    if (!currentConfig || !currentConfig.provider) {
        toast({ title: actionName, description: "Configuration IA non définie.", variant: "destructive" });
        setAiLoading(false);
        return;
    }

    try {
      const formattedText = await executeAiRequest(formattingPrompt, currentConfig, actionName);
      
      if (quillRef.current) {
        const quill = quillRef.current.getEditor(); // Ensure we get the Quill instance
        quill.setText(formattedText + '\n'); // Replace entire content
        handleLyricsChange(quill.root.innerHTML); // Update parent state
      }
      setAiLastResult(formattedText);
      setLastAiActionType(AiActionType.FORMATTING);
      addAiHistory(formattingPrompt, formattedText);
      toast({ title: `${actionName} (${currentConfig.provider})`, description: "Les paroles ont été mises en forme." });
    } catch (error) {
      // Error is already logged and toasted by executeAiRequest
      console.error(`${actionName} failed:`, error);
    } finally {
      setAiLoading(false);
    }
  }; // End of handleAiFormatLayout

// AI Action: Rhyme Suggestions
const handleAiRhymeSuggestions = async () => {
  const actionName = "Suggestions de Rimes";
  console.log(`Trigger ${actionName} (in LyricsEditorWithAI)`);
  setAiLoading(true);
  setAiError(undefined);
  setAiLastResult(undefined);
  setLastAiActionType(null);

  const selection = currentSelectionRange;
  const editor = quillRef.current?.getEditor();
  let selectedText = '';

  if (selection && selection.length > 0 && editor) {
    selectedText = editor.getText(selection.index, selection.length).trim();
  } else {
    toast({ title: actionName, description: "Veuillez sélectionner du texte pour obtenir des rimes.", variant: "default" });
    setAiLoading(false);
    return;
  }
  if (!selectedText) {
      toast({ title: actionName, description: "La sélection est vide. Veuillez sélectionner du texte.", variant: "default" });
      setAiLoading(false);
      return;
  }

  const promptForAI = `${aiGeneralPrompt}\n\nSuggest rhymes for the last word of the selected text: "${selectedText}"`;
  
  const currentConfig = formControl._getWatch('ai_config' as any) as AiConfig;
  if (!currentConfig || !currentConfig.provider) {
      toast({ title: actionName, description: "Configuration IA non définie.", variant: "destructive" });
      setAiLoading(false);
      return;
  }

  try {
    const aiResponseText = await executeAiRequest(promptForAI, currentConfig, actionName);
    setAiLastResult(aiResponseText); // Display rhymes (e.g., in a modal or a dedicated area)
    setLastAiActionType(AiActionType.SUGGESTION);
    addAiHistory(promptForAI, aiResponseText);
    toast({ title: `${actionName} (${currentConfig.provider})`, description: "Suggestions de rimes générées." });
    // Note: This action doesn't directly modify the editor content with rhymes.
    // The response (aiLastResult) should be displayed to the user, perhaps in a separate UI element.
  } catch (error) {
    // Error is already logged and toasted by executeAiRequest
    console.error(`${actionName} failed:`, error);
  } finally {
    setAiLoading(false);
  }
};

  // AI Action: Tone Analysis
  const handleAiAnalyzeTone = async () => {
    const actionName = "Analyse de Ton";
    console.log(`Trigger ${actionName} (in LyricsEditorWithAI)`);
    setAiLoading(true);
    setAiError(undefined);
    setAiLastResult(undefined);
    setLastAiActionType(null);

    const currentLyricsText = stripHtml(lyricsContent);
    if (!currentLyricsText.trim()) {
      toast({ title: actionName, description: "Aucune parole à analyser.", variant: "default" });
      setAiLoading(false);
      return;
    }

    const promptForAI = `${aiGeneralPrompt}\n\nAnalyze the overall tone and mood of the following lyrics:\n\n${currentLyricsText}`;
    
    const currentConfig = formControl._getWatch('ai_config' as any) as AiConfig;
    if (!currentConfig || !currentConfig.provider) {
        toast({ title: actionName, description: "Configuration IA non définie.", variant: "destructive" });
        setAiLoading(false);
        return;
    }

    try {
      const aiResponseText = await executeAiRequest(promptForAI, currentConfig, actionName);
      setAiLastResult(aiResponseText); // Display analysis (e.g., in a modal or a dedicated area)
      setLastAiActionType(AiActionType.ANALYSIS);
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: `${actionName} (${currentConfig.provider})`, description: "Analyse de ton terminée." });
      // Note: This action doesn't directly modify the editor content.
      // The response (aiLastResult) should be displayed to the user.
    } catch (error) {
      // Error is already logged and toasted by executeAiRequest
      console.error(`${actionName} failed:`, error);
    } finally {
      setAiLoading(false);
    }
  };

  // AI Action: Correct Text
  const handleAiCorrect = async () => {
    const actionName = "Correction de Texte";
    console.log(`Trigger ${actionName} (in LyricsEditorWithAI)`);
    setAiLoading(true);
    setAiError(undefined);
    setAiLastResult(undefined);
    setLastAiActionType(null);

    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange;
    const editor = quillRef.current?.getEditor();
    const isSelection = selection && selection.length > 0 && editor;

    if (isSelection) {
      textToProcess = editor.getText(selection.index, selection.length).trim();
      promptForAI = `${aiGeneralPrompt}\n\nCorrect the grammar and spelling of the following selected text:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent).trim();
      promptForAI = `${aiGeneralPrompt}\n\nCorrect the grammar and spelling of the following text:\n\n${textToProcess}`;
    }

    if (!textToProcess) {
      toast({ title: actionName, description: "Aucun texte à corriger.", variant: "default" });
      setAiLoading(false);
      return;
    }

    const currentConfig = formControl._getWatch('ai_config' as any) as AiConfig;
    if (!currentConfig || !currentConfig.provider) {
      toast({ title: actionName, description: "Configuration IA non définie.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    try {
      const aiResponseText = await executeAiRequest(promptForAI, currentConfig, actionName);
      
      if (isSelection && editor && selection) { // selection is re-checked for type safety
        editor.deleteText(selection.index, selection.length, 'user');
        // Insert plain text, letting Quill handle paragraphing if needed, or convert newlines to <br>
        // For simplicity, inserting as plain text. If HTML structure is desired from AI, adjust accordingly.
        editor.insertText(selection.index, aiResponseText, 'user'); 
        handleLyricsChange(editor.root.innerHTML);
      } else if (editor) { // editor is checked for type safety
        editor.setText(aiResponseText + '\n'); // Replace entire content
        handleLyricsChange(editor.root.innerHTML);
      }

      setAiLastResult(aiResponseText);
      setLastAiActionType(AiActionType.CORRECTION);
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: `${actionName} (${currentConfig.provider})`, description: "Le texte a été corrigé." });
    } catch (error) {
      // Error is already logged and toasted by executeAiRequest
      console.error(`${actionName} failed:`, error);
    } finally {
      setAiLoading(false);
    }
  };

  // AI Action: Translate Text
  const handleAiTranslate = async (lang: string) => {
    const actionName = `Traduction en ${lang}`;
    console.log(`Trigger ${actionName} (in LyricsEditorWithAI)`);
    setAiLoading(true);
    setAiError(undefined);
    setAiLastResult(undefined);
    setLastAiActionType(null);

    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange;
    const editor = quillRef.current?.getEditor();
    const isSelection = selection && selection.length > 0 && editor;

    if (isSelection) {
      textToProcess = editor.getText(selection.index, selection.length).trim();
      promptForAI = `${aiGeneralPrompt}\n\nTranslate the following selected text to ${lang}:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent).trim();
      promptForAI = `${aiGeneralPrompt}\n\nTranslate the following text to ${lang}:\n\n${textToProcess}`;
    }

    if (!textToProcess) {
      toast({ title: actionName, description: "Aucun texte à traduire.", variant: "default" });
      setAiLoading(false);
      return;
    }

    const currentConfig = formControl._getWatch('ai_config' as any) as AiConfig;
    if (!currentConfig || !currentConfig.provider) {
      toast({ title: actionName, description: "Configuration IA non définie.", variant: "destructive" });
      setAiLoading(false);
      return;
    }

    try {
      const aiResponseText = await executeAiRequest(promptForAI, currentConfig, actionName);
      
      if (isSelection && editor && selection) { // selection is re-checked for type safety
        editor.deleteText(selection.index, selection.length, 'user');
        // Insert plain text, letting Quill handle paragraphing if needed.
        editor.insertText(selection.index, aiResponseText, 'user');
        handleLyricsChange(editor.root.innerHTML);
      } else if (editor) { // editor is checked for type safety
        editor.setText(aiResponseText + '\n'); // Replace entire content
        handleLyricsChange(editor.root.innerHTML);
      }

      setAiLastResult(aiResponseText);
      setLastAiActionType(AiActionType.TRANSLATION);
      addAiHistory(promptForAI, aiResponseText);
      toast({ title: `${actionName} (${currentConfig.provider})`, description: "Le texte a été traduit." });
    } catch (error) {
      // Error is already logged and toasted by executeAiRequest
      console.error(`${actionName} failed:`, error);
    } finally {
      setAiLoading(false);
    }
  };
  
  // AI Action: Generate Text
  const handleAiGenerate = async (customPrompt?: string) => { 
    console.log("Trigger AI Generate (in LyricsEditorWithAI)");
    setAiLoading(true); 
    setAiError(undefined); 
    setAiLastResult(undefined);
    setLastAiActionType(null);
    
    let textToProcess = "";
    let promptForAI = "";
    const selection = currentSelectionRange; 
    const editor = quillRef.current?.getEditor();

    if (selection && selection.length > 0 && editor) {
      textToProcess = editor.getText(selection.index, selection.length);
      promptForAI = `${aiGeneralPrompt}\n\n${customPrompt || 'Improve or continue this selected text'}:\n\n${textToProcess}`;
    } else {
      textToProcess = stripHtml(lyricsContent); // Use full content if no selection
      promptForAI = `${aiGeneralPrompt}\n\n${customPrompt || 'Continue writing lyrics based on this text'}:\n\n${textToProcess}`;
    }

    try {
      // TODO: Replace with actual AI call logic using promptForAI and aiConfig
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      const aiResponseText = ` [AI Sim Response for: ${textToProcess.substring(0, 30)}...]`; // Simulate response

      setAiLastResult(`Texte généré (simulation) pour: ${textToProcess.substring(0, 50)}...`);
      setLastAiActionType(AiActionType.GENERATION);
      
      if (selection && selection.length > 0 && editor) {
        editor.deleteText(selection.index, selection.length, 'user');
        editor.clipboard.dangerouslyPasteHTML(selection.index, `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`, 'user'); 
        const newFullContent = editor.root.innerHTML; 
        handleLyricsChange(newFullContent); // Update parent state
      } else {
        // Replace full content if no selection
        const newHtmlContent = `<p>${aiResponseText.replace(/\n/g, "</p><p>")}</p>`; 
        handleLyricsChange(newHtmlContent); // Update parent state
      }

      addAiHistory(promptForAI, aiResponseText);
      toast({ title: "IA: Texte généré!"});

    } catch (e: any) { 
      setAiError(e.message); 
      toast({ title: "IA: Erreur de génération", description: e.message, variant: "destructive"});
    } finally { 
      setAiLoading(false); 
    }
  }

  // TODO: Need to pass these handlers down to AiQuickActions, likely via AiAssistantPanel
  // For now, they are defined here but not connected to the UI buttons yet.
  // We will connect them when updating AiAssistantPanel and SongForm props.

  // TODO: Add language selection state if needed for translate button
  const [translateLang, setTranslateLang] = useState('en'); // Default to English



  return (
    <>
      {aiConfigurationSection}

      {/* AI Actions Toolbar for Lyrics */}
      <div className="flex flex-wrap gap-2 mb-2 p-2 border rounded-md bg-muted/30">
         {/* Wrap handleAiGenerate in an arrow function for onClick */}
         <Button size="sm" variant="outline" onClick={() => handleAiGenerate()} disabled={aiLoading} className="text-xs" title="Générer/continuer le texte (utilise la sélection si présente)">
           <Wand2 className="w-4 h-4 mr-1" /> Générer
         </Button>
         <Button size="sm" variant="outline" onClick={handleAiCorrect} disabled={aiLoading} className="text-xs" title="Corriger la grammaire/orthographe (utilise la sélection si présente)">
           <RefreshCcw className="w-4 h-4 mr-1" /> Corriger
         </Button>
         <Button size="sm" variant="outline" onClick={handleAiFormatLayout} disabled={aiLoading} className="text-xs" title="Mettre en forme paroles/accords">
           <AlignLeft className="w-4 h-4 mr-1" /> Format Auto
         </Button>
         <Button size="sm" variant="outline" onClick={handleAiRhymeSuggestions} disabled={aiLoading || !(currentSelectionRange && currentSelectionRange.length > 0)} className="text-xs" title="Suggérer des rimes (sélection requise)">
           <Lightbulb className="w-4 h-4 mr-1" /> Rimes
         </Button>
         <Button size="sm" variant="outline" onClick={handleAiAnalyzeTone} disabled={aiLoading} className="text-xs" title="Analyser le ton des paroles">
           <Palette className="w-4 h-4 mr-1" /> Analyser Ton
         </Button>
         <div className="flex gap-1 items-center">
            {/* Basic language select for translate - can be improved */}
            <Select value={translateLang} onValueChange={setTranslateLang}>
                <SelectTrigger className="w-[90px] h-8 text-xs">
                    <SelectValue placeholder="Langue" />
                </SelectTrigger>
                <SelectContent className="text-xs">
                    <SelectItem value="en">EN</SelectItem>
                    <SelectItem value="fr">FR</SelectItem>
                    <SelectItem value="es">ES</SelectItem>
                    {/* Add more common languages */}
                </SelectContent>
            </Select>
            <Button size="sm" variant="outline" onClick={() => handleAiTranslate(translateLang)} disabled={aiLoading} className="text-xs h-8" title={`Traduire en ${translateLang.toUpperCase()} (utilise la sélection si présente)`}>
              <Languages className="w-4 h-4 mr-1" /> Traduire
            </Button>
         </div>
      </div>

      {/* Removed FormControl wrapper */}
      <FormField
        control={formControl}
        name="lyrics"
        render={({ field }) => ( // field is not used here, but keep render prop structure
          <FormItem>
            {/* Removed FormControl wrapper */}
            <RichLyricsEditor
              value={lyricsContent}
              onChange={handleLyricsChange} // Updates parent state
              placeholder="Commencez à écrire les paroles ici..."
              className="min-h-[400px]"
              quillRef={quillRef}
              onSelectionChange={handleEditorSelectionChange} // Updates local state
            />
            <FormMessage />
          </FormItem>
        )}
      />
      {/* Display AI results/errors specific to this component */}
      {aiLoading && <p className="text-sm text-muted-foreground mt-2">IA en cours de traitement...</p>}
      {aiError && <p className="text-sm text-destructive mt-2">Erreur IA: {aiError}</p>}
      {aiLastResult && !aiLoading && (lastAiActionType === AiActionType.SUGGESTION || lastAiActionType === AiActionType.ANALYSIS) && (
        <Card className="mt-4">
          <CardHeader className="pb-2 pt-4">
            <CardTitle className="text-md">
              {lastAiActionType === AiActionType.SUGGESTION && "Suggestions de Rimes"}
              {lastAiActionType === AiActionType.ANALYSIS && "Analyse de Ton"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="whitespace-pre-wrap text-sm bg-muted/50 p-3 rounded-md">{aiLastResult}</pre>
          </CardContent>
        </Card>
      )}
      {/* Fallback for other types of aiLastResult if needed, or remove if card covers all desired displays */}
      {/* {aiLastResult && !aiLoading && !(lastAiActionType === AiActionType.SUGGESTION || lastAiActionType === AiActionType.ANALYSIS) && (
        <p className="text-sm text-muted-foreground mt-2">Dernier résultat IA ({lastAiActionType}): {aiLastResult}</p>
      )} */}

      {/* Collapsible AI History Section (Display only) */}
      <Collapsible open={showAiHistory} onOpenChange={setShowAiHistory} className="mt-4">
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="text-xs w-full justify-start text-muted-foreground hover:text-foreground">
            <History className="mr-2 h-4 w-4" />
            Historique IA ({Math.floor(aiHistory.length / 2)} interactions)
            {showAiHistory ? <ChevronUp className="ml-auto h-4 w-4" /> : <ChevronDown className="ml-auto h-4 w-4" />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2">
          <>
            {aiConfigurationSection}
            <ScrollArea className="h-[200px] border rounded-md p-2 bg-muted/20 mt-4">
              {aiHistory.length === 0 ? (
                <p className="text-xs text-muted-foreground text-center py-4">Aucun historique pour le moment.</p>
              ) : (
                <div className="space-y-3">
                  {aiHistory.map((item, index) => (
                    <div key={index} className={`p-2 rounded-md text-xs ${item.role === 'user' ? 'bg-blue-500/10 text-blue-700 dark:text-blue-400' : 'bg-green-500/10 text-green-700 dark:text-green-400'}`}>
                      <p className="font-semibold mb-1 capitalize">{item.role}:</p>
                      <p className="whitespace-pre-wrap break-words">{item.content}</p>
                      {item.timestamp && <p className="text-muted-foreground text-[10px] mt-1">{new Date(item.timestamp).toLocaleTimeString()}</p>}
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </>
        </CollapsibleContent>
      </Collapsible>
    </>
  );
}
