import { create } from 'zustand';
import type { Song } from '@/types'; // Assurez-vous que Song inclut audio_url, cover_art_url, etc.

const DEFAULT_COVER_IMAGE_PATH = '/images/covers/mouvk.png'; // Chemin vers votre image par défaut

interface AudioPlayerState {
  queue: Song[];
  currentQueueIndex: number; // -1 if no song from queue is playing, or index in queue
  currentSong: Song | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  coverUrl: string | null;
  audioElement: HTMLAudioElement | null;

  initAudioElement: () => void;
  playSong: (song: Song) => void;
  pauseSong: () => void;
  togglePlayPause: () => void;
  seek: (time: number) => void;
  cleanup: () => void;

  // Queue methods
  setQueue: (songs: Song[], playImmediately?: boolean, startIndex?: number) => void;
  addToQueue: (song: Song) => void;
  nextSong: () => void;
  previousSong: () => void;
  clearQueue: () => void;
  playFromQueue: (index: number) => void;
}

export const useAudioPlayerStore = create<AudioPlayerState>((set, get) => {
  // let audioInstance: HTMLAudioElement | null = null; // audioInstance n'est pas nécessaire si on utilise get().audioElement

  const setupAudioEvents = (audio: HTMLAudioElement) => {
    audio.onended = () => {
      // set({ isPlaying: false, currentTime: get().duration }); // nextSong will handle state changes
      get().nextSong();
    };
    audio.ontimeupdate = () => {
      if (audio) set({ currentTime: audio.currentTime });
    };
    audio.onloadedmetadata = () => {
      if (audio && !isNaN(audio.duration) && isFinite(audio.duration)) {
        set({ duration: audio.duration });
      } else {
        set({ duration: 0 });
      }
    };
    audio.onerror = (e) => {
      console.error("Audio Element Error:", e);
      set({ isPlaying: false, currentSong: null, coverUrl: null, currentTime: 0, duration: 0 });
    };
  };

  return {
    currentSong: null,
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    coverUrl: null,
    audioElement: null,
    queue: [],
    currentQueueIndex: -1,

    initAudioElement: () => {
      if (typeof window !== 'undefined' && !get().audioElement) {
        const newAudio = new Audio();
        setupAudioEvents(newAudio);
        // audioInstance = newAudio; // Non nécessaire
        set({ audioElement: newAudio });
      }
    },

    playSong: (song) => {
      let audio = get().audioElement;
      if (!audio) {
        get().initAudioElement(); // S'assure que l'élément audio est initialisé
        audio = get().audioElement;
      }

      if (!audio) {
        console.error("Audio element could not be initialized.");
        return;
      }

      const state = get();
      let songToPlayIsFromCurrentQueue = false;
      let songIndexInQueue = state.queue.findIndex(s => s.id === song.id);

      if (state.currentSong?.id === song.id && audio.src === song.audio_url && state.isPlaying) {
        // If the same song is requested and it's already playing, do nothing or maybe seek to start?
        // For now, do nothing to prevent interruption if called multiple times.
        // If it's paused, this call will resume it below.
      } else if (song.audio_url && (state.currentSong?.id !== song.id || audio.src !== song.audio_url)) {
        audio.src = song.audio_url!; // Corrected with non-null assertion
        audio.load();
      } else if (!song.audio_url) {
        console.error("Cannot play song: audio_url is missing or invalid.", song);
        set({ isPlaying: false }); // Ensure isPlaying is false if URL is invalid
        return; // Stop if audio_url is not valid
      }
      
      audio.play().then(() => {
        const newCoverUrl = song.cover_art_url || song.albums?.cover_url || DEFAULT_COVER_IMAGE_PATH;
        set({
          currentSong: song,
          isPlaying: true,
          coverUrl: newCoverUrl,
          currentQueueIndex: songIndexInQueue, // Update queue index if song is from queue
          // duration sera mis à jour par l'événement 'loadedmetadata'
        });
      }).catch(error => {
        console.error("Error playing audio:", error);
        set({ isPlaying: false }); // S'assure que isPlaying est false en cas d'erreur de lecture
      });
    },

    pauseSong: () => {
      const audio = get().audioElement;
      if (audio && get().isPlaying) {
        audio.pause();
        set({ isPlaying: false });
      }
    },

    togglePlayPause: () => {
      const { isPlaying, currentSong, playSong, pauseSong } = get();
      if (currentSong) {
        if (isPlaying) {
          pauseSong();
        } else {
          playSong(currentSong);
        }
      }
    },

    seek: (time) => {
      const audio = get().audioElement;
      const { duration: songDuration } = get();
      if (audio && songDuration > 0) {
        const newTime = Math.max(0, Math.min(time, songDuration));
        audio.currentTime = newTime;
        set({ currentTime: newTime }); // Met à jour immédiatement le state
      }
    },

    // Queue methods implementation
    setQueue: (songs, playImmediately = true, startIndex = 0) => {
      set({ queue: songs, currentQueueIndex: -1 });
      if (playImmediately && songs.length > 0 && startIndex < songs.length) {
        get().playSong(songs[startIndex]);
        set({ currentQueueIndex: startIndex });
      }
    },

    addToQueue: (song) => {
      set(state => ({ queue: [...state.queue, song] }));
    },

    nextSong: () => {
      const { queue, currentQueueIndex, playSong, currentSong } = get();
      let nextIndex = -1;

      if (currentQueueIndex !== -1 && currentQueueIndex < queue.length - 1) {
        // If currently playing from queue and there's a next song in queue
        nextIndex = currentQueueIndex + 1;
      } else if (queue.length > 0 && currentQueueIndex === -1 && currentSong) {
        // If not playing from queue, but there's a queue, try to find current song in queue and play next
        const currentIndexInQueueIfAny = queue.findIndex(s => s.id === currentSong.id);
        if (currentIndexInQueueIfAny !== -1 && currentIndexInQueueIfAny < queue.length - 1) {
          nextIndex = currentIndexInQueueIfAny + 1;
        }
      } else if (queue.length > 0 && currentQueueIndex === queue.length -1) {
        // If at the end of the queue, stop or loop (for now, stop by playing nothing)
        // Or play first song of queue to loop
        // nextIndex = 0; // Loop to start
        set({ isPlaying: false }); // Stop at end of queue
        return;
      }

      if (nextIndex !== -1) {
        playSong(queue[nextIndex]);
        set({ currentQueueIndex: nextIndex });
      } else {
        // No next song in queue, or not playing from queue and current song not in queue
        set({ isPlaying: false }); // Stop playback
        // Optionally: set({ currentSong: null, coverUrl: null, currentTime: 0, duration: 0, currentQueueIndex: -1 });
      }
    },

    previousSong: () => {
      const { queue, currentQueueIndex, playSong } = get();
      if (currentQueueIndex > 0 && currentQueueIndex < queue.length) {
        const prevIndex = currentQueueIndex - 1;
        playSong(queue[prevIndex]);
        set({ currentQueueIndex: prevIndex });
      } else if (currentQueueIndex === 0) {
        // If at the start of the queue, restart current song or do nothing
        if (get().audioElement) get().audioElement!.currentTime = 0;
        if(!get().isPlaying) playSong(queue[0]); // if paused, play it
      }
      // If not playing from queue, or queue is empty, this does nothing for now
    },

    clearQueue: () => {
      set({ queue: [], currentQueueIndex: -1 });
    },

    playFromQueue: (index: number) => {
      const { queue, playSong } = get();
      if (index >= 0 && index < queue.length) {
        playSong(queue[index]);
        set({ currentQueueIndex: index });
      }
    },
    
    cleanup: () => {
      const audio = get().audioElement;
      if (audio) {
        audio.pause();
        audio.src = '';
        audio.load(); // Explicitly tell the browser to abandon loading the old src
        // Les listeners d'événements sont sur l'instance, donc si l'instance est recréée, c'est ok.
        // Si on garde la même instance, il faudrait les retirer explicitement.
        set({ 
          currentSong: null, 
          isPlaying: false, 
          currentTime: 0, 
          duration: 0, 
          coverUrl: null,
          // audioElement: null // Optionnel: on peut garder l'instance pour la réutiliser
        });
      }
    },
  };
});

// Auto-initialize audio element on client-side
if (typeof window !== 'undefined') {
  useAudioPlayerStore.getState().initAudioElement();
}
