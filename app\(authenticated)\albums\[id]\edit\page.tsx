"use client"

import type React from "react"
import Link from "next/link"; 
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation" 
import { getSupabaseClient } from "@/lib/supabase/client"
import { Disc, Upload, Save, X, Tag, Info, Plus, Music, FlipVerticalIcon as DragVertical, Trash2, Eye, PlusCircle, CheckCircle2 } from "lucide-react" 
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"; 

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { ImageUploader } from "@/components/ui/image-uploader"
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { MultiSelect } from "@/components/ui/multi-select"
import { genreOptions, moodOptions, instrumentationOptions, albumTypeOptions } from '@/lib/constants/song-options';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; 
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import type { Song } from "@/types"; 
import { cn } from "@/lib/utils"; 
import { CommentSection } from '@/components/comments/comment-section'; 
import { AlbumGalleryUploader } from '@/components/ui/album-gallery-uploader';
import { PlayButton } from "@/components/audio/play-button";
import { AudioSliderPlayer } from "@/components/audio-slider-player";
import { useAudio } from "@/contexts/audio-context";
import { usePlaySong } from "@/hooks/use-play-song";

interface UserBand {
  id: string;
  name: string;
}

const aiContentOriginOptions = [
  { value: 'full_human', label: '100% Humain' },
  { value: 'hybrid', label: 'Hybride (Humain + IA)' },
  { value: '100%_ia', label: '100% IA' },
];

export default function EditAlbumPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [currentAlbumSlug, setCurrentAlbumSlug] = useState<string | null>(null);
  const [userBands, setUserBands] = useState<UserBand[]>([]);
  const [isLoadingBands, setIsLoadingBands] = useState(true); 
  const [albumCreatorId, setAlbumCreatorId] = useState<string | null>(null); 
  const [activeTab, setActiveTab] = useState("info"); 
  const { currentSong, isPlaying } = useAudio(); 
  const { play } = usePlaySong(); 

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    genres: [] as string[], 
    moods: [] as string[], 
    instrumentation: [] as string[], 
    album_type: "", 
    label: "",
    upc: "",
    coverUrl: "",
    releaseDate: "",
    status: "draft",
    isPublic: true, 
    isExplicit: false,
    notifyFollowers: true,
    addToDiscovery: true,
    ai_content_origin: null as '100%_ia' | 'hybrid' | 'full_human' | null,
    create_playlist_from_album: false,
    attribution_type: 'user' as 'user' | 'band', 
    band_id: null as string | null, 
    are_comments_public: false, 
    gallery_image_urls: [] as string[], 
    is_gallery_public: true, 
  })

  interface AlbumEditorTrack {
    id: string; 
    songId: string; 
    title: string; 
    originalTitle: string; 
    duration_ms: number | null;
    cover_art_url: string | null;
    artist_name?: string | null; 
    audio_url?: string | null;
    status?: Song['status'];
    creator_user_id: string; // creator_user_id for the song's creator
    // genres and moods will be inherited from Song type which now uses plural
  }

  const [tracks, setTracks] = useState<AlbumEditorTrack[]>([]);
  const [currentTag, setCurrentTag] = useState("")
  const [tags, setTags] = useState<string[]>([])
  
  type AvailableSong = Pick<Song, 'id' | 'creator_user_id' | 'title' | 'audio_url' | 'duration_ms' | 'cover_art_url' | 'status' | 'artist_name'> & { profiles?: { display_name?: string | null, username?: string | null } | null };

  const [availableSongs, setAvailableSongs] = useState<AvailableSong[]>([]);
  const [isAddSongModalOpen, setIsAddSongModalOpen] = useState(false)
  const [songsToAddInModal, setSongsToAddInModal] = useState<Set<string>>(new Set());

  const loadAvailableSongs = async () => {
    try {
      const supabase = getSupabaseClient()
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) return;

      // Select songs.genres (plural) and songs.moods (plural)
      const { data, error } = await supabase
        .from("songs")
        .select("id, creator_user_id, title, audio_url, duration_ms, cover_art_url, status, genres, moods, artist_name, profiles(display_name, username)") 
        .eq("creator_user_id", session.user.id)
        .in("status", ["published", "draft"]) 
        .order("title", { ascending: true });

      if (error) throw error;
      
      const mappedSongs = (data || []).map(s => ({
        ...s,
        profiles: Array.isArray(s.profiles) ? s.profiles[0] : s.profiles
      }));
      console.log('[EditAlbumPage] loadAvailableSongs - availableSongs:', mappedSongs);
      setAvailableSongs(mappedSongs);

    } catch (error) {
      console.error("Erreur lors du chargement des morceaux disponibles:", error);
      toast({ title: "Erreur", description: "Impossible de charger vos morceaux disponibles.", variant: "destructive" });
    }
  }

  useEffect(() => {
    const fetchAlbumData = async () => {
      setIsLoadingData(true);
      try {
        const supabase = getSupabaseClient()
        // Select albums.genre (singular, ARRAY), albums.moods (plural, ARRAY), albums.instrumentation (singular, ARRAY)
        const { data: album, error } = await supabase.from("albums").select("*, genre, moods, instrumentation, are_comments_public, gallery_image_urls, is_gallery_public").eq("id", params.id).single()

        if (error) throw error;
        if (!album) {
          toast({ title: "Erreur", description: "Album non trouvé", variant: "destructive" });
          router.push("/albums");
          return;
        }
        
        type AlbumSongJoin = {
          track_number: number;
          songs: { 
            id: string;
            title: string;
            duration_ms: number | null;
            cover_art_url: string | null;
            audio_url?: string | null; 
            status?: Song['status']; 
            creator_user_id: string; 
            genres?: string[]; 
            moods?: string[];  
            profiles?: { display_name?: string | null, username?: string | null } | null; 
          } | null; 
        };

        // Select songs.genres (plural) and songs.moods (plural)
        const { data: albumSongsData, error: albumSongsError } = await supabase
          .from("album_songs")
          .select("track_number, songs(id, creator_user_id, title, duration_ms, cover_art_url, audio_url, status, genres, moods, profiles(display_name, username))") 
          .eq("album_id", params.id)
          .order("track_number", { ascending: true })
          .returns<AlbumSongJoin[]>(); 

        if (albumSongsError) {
          console.error('[EditAlbumPage] fetchAlbumData - albumSongsError:', albumSongsError);
          throw albumSongsError;
        }
        console.log('[EditAlbumPage] fetchAlbumData - albumSongsData:', albumSongsData);
        
        const fetchedTracks: AlbumEditorTrack[] = albumSongsData
          ? albumSongsData
              .filter((as: AlbumSongJoin) => as.songs !== null)
              .map((as: AlbumSongJoin) => {
                const songData = as.songs!; 
                const profileData = Array.isArray(songData.profiles) ? songData.profiles[0] : songData.profiles;
                return {
                  id: `track-${songData.id}`,
                  songId: songData.id,
                  title: songData.title, 
                  originalTitle: songData.title, 
                  duration_ms: songData.duration_ms ?? null,
                  artist_name: profileData?.display_name || profileData?.username || 'Artiste inconnu',
                  cover_art_url: songData.cover_art_url, 
                  audio_url: songData.audio_url,
                  status: songData.status,
                  creator_user_id: songData.creator_user_id,
                };
              })
          : [];
        console.log('[EditAlbumPage] fetchAlbumData - fetchedTracks:', fetchedTracks);
        setTracks(fetchedTracks);

        const { data: resourceTags } = await supabase
          .from("resource_tags")
          .select("tags(id, name)")
          .eq("resource_type", "album")
          .eq("resource_id", params.id)

        const albumTags = resourceTags?.map((rt) => (rt.tags as any).name) || []

        const { data: linkedPlaylist, error: linkedPlaylistError } = await supabase
          .from('playlists')
          .select('id')
          .eq('source_album_id', params.id)
          .maybeSingle();
        if (linkedPlaylistError) console.error("Error checking linked playlist:", linkedPlaylistError);
        
        setCurrentAlbumSlug(album.slug || null); 
        setAlbumCreatorId(album.user_id); 

        setFormData({
          title: album.title || '',
          description: album.description || '',
          genres: Array.isArray(album.genre) ? album.genre : [], // albums.genre (singular in DB) is an array
          moods: Array.isArray(album.moods) ? album.moods : [], // albums.moods (plural in DB) is an array
          instrumentation: Array.isArray(album.instrumentation) ? album.instrumentation : [], // albums.instrumentation (singular in DB) is an array
          album_type: album.album_type || "",
          label: album.label || '',
          upc: album.upc || '',
          coverUrl: album.cover_url || '',
          releaseDate: album.release_date ? new Date(album.release_date).toISOString().split('T')[0] : '',
          status: album.status || 'draft',
          isPublic: album.is_public !== undefined ? album.is_public : true, 
          isExplicit: album.is_explicit || false, 
          notifyFollowers: (album as any).notify_followers !== undefined ? (album as any).notify_followers : true, 
          addToDiscovery: (album as any).add_to_discovery !== undefined ? (album as any).add_to_discovery : true,
          ai_content_origin: album.ai_content_origin || null,
          create_playlist_from_album: !!linkedPlaylist,
          attribution_type: album.band_id ? 'band' : 'user', 
          band_id: album.band_id || null, 
          are_comments_public: album.are_comments_public === undefined ? false : album.are_comments_public,
          gallery_image_urls: Array.isArray(album.gallery_image_urls) ? album.gallery_image_urls : [], 
          is_gallery_public: album.is_gallery_public === undefined ? true : album.is_gallery_public,
        });
        setTags(albumTags);

      } catch (error: any) {
        toast({ title: "Erreur", description: error.message || "Une erreur s'est produite lors du chargement de l'album", variant: "destructive" });
      } finally {
        setIsLoadingData(false);
      }
    }
    fetchAlbumData();
    loadAvailableSongs();
  }, [params.id, router, toast]);

  useEffect(() => {
    const fetchUserBands = async () => {
      setIsLoadingBands(true);
      const supabase = getSupabaseClient();
      const { data: userSessionData, error: sessionError } = await supabase.auth.getUser();
      if (sessionError || !userSessionData?.user) {
        setIsLoadingBands(false);
        return;
      }
      const userId = userSessionData.user.id;

      try {
        const { data: bandMemberships, error: membershipError } = await supabase
          .from('band_members')
          .select('band_id')
          .eq('user_id', userId);

        if (membershipError) throw membershipError;

        if (bandMemberships && bandMemberships.length > 0) {
          const bandIds = bandMemberships.map(m => m.band_id);
          const { data: bandsData, error: bandsError } = await supabase
            .from('bands')
            .select('id, name')
            .in('id', bandIds);

          if (bandsError) throw bandsError;
          setUserBands(bandsData || []);
        } else {
          setUserBands([]);
        }
      } catch (error: any) {
        console.error('Error fetching user bands:', error);
        toast({
          title: "Erreur chargement groupes",
          description: `Impossible de charger les groupes: ${error.message}`,
          variant: "destructive",
        });
        setUserBands([]);
      } finally {
        setIsLoadingBands(false);
      }
    };
    fetchUserBands();
  }, [toast]);

  const handleConfirmAddSongs = () => {
    const songsToAddDetails = availableSongs.filter(s => songsToAddInModal.has(s.id) && !tracks.some(t => t.songId === s.id));
    
    const newTracks: AlbumEditorTrack[] = songsToAddDetails.map(s => ({
      id: `track-${s.id}-${Math.random().toString(36).substring(2, 9)}`,
      songId: s.id,
      title: s.title,
      originalTitle: s.title, 
      duration_ms: s.duration_ms ?? null,
      artist_name: s.artist_name || (s.profiles?.display_name || s.profiles?.username || "Artiste Inconnu"),
      cover_art_url: s.cover_art_url, 
      audio_url: s.audio_url, 
      status: s.status as Song['status'],
      creator_user_id: s.creator_user_id, 
      genres: (s as Song & { genres?: string[] }).genres || [], // s comes from AvailableSong which picks Song.genres
      moods: (s as Song & { moods?: string[] }).moods || [],   // s comes from AvailableSong which picks Song.moods
    }));
    setTracks(prev => [...prev, ...newTracks]);
    setIsAddSongModalOpen(false);
    setSongsToAddInModal(new Set()); 
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => { setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }))}
  const handleSwitchChange = (name: string, checked: boolean) => { setFormData((prev) => ({ ...prev, [name]: checked }))}
  const handleCoverUpload = (url: string) => { setFormData((prev) => ({ ...prev, coverUrl: url }))}
  const addTag = () => { if (currentTag && !tags.includes(currentTag)) { setTags((prev) => [...prev, currentTag]); setCurrentTag(""); }}
  const removeTag = (tag: string) => { setTags((prev) => prev.filter((t) => t !== tag))}
  const updateTrack = (id: string, field: string, value: any) => { setTracks((prev) => prev.map((track) => (track.id === id ? { ...track, [field]: value } : track)))}
  const removeTrack = (id: string) => { setTracks((prev) => prev.filter((track) => track.id !== id))}
  const handleDragEnd = (result: DropResult) => { if (!result.destination) return; const items = Array.from(tracks); const [reorderedItem] = items.splice(result.source.index, 1); items.splice(result.destination.index, 0, reorderedItem); setTracks(items); }
  const formatDuration = (milliseconds: number | null | undefined): string => {
    if (milliseconds === null || milliseconds === undefined || milliseconds <= 0) return "0:00";
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }
  const calculateTotalDuration = () => { return tracks.reduce((total, track) => total + (track.duration_ms || 0), 0); }

  const handleSave = async (newStatus?: "draft" | "published") => {
    setIsLoading(true);
    const finalStatus = newStatus || formData.status;
    if (!formData.title) { toast({ title: "Erreur", description: "Le titre est obligatoire", variant: "destructive" }); setIsLoading(false); return; }
    if (finalStatus === "published" && !formData.coverUrl) {
      toast({ title: "Erreur", description: "Vous devez ajouter une pochette pour publier l'album.", variant: "destructive" });
      setIsLoading(false); return;
    }

    const supabase = getSupabaseClient();
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) { toast({ title: "Erreur d'authentification", variant: "destructive" }); setIsLoading(false); return; }

    let slugToSave = currentAlbumSlug;
    const { data: originalAlbumData, error: fetchError } = await supabase.from('albums').select('title, is_public, slug').eq('id', params.id).single();
    if(fetchError && !originalAlbumData) {
        toast({ title: "Erreur", description: "Impossible de vérifier les données originales de l'album.", variant: "destructive" });
        setIsLoading(false); return;
    }

    if (formData.isPublic) {
      const titleChanged = formData.title !== originalAlbumData?.title;
      const becamePublic = formData.isPublic && !originalAlbumData?.is_public;
      const needsNewSlug = titleChanged || becamePublic || !currentAlbumSlug;

      if (needsNewSlug && formData.title) {
        const { data: slugData, error: slugError } = await supabase.rpc('slugify', { value: formData.title });
        if (slugError || !slugData) {
          console.error("Error generating slug:", slugError);
          toast({ title: "Erreur de slug", description: "Impossible de générer un slug.", variant: "destructive" });
          setIsLoading(false); return;
        }
        let tempSlug = slugData;
        let isUnique = false;
        let attempt = 0;
        while(!isUnique && attempt < 5) {
          const {data: existing} = await supabase.from('albums').select('id').eq('slug', tempSlug).neq('id', params.id).maybeSingle();
          if(!existing) { isUnique = true; slugToSave = tempSlug; }
          else { attempt++; tempSlug = `${slugData}-${Math.random().toString(36).substring(2,7)}`; }
        }
        if(!isUnique) { 
          slugToSave = `${slugData}-${Date.now()}`; 
          toast({ title: "Slug généré", description: `Un slug unique a été généré: ${slugToSave}`, variant: "default" });
        }
      }
    } else {
      slugToSave = null;
    }

    try {
      const updatePayload: any = {
        title: formData.title,
        description: formData.description,
        genre: formData.genres.length > 0 ? formData.genres : null, // Save to albums.genre (singular)
        moods: formData.moods.length > 0 ? formData.moods : null, // Save to albums.moods (plural)
        instrumentation: formData.instrumentation.length > 0 ? formData.instrumentation : null, // Save to albums.instrumentation (singular)
        album_type: formData.album_type || null,
        label: formData.label,
        upc: formData.upc,
        cover_url: formData.coverUrl,
        status: finalStatus,
        release_date: formData.releaseDate || null,
        is_explicit: formData.isExplicit, 
        ai_content_origin: formData.ai_content_origin, 
        is_public: formData.isPublic,
        slug: slugToSave,
        band_id: formData.band_id, 
        are_comments_public: formData.are_comments_public,
        gallery_image_urls: formData.gallery_image_urls.length > 0 ? formData.gallery_image_urls : null, 
        is_gallery_public: formData.is_gallery_public, 
      };

      const { error: albumUpdateError } = await supabase
        .from("albums")
        .update(updatePayload)
        .eq("id", params.id)
        .select() 
        .single(); 
      if (albumUpdateError) throw albumUpdateError;

      for (const track of tracks) {
        if (track.title !== track.originalTitle) {
          const { error: songTitleUpdateError } = await supabase
            .from('songs')
            .update({ title: track.title, updated_at: new Date().toISOString() })
            .eq('id', track.songId)
            .eq('user_id', session.user.id); 
          
          if (songTitleUpdateError) {
            console.error(`Error updating title for song ${track.songId}:`, songTitleUpdateError);
            toast({ title: "Erreur partielle", description: `Le titre du morceau "${track.originalTitle}" n'a pas pu être mis à jour.`, variant: "destructive" });
          }
        }
      }
      
      const { error: deleteError } = await supabase.from('album_songs').delete().eq('album_id', params.id);
      if (deleteError) console.error("Error deleting old album_songs:", deleteError);

      if (tracks.length > 0) {
        const albumSongsToInsert = tracks
          .filter(track => track.songId) 
          .map((track, index) => ({
            album_id: params.id,
            song_id: track.songId as string,
            track_number: index + 1,
            // user_id: session.user.id, // Removed user_id as it's not in the table schema
          }));
        if (albumSongsToInsert.length > 0) {
          const { error: insertError } = await supabase.from('album_songs').insert(albumSongsToInsert);
          if (insertError) console.error("Error inserting new album_songs:", insertError);
        }
      }
      
      await supabase.from('resource_tags').delete().eq('resource_id', params.id).eq('resource_type', 'album');
      if (tags.length > 0) {
        const tagObjects = tags.map(tag => ({ name: tag, user_id: session.user.id }));
        const { data: insertedTags, error: tagsErrorUpsert } = await supabase.from('tags').upsert(tagObjects, { onConflict: 'name, user_id' }).select('id, name');
        if (tagsErrorUpsert) console.error("Error upserting tags:", tagsErrorUpsert);
        if (insertedTags) {
          const resourceTagsToInsert = insertedTags.map(tag => ({ resource_id: params.id, tag_id: tag.id, resource_type: 'album', user_id: session.user.id }));
          await supabase.from('resource_tags').insert(resourceTagsToInsert);
        }
      }

      const { data: existingLinkedPlaylist, error: fetchLinkedErrorPl } = await supabase
        .from('playlists')
        .select('id, name, slug')
        .eq('source_album_id', params.id)
        .maybeSingle();
      if (fetchLinkedErrorPl) console.error("Error fetching existing linked playlist:", fetchLinkedErrorPl);

      if (formData.create_playlist_from_album) {
        const isPlaylistPublic = formData.isPublic && finalStatus === "published";
        let playlistSlugForAuto = null;
        if (isPlaylistPublic) {
            const { data: slugDataPl } = await supabase.rpc('slugify', { value: `Album: ${formData.title}` });
            playlistSlugForAuto = slugDataPl;
            const {data: existingPl} = await supabase.from('playlists').select('id').eq('slug', playlistSlugForAuto).neq('source_album_id', params.id).maybeSingle();
            if(existingPl) playlistSlugForAuto = `${playlistSlugForAuto}-${Date.now().toString().slice(-4)}`;
        }

        if (!existingLinkedPlaylist) {
          const { data: newPlaylist, error: playlistCreationError } = await supabase.from('playlists').insert({
            user_id: session.user.id, name: `Album: ${formData.title}`, description: `Playlist pour l'album "${formData.title}".`,
            cover_url: formData.coverUrl, is_public: isPlaylistPublic, slug: playlistSlugForAuto, source_album_id: params.id,
          }).select('id').single();
          if (!playlistCreationError && newPlaylist && tracks.length > 0) { 
            const playlistSongsToInsert = tracks.filter(t => t.songId).map((t, i) => ({playlist_id: newPlaylist.id, song_id: t.songId!, user_id: session.user.id, track_order: i + 1}));
            if(playlistSongsToInsert.length > 0) await supabase.from('playlist_songs').insert(playlistSongsToInsert);
          }
        } else { 
          await supabase.from('playlists').update({
              name: `Album: ${formData.title}`, cover_url: formData.coverUrl, is_public: isPlaylistPublic, slug: playlistSlugForAuto,
          }).eq('id', existingLinkedPlaylist.id);
          await supabase.from('playlist_songs').delete().eq('playlist_id', existingLinkedPlaylist.id);
          if (tracks.length > 0) {
            const playlistSongsToInsert = tracks.filter(t => t.songId).map((t, i) => ({playlist_id: existingLinkedPlaylist.id, song_id: t.songId!, user_id: session.user.id, track_order: i + 1}));
            if(playlistSongsToInsert.length > 0) await supabase.from('playlist_songs').insert(playlistSongsToInsert);
          }
        }
      } else if (!formData.create_playlist_from_album && existingLinkedPlaylist) {
        await supabase.from('playlists').update({ source_album_id: null }).eq('id', existingLinkedPlaylist.id);
      }
      
      setCurrentAlbumSlug(slugToSave); 
      toast({ title: "Album mis à jour", description: "Les modifications ont été enregistrées." });
      
    } catch (error: any) {
      toast({ title: "Erreur de sauvegarde", description: error.message, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }

  const handleDelete = async () => { /* ... */ } 

  if (isLoadingData) { return <div className="w-full px-4 md:px-6 lg:px-8 py-6 text-center">Chargement...</div>; }
  const validTracks = Array.isArray(tracks) ? tracks.filter(track => track && typeof track === 'object' && 'id' in track && 'title' in track) : [];
  
  const previewLinkHref = formData.isPublic && currentAlbumSlug 
    ? `/album/${currentAlbumSlug}` 
    : `/albums/${params.id}`; 
  const previewLinkText = formData.isPublic && currentAlbumSlug ? "PAGE PUBLIQUE" : (formData.status === 'published' ? "Voir l'album (privé)" : "Prévisualiser (privé)");

  return (
    <div className="w-full px-4 md:px-6 lg:px-8 py-6">
      <div className="flex items-center justify-between mb-6">
        <div><h1 className="text-3xl font-bold tracking-tight">Modifier l'album</h1><p className="text-muted-foreground">Modifiez les détails de votre album et organisez vos morceaux</p></div>
        <div className="flex items-center gap-2">
          <Link href={previewLinkHref} target={formData.isPublic && currentAlbumSlug ? "_blank" : "_self"} rel={formData.isPublic && currentAlbumSlug ? "noopener noreferrer" : undefined}>
            <Button variant="outline">
              <Eye className="mr-2 h-4 w-4" />
              {previewLinkText}
            </Button>
          </Link>
          <AlertDialog><AlertDialogTrigger asChild><Button variant="destructive"><Trash2 className="mr-2 h-4 w-4" />Supprimer</Button></AlertDialogTrigger><AlertDialogContent><AlertDialogHeader><AlertDialogTitle>Êtes-vous sûr de vouloir supprimer cet album ?</AlertDialogTitle><AlertDialogDescription>Cette action est irréversible. L'album et sa playlist associée (si existante) seront définitivement supprimés.</AlertDialogDescription></AlertDialogHeader><AlertDialogFooter><AlertDialogCancel>Annuler</AlertDialogCancel><AlertDialogAction onClick={handleDelete} disabled={isDeleting} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">{isDeleting ? "Suppression..." : "Supprimer"}</AlertDialogAction></AlertDialogFooter></AlertDialogContent></AlertDialog>
          <Button variant="outline" onClick={() => router.back()}>Annuler</Button>
          <Button variant="outline" onClick={() => handleSave("draft")} disabled={isLoading}><Save className="mr-2 h-4 w-4" />Enregistrer</Button>
          <Button onClick={() => handleSave("published")} disabled={isLoading}>Publier</Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3"> 
          <TabsTrigger value="info">Informations</TabsTrigger>
          <TabsTrigger value="songs">Morceaux</TabsTrigger>
          <TabsTrigger value="comments">Commentaires</TabsTrigger>
        </TabsList>
        <TabsContent value="info">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader><CardTitle className="flex items-center"><Info className="mr-2 h-5 w-5" />Informations de l'album</CardTitle><CardDescription>Modifiez les informations de base de votre album</CardDescription></CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2"><Label htmlFor="title">Titre de l'album *</Label><Input id="title" name="title" value={formData.title} onChange={handleInputChange} placeholder="Entrez le titre de votre album" required /></div>
                  <div className="space-y-2">
                    <Label>Publié par</Label>
                    <Select
                      value={formData.attribution_type}
                      onValueChange={(value: 'user' | 'band') => {
                        setFormData(prev => ({
                          ...prev,
                          attribution_type: value,
                          band_id: value === 'user' ? null : (prev.band_id || userBands?.[0]?.id || null)
                        }));
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner l'auteur" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">Moi</SelectItem>
                        <SelectItem value="band" disabled={!userBands || userBands.length === 0}>
                          Un de mes groupes {isLoadingBands ? '(Chargement...)' : ''}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {formData.attribution_type === 'band' && (
                    <div className="space-y-2">
                      <Label htmlFor="band_id_select_edit">Groupe</Label>
                      <Select
                        value={formData.band_id || undefined}
                        onValueChange={(value) => {
                          setFormData(prev => ({ ...prev, band_id: value }));
                        }}
                        disabled={isLoadingBands || !userBands || userBands.length === 0}
                      >
                        <SelectTrigger id="band_id_select_edit">
                          <SelectValue placeholder="Sélectionner un groupe" />
                        </SelectTrigger>
                        <SelectContent>
                          {userBands.map(band => (
                            <SelectItem key={band.id} value={band.id}>{band.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {isLoadingBands && <p className="text-xs text-muted-foreground">Chargement des groupes...</p>}
                      {!isLoadingBands && (!userBands || userBands.length === 0) && <p className="text-xs text-muted-foreground">Vous n'êtes membre d'aucun groupe.</p>}
                    </div>
                  )}
                  <div className="space-y-2"><Label htmlFor="description">Description</Label><Textarea id="description" name="description" value={formData.description} onChange={handleInputChange} placeholder="Décrivez votre album" rows={4} /></div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="genres">Genres</Label>
                      <MultiSelect options={genreOptions} selected={formData.genres} onChange={(selected) => setFormData((prev) => ({ ...prev, genres: selected }))} placeholder="Sélectionnez des genres" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="moods">Ambiances / Moods</Label>
                      <MultiSelect options={moodOptions} selected={formData.moods} onChange={(selected) => setFormData((prev) => ({ ...prev, moods: selected }))} placeholder="Sélectionnez des ambiances" />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="instrumentation">Instrumentation</Label>
                      <MultiSelect options={instrumentationOptions} selected={formData.instrumentation} onChange={(selected) => setFormData((prev) => ({ ...prev, instrumentation: selected }))} placeholder="Sélectionnez des instruments" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="album_type">Type d'album</Label>
                      <Select value={formData.album_type} onValueChange={(value) => setFormData(prev => ({ ...prev, album_type: value }))}>
                        <SelectTrigger id="album_type"><SelectValue placeholder="Sélectionnez un type" /></SelectTrigger>
                        <SelectContent>{albumTypeOptions.map(option => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}</SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2"><Label htmlFor="releaseDate">Date de sortie</Label><Input id="releaseDate" name="releaseDate" type="date" value={formData.releaseDate} onChange={handleInputChange} /></div>
                    <div className="space-y-2"><Label htmlFor="label">Label</Label><Input id="label" name="label" value={formData.label} onChange={handleInputChange} placeholder="Nom du label (optionnel)" /></div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2"><Label htmlFor="upc">Code UPC</Label><Input id="upc" name="upc" value={formData.upc} onChange={handleInputChange} placeholder="Code UPC (optionnel)" /></div>
                    <div className="flex items-center space-x-2 pt-5">
                      <Switch id="isExplicit" checked={formData.isExplicit} onCheckedChange={(checked) => handleSwitchChange("isExplicit", checked)} />
                      <Label htmlFor="isExplicit" className="cursor-pointer">Contenu explicite</Label>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ai_content_origin">Origine du Contenu IA</Label>
                    <Select value={formData.ai_content_origin || undefined} onValueChange={(value: '100%_ia' | 'hybrid' | 'full_human') => setFormData(prev => ({ ...prev, ai_content_origin: value }))}>
                      <SelectTrigger id="ai_content_origin"><SelectValue placeholder="Sélectionner l'origine" /></SelectTrigger>
                      <SelectContent>{aiContentOriginOptions.map(option => (<SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>))}</SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">Indiquez la part de l'IA dans la création de cet album.</p>
                  </div>
                  <div className="flex items-center space-x-2 pt-2">
                    <Switch id="isPublic" checked={formData.isPublic} onCheckedChange={(checked) => handleSwitchChange("isPublic", checked)} />
                    <Label htmlFor="isPublic" className="cursor-pointer">Rendre l'album public</Label>
                  </div>
                  <div className="flex items-center space-x-2 pt-2">
                    <Switch id="create_playlist_from_album" checked={formData.create_playlist_from_album} onCheckedChange={(checked) => handleSwitchChange("create_playlist_from_album", checked)} />
                    <Label htmlFor="create_playlist_from_album" className="cursor-pointer">Gérer automatiquement une playlist pour cet album</Label>
                  </div>
                  <div className="flex items-center space-x-2 pt-2">
                    <Switch 
                      id="are_comments_public_album_edit" 
                      checked={formData.are_comments_public} 
                      onCheckedChange={(checked) => handleSwitchChange("are_comments_public", checked)} 
                    />
                    <Label htmlFor="are_comments_public_album_edit" className="cursor-pointer">Rendre les commentaires publics ?</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Si coché, les commentaires seront visibles par tous. Sinon, ils resteront privés.
                  </p>
                  <div className="flex items-center space-x-2 pt-2">
                    <Switch 
                      id="is_gallery_public_edit" 
                      checked={formData.is_gallery_public} 
                      onCheckedChange={(checked) => handleSwitchChange("is_gallery_public", checked)} 
                    />
                    <Label htmlFor="is_gallery_public_edit" className="cursor-pointer">Rendre la galerie d'images publique ?</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Si coché, la galerie d'images sera visible sur la page publique de l'album.
                  </p>
                  
                  <div className="space-y-2 pt-4">
                    <Label>Galerie d'images de l'album (jusqu'à 12 images)</Label>
                    <AlbumGalleryUploader
                      bucketName="album-gallery" 
                      initialFileUrls={formData.gallery_image_urls}
                      onUpdate={(urls) => {
                        console.log('[EditAlbumPage] Gallery Uploader onUpdate, new URLs:', urls);
                        setFormData(prev => ({ ...prev, gallery_image_urls: urls }));
                      }}
                      maxFiles={12}
                    />
                  </div>

                  <div className="space-y-2"><Label>Tags</Label><div className="flex flex-wrap gap-2 mb-2">{tags.map((tag) => ( <Badge key={tag} variant="secondary" className="flex items-center gap-1">{tag}<X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} /></Badge> ))}</div><div className="flex gap-2"><Input value={currentTag} onChange={(e) => setCurrentTag(e.target.value)} placeholder="Ajouter un tag" onKeyDown={(e) => { if (e.key === "Enter") { e.preventDefault(); addTag(); }}} /><Button type="button" variant="outline" onClick={addTag}><Tag className="h-4 w-4" /></Button></div></div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-6">
              <Card>
                <CardHeader><CardTitle>Pochette</CardTitle><CardDescription>Image de couverture de l'album</CardDescription></CardHeader>
                <CardContent className="flex flex-col items-center">
                  <ImageUploader 
                    onImageUploaded={handleCoverUpload} 
                    existingImageUrl={formData.coverUrl || undefined}
                    bucketName="album-covers" 
                    aspectRatio="square"
                    maxWidth={1200} maxHeight={1200} 
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="songs">
          <Card className="mt-6">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center"><Music className="mr-2 h-5 w-5" />Morceaux de l'album</CardTitle>
                  <CardDescription>Ajoutez, organisez et modifiez les morceaux de votre album.</CardDescription>
                </div>
                <Dialog open={isAddSongModalOpen} onOpenChange={setIsAddSongModalOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" onClick={() => { loadAvailableSongs(); setIsAddSongModalOpen(true); }}>
                      <PlusCircle className="mr-2 h-4 w-4" /> Ajouter des morceaux
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle>Ajouter des morceaux à l'album</DialogTitle>
                      <DialogDescription>
                        Sélectionnez les morceaux que vous souhaitez ajouter à cet album. Seuls vos morceaux (publiés ou brouillons) sont listés ici.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="max-h-[400px] overflow-y-auto space-y-2 p-1">
                      {availableSongs.length > 0 ? availableSongs.map(song => {
                        const isAlreadyAdded = tracks.some(t => t.songId === song.id);
                        const artistDisplayName = song.artist_name || song.profiles?.display_name || song.profiles?.username || "Artiste inconnu";
                        return (
                          <div 
                            key={song.id} 
                            className={cn(
                              "flex items-center justify-between p-2 border rounded-md",
                              isAlreadyAdded ? "bg-muted cursor-not-allowed opacity-50" : "hover:bg-accent cursor-pointer",
                              songsToAddInModal.has(song.id) && !isAlreadyAdded ? "ring-2 ring-primary" : ""
                            )}
                            onClick={() => {
                              if (isAlreadyAdded) return;
                              setSongsToAddInModal(prev => {
                                const newSet = new Set(prev);
                                if (newSet.has(song.id)) {
                                  newSet.delete(song.id);
                                } else {
                                  newSet.add(song.id);
                                }
                                return newSet;
                              });
                            }}
                          >
                            <div className="flex items-center gap-3">
                              {song.cover_art_url ? (
                                <img src={song.cover_art_url} alt={song.title} className="h-10 w-10 rounded object-cover" />
                              ) : (
                                <div className="h-10 w-10 rounded bg-muted flex items-center justify-center">
                                  <Music className="h-5 w-5 text-muted-foreground" />
                                </div>
                              )}
                              <div>
                                <p className="font-medium">{song.title}</p>
                                <p className="text-xs text-muted-foreground">{artistDisplayName} - {formatDuration(song.duration_ms)} - <Badge variant={song.status === 'published' ? 'default' : 'secondary'}>{song.status}</Badge></p>
                              </div>
                            </div>
                            {isAlreadyAdded && <Badge variant="outline">Déjà ajouté</Badge>}
                            {!isAlreadyAdded && songsToAddInModal.has(song.id) && <CheckCircle2 className="h-5 w-5 text-primary" />}
                          </div>
                        );
                      }) : <p>Aucun morceau disponible ou tous ont déjà été ajoutés.</p>}
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsAddSongModalOpen(false)}>Annuler</Button>
                      <Button onClick={handleConfirmAddSongs} disabled={songsToAddInModal.size === 0}>
                        Ajouter {songsToAddInModal.size > 0 ? `(${songsToAddInModal.size})` : ''} morceau(x)
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              {validTracks.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">Aucun morceau dans cet album pour le moment.</p>
              ) : (
                <DragDropContext onDragEnd={handleDragEnd}>
                  <Droppable droppableId="tracks">
                    {(provided) => (
                      <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-3">
                        {validTracks.map((track, index) => (
                          <Draggable key={track.id} draggableId={track.id} index={index}>
                            {(provided, snapshot) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className={cn(
                                  "flex items-center gap-3 p-3 border rounded-md bg-card hover:bg-accent",
                                  snapshot.isDragging && "shadow-lg ring-2 ring-primary"
                                )}
                              >
                                <div {...provided.dragHandleProps} className="cursor-grab p-1">
                                  <DragVertical className="h-5 w-5 text-muted-foreground" />
                                </div>
                                <div className="w-12 h-12 flex-shrink-0">
                                  {track.cover_art_url ? (
                                    <img src={track.cover_art_url} alt={track.title} className="h-full w-full rounded object-cover" />
                                  ) : (
                                    <div className="h-full w-full rounded bg-muted flex items-center justify-center">
                                      <Music className="h-6 w-6 text-muted-foreground" />
                                    </div>
                                  )}
                                </div>
                                <div className="flex-grow space-y-1">
                                  <Input
                                    value={track.title}
                                    onChange={(e) => updateTrack(track.id, "title", e.target.value)}
                                    className="text-base font-medium border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 h-auto"
                                    placeholder="Titre du morceau"
                                  />
                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <span>{track.artist_name || "Artiste inconnu"}</span>
                                    <span>-</span>
                                    <span>{formatDuration(track.duration_ms)}</span>
                                    {track.status && (
                                      <>
                                        <span>-</span>
                                        <Badge variant={track.status === 'published' ? 'default' : 'secondary'} className="text-xs px-1.5 py-0.5">
                                          {track.status === 'published' ? 'Publié' : 'Brouillon'}
                                        </Badge>
                                      </>
                                    )}
                                  </div>
                                  {track.title !== track.originalTitle && <Badge variant="outline" className="mt-1 text-xs">Titre modifié</Badge>}
                                  
                                  {track.audio_url && (
                                    <div className="mt-2">
                                      <AudioSliderPlayer
                                        audioSrc={track.audio_url}
                                        isPlaying={currentSong?.id === track.songId && isPlaying}
                                        onPlayPause={() => {
                                          const songToPlay = {
                                            id: track.songId,
                                            title: track.title,
                                            audio_url: track.audio_url,
                                            cover_art_url: track.cover_art_url,
                                            duration_ms: track.duration_ms,
                                            artist: track.artist_name || "Artiste inconnu", // PlayButton uses 'artist', AudioContextSong uses 'artist'
                                            creator_user_id: track.creator_user_id,
                                          };
                                          play(songToPlay as Song); 
                                        }}
                                      />
                                    </div>
                                  )}
                                </div>
                                <div className="flex flex-col items-center gap-2">
                                  {track.audio_url && (
                                    <PlayButton
                                      song={{
                                        id: track.songId,
                                        title: track.title,
                                        audio_url: track.audio_url,
                                        cover_art_url: track.cover_art_url,
                                        duration_ms: track.duration_ms,
                                        artist_name: track.artist_name, // PlayButton expects artist_name
                                        creator_user_id: track.creator_user_id,
                                      } as Song } 
                                      size="sm"
                                    />
                                  )}
                                  <Button variant="ghost" size="icon" onClick={() => removeTrack(track.id)} className="h-8 w-8">
                                    <Trash2 className="h-4 w-4 text-destructive" />
                                  </Button>
                                </div>
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              )}
            </CardContent>
            <CardFooter className="flex justify-between items-center text-sm text-muted-foreground">
              <p>Nombre de morceaux: {tracks.length}</p>
              <p>Durée totale: {formatDuration(calculateTotalDuration())}</p>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="comments">
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Commentaires</CardTitle>
              <CardDescription>
                Gérez les commentaires pour cet album. Vous pouvez activer/désactiver les commentaires publics dans l'onglet "Informations".
              </CardDescription>
            </CardHeader>
            <CardContent>
              {params.id && albumCreatorId ? (
                <CommentSection 
                  resourceId={params.id}
                  resourceType="album"
                  areCommentsPublic={formData.are_comments_public}
                  resourceCreatorId={albumCreatorId} 
                  isModeratorView={true} // Ensure moderator view for album owner
                />
              ) : (
                <p className="text-muted-foreground">Chargement des informations nécessaires pour les commentaires...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
