"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "../ui/dialog";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";

interface PromptEditDialogProps {
  open: boolean;
  initialPrompt: string;
  onClose: () => void;
  onSave: (newPrompt: string) => void;
}

export function PromptEditDialog({ open, initialPrompt, onClose, onSave }: PromptEditDialogProps) {
  const [prompt, setPrompt] = useState(initialPrompt);

  // Reset prompt when dialog opens with a new initialPrompt
  React.useEffect(() => {
    if (open) setPrompt(initialPrompt);
  }, [open, initialPrompt]);

  return (
    <Dialog open={open} onOpenChange={v => !v && onClose()}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Éditer le prompt général</DialogTitle>
        </DialogHeader>
        <Textarea
          value={prompt}
          onChange={e => setPrompt(e.target.value)}
          rows={8}
          className="w-full border rounded-lg p-2 text-sm mt-2"
          placeholder="Modifiez le prompt général utilisé pour chaque suggestion IA..."
        />
        <DialogFooter className="mt-4 flex gap-2 justify-end">
          <Button variant="ghost" onClick={onClose}>Annuler</Button>
          <Button variant="default" onClick={() => onSave(prompt)} disabled={!prompt.trim()}>
            Enregistrer
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
