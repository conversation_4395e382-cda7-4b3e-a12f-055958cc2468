import { createSupabaseServerClient } from "@/lib/supabase/server";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card"; // Assuming Card is used for song items
import { Heart, Music, Play, ArrowLeft } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { AddToPlaylistButton } from "@/components/playlists/add-to-playlist-button";

// Simplified Song type for this page
interface LikedSong {
  id: string;
  title: string;
  duration: number | null;
  cover_url: string | null;
  // artist_name: string | null; // If songs table has artist_name directly
  profiles: { // Assuming songs table has user_id linking to profiles
    username: string | null;
    display_name: string | null;
  } | null;
  created_at: string; // from likes table, to show when it was liked
}

export default async function LikedSongsPage() {
  const supabase = createSupabaseServerClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return <p className="container py-8">Veuillez vous connecter pour voir vos morceaux likés.</p>;
  }

  // Fetch songs liked by the user
  // This requires joining 'likes' with 'songs' and then 'songs' with 'profiles' for artist info
  const { data: likedSongsData, error: likedSongsError } = await supabase
    .from('likes')
    .select(`
      created_at, 
      songs (
        id, 
        title, 
        duration, 
        cover_url,
        profiles:creator_user_id (username, display_name)
      )
    `)
    .eq('user_id', user.id)
    .eq('resource_type', 'song')
    .order('created_at', { ascending: false }); // Order by when liked

  if (likedSongsError) {
    console.error("Error fetching liked songs:", likedSongsError);
    // Handle error display
  }

  const songs: LikedSong[] = likedSongsData
    ?.map(item => ({
      ...(item.songs as any), // Spread song details
      created_at: item.created_at, // This is the like's created_at
    }))
    .filter(song => song.id) || []; // Filter out any null songs if join failed

  const formatDuration = (seconds: number | null) => {
    if (seconds === null) return '-:--';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${String(remainingSeconds).padStart(2, '0')}`;
  };
  
  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold flex items-center">
          <Heart className="mr-3 h-8 w-8 text-red-500 fill-red-500" />
          Morceaux Likés
        </h1>
        <Button variant="outline" size="sm" asChild>
          <Link href="/playlists"><ArrowLeft className="mr-2 h-4 w-4" />Retour aux Playlists</Link>
        </Button>
      </div>

      {songs.length > 0 ? (
        <div className="space-y-3">
          {songs.map((song, index) => (
            <Card key={song.id + '-' + index} className="flex items-center p-3 gap-4 hover:bg-muted/50"> {/* Added index to key for safety if song IDs could repeat due to joins */}
              <span className="text-sm text-muted-foreground w-6 text-center">{index + 1}</span>
              {song.cover_url ? (
                <img src={song.cover_url} alt={song.title} className="h-12 w-12 object-cover rounded-md" />
              ) : (
                <div className="h-12 w-12 bg-muted rounded-md flex items-center justify-center">
                  <Music className="h-6 w-6 text-muted-foreground" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <Link href={`/songs/${song.id}`} className="font-medium hover:underline truncate block" title={song.title}>
                  {song.title}
                </Link>
                <p className="text-xs text-muted-foreground truncate" title={song.profiles?.display_name || song.profiles?.username || "Artiste inconnu"}>
                  {song.profiles?.display_name || song.profiles?.username || "Artiste inconnu"}
                </p>
                <p className="text-xs text-muted-foreground">
                  Liké {formatDistanceToNow(new Date(song.created_at), { locale: fr, addSuffix: true })}
                </p>
              </div>
              <span className="text-sm text-muted-foreground hidden sm:inline">{formatDuration(song.duration)}</span>
              <div className="flex items-center gap-2">
                {/* Play button could be added here if context/player can handle it */}
                {/* <Button variant="ghost" size="icon" className="h-8 w-8"><Play className="h-4 w-4" /></Button> */}
                <AddToPlaylistButton songId={song.id} />
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Heart className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold mb-2">Aucun morceau liké pour le moment</h3>
          <p className="text-muted-foreground">Explorez la musique et cliquez sur le cœur pour l'ajouter ici !</p>
        </div>
      )}
    </div>
  );
}
