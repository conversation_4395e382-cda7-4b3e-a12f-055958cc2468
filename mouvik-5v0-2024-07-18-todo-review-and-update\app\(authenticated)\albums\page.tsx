"use client";

import { useState, useEffect } from 'react';
import Link from "next/link";
import { useRouter } from 'next/navigation';
import { getSupabaseClient } from "@/lib/supabase/client";
import { useUser } from '@/contexts/user-context';
import { Disc, PlusCircle, LayoutGrid, ListFilter, Search as SearchIcon, Loader2, Music, ListMusic as ListMusicIcon, ZoomIn, ZoomOut, Rows3 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Slider } from "@/components/ui/slider";
import { AlbumCard } from "@/components/albums/album-card"; 
import { AlbumListItem } from "@/components/albums/album-list-item"; 
import { AlbumCardCompact } from "@/components/albums/album-card-compact";
import type { Album as AlbumType, UserProfile } from "@/types";
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface FetchedAlbum extends AlbumType {
  profiles: UserProfile | null; 
  songs: { count: number }[]; 
  is_public?: boolean; 
  slug?: string | null; // Added slug
}

interface AlbumForCardDisplay extends AlbumType {
  songs_count?: number;
  is_public?: boolean; 
  slug?: string | null; // Added slug
}

export default function AlbumsPage() {
  const supabase = getSupabaseClient();
  const { user } = useUser();
  const router = useRouter();

  const [allAlbums, setAllAlbums] = useState<AlbumForCardDisplay[]>([]);
  const [displayedAlbums, setDisplayedAlbums] = useState<AlbumForCardDisplay[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('created_at_desc'); 
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid'); // Removed 'grid-compact'
  const [gridCols, setGridCols] = useState(5); // Default for 'grid'
  const [listItemDensity, setListItemDensity] = useState(1); 


  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
    const fetchAlbums = async () => {
      setIsLoading(true);
      const { data, error } = await supabase
        .from("albums")
        .select(`
          *, 
          is_public,
          slug, 
          profiles:user_id (username, display_name, avatar_url), 
          songs (count)
        `)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching albums:", error);
        toast({ title: "Erreur", description: "Impossible de charger les albums.", variant: "destructive" });
        setAllAlbums([]);
      } else {
        const mappedAlbums: AlbumForCardDisplay[] = (data || []).map((album: any) => ({
          ...album,
          songs_count: album.songs && album.songs.length > 0 ? album.songs[0].count : 0,
          profiles: album.profiles,
          is_public: album.is_public,
          slug: album.slug, // Map slug
        }));
        setAllAlbums(mappedAlbums);
      }
      setIsLoading(false);
    };
    fetchAlbums();
  }, [user, supabase, router]);

  useEffect(() => {
    let processedAlbums = [...allAlbums];
    if (searchTerm) {
      processedAlbums = processedAlbums.filter(a => 
        a.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (a.artist_name && a.artist_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (a.profiles?.display_name && a.profiles.display_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (a.profiles?.username && a.profiles.username.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    switch (sortOption) {
      case 'title_asc':
        processedAlbums.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'title_desc':
        processedAlbums.sort((a, b) => b.title.localeCompare(a.title));
        break;
      case 'created_at_asc':
        processedAlbums.sort((a, b) => new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime());
        break;
      case 'created_at_desc':
      default:
        processedAlbums.sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime());
        break;
    }
    setDisplayedAlbums(processedAlbums);
  }, [allAlbums, searchTerm, sortOption]);

  const handleUpdateAlbumStatus = (albumId: string, newStatus: { is_public: boolean; slug: string | null }) => {
    console.log(`Updating status for album ${albumId} to:`, newStatus); // DEBUG
    const updateInList = (list: AlbumForCardDisplay[]) =>
      list.map(a =>
        a.id === albumId
          ? { ...a, is_public: newStatus.is_public, slug: newStatus.slug }
          : a
      );
    setAllAlbums(prev => {
      const newList = updateInList(prev);
      console.log(`New allAlbums list after status update for ${albumId}:`, newList.find(a=>a.id === albumId)); // DEBUG
      return newList;
    });
  };

  const handleAlbumStatsChange = (albumId: string, newStats: { 
    like_count: number; 
    dislike_count: number; 
    // Albums might not have user-specific is_liked/is_disliked state propagated this way
    // but we'll update counts if they are part of AlbumForCardDisplay
  }) => {
    setAllAlbums(prevAlbums => 
      prevAlbums.map(a => 
        a.id === albumId 
          ? { 
              ...a, 
              like_count: newStats.like_count, 
              dislike_count: newStats.dislike_count,
            } 
          : a
      )
    );
  };

  const handleDeleteAlbum = async (albumId: string) => {
    if (!user) return;
    const { error } = await supabase
      .from('albums')
      .delete()
      .eq('id', albumId)
      .eq('user_id', user.id);
    if (error) {
      toast({ title: "Erreur de suppression", description: error.message, variant: "destructive" });
    } else {
      toast({ title: "Album supprimé", description: "L'album a été supprimé avec succès." });
      setAllAlbums(prev => prev.filter(a => a.id !== albumId));
    }
  };
  
  // useEffect for gridCols based on viewMode is no longer needed if 'grid-compact' is removed.
  // Default gridCols is set above.

  const handleGridColsChange = (newCols: number) => {
    const minCols = 2; // Define for 'grid'
    const maxCols = 6; // Define for 'grid'
    setGridCols(Math.max(minCols, Math.min(maxCols, newCols)));
  };
  
  if (isLoading) {
    return <div className="container py-8 flex justify-center items-center min-h-[calc(100vh-200px)]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }

  const getGridClass = () => {
    let classes = "grid gap-6 "; // Default gap for 'grid'

    if (gridCols <= 1) classes += "grid-cols-1";
    else if (gridCols === 2) classes += "grid-cols-1 sm:grid-cols-2";
    else if (gridCols === 3) classes += "grid-cols-1 sm:grid-cols-2 md:grid-cols-3";
    else if (gridCols === 4) classes += "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4";
    else if (gridCols === 5) classes += "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5";
    else if (gridCols >= 6) classes += "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6"; // Max at 6 for albums
    
    if (!classes.includes("grid-cols-")) {
        classes += "xl:grid-cols-5"; // Default fallback for grid
    }
    return classes;
  };

  return (
    <div className="container py-8">
      <div className="flex flex-col md:flex-row items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <Disc className="mr-3 h-8 w-8 text-primary" />
            Mes Albums
          </h1>
          <p className="text-muted-foreground text-sm">Gérez vos albums et collections.</p>
        </div>
        <div className="flex items-center gap-2 flex-wrap">
            <div className="relative">
                <SearchIcon className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input 
                  placeholder="Rechercher albums..." 
                  className="pl-8 w-40 md:w-auto" 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
            </div>
            <TooltipProvider>
              <Tooltip>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="icon" className="h-9 w-9">
                        <ListFilter className="h-4 w-4" />
                        <span className="sr-only">Trier par</span>
                      </Button>
                    </TooltipTrigger>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setSortOption('created_at_desc')}>
                      Date (Plus Récents) {sortOption === 'created_at_desc' && <span className="ml-auto text-xs">✓</span>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('created_at_asc')}>
                      Date (Plus Anciens) {sortOption === 'created_at_asc' && <span className="ml-auto text-xs">✓</span>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('title_asc')}>
                      Titre (A-Z) {sortOption === 'title_asc' && <span className="ml-auto text-xs">✓</span>}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortOption('title_desc')}>
                      Titre (Z-A) {sortOption === 'title_desc' && <span className="ml-auto text-xs">✓</span>}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <TooltipContent>
                  <p>Trier par: {
                      sortOption === 'title_asc' ? 'Titre A-Z' :
                      sortOption === 'title_desc' ? 'Titre Z-A' :
                      sortOption === 'created_at_asc' ? 'Date (Anciens)' :
                      'Date (Récents)'
                    }
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <Button variant={viewMode === 'grid' ? "secondary" : "outline"} size="icon" onClick={() => setViewMode('grid')} title="Vue Grille Standard">
              <LayoutGrid className="h-4 w-4" />
            </Button>
            {/* Removed grid-compact button */}
            <Button variant={viewMode === 'list' ? "secondary" : "outline"} size="icon" onClick={() => setViewMode('list')} title="Vue Liste"> 
              <ListMusicIcon className="h-4 w-4" />
            </Button>

            {viewMode === 'grid' && (
              <div className="flex items-center gap-1.5 ml-1" title="Densité de la grille">
                {/* Bouton GAUCHE (-) => Éléments PLUS PETITS (plus de colonnes) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleGridColsChange(gridCols + 1)} title="Éléments plus petits"><ZoomOut className="h-4 w-4 text-muted-foreground" /></Button> 
                <Slider 
                  value={[ 6 - gridCols ]} // Inverted value: maxCols (6 for grid) - currentCols
                  min={0} 
                  max={6 - 2} // maxSlider = maxCols (6) - minCols (2) = 4
                  step={1} 
                  className="w-[80px]" 
                  onValueChange={(v) => handleGridColsChange(6 - v[0])} 
                  dir="ltr" 
                />
                {/* Bouton DROITE (+) => Éléments PLUS GRANDS (moins de colonnes) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleGridColsChange(gridCols - 1)} title="Éléments plus grands"><ZoomIn className="h-4 w-4 text-muted-foreground" /></Button>
              </div>
            )}
             {/* List Item Density Slider */}
             {viewMode === 'list' && (
              <div className="flex items-center gap-1.5 ml-1" title="Densité de la liste">
                 {/* Bouton GAUCHE (-) => Éléments PLUS PETITS (compact, density 0) */}
                 <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setListItemDensity(prev => Math.max(0, prev - 1))} title="Plus compact">
                  <ZoomOut className="h-4 w-4 text-muted-foreground" />
                </Button>
                <Slider
                  value={[listItemDensity]} // Direct value: 0 (compact) to 2 (spacious)
                  min={0} 
                  max={2} 
                  step={1}
                  className="w-[80px]"
                  onValueChange={(value) => setListItemDensity(value[0])} // Slider left (0) = compact, Slider right (2) = spacious
                  dir="ltr"
                />
                {/* Bouton DROITE (+) => Éléments PLUS GRANDS (spacious, density 2) */}
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setListItemDensity(prev => Math.min(2, prev + 1))} title="Plus espacé">
                  <ZoomIn className="h-4 w-4 text-muted-foreground" />
                </Button>
              </div>
            )}
            
            <Button asChild> 
              <Link href="/albums/create">
                <PlusCircle className="mr-2 h-4 w-4" /> Créer
              </Link>
            </Button>
        </div>
      </div>

      {viewMode === 'grid' && (
        <div className={cn(getGridClass())}> 
          {displayedAlbums.map((album) => (
            <AlbumCard 
              key={album.id} 
              album={album} 
              onDelete={handleDeleteAlbum} 
              onUpdateStatus={handleUpdateAlbumStatus}
              // onStatsChange={handleAlbumStatsChange} // TODO: Implement if AlbumCard gets Like/Dislike buttons
            />
          ))}
        </div>
      )}
      {/* Removed grid-compact rendering section */}
      {viewMode === 'list' && ( 
        <div className="space-y-3">
          {displayedAlbums.map((album) => (
            <AlbumListItem 
              key={album.id} 
              album={album} 
              onDelete={handleDeleteAlbum} 
              onUpdateStatus={handleUpdateAlbumStatus} 
              density={listItemDensity}
              // onStatsChange={handleAlbumStatsChange} // TODO: Implement if AlbumListItem gets Like/Dislike buttons
            />
          ))}
        </div>
      )}

      {allAlbums.length > 0 && displayedAlbums.length === 0 && searchTerm && (
         <div className="text-center py-12 col-span-full">
          <SearchIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold mb-2">Aucun album trouvé</h3>
          <p className="text-muted-foreground mb-4">Essayez d'affiner votre recherche.</p>
        </div>
      )}

      {allAlbums.length === 0 && !isLoading && (
        <div className="text-center py-12 col-span-full">
          <Music className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold mb-2">Aucun album pour le moment</h3>
          <p className="text-muted-foreground mb-4">Commencez par créer votre premier album !</p>
          <Button asChild> 
            <Link href="/albums/create">
              <PlusCircle className="mr-2 h-4 w-4" /> Créer un album
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
