"use client";

import { useEffect, useState } from "react";
import { useR<PERSON>er, use<PERSON>ara<PERSON> } from "next/navigation";
import { getSupabaseClient } from "@/lib/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";
import { UserPlus, ShieldCheck, Trash2, Edit3, UserCog } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { MultiSelect } from "@/components/ui/multi-select"; // Added MultiSelect

// Define types for clarity
interface Profile {
  id: string;
  name?: string | null;
  avatar_url?: string | null;
  username?: string | null; // Added username for display
}

interface BandMember {
  user_id: string;
  role: string | null;
  is_admin: boolean;
  joined_at: string;
  profiles: Profile | null;
  permissions?: string[] | null; // Added permissions
}

interface Band {
  id: string;
  name: string;
  creator_id: string;
}

const ALL_PERMISSIONS = [
  { value: 'manage_members', label: 'Gérer les membres (inviter, retirer, changer rôles/permissions)' },
  { value: 'manage_projects', label: 'Gérer les projets (créer, modifier, supprimer morceaux/albums)' },
  { value: 'edit_band_details', label: 'Modifier les détails du groupe (nom, description, image)' },
  { value: 'delete_band', label: 'Supprimer le groupe' },
  { value: 'create_content', label: 'Créer du contenu (morceaux, albums) pour le groupe' },
  { value: 'view_finances', label: 'Voir les finances/statistiques du groupe' },
  { value: 'manage_band_page', label: 'Gérer la page publique du groupe' },
];

export default function BandMembersManagementPage() {
  const router = useRouter();
  const params = useParams();
  const bandId = params.id as string; // Changed from params.bandId to params.id
  const supabase = getSupabaseClient();

  const [band, setBand] = useState<Band | null>(null);
  const [members, setMembers] = useState<BandMember[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [currentUserIsBandAdmin, setCurrentUserIsBandAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // State for modals
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState("member");
  const [invitePermissions, setInvitePermissions] = useState<string[]>([]); // State for invite permissions
  const [isInviting, setIsInviting] = useState(false);

  const [editingMember, setEditingMember] = useState<BandMember | null>(null);
  const [editRole, setEditRole] = useState("");
  const [editIsAdmin, setEditIsAdmin] = useState(false);
  const [editPermissions, setEditPermissions] = useState<string[]>([]); // State for edit permissions
  const [isUpdatingMember, setIsUpdatingMember] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);

      if (!user || !bandId) {
        setIsLoading(false);
        toast({ title: "Erreur", description: "Utilisateur ou groupe non identifié.", variant: "destructive" });
        router.push("/bands");
        return;
      }

      // Fetch band details (including creator_id)
      const { data: bandData, error: bandError } = await supabase
        .from("bands")
        .select("id, name, creator_id")
        .eq("id", bandId)
        .single();

      if (bandError || !bandData) {
        toast({ title: "Erreur", description: "Groupe non trouvé.", variant: "destructive" });
        router.push("/bands");
        setIsLoading(false);
        return;
      }
      setBand(bandData);

      // Fetch current user's membership to determine admin status
      const { data: currentUserMembership, error: currentUserMembershipError } = await supabase
        .from("band_members")
        .select("is_admin")
        .eq("band_id", bandId)
        .eq("user_id", user.id)
        .single();
      
      const isCreator = bandData.creator_id === user.id;
      const isAdminViaMembership = currentUserMembership?.is_admin === true;
      setCurrentUserIsBandAdmin(isCreator || isAdminViaMembership);
      
      // Fetch all members
      const { data: membersData, error: membersError } = await supabase
        .from("band_members")
        .select("user_id, role, is_admin, joined_at, permissions, profiles(id, username, name, avatar_url)") // Added permissions
        .eq("band_id", bandId);

      if (membersError) {
        toast({ title: "Erreur", description: "Impossible de charger les membres.", variant: "destructive" });
        setMembers([]); // Set to empty array on error
      } else {
        const processedMembers = (membersData || []).map(member => ({
          ...member,
          profiles: Array.isArray(member.profiles) 
            ? (member.profiles[0] || null) 
            : (member.profiles || null)
        }));
        setMembers(processedMembers as BandMember[]);
      }
      setIsLoading(false);
    };
    fetchData();
  }, [bandId, supabase, router]);

  const handleSendInvitation = async () => {
    if (!inviteEmail) {
      toast({ title: "Erreur", description: "Veuillez entrer l'email de l'utilisateur à inviter.", variant: "destructive" });
      return;
    }
    setIsInviting(true);
    try {
      const { data: invitedUserData, error: userFetchError } = await supabase
        .from("profiles").select("id").eq("email", inviteEmail).single();
      if (userFetchError || !invitedUserData) throw new Error("Utilisateur non trouvé avec cet email.");
      
      // TODO: Update band_invitations table to include permissions or handle on accept
      // For now, assuming permissions are set when member joins or is updated directly.
      // The `permissions` field in `band_invitations` would be ideal.
      const { error: invitationError } = await supabase.from("band_invitations").insert({
          band_id: bandId, user_id: invitedUserData.id, invited_by: currentUser?.id,
          role: inviteRole, status: "pending", 
          // permissions: invitePermissions, // Add this if band_invitations table supports it
      });
      if (invitationError) throw invitationError;
      toast({ title: "Invitation envoyée", description: `Invitation envoyée à ${inviteEmail}.` });
      setIsInviteModalOpen(false); setInviteEmail(""); setInviteRole("member"); setInvitePermissions([]);
    } catch (error: any) {
      toast({ title: "Erreur d'invitation", description: error.message, variant: "destructive" });
    } finally {
      setIsInviting(false);
    }
  };

  const openEditModal = (member: BandMember) => {
    setEditingMember(member);
    setEditRole(member.role || "member");
    setEditIsAdmin(member.is_admin || false);
    setEditPermissions(member.permissions || []);
  };

  const handleUpdateMember = async () => {
    if (!editingMember) return;
    setIsUpdatingMember(true);
    try {
      const { error } = await supabase
        .from("band_members")
        .update({ role: editRole, is_admin: editIsAdmin, permissions: editPermissions }) // Add permissions to update
        .eq("band_id", bandId)
        .eq("user_id", editingMember.user_id);
      if (error) throw error;
      toast({ title: "Membre mis à jour", description: "Le rôle et/ou les permissions du membre ont été mis à jour." });
      setMembers(prev => prev.map(m => m.user_id === editingMember.user_id ? {...m, role: editRole, is_admin: editIsAdmin, permissions: editPermissions} : m));
      setEditingMember(null);
    } catch (error: any) {
      toast({ title: "Erreur de mise à jour", description: error.message, variant: "destructive" });
    } finally {
      setIsUpdatingMember(false);
    }
  };

  const handleRemoveMember = async (memberToRemove: BandMember) => {
    if (!band || memberToRemove.user_id === band.creator_id) {
      toast({ title: "Action non autorisée", description: "Le créateur du groupe ne peut pas être retiré.", variant: "destructive"});
      return;
    }
    if (window.confirm(`Êtes-vous sûr de vouloir retirer ${memberToRemove.profiles?.name || 'ce membre'} du groupe ?`)) {
      try {
        const { error } = await supabase
          .from("band_members")
          .delete()
          .eq("band_id", bandId)
          .eq("user_id", memberToRemove.user_id);
        if (error) throw error;
        toast({ title: "Membre retiré", description: `${memberToRemove.profiles?.name || 'Le membre'} a été retiré du groupe.` });
        setMembers(prev => prev.filter(m => m.user_id !== memberToRemove.user_id));
      } catch (error: any) {
        toast({ title: "Erreur", description: `Impossible de retirer le membre: ${error.message}`, variant: "destructive" });
      }
    }
  };

  if (isLoading) {
    return <div className="container py-8 text-center">Chargement...</div>;
  }

  if (!band) {
    return <div className="container py-8 text-center">Groupe non trouvé.</div>;
  }

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Gestion des Membres : {band.name}</h1>
          <p className="text-muted-foreground">Gérez les membres et leurs rôles dans votre groupe.</p>
        </div>
        {currentUserIsBandAdmin && (
          <Button onClick={() => setIsInviteModalOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" /> Inviter un Membre
          </Button>
        )}
      </div>

      <div className="space-y-4">
        {members.map((member) => (
          <Card key={member.user_id}>
            <CardContent className="p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={member.profiles?.avatar_url || "/placeholder.svg"} />
                  <AvatarFallback>{member.profiles?.name?.charAt(0)?.toUpperCase() || member.profiles?.username?.charAt(0)?.toUpperCase() || "U"}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-semibold">{member.profiles?.name || member.profiles?.username || "Utilisateur inconnu"}</p>
                  <p className="text-sm text-muted-foreground">Rôle: {member.role || "Non spécifié"}</p>
                  <div className="text-xs text-muted-foreground">
                    Permissions: {member.permissions && member.permissions.length > 0 ? member.permissions.join(', ') : 'Aucune'}
                  </div>
                  <p className="text-xs text-muted-foreground">Rejoint le: {new Date(member.joined_at).toLocaleDateString()}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {member.is_admin && <Badge variant="destructive"><ShieldCheck className="mr-1 h-3 w-3" /> Admin</Badge>}
                {band.creator_id === member.user_id && <Badge variant="default">Créateur</Badge>}
                
                {currentUserIsBandAdmin && member.user_id !== band.creator_id && (
                  <>
                    <Button variant="outline" size="sm" onClick={() => openEditModal(member)}>
                      <Edit3 className="mr-1 h-3 w-3" /> Modifier
                    </Button>
                    <Button variant="destructive" size="sm" onClick={() => handleRemoveMember(member)}>
                      <Trash2 className="mr-1 h-3 w-3" /> Retirer
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Invite Member Dialog */}
      <Dialog open={isInviteModalOpen} onOpenChange={setIsInviteModalOpen}>
        <DialogContent>
          <DialogHeader><DialogTitle>Inviter un nouveau membre</DialogTitle><DialogDescription>Entrez l'email, le rôle et les permissions.</DialogDescription></DialogHeader>
          <div className="space-y-4 py-2">
            <div><Label htmlFor="invite-email">Email</Label><Input id="invite-email" type="email" value={inviteEmail} onChange={(e) => setInviteEmail(e.target.value)} placeholder="<EMAIL>" /></div>
            <div><Label htmlFor="invite-role">Rôle (musical, ex: Guitariste)</Label><Input id="invite-role" value={inviteRole} onChange={(e) => setInviteRole(e.target.value)} placeholder="Membre" /></div>
            <div>
              <Label htmlFor="invite-permissions">Permissions Initiales</Label>
              <MultiSelect
                options={ALL_PERMISSIONS}
                selected={invitePermissions}
                onChange={setInvitePermissions}
                placeholder="Sélectionner des permissions..."
                className="mt-1"
              />
            </div>
          </div>
          <DialogFooter><Button variant="outline" onClick={() => {setIsInviteModalOpen(false); setInvitePermissions([]);}}>Annuler</Button><Button onClick={handleSendInvitation} disabled={isInviting}>{isInviting ? "Envoi..." : "Envoyer l'invitation"}</Button></DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Member Dialog */}
      {editingMember && (
        <Dialog open={!!editingMember} onOpenChange={() => setEditingMember(null)}>
          <DialogContent>
            <DialogHeader><DialogTitle>Modifier Membre: {editingMember.profiles?.name || editingMember.profiles?.username}</DialogTitle></DialogHeader>
            <div className="space-y-4 py-2">
              <div><Label htmlFor="edit-role">Rôle (musical)</Label><Input id="edit-role" value={editRole} onChange={(e) => setEditRole(e.target.value)} /></div>
              <div className="flex items-center space-x-2"><Switch id="edit-is-admin" checked={editIsAdmin} onCheckedChange={setEditIsAdmin} /><Label htmlFor="edit-is-admin">Co-Admin du groupe</Label></div>
              <div>
                <Label htmlFor="edit-permissions">Permissions</Label>
                <MultiSelect
                  options={ALL_PERMISSIONS}
                  selected={editPermissions}
                  onChange={setEditPermissions}
                  placeholder="Sélectionner des permissions..."
                  className="mt-1"
                />
              </div>
            </div>
            <DialogFooter><Button variant="outline" onClick={() => setEditingMember(null)}>Annuler</Button><Button onClick={handleUpdateMember} disabled={isUpdatingMember}>{isUpdatingMember ? "Mise à jour..." : "Sauvegarder"}</Button></DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
