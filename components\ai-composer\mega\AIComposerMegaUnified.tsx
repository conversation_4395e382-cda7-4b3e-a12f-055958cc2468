'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Music, Brain, Sparkles, MessageSquare, Settings, Target,
  FileText, Guitar, Layers, Volume2, TrendingUp, Lightbulb,
  Wand2, BarChart3, Heart, Clock, Users, Mic, Play, Save
} from 'lucide-react';

// Import des composants optimisés
import { LyricsEditorMega } from './LyricsEditorMega';
import { AIAssistantMega } from './AIAssistantMega';
import { SongInfoHeader } from './SongInfoHeader';
import { TimelineSections } from './TimelineSections';
import { AIActivityFeedback } from './AIActivityFeedback';

interface AIComposerMegaUnifiedProps {
  // États principaux
  songId?: string;
  currentSong: any;
  setCurrentSong: (song: any) => void;
  
  // Contenu musical
  lyricsContent: string;
  setLyricsContent: (content: string) => void;
  songSections: any[];
  setSongSections: (sections: any[]) => void;
  selectedSection: string;
  setSelectedSection: (section: string) => void;
  
  // Configuration
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  
  // IA
  isConfigured: boolean;
  aiHistory: any[];
  setAiHistory: (history: any[]) => void;
  lastAiResult: string;
  setLastAiResult: (result: string) => void;
  aiLoading: boolean;
  aiError: string | null;
  aiConfig?: any;
  setAiConfig?: (config: any) => void;
  
  // Actions
  handleAIGenerate: (prompt: string, type: string) => Promise<void>;
  handleLyricsChange: (content: string) => void;
  handleSave: () => Promise<void>;
  
  // Instruments
  availableInstruments: any[];
}

export const AIComposerMegaUnified: React.FC<AIComposerMegaUnifiedProps> = ({
  songId,
  currentSong,
  setCurrentSong,
  lyricsContent,
  setLyricsContent,
  songSections,
  setSongSections,
  selectedSection,
  setSelectedSection,
  styleConfig,
  setStyleConfig,
  isConfigured,
  aiHistory,
  setAiHistory,
  lastAiResult,
  setLastAiResult,
  aiLoading,
  aiError,
  aiConfig,
  setAiConfig,
  handleAIGenerate,
  handleLyricsChange,
  handleSave,
  availableInstruments
}) => {
  
  // États pour le prompt général et l'interface
  const [generalPrompt, setGeneralPrompt] = useState('');
  const [showAIPanel, setShowAIPanel] = useState(true);
  const [showTimeline, setShowTimeline] = useState(true);
  const [showActivityFeedback, setShowActivityFeedback] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(240);

  // Prompt général par défaut basé sur le style
  React.useEffect(() => {
    if (!generalPrompt && styleConfig) {
      const defaultPrompt = `Créer une chanson ${styleConfig.genres?.[0] || 'pop'} en ${styleConfig.key || 'C'} majeur, tempo ${styleConfig.bpm || 120} BPM. 

Style et ambiance : ${styleConfig.mood || 'énergique et positive'}
Thème principal : ${styleConfig.theme || 'amour et liberté'}
Influences : ${styleConfig.influences || 'moderne avec touches classiques'}

Directives créatives :
- Privilégier des mélodies accrocheuses
- Utiliser des progressions d'accords modernes
- Créer des paroles émotionnelles et authentiques
- Maintenir une cohérence stylistique

Cette vision guide toutes les suggestions IA pour ce projet.`;
      
      setGeneralPrompt(defaultPrompt);
    }
  }, [styleConfig, generalPrompt]);

  // Métriques du projet
  const projectMetrics = React.useMemo(() => {
    const totalWords = lyricsContent.trim().split(/\s+/).filter(Boolean).length;
    const totalSections = songSections.length;
    const totalChords = songSections.reduce((total, section) => total + (section.chords?.length || 0), 0);
    const completionScore = Math.min(100, (totalWords / 100) * 100);
    const structureScore = Math.min(100, (totalSections / 4) * 100);
    const harmonyScore = Math.min(100, (totalChords / 8) * 100);
    const aiUsageScore = Math.min(100, (aiHistory.length / 10) * 100);
    
    return {
      totalWords,
      totalSections,
      totalChords,
      completion: completionScore,
      structure: structureScore,
      harmony: harmonyScore,
      aiUsage: aiUsageScore,
      overall: Math.round((completionScore + structureScore + harmonyScore + aiUsageScore) / 4),
      estimatedDuration: totalSections * 30,
      lastModified: new Date().toLocaleTimeString()
    };
  }, [lyricsContent, songSections, aiHistory]);

  // Gestionnaire pour sauvegarder le prompt général
  const handleSaveGeneralPrompt = useCallback(() => {
    // Sauvegarder dans le styleConfig
    setStyleConfig(prev => ({
      ...prev,
      generalPrompt
    }));
    
    // Mettre à jour la chanson
    setCurrentSong(prev => ({
      ...prev,
      generalPrompt
    }));
  }, [generalPrompt, setStyleConfig, setCurrentSong]);

  // Gestionnaire pour générer avec contexte
  const handleAIGenerateWithContext = useCallback(async (prompt: string, type: string) => {
    if (!isConfigured) return;
    
    // Ajouter le contexte général à tous les prompts
    const contextualPrompt = `${generalPrompt}

CONTEXTE ACTUEL :
- Section : ${songSections.find(s => s.id === selectedSection)?.title || 'Nouvelle section'}
- Type : ${songSections.find(s => s.id === selectedSection)?.type || 'verse'}
- Contenu actuel : ${lyricsContent.substring(0, 200)}${lyricsContent.length > 200 ? '...' : ''}
- Progression : ${projectMetrics.overall}% complété

DEMANDE SPÉCIFIQUE :
${prompt}

Réponds en tenant compte de la vision générale du projet et du contexte actuel.`;

    await handleAIGenerate(contextualPrompt, type);
  }, [generalPrompt, selectedSection, songSections, lyricsContent, projectMetrics.overall, isConfigured, handleAIGenerate]);

  // Gestionnaires pour les nouveaux composants
  const handleClearAIHistory = useCallback(() => {
    setAiHistory([]);
  }, [setAiHistory]);

  const handleCopyAIResult = useCallback((result: string) => {
    navigator.clipboard.writeText(result);
  }, []);

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* Header avec infos du morceau */}
      <div className="border-b border-slate-700">
        <SongInfoHeader
          currentSong={currentSong}
          setCurrentSong={setCurrentSong}
          styleConfig={styleConfig}
          setStyleConfig={setStyleConfig}
          onSave={handleSave}
        />
      </div>

      {/* Header de contrôle */}
      <div className="border-b border-slate-700 bg-slate-800/80 backdrop-blur-sm">
        <div className="flex items-center justify-between p-4">
          {/* Logo et titre */}
          <div className="flex items-center gap-4">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl">
              <Music className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                AI Composer Mega
              </h1>
              <p className="text-sm text-slate-400">Interface complète et professionnelle</p>
            </div>
          </div>

          {/* Métriques rapides */}
          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{projectMetrics.overall}%</div>
              <div className="text-xs text-slate-400">Complétude</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">{projectMetrics.totalWords}</div>
              <div className="text-xs text-slate-400">Mots</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">{projectMetrics.totalSections}</div>
              <div className="text-xs text-slate-400">Sections</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-white">{Math.floor(projectMetrics.estimatedDuration / 60)}:{(projectMetrics.estimatedDuration % 60).toString().padStart(2, '0')}</div>
              <div className="text-xs text-slate-400">Durée est.</div>
            </div>
          </div>

          {/* Actions rapides */}
          <div className="flex items-center gap-3">
            {isConfigured && (
              <Badge variant="default" className="gap-1 bg-green-500">
                <Brain className="h-3 w-3" />
                IA Active
              </Badge>
            )}
            
            <Button 
              variant={isPlaying ? "default" : "outline"} 
              size="sm" 
              onClick={() => setIsPlaying(!isPlaying)}
              className="gap-1"
            >
              <Play className="h-4 w-4" />
              {isPlaying ? 'Pause' : 'Play'}
            </Button>
            
            <Button variant="outline" size="sm" onClick={handleSave} className="gap-1">
              <Save className="h-4 w-4" />
              Sauver
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTimeline(!showTimeline)}
              className="gap-1"
            >
              <Clock className="h-4 w-4" />
              Timeline
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowActivityFeedback(!showActivityFeedback)}
              className="gap-1"
            >
              <BarChart3 className="h-4 w-4" />
              Activité
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAIPanel(!showAIPanel)}
              className="gap-1"
            >
              <Brain className="h-4 w-4" />
              Assistant IA
            </Button>
          </div>
        </div>

        {/* Prompt général */}
        <div className="border-t border-slate-700 p-4 bg-slate-800/50">
          <div className="flex items-start gap-3">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Target className="h-4 w-4 text-purple-400" />
                <span className="text-sm font-medium text-white">Vision Générale du Projet</span>
                <Badge variant="outline" className="text-xs">
                  Guide toutes les suggestions IA
                </Badge>
              </div>
              <Textarea
                value={generalPrompt}
                onChange={(e) => setGeneralPrompt(e.target.value)}
                placeholder="Décrivez la vision, l'ambiance, le style et les influences de votre chanson..."
                className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400 text-sm"
                rows={3}
              />
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleSaveGeneralPrompt}
              className="mt-6"
            >
              <Save className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Workspace principal */}
      <div className="flex-1 overflow-hidden flex">
        {/* Timeline des sections */}
        {showTimeline && (
          <div className="w-80 border-r border-slate-700 bg-slate-800/30">
            <TimelineSections
              sections={songSections}
              setSections={setSongSections}
              selectedSection={selectedSection}
              setSelectedSection={setSelectedSection}
              isPlaying={isPlaying}
              setIsPlaying={setIsPlaying}
              currentTime={currentTime}
              duration={duration}
            />
          </div>
        )}

        {/* Éditeur principal */}
        <div className="flex-1">
          <LyricsEditorMega
            content={lyricsContent}
            onContentChange={handleLyricsChange}
            selectedSection={selectedSection}
            sections={songSections}
            setSections={setSongSections}
            setSelectedSection={setSelectedSection}
            onAIGenerate={handleAIGenerateWithContext}
            styleConfig={styleConfig}
            generalPrompt={generalPrompt}
            projectMetrics={projectMetrics}
          />
        </div>

        {/* Activité IA */}
        {showActivityFeedback && (
          <div className="w-80 border-l border-slate-700 bg-slate-800/30">
            <AIActivityFeedback
              aiLoading={aiLoading}
              aiError={aiError}
              lastAiResult={lastAiResult}
              aiHistory={aiHistory}
              onClearHistory={handleClearAIHistory}
              onCopyResult={handleCopyAIResult}
            />
          </div>
        )}

        {/* Assistant IA Mega */}
        {showAIPanel && (
          <div className="w-96 border-l border-slate-700 bg-slate-800/30">
            <AIAssistantMega
              isConfigured={isConfigured}
              aiLoading={aiLoading}
              aiError={aiError}
              aiConfig={aiConfig}
              setAiConfig={setAiConfig}
              currentSection={selectedSection}
              songSections={songSections}
              styleConfig={styleConfig}
              lyricsContent={lyricsContent}
              aiHistory={aiHistory}
              lastAiResult={lastAiResult}
              setAiHistory={setAiHistory}
              setLastAiResult={setLastAiResult}
              onAIGenerate={handleAIGenerateWithContext}
              generalPrompt={generalPrompt}
              projectMetrics={projectMetrics}
            />
          </div>
        )}
      </div>

      {/* Footer avec statut */}
      <div className="border-t border-slate-700 bg-slate-800/80 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-slate-400">
          <div className="flex items-center gap-4">
            <span>Section: {songSections.find(s => s.id === selectedSection)?.title || 'Aucune'}</span>
            <span>•</span>
            <span>Tonalité: {currentSong.key || styleConfig.key || 'C'}</span>
            <span>•</span>
            <span>Tempo: {currentSong.tempo || styleConfig.bpm || 120} BPM</span>
            <span>•</span>
            <span>Complétude: {projectMetrics.overall}%</span>
          </div>
          <div className="flex items-center gap-2">
            <span>Dernière modif: {projectMetrics.lastModified}</span>
            {songId && (
              <Badge variant="outline" className="text-xs">
                Synchronisé
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
