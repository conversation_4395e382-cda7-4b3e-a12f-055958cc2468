import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Or specific origin like 'http://localhost:3000'
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Fonction pour gérer l'incrémentation et l'enregistrement des écoutes
async function handleRecordPlay(supabaseAdmin: SupabaseClient, songId: string, userId?: string) {
  // Étape 1: Incrémenter le compteur `plays` sur la table `songs`
  // via la fonction RPC `increment_song_plays`
  const { error: rpcError } = await supabaseAdmin.rpc('increment_song_plays', {
    song_id_to_increment: songId,
  });

  if (rpcError) {
    console.error(`Error incrementing plays for song ${songId} via RPC:`, rpcError);
    throw new Error(`Failed to increment play count: ${rpcError.message}`);
  }

  // Étape 2: Insérer un enregistrement dans la table `plays`
  const playRecord: { song_id: string; user_id?: string } = { song_id: songId };
  // Note: La table `plays` a une colonne `user_id` qui est nullable.
  // Si l'authentification est configurée pour cette fonction et que l'user_id est extrait,
  // il sera ajouté ici. Sinon, il sera inséré comme NULL.
  if (userId) {
    playRecord.user_id = userId;
  }

  const { error: insertError } = await supabaseAdmin.from('plays').insert(playRecord);

  if (insertError) {
    console.error(`Error inserting into plays table for song ${songId}:`, insertError);
    // Si cette étape échoue, l'incrémentation de `songs.plays` a déjà eu lieu.
    // Une gestion de transaction plus complexe serait nécessaire pour un rollback atomique complet,
    // ce qui est difficile avec des appels séparés (RPC puis insert).
    // Pour l'instant, on log l'erreur et on continue.
    // L'impact est que le total `songs.plays` pourrait être légèrement supérieur
    // au nombre réel d'entrées dans `public.plays` en cas d'erreur ici.
    throw new Error(`Failed to record play event into plays table: ${insertError.message}`);
  }

  return { success: true, message: `Play recorded for song ${songId}` };
}


serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { song_id: songId, user_id: clientUserId } = await req.json(); // Le client envoie { "song_id": "...", "user_id": "..." (optionnel) }
    let userIdToRecord = clientUserId; // Utiliser l'user_id fourni par le client s'il existe

    // Optionnel: Extraire et valider l'user_id du token JWT si l'authentification est requise et prioritaire
    // Si vous voulez que la fonction soit sécurisée et que user_id provienne toujours du token authentifié:
    // const supabaseUrl = Deno.env.get('SUPABASE_URL');
    // const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    // if (!supabaseUrl || !supabaseServiceRoleKey) {
    //   throw new Error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables');
    // }
    // const supabaseAdminForAuth = createClient(supabaseUrl, supabaseServiceRoleKey);
    // const authHeader = req.headers.get('Authorization');
    // if (authHeader) {
    //   const token = authHeader.replace('Bearer ', '');
    //   const { data: { user }, error: userError } = await supabaseAdminForAuth.auth.getUser(token);
    //   if (userError || !user) {
    //     console.warn('Could not get user from token or token invalid:', userError);
    //     // Si l'authentification est stricte, retourner une erreur 401 ici
    //     // return new Response(JSON.stringify({ error: 'Unauthorized: Invalid token' }), { status: 401, headers: corsHeaders });
    //   } else {
    //     userIdToRecord = user.id; // Prioriser l'user_id du token authentifié
    //   }
    // } else if (/* condition pour exiger l'authentification */) {
    //   // return new Response(JSON.stringify({ error: 'Unauthorized: Missing token' }), { status: 401, headers: corsHeaders });
    // }

    if (!songId) {
      return new Response(JSON.stringify({ error: 'song_id is required' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const result = await handleRecordPlay(supabaseAdmin, songId, userIdToRecord);

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (e: any) {
    console.error('Error in record-play function:', e);
    return new Response(JSON.stringify({ error: e.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
