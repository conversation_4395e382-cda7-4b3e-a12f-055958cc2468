'use client';

import React, { useState, useCallback, memo, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Wand2, MessageSquare, Lightbulb, ArrowRight, 
  Sparkles, Brain, Music, FileText, Settings
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface SimplifiedAIAssistantProps {
  songId?: string;
  currentLyrics?: string;
  currentChords?: string;
  onSuggestionApply?: (suggestion: string, type: 'lyrics' | 'chords' | 'structure') => void;
  onLyricsChange?: (lyrics: string) => void;
  onChordsChange?: (chords: string) => void;
}

interface AISuggestion {
  id: string;
  type: 'lyrics' | 'chords' | 'structure' | 'general';
  title: string;
  content: string;
  confidence: number;
}

// Fonctions utilitaires pour la génération mock
const generateMockLyrics = (prompt: string, currentLyrics?: string): string => {
  const templates = [
    "Dans le silence de la nuit\nJe pense à toi\nTon sourire illumine\nMon cœur qui bat",
    "Les étoiles brillent\nComme tes yeux\nDans cette mélodie\nQui nous unit",
    "Chaque note résonne\nComme un écho\nDe notre amour\nQui grandit encore"
  ];
  
  if (currentLyrics) {
    return currentLyrics + "\n\n" + templates[Math.floor(Math.random() * templates.length)];
  }
  
  return templates[Math.floor(Math.random() * templates.length)];
};

const generateMockChords = (prompt: string, currentLyrics?: string): string => {
  const chordProgressions = [
    "Am F C G",
    "C G Am F",
    "Em C G D",
    "F C Dm Bb",
    "Am Dm G C"
  ];
  
  return chordProgressions[Math.floor(Math.random() * chordProgressions.length)];
};

const MOCK_SUGGESTIONS: AISuggestion[] = [
  {
    id: '1',
    type: 'lyrics',
    title: 'Amélioration du refrain',
    content: 'Essayez d\'ajouter une rime interne pour renforcer l\'impact émotionnel',
    confidence: 85
  },
  {
    id: '2',
    type: 'chords',
    title: 'Progression d\'accords',
    content: 'Considérez Am - F - C - G pour une progression plus moderne',
    confidence: 92
  },
  {
    id: '3',
    type: 'structure',
    title: 'Structure suggérée',
    content: 'Ajoutez un pont après le deuxième refrain pour plus de dynamisme',
    confidence: 78
  }
];

const SimplifiedAIAssistantComponent = ({
  songId,
  currentLyrics,
  currentChords,
  onSuggestionApply,
  onLyricsChange,
  onChordsChange
}: SimplifiedAIAssistantProps) => {
  const router = useRouter();
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [suggestions, setSuggestions] = useState<AISuggestion[]>(MOCK_SUGGESTIONS);
  
  // Générer des suggestions contextuelles basées sur le contenu actuel
  const contextualSuggestions = useMemo(() => {
    const baseSuggestions = [...MOCK_SUGGESTIONS];
    
    if (currentLyrics) {
      const lyricsLength = currentLyrics.length;
      const hasChords = /\[.*\]/.test(currentLyrics);
      
      if (lyricsLength < 100) {
        baseSuggestions.unshift({
          id: 'context-1',
          type: 'lyrics',
          title: 'Développer les paroles',
          content: 'Ajoutez plus de couplets pour enrichir votre chanson',
          confidence: 88
        });
      }
      
      if (!hasChords) {
        baseSuggestions.unshift({
          id: 'context-2',
          type: 'chords',
          title: 'Ajouter des accords',
          content: 'Générez des accords qui s\'harmonisent avec vos paroles',
          confidence: 90
        });
      }
    } else {
      baseSuggestions.unshift({
        id: 'context-3',
        type: 'lyrics',
        title: 'Commencer l\'écriture',
        content: 'Créez vos premières paroles avec l\'aide de l\'IA',
        confidence: 95
      });
    }
    
    return baseSuggestions.slice(0, 4); // Limiter à 4 suggestions
  }, [currentLyrics]);

  const handleGenerate = useCallback(async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    
    try {
      // Simulation d'une génération IA avec des résultats plus réalistes
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Génération basée sur le type de prompt
      if (prompt.toLowerCase().includes('paroles') || prompt.toLowerCase().includes('lyrics')) {
        const generatedLyrics = generateMockLyrics(prompt, currentLyrics);
        onLyricsChange?.(generatedLyrics);
        onSuggestionApply?.(generatedLyrics, 'lyrics');
      } else if (prompt.toLowerCase().includes('accord') || prompt.toLowerCase().includes('chord')) {
        const generatedChords = generateMockChords(prompt, currentLyrics);
        onChordsChange?.(generatedChords);
        onSuggestionApply?.(generatedChords, 'chords');
      }
      
      setPrompt(''); // Clear prompt after generation
    } catch (error) {
      console.error('Erreur lors de la génération IA:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [prompt, currentLyrics, onLyricsChange, onChordsChange, onSuggestionApply]);

  const handleOpenFullComposer = useCallback(() => {
    if (songId) {
      router.push(`/ai-composer-workspace?songId=${songId}`);
    } else {
      router.push('/ai-composer-workspace');
    }
  }, [songId, router]);

  const getSuggestionIcon = useCallback((type: string) => {
    switch (type) {
      case 'lyrics': return <FileText className="h-4 w-4" />;
      case 'chords': return <Music className="h-4 w-4" />;
      case 'structure': return <Settings className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  }, []);

  const getSuggestionColor = useCallback((type: string) => {
    switch (type) {
      case 'lyrics': return 'bg-blue-100 text-blue-800';
      case 'chords': return 'bg-green-100 text-green-800';
      case 'structure': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }, []);

  return (
    <div className="space-y-4">
      {/* Assistant IA Simplifié */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-500" />
              Assistant IA
            </CardTitle>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleOpenFullComposer}
              className="text-purple-600 border-purple-200 hover:bg-purple-50"
            >
              <Wand2 className="h-4 w-4 mr-2" />
              AI Composer Complet
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Zone de saisie */}
          <div className="space-y-2">
            <Textarea
              placeholder="Décrivez ce que vous voulez créer ou améliorer... (ex: 'Écris un refrain accrocheur sur l'amour', 'Suggère des accords pour un style folk')"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              rows={3}
              className="resize-none"
            />
            <Button 
              onClick={handleGenerate}
              disabled={!prompt.trim() || isGenerating}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Génération en cours...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Générer avec l'IA
                </>
              )}
            </Button>
          </div>

          <Separator />

          {/* Suggestions rapides */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-muted-foreground">Suggestions intelligentes</h4>
              {currentLyrics && (
                <Badge variant="outline" className="text-xs">
                  {currentLyrics.length} caractères
                </Badge>
              )}
            </div>
            <div className="space-y-2">
              {contextualSuggestions.map((suggestion) => (
                <div 
                  key={suggestion.id}
                  className="p-3 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer group"
                  onClick={() => onSuggestionApply?.(suggestion.content, suggestion.type as any)}
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex items-start gap-2 flex-1">
                      <div className={cn(
                        "p-1 rounded",
                        getSuggestionColor(suggestion.type)
                      )}>
                        {getSuggestionIcon(suggestion.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="text-sm font-medium">{suggestion.title}</h5>
                          <Badge variant="secondary" className="text-xs">
                            {suggestion.confidence}%
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{suggestion.content}</p>
                      </div>
                    </div>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        onSuggestionApply?.(suggestion.content, suggestion.type as any);
                      }}
                    >
                      Appliquer
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Actions rapides */}
          <div className="space-y-3">
            <div className="grid grid-cols-3 gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setPrompt(currentLyrics ? 'Améliore les paroles de cette chanson' : 'Écris des paroles pour une chanson')}
                className="text-xs"
              >
                <FileText className="h-3 w-3 mr-1" />
                {currentLyrics ? 'Améliorer' : 'Créer'}
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setPrompt('Suggère des accords pour cette progression')}
                className="text-xs"
              >
                <Music className="h-3 w-3 mr-1" />
                Accords
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setPrompt('Propose une structure de chanson')}
                className="text-xs"
              >
                <Settings className="h-3 w-3 mr-1" />
                Structure
              </Button>
            </div>
            
            {/* Indicateur de statut */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  currentLyrics ? "bg-green-500" : "bg-gray-300"
                )} />
                Paroles {currentLyrics ? 'présentes' : 'manquantes'}
              </div>
              <div className="flex items-center gap-1">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  currentLyrics && /\[.*\]/.test(currentLyrics) ? "bg-green-500" : "bg-gray-300"
                )} />
                Accords {currentLyrics && /\[.*\]/.test(currentLyrics) ? 'détectés' : 'manquants'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export const SimplifiedAIAssistant = memo(SimplifiedAIAssistantComponent);
export default SimplifiedAIAssistant;