-- Enable RLS on the profiles table if not already enabled
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies before creating new ones
DROP POLICY IF EXISTS "Allow users to update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Allow users to see their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Allow public read access to public profile information" ON public.profiles;
DROP POLICY IF EXISTS "Allow users to insert their own profile" ON public.profiles;

-- Policy: Allow users to update their own profile
CREATE POLICY "Allow users to update their own profile"
ON public.profiles
FOR UPDATE
TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Policy: Allow users to see their own profile
CREATE POLICY "Allow users to see their own profile"
ON public.profiles
FOR SELECT
TO authenticated
USING (auth.uid() = id);

-- Policy: Allow public read access to public profile information
-- This policy allows anyone to read specific columns if the profile is public.
CREATE POLICY "Allow public read access to public profile information"
ON public.profiles
FOR SELECT
USING (is_profile_public = TRUE); 

-- Policy: Allow users to insert their own profile 
-- (Often a trigger on auth.users handles profile creation, but this policy allows direct insert if needed)
CREATE POLICY "Allow users to insert their own profile"
ON public.profiles
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = id);

-- IMPORTANT: Review these policies. The "Allow public read access" is key for comments.
-- Ensure the 'is_profile_public' column exists in your 'profiles' table.
