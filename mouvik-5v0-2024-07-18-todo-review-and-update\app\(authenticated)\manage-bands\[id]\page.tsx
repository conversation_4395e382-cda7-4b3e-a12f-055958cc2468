import Link from "next/link"
import { notFound } from "next/navigation"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { Edit, Users, Calendar, MessageSquare, Plus, Heart, ThumbsDown } from "lucide-react" 
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { BandProjects } from "@/components/bands/band-projects"
import { BandMembers } from "@/components/bands/band-members"
import { BandActivity } from "@/components/bands/band-activity"
import { BandAnalytics } from "@/components/bands/band-analytics"
import { BandCommunication } from "@/components/bands/band-communication"
import { ResourceViewTracker } from "@/components/stats/resource-view-tracker"; 
import { LikeButton } from "@/components/social/like-button"; 
import { DislikeButton } from "@/components/social/dislike-button"; 
import { CommentSection } from "@/components/comments/comment-section"; // Added CommentSection import

export default async function BandPage({ params }: { params: { id: string } }) {
  const supabase = createSupabaseServerClient()

  const {
    data: { user: currentUser }, 
  } = await supabase.auth.getUser(); 

  const { data: bandData } = await supabase
    .from("bands")
    .select("*, dislike_count") // Removed like_count as it's not in the table schema
    .eq("id", params.id)
    .single();
  
  // like_count needs to be fetched separately, e.g., via an RPC or a related table/view
  // For now, let's assume it might be handled by the LikeButton component or fetched differently.
  // We'll add a placeholder for it if needed by the LikeButton component.
  // like_count needs to be fetched separately, e.g., via an RPC or a related table/view
  // For now, let's assume it might be handled by the LikeButton component or fetched differently.
  // We'll add a placeholder for it if needed by the LikeButton component.
  const band = bandData as (typeof bandData & { like_count?: number, are_comments_public?: boolean /* placeholder */ }) | null;


  if (!band) {
    notFound()
  }

  let isLikedByCurrentUser = false;
  let isDislikedByCurrentUser = false;
  if (currentUser) {
    const { data: likeData } = await supabase.from('likes').select('id').eq('resource_id', band.id).eq('resource_type', 'band').eq('user_id', currentUser.id).maybeSingle();
    isLikedByCurrentUser = !!likeData;
    const { data: dislikeData } = await supabase.from('dislikes').select('id').eq('resource_id', band.id).eq('resource_type', 'band').eq('user_id', currentUser.id).maybeSingle();
    isDislikedByCurrentUser = !!dislikeData;
  }

  const { data: membership } = await supabase
    .from("band_members")
    .select("role, is_admin") 
    .eq("band_id", params.id)
    .eq("user_id", currentUser?.id) 
    .single()

  const isAdmin = band.creator_id === currentUser?.id || membership?.is_admin === true;

  const { data: members } = await supabase
    .from("band_members")
    .select(`user_id, role, joined_at, profiles (id, name, avatar_url)`)
    .eq("band_id", params.id)

  // Process members to ensure profiles is an object
  const processedMembers = members?.map(m => ({
    ...m,
    // Supabase might return profiles as an array even for a one-to-one join if not explicitly singularized by PostgREST.
    // Or, if the 'profiles' relation in band_members could theoretically point to multiple profiles (though unlikely for a user_id FK).
    // This ensures that 'profiles' is an object as expected by downstream components.
    profiles: Array.isArray(m.profiles) ? m.profiles[0] : m.profiles
  })) || [];

  const { data: projects } = await supabase
    .from("band_projects")
    .select("*")
    .eq("band_id", params.id)
    .order("updated_at", { ascending: false })

  const { data: bandSongs } = await supabase
    .from("songs")
    .select("*, profiles:user_id (username, display_name, avatar_url)") 
    .eq("band_id", params.id)
    .order("created_at", { ascending: false });

  const { data: bandAlbums } = await supabase
    .from("albums")
    .select("*, profiles:user_id (username, display_name, avatar_url)") 
    .eq("band_id", params.id)
    .order("created_at", { ascending: false });

  return (
    <div className="flex flex-col">
      <ResourceViewTracker resourceId={band.id} resourceType="band" />
      <div className="relative h-48 md:h-64 bg-gradient-to-r from-primary/20 to-primary/5">
        {band.cover_url && (
          <img src={band.cover_url || "/placeholder.svg"} alt={band.name} className="w-full h-full object-cover opacity-50" />
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent" />
      </div>

      <div className="container px-4 md:px-6">
        <div className="flex flex-col md:flex-row items-start md:items-end gap-4 -mt-16 md:-mt-20 mb-6 relative z-10">
          <Avatar className="h-32 w-32 border-4 border-background">
            <AvatarImage src={band.avatar_url || "/placeholder.svg?height=128&width=128&query=band logo"} />
            <AvatarFallback className="text-4xl">{band.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4">
              <h1 className="text-3xl font-bold">{band.name}</h1>
              <div className="flex items-center gap-2 flex-wrap">
                {Array.isArray(band.genres) && band.genres.map((g: string) => <Badge key={g} variant="outline">{g}</Badge>)}
                {Array.isArray(band.moods) && band.moods.map((m: string) => <Badge key={m} variant="secondary">{m}</Badge>)}
                {band.location && <Badge variant="outline">{band.location}</Badge>}
              </div>
            </div>
            <p className="text-muted-foreground mt-1">{members?.length || 0} membres actifs</p>
            <p className="mt-2 max-w-2xl">{band.description}</p>
          </div>
          <div className="flex gap-2 mt-4 md:mt-0 items-center">
            {currentUser?.id !== band.creator_id && (
              <>
                <LikeButton
                  resourceId={band.id}
                  resourceType="band"
                  initialLikes={band.like_count || 0} // This will use the placeholder if like_count is not on bandData
                  initialIsLiked={isLikedByCurrentUser}
                  userId={currentUser?.id}
                  size="default"
                  // onLikeToggle can be added if client-side count updates are needed here
                />
                <DislikeButton
                  resourceId={band.id}
                  resourceType="band"
                  initialDislikes={band.dislike_count || 0}
                  initialIsDisliked={isDislikedByCurrentUser}
                  userId={currentUser?.id}
                  size="default"
                  // onDislikeToggle can be added
                />
              </>
            )}
            {isAdmin && (
              <Button variant="outline" asChild>
                <Link href={`/manage-bands/${params.id}/edit`}><Edit className="mr-2 h-4 w-4" />Éditer</Link> {/* Updated link */}
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-6">
          <Card><CardHeader className="pb-2"><CardTitle className="text-sm font-medium">Projets</CardTitle></CardHeader><CardContent><div className="text-2xl font-bold">{projects?.length || 0}</div></CardContent></Card>
          <Card><CardHeader className="pb-2"><CardTitle className="text-sm font-medium">Likes</CardTitle></CardHeader><CardContent><div className="text-2xl font-bold">{band.like_count || 0}</div></CardContent></Card> {/* This will show 0 if like_count is not fetched */}
          <Card><CardHeader className="pb-2"><CardTitle className="text-sm font-medium">Dislikes</CardTitle></CardHeader><CardContent><div className="text-2xl font-bold">{band.dislike_count || 0}</div></CardContent></Card>
          <Card><CardHeader className="pb-2"><CardTitle className="text-sm font-medium">Membres</CardTitle></CardHeader><CardContent><div className="text-2xl font-bold">{members?.length || 0}</div></CardContent></Card>
        </div>

        <Tabs defaultValue="overview" className="mb-6">
          <TabsList>
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="songs">Morceaux ({bandSongs?.length || 0})</TabsTrigger>
            <TabsTrigger value="albums">Albums</TabsTrigger>
            <TabsTrigger value="members">Membres</TabsTrigger>
            <TabsTrigger value="comments">Commentaires</TabsTrigger> {/* Added Comments Tab Trigger */}
            <TabsTrigger value="settings">Paramètres & Admin</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="mt-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2 space-y-6">
                <BandProjects projects={projects || []} bandId={params.id} />
                <BandActivity bandId={params.id} />
              </div>
              <div className="space-y-6">
                <BandMembers members={processedMembers} bandId={params.id} isAdmin={isAdmin} />
                {/* Assuming BandAnalytics might also consume members or a similar structure indirectly, 
                    or has its own fetch that needs similar processing. 
                    For now, only explicitly changing BandMembers. 
                    If BandAnalytics error persists, it needs separate investigation. */}
                <BandAnalytics bandId={params.id} />
                <BandCommunication bandId={params.id} />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="projects" className="mt-6">
            <BandProjects projects={projects || []} bandId={params.id} showAll />
          </TabsContent>
          <TabsContent value="songs" className="mt-6">
            <h2 className="text-2xl font-semibold mb-4">Morceaux du Groupe</h2>
            {isAdmin && (<Button asChild className="mb-4"><Link href={`/songs/create?bandId=${band.id}`}><Plus className="mr-2 h-4 w-4" /> Ajouter un morceau</Link></Button>)}
            {bandSongs && bandSongs.length > 0 ? (<div className="space-y-4">{bandSongs.map((song: any) => ( <Card key={song.id}><CardHeader><CardTitle>{song.title}</CardTitle><CardDescription>Par {song.profiles?.display_name || song.profiles?.username || 'Artiste inconnu'} - Ajouté le {new Date(song.created_at).toLocaleDateString()}</CardDescription></CardHeader><CardContent><p className="text-sm text-muted-foreground">{song.description || "Pas de description."}</p></CardContent><CardFooter><Button variant="outline" size="sm" asChild><Link href={`/songs/${song.id}/edit?bandId=${band.id}`}>Éditer</Link></Button></CardFooter></Card>))}</div>) : (<p>Aucun morceau associé à ce groupe pour le moment.</p>)}
          </TabsContent>
          <TabsContent value="albums" className="mt-6">
            <h2 className="text-2xl font-semibold mb-4">Albums du Groupe</h2>
            {isAdmin && (<Button asChild className="mb-4"><Link href={`/albums/create?bandId=${band.id}`}><Plus className="mr-2 h-4 w-4" /> Ajouter un album</Link></Button>)}
            {bandAlbums && bandAlbums.length > 0 ? (<div className="space-y-4">{bandAlbums.map((album: any) => ( <Card key={album.id}><CardHeader><CardTitle>{album.title}</CardTitle><CardDescription>Créé le {new Date(album.created_at).toLocaleDateString()}</CardDescription></CardHeader><CardContent><p className="text-sm text-muted-foreground line-clamp-3">{album.description || "Pas de description."}</p>{album.cover_url && (<div className="mt-2"><img src={album.cover_url} alt={album.title} className="h-20 w-20 object-cover rounded-md" /></div>)}</CardContent><CardFooter><Button variant="outline" size="sm" asChild><Link href={`/albums/${album.id}/edit?bandId=${band.id}`}>Éditer</Link></Button><Button variant="ghost" size="sm" asChild className="ml-2"><Link href={`/albums/${album.id}`}>Voir</Link></Button></CardFooter></Card>))}</div>) : (<p>Aucun album associé à ce groupe pour le moment.</p>)}
          </TabsContent>
          <TabsContent value="members" className="mt-6">
             <BandMembers members={processedMembers} bandId={params.id} isAdmin={isAdmin} />
          </TabsContent>
          <TabsContent value="settings" className="mt-6">
             {isAdmin ? (<div><h2 className="text-2xl font-semibold mb-4">Paramètres du Groupe</h2><p className="mb-2">Gérez les informations et les options de votre groupe.</p><Button variant="outline" asChild><Link href={`/manage-bands/${params.id}/edit`}><Edit className="mr-2 h-4 w-4" />Modifier les informations du groupe</Link></Button></div>) : (<p>Vous n'avez pas les droits pour accéder aux paramètres de ce groupe.</p>)} {/* Updated link */}
          </TabsContent>
          <TabsContent value="comments" className="mt-6">
            <CommentSection 
              resourceId={band.id}
              resourceType="band"
              resourceCreatorId={band.creator_id}
              isModeratorView={isAdmin}
              areCommentsPublic={band.are_comments_public ?? false} // Pass the flag, default to false (private) if undefined
              isCurrentUserMember={!!membership} // True if user has a membership record for this band
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
