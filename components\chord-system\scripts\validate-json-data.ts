/**
 * 🎼 SCRIPT DE VALIDATION DES DONNÉES JSON
 * 
 * Script pour valider la compatibilité des fichiers JSON existants
 * avec le nouveau système d'accords unifié
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

import { ChordValidation } from '../utils/ChordValidation';
import { createUnifiedChord } from '../index';
import type { ChordJsonDefinition } from '../types/chord-system';

// ============================================================================
// CONFIGURATION
// ============================================================================

const JSON_FILES_TO_TEST = [
  'lib/chords/ukulele_gcea_complete.json',
  'lib/chords/mandolin_gdae_tuning.json',
  'lib/chords/banjo_5string_complete.json',
  // Ajoutez d'autres fichiers ici
];

// ============================================================================
// FONCTIONS UTILITAIRES
// ============================================================================

/**
 * Charge un fichier JSON de manière sécurisée
 */
async function loadJsonFile(filePath: string): Promise<any> {
  try {
    // En environnement Node.js
    if (typeof require !== 'undefined') {
      return require(`../../../${filePath}`);
    }
    
    // En environnement navigateur
    const response = await fetch(`/${filePath}`);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Erreur lors du chargement de ${filePath}:`, error);
    return null;
  }
}

/**
 * Analyse un fichier JSON d'instrument
 */
function analyzeInstrumentFile(data: ChordJsonDefinition, fileName: string) {
  console.log(`\n📁 Analyse de ${fileName}`);
  console.log('=' .repeat(50));
  
  // Informations générales
  console.log(`🎸 Instrument: ${data.instrument}`);
  console.log(`🎵 Accordage: ${data.tuning?.join('-') || 'Non spécifié'}`);
  console.log(`🎯 Cordes: ${data.strings || 'Non spécifié'}`);
  console.log(`🎼 Tonalités: ${data.keys?.length || 0}`);
  console.log(`🎶 Types d'accords: ${data.suffixes?.length || 0}`);
  
  // Comptage des accords
  let totalChords = 0;
  let totalPositions = 0;
  const difficultyCount = { beginner: 0, intermediate: 0, advanced: 0, unknown: 0 };
  
  if (data.chords) {
    for (const [key, variations] of Object.entries(data.chords)) {
      totalChords += variations.length;
      for (const variation of variations) {
        totalPositions += variation.positions?.length || 0;
        
        // Comptage par difficulté
        for (const position of variation.positions || []) {
          const difficulty = position.difficulty || 'unknown';
          if (difficulty in difficultyCount) {
            difficultyCount[difficulty as keyof typeof difficultyCount]++;
          } else {
            difficultyCount.unknown++;
          }
        }
      }
    }
  }
  
  console.log(`🎵 Total accords: ${totalChords}`);
  console.log(`📍 Total positions: ${totalPositions}`);
  console.log(`📊 Difficulté:`);
  console.log(`   - Débutant: ${difficultyCount.beginner}`);
  console.log(`   - Intermédiaire: ${difficultyCount.intermediate}`);
  console.log(`   - Avancé: ${difficultyCount.advanced}`);
  console.log(`   - Non spécifié: ${difficultyCount.unknown}`);
  
  return {
    totalChords,
    totalPositions,
    difficultyCount
  };
}

/**
 * Valide un fichier JSON avec notre système
 */
function validateInstrumentFile(data: ChordJsonDefinition, fileName: string) {
  console.log(`\n🔍 Validation de ${fileName}`);
  console.log('-'.repeat(50));
  
  const validation = ChordValidation.validateInstrumentFile(data);
  
  if (validation.valid) {
    console.log('✅ Validation réussie !');
  } else {
    console.log('❌ Validation échouée');
    console.log('Erreurs:');
    validation.errors.slice(0, 10).forEach(error => {
      console.log(`   - ${error}`);
    });
    if (validation.errors.length > 10) {
      console.log(`   ... et ${validation.errors.length - 10} autres erreurs`);
    }
  }
  
  if (validation.warnings && validation.warnings.length > 0) {
    console.log('⚠️  Avertissements:');
    validation.warnings.slice(0, 5).forEach(warning => {
      console.log(`   - ${warning}`);
    });
    if (validation.warnings.length > 5) {
      console.log(`   ... et ${validation.warnings.length - 5} autres avertissements`);
    }
  }
  
  if (validation.stats) {
    console.log(`📊 Statistiques:`);
    console.log(`   - Total: ${validation.stats.totalChords}`);
    console.log(`   - Valides: ${validation.stats.validChords}`);
    console.log(`   - Invalides: ${validation.stats.invalidChords}`);
    console.log(`   - Taux de réussite: ${((validation.stats.validChords / validation.stats.totalChords) * 100).toFixed(1)}%`);
  }
  
  return validation;
}

/**
 * Teste la conversion vers le format unifié
 */
function testConversionToUnified(data: ChordJsonDefinition, fileName: string) {
  console.log(`\n🔄 Test de conversion ${fileName}`);
  console.log('-'.repeat(50));
  
  let convertedCount = 0;
  let validCount = 0;
  let errorCount = 0;
  const errors: string[] = [];
  
  try {
    for (const [key, variations] of Object.entries(data.chords || {})) {
      for (const variation of variations) {
        for (const position of variation.positions || []) {
          convertedCount++;
          
          try {
            const unified = createUnifiedChord(
              position,
              `${key}${variation.suffix}`,
              data.instrument as any,
              'standard'
            );
            
            const validation = ChordValidation.validateChord(unified);
            if (validation.valid) {
              validCount++;
            } else {
              errorCount++;
              errors.push(`${key}${variation.suffix}: ${validation.errors.join(', ')}`);
            }
          } catch (error) {
            errorCount++;
            errors.push(`${key}${variation.suffix}: Erreur de conversion - ${error}`);
          }
        }
      }
    }
  } catch (error) {
    console.log(`❌ Erreur lors de la conversion: ${error}`);
    return { success: false, convertedCount: 0, validCount: 0, errorCount: 0 };
  }
  
  const successRate = convertedCount > 0 ? (validCount / convertedCount) * 100 : 0;
  
  console.log(`📊 Résultats de conversion:`);
  console.log(`   - Convertis: ${convertedCount}`);
  console.log(`   - Valides: ${validCount}`);
  console.log(`   - Erreurs: ${errorCount}`);
  console.log(`   - Taux de réussite: ${successRate.toFixed(1)}%`);
  
  if (errorCount > 0) {
    console.log(`❌ Premières erreurs:`);
    errors.slice(0, 5).forEach(error => {
      console.log(`   - ${error}`);
    });
    if (errors.length > 5) {
      console.log(`   ... et ${errors.length - 5} autres erreurs`);
    }
  }
  
  return {
    success: successRate > 90, // 90% de réussite minimum
    convertedCount,
    validCount,
    errorCount,
    successRate
  };
}

// ============================================================================
// FONCTION PRINCIPALE
// ============================================================================

/**
 * Fonction principale de validation
 */
export async function validateAllJsonData() {
  console.log('🎼 VALIDATION DU SYSTÈME D\'ACCORDS UNIFIÉ');
  console.log('='.repeat(60));
  console.log(`📅 Date: ${new Date().toLocaleString()}`);
  console.log(`🔧 Version: 1.0.0`);
  
  const results = {
    totalFiles: 0,
    validFiles: 0,
    totalChords: 0,
    validChords: 0,
    conversionSuccess: 0,
    errors: [] as string[]
  };
  
  // Test avec des données simulées si les vrais fichiers ne sont pas disponibles
  const testData = [
    {
      fileName: 'ukulele_gcea_complete.json (simulé)',
      data: {
        instrument: 'ukulele',
        tuning: ['G', 'C', 'E', 'A'],
        strings: 4,
        keys: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
        suffixes: ['major', 'minor', '7'],
        chords: {
          'C': [
            {
              suffix: 'major',
              name: 'Cmajor',
              positions: [
                {
                  frets: [0, 0, 0, 3],
                  fingers: [0, 0, 0, 1],
                  baseFret: 1,
                  barres: [],
                  midi: [67, 60, 64, 72],
                  difficulty: 'beginner'
                }
              ]
            }
          ]
        }
      } as ChordJsonDefinition
    }
  ];
  
  for (const { fileName, data } of testData) {
    results.totalFiles++;
    
    try {
      // Analyse du fichier
      const stats = analyzeInstrumentFile(data, fileName);
      results.totalChords += stats.totalPositions;
      
      // Validation
      const validation = validateInstrumentFile(data, fileName);
      if (validation.valid) {
        results.validFiles++;
        results.validChords += validation.stats?.validChords || 0;
      } else {
        results.errors.push(`${fileName}: ${validation.errors.length} erreurs`);
      }
      
      // Test de conversion
      const conversion = testConversionToUnified(data, fileName);
      if (conversion.success) {
        results.conversionSuccess++;
      }
      
    } catch (error) {
      console.log(`❌ Erreur lors du traitement de ${fileName}:`, error);
      results.errors.push(`${fileName}: Erreur de traitement`);
    }
  }
  
  // Résumé final
  console.log('\n🎯 RÉSUMÉ FINAL');
  console.log('='.repeat(60));
  console.log(`📁 Fichiers traités: ${results.totalFiles}`);
  console.log(`✅ Fichiers valides: ${results.validFiles}/${results.totalFiles}`);
  console.log(`🎵 Accords traités: ${results.totalChords}`);
  console.log(`✅ Accords valides: ${results.validChords}/${results.totalChords}`);
  console.log(`🔄 Conversions réussies: ${results.conversionSuccess}/${results.totalFiles}`);
  
  const overallSuccess = results.validFiles === results.totalFiles && 
                         results.conversionSuccess === results.totalFiles;
  
  if (overallSuccess) {
    console.log('\n🎉 VALIDATION COMPLÈTE RÉUSSIE !');
    console.log('✅ Tous les fichiers JSON sont compatibles avec le système unifié');
  } else {
    console.log('\n⚠️  VALIDATION PARTIELLE');
    console.log('❌ Certains fichiers nécessitent des ajustements');
    if (results.errors.length > 0) {
      console.log('\nErreurs détectées:');
      results.errors.forEach(error => console.log(`   - ${error}`));
    }
  }
  
  return {
    success: overallSuccess,
    results
  };
}

// ============================================================================
// EXÉCUTION SI SCRIPT APPELÉ DIRECTEMENT
// ============================================================================

if (typeof require !== 'undefined' && require.main === module) {
  validateAllJsonData()
    .then(result => {
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('Erreur fatale:', error);
      process.exit(1);
    });
}
