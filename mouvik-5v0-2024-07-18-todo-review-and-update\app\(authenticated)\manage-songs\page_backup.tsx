import { cookies } from 'next/headers';
import { createServerClient } from '@/lib/supabase/server'; // Keep only one import
import { notFound } from 'next/navigation';
import Image from 'next/image';
import type { Song } from '@/types'; // Import the Song type
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ExternalLink, Music, Calendar, Clock, User, Info, ListMusic, GitBranch, MicVocal, Users, UserSquare, Guitar, Drum, Piano, Eye, Heart, PlayCircle, Share2, Pencil } from 'lucide-react'; // Add Share2 icon, Pencil
import SongViewClientWrapper from '@/components/songs/SongViewClientWrapper'; // Import the client wrapper
import EditSongButton from '@/components/songs/EditSongButton'; // Import the new EditSongButton

// Helper function to format dates (can be moved to utils)
const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (e) {
    return 'Date invalide';
  }
};

// Helper function to format duration (can be moved to utils)
const formatDuration = (seconds: number | null | undefined): string => {
    if (seconds === null || seconds === undefined) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

// Define the expected shape of the fetched song data including album title
// Ensure Song type includes all fields fetched, including arrays like writers, producers, featured_artists
interface SongWithAlbum extends Song { 
  albums: { title: string } | null; 
}

// Define the Page component as an async Server Component
export default async function AuthenticatedSongPage({ params }: { params: { id: string } }) {
  const cookieStore = cookies();
  // Correct call signature for createServerClient - needs URL, Key, and cookies
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          cookieStore.set({ name, value, ...options });
        },
        remove(name: string, options: any) {
          cookieStore.delete({ name, ...options });
        },
      },
    }
  );

  // Fetch song data including related album title, tags, and all new fields
  const { data: song, error: songError } = await supabase
    .from('songs')
    .select(`
      *,
      albums (title),
      genres,
      moods,
      tags,
      instrumentation,
      language,
      lyrics_language,
      recording_date,
      is_ai_generated,
      writers,
      producers,
      allow_downloads 
    `)
    .eq('id', params.id)
    .single();

  // Handle song not found
  if (songError || !song) {
    console.error("Error fetching song:", songError);
    notFound(); // Render the nearest not-found page
  }

  // Fetch related versions/remixes (songs that list this song as original OR that this song is a remix of)
  // Also include the original if this song IS a remix/version
  const relatedSongIds = [song.original_song_id, song.remix_of].filter(Boolean); // Get IDs this song points to
  
  let relatedSongsQuery = supabase
    .from('songs')
    .select('id, title, artist_name, cover_url, audio_url, is_remix, original_song_id, remix_of') // Select needed fields
    .or(`original_song_id.eq.${song.id},remix_of.eq.${song.id}${relatedSongIds.length > 0 ? `,id.in.(${relatedSongIds.join(',')})` : ''}`) // Find songs pointing to this OR songs this points to
    .neq('id', song.id); // Exclude the current song itself

  const { data: relatedSongs, error: relatedSongsError } = await relatedSongsQuery;

  if (relatedSongsError) {
    console.error("Error fetching related songs:", relatedSongsError);
    // Non-critical error, maybe just log it or show a message in the tab
  }

  // --- Fetch Stats ---
  // Fetch Likes Count
  const { count: likesCount, error: likesError } = await supabase
    .from('likes')
    .select('id', { count: 'exact', head: true })
    .eq('resource_type', 'song')
    .eq('resource_id', params.id);
  
  if (likesError) {
    console.error("Error fetching likes count:", likesError);
  }

  // Fetch Views Count (Assuming you have a 'views' table similar to 'likes')
  // If you don't have a views table, you might need to implement view tracking differently
  const { count: viewsCount, error: viewsError } = await supabase
    .from('views') // Replace 'views' with your actual table name if different
    .select('id', { count: 'exact', head: true })
    .eq('resource_type', 'song') // Ensure these match your table structure
    .eq('resource_id', params.id);

  if (viewsError) {
     console.error("Error fetching views count:", viewsError);
     // Handle error - maybe views table doesn't exist?
  }
  // Note: song.plays is already fetched with the main song data.

  // --- Fetch "Vous pourriez aussi aimer" songs (Example: 5 most recent by same artist, excluding current) ---
  const { data: suggestedSongs, error: suggestedSongsError } = await supabase
    .from('songs')
    .select('id, title, artist_name, cover_url')
    .eq('user_id', song.user_id) // Assuming user_id links to the artist profile
    .neq('id', song.id) // Exclude the current song
    .order('created_at', { ascending: false })
    .limit(5);

   if (suggestedSongsError) {
     console.error("Error fetching suggested songs:", suggestedSongsError);
   }
  // --- End Fetching ---

  return (
    <div className="px-4 py-8"> {/* Removed container and mx-auto for full width */}
      {/* New SoundCloud-Style Header */}
      <div className="relative mb-8 p-6 bg-gradient-to-r from-slate-900 to-slate-800 rounded-lg shadow-xl text-white md:mx-auto md:max-w-screen-xl lg:max-w-screen-2xl"> {/* Added relative positioning and responsive max-width */}
        {/* Edit Button - Top Right */}
        {song.user_id && (
          <EditSongButton 
            songId={song.id} 
            songUserId={song.user_id} 
            className="absolute top-4 right-4 text-slate-300 border-slate-600 hover:bg-slate-700 hover:text-white" 
          />
        )}
        <div className="flex flex-col md:flex-row gap-6 items-start">
          {/* Left: Cover Image */}
          <div className="w-full md:w-48 lg:w-64 aspect-square rounded-md overflow-hidden bg-slate-700 flex items-center justify-center relative border border-slate-600 shrink-0">
            {song.cover_url ? (
              <Image
                src={song.cover_url}
                alt={`Pochette de ${song.title}`}
                fill
                className="object-cover"
                priority
              />
            ) : (
              <Music className="h-24 w-24 text-slate-500" />
            )}
          </div>

          {/* Right: Song Info, Player, Actions, Stats */}
          <div className="flex-grow min-w-0"> {/* Added min-w-0 for flex child truncation */}
            {/* Pass song details, counts, and user ID for artist link to client wrapper */}
            <SongViewClientWrapper 
              song={song} 
              serverLikesCount={likesCount ?? 0}
              serverPlaysCount={song.plays ?? 0}
              serverViewsCount={viewsCount ?? 0}
            />
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Left Column (Now for Basic Info and other details if any) */}
        <div className="md:col-span-1 space-y-6">
          {/* Basic Info Card */}
          <Card>
             <CardHeader><CardTitle className="text-lg">Infos Rapides</CardTitle></CardHeader>
             <CardContent className="space-y-2 text-sm text-muted-foreground">
               <p><strong>BPM:</strong> {song.bpm ?? 'N/A'}</p>
               <p><strong>Tonalité:</strong> {song.key ?? 'N/A'}</p>
               <p><strong>Signature:</strong> {song.time_signature ?? 'N/A'}</p>
               <p><strong>Durée:</strong> {formatDuration(song.duration)}</p>
               <p><strong>Date de sortie:</strong> {formatDate(song.release_date)}</p>
             </CardContent>
           </Card>
         </div>

         {/* Right Column */}
         <div className="md:col-span-2 space-y-8"> 
           <Tabs defaultValue="infos" className="w-full">
             <TabsList className="grid w-full grid-cols-4 mb-4">
               <TabsTrigger value="infos">Infos</TabsTrigger>
               <TabsTrigger value="lyrics">Paroles & Accords</TabsTrigger>
               <TabsTrigger value="credits">Crédits</TabsTrigger>
               <TabsTrigger value="versions">Versions</TabsTrigger>
             </TabsList>

             <TabsContent value="infos" className="space-y-6">
               <Card>
                 <CardHeader><CardTitle>À propos de ce morceau</CardTitle></CardHeader>
                 <CardContent>
                   <p className="text-muted-foreground whitespace-pre-wrap">{song.description || "Aucune description fournie."}</p>
                 </CardContent>
               </Card>
              
               <Card>
                 <CardHeader><CardTitle className="text-lg">Détails du Morceau</CardTitle></CardHeader>
                 <CardContent className="space-y-3 text-sm">
                   <div>
                     <strong className="block mb-1">Genres:</strong>
                     {song.genres && song.genres.length > 0 ? (
                       <div className="flex flex-wrap gap-2">
                         {song.genres.map((genre: string, index: number) => (
                           <span key={index} className="px-2.5 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full">{genre}</span>
                         ))}
                       </div>
                     ) : <span className="text-muted-foreground">N/A</span>}
                   </div>
                   <div>
                     <strong className="block mb-1">Moods:</strong>
                     {song.moods && song.moods.length > 0 ? (
                       <div className="flex flex-wrap gap-2">
                         {song.moods.map((mood: string, index: number) => (
                           <span key={index} className="px-2.5 py-1 bg-secondary/20 text-secondary-foreground text-xs font-medium rounded-full">{mood}</span>
                         ))}
                       </div>
                     ) : <span className="text-muted-foreground">N/A</span>}
                   </div>
                   <div>
                     <strong className="block mb-1">Tags:</strong>
                     {song.tags && song.tags.length > 0 ? (
                       <div className="flex flex-wrap gap-2">
                         {song.tags.map((tag: string, index: number) => (
                           <span key={index} className="px-2.5 py-1 bg-muted text-muted-foreground text-xs font-medium rounded-full">{tag}</span>
                         ))}
                       </div>
                     ) : <span className="text-muted-foreground">N/A</span>}
                   </div>
                   <div>
                     <strong className="block mb-1">Instrumentation:</strong>
                     {song.instrumentation && song.instrumentation.length > 0 ? (
                       <div className="flex flex-wrap gap-2">
                         {song.instrumentation.map((instrument: string, index: number) => (
                           <span key={index} className="px-2.5 py-1 bg-accent/20 text-accent-foreground text-xs font-medium rounded-full">{instrument}</span>
                         ))}
                       </div>
                     ) : <span className="text-muted-foreground">N/A</span>}
                   </div>
                   <p><strong>Capo:</strong> {song.capo ?? 'Aucun'}</p>
                   <p><strong>Accordage (Hz):</strong> {song.tuning_frequency ?? 'N/A'}</p>
                   <p><strong>Langue du morceau:</strong> {song.language || 'N/A'}</p>
                   <p><strong>Langue des paroles:</strong> {song.lyrics_language || 'N/A'}</p>
                 </CardContent>
               </Card>

               <Card>
                 <CardHeader><CardTitle className="text-lg">Informations de Production & Publication</CardTitle></CardHeader>
                 <CardContent className="space-y-2 text-sm">
                   <p><strong>Date d'enregistrement:</strong> {formatDate(song.recording_date)}</p> {/* Corrected label */}
                   <p><strong>Lieu d'enregistrement:</strong> {song.recording_location || 'N/A'}</p>
                   <p><strong>Album:</strong> {song.albums?.title || 'Aucun'}</p>
                   <p><strong>ISRC:</strong> {song.isrc || 'N/A'}</p>
                   {/* Add Record Label, Distributor, UPC if/when available in DB and fetched */}
                   <p><strong>Généré par IA:</strong> {song.is_ai_generated ? 'Oui' : 'Non'}</p>
                   <p><strong>Contenu Explicite:</strong> {song.is_explicit ? 'Oui' : 'Non'}</p>
                 </CardContent>
               </Card>

               {song.bloc_note && (
                <Card>
                  <CardHeader><CardTitle>Notes de Production</CardTitle></CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground whitespace-pre-wrap">{song.bloc_note}</p>
                  </CardContent>
                </Card>
               )}
               {/* right_column_notepad is likely for internal use, not public display. Omitting for now. */}
             </TabsContent>

             {/* Lyrics Tab */}
             <TabsContent value="lyrics">
               <Card>
                 <CardHeader><CardTitle>Paroles & Accords</CardTitle></CardHeader>
                 <CardContent>
                   <h3 className="font-semibold mb-2">Paroles</h3>
                   <pre className="whitespace-pre-wrap font-sans text-sm bg-muted p-4 rounded">{song.lyrics || "Paroles non disponibles."}</pre>
                   <h3 className="font-semibold mt-4 mb-2">Accords</h3>
                   <pre className="whitespace-pre-wrap font-mono text-sm bg-muted p-4 rounded">{song.chords || "Accords non disponibles."}</pre>
                 </CardContent>
               </Card>
             </TabsContent>

             {/* Credits Tab */}
              <TabsContent value="credits">
               <Card>
                 <CardHeader><CardTitle>Crédits</CardTitle></CardHeader>
                 <CardContent className="space-y-4">
                   <div>
                     <h3 className="font-semibold mb-2 flex items-center"><User className="mr-2 h-4 w-4"/>Artiste Principal</h3>
                     {/* Make artist name clickable */}
                     {song.user_id && song.artist_name ? (
                       <Link href={`/profile/${song.user_id}`} className="text-muted-foreground hover:underline">
                         {song.artist_name}
                       </Link>
                     ) : (
                       <p className="text-muted-foreground">{song.artist_name || 'N/A'}</p>
                     )}
                   </div>
                   <div>
                      <h3 className="font-semibold mb-2 flex items-center"><MicVocal className="mr-2 h-4 w-4"/>Artistes Invités</h3>
                     {song.featured_artists && song.featured_artists.length > 0 ? (
                       <ul className="list-disc list-inside text-muted-foreground">
                         {/* Add explicit types */}
                         {song.featured_artists.map((artist: string, index: number) => <li key={index}>{artist}</li>)}
                       </ul>
                     ) : <p className="text-muted-foreground">Aucun</p>}
                   </div>
                    <div>
                     <h3 className="font-semibold mb-2 flex items-center"><UserSquare className="mr-2 h-4 w-4"/>Compositeur</h3>
                     {/* Composer name - not linking for now as we don't have a composer ID */}
                     <p className="text-muted-foreground">{song.composer_name || 'N/A'}</p>
                   </div>
                   <div>
                     <h3 className="font-semibold mb-2 flex items-center"><Users className="mr-2 h-4 w-4"/>Auteurs (Writers)</h3>
                     {song.writers && song.writers.length > 0 ? (
                       <ul className="list-disc list-inside text-muted-foreground">
                         {song.writers.map((writer: string, index: number) => <li key={index}>{writer}</li>)}
                       </ul>
                     ) : <p className="text-muted-foreground">N/A</p>}
                   </div>
                   <div>
                      <h3 className="font-semibold mb-2 flex items-center"><Users className="mr-2 h-4 w-4"/>Producteurs</h3>
                     {song.producers && song.producers.length > 0 ? (
                       <ul className="list-disc list-inside text-muted-foreground">
                         {song.producers.map((producer: string, index: number) => <li key={index}>{producer}</li>)}
                       </ul>
                     ) : <p className="text-muted-foreground">N/A</p>}
                   </div>
                 </CardContent>
               </Card>
             </TabsContent>

             {/* Versions Tab */}
              <TabsContent value="versions">
               <Card>
                 <CardHeader><CardTitle>Versions & Remixes</CardTitle></CardHeader>
                 <CardContent className="space-y-4">
                   {/* Display fetched versions/remixes */}
                   {relatedSongs && relatedSongs.length > 0 ? (
                     relatedSongs.map((relatedSong) => (
                       <div key={relatedSong.id} className="flex items-center gap-4 p-2 border rounded hover:bg-muted/50">
                         <div className="w-12 h-12 bg-muted rounded flex items-center justify-center relative overflow-hidden">
                           {relatedSong.cover_url ? (
                             <Image src={relatedSong.cover_url} alt={relatedSong.title ?? ''} fill className="object-cover"/>
                           ) : <Music className="h-6 w-6 text-muted-foreground"/>}
                         </div>
                         <div className="flex-1">
                           <Link href={`/songs/${relatedSong.id}`} className="font-medium hover:underline">{relatedSong.title}</Link>
                           <p className="text-sm text-muted-foreground">{relatedSong.artist_name}</p>
                           {/* Indicate relationship */}
                           <p className="text-xs text-muted-foreground italic">
                             {relatedSong.original_song_id === song.id ? 'Version de ce morceau' : ''}
                             {relatedSong.remix_of === song.id ? 'Remix de ce morceau' : ''}
                             {song.original_song_id === relatedSong.id ? 'Version originale' : ''}
                             {song.remix_of === relatedSong.id ? 'Remixé par ce morceau' : ''}
                           </p>
                         </div>
                         {/* Optional: Add a play button for related songs */}
                         {/* <PlayButton song={relatedSong} size="sm" /> */}
                       </div>
                     ))
                   ) : (
                     <p className="text-muted-foreground">Aucune version ou remix lié trouvé.</p>
                   )}
                   <hr/> {/* Separator */}
                   {song.instrumental_version_url && <p><Link href={song.instrumental_version_url} className="text-blue-500 hover:underline">Lien Version Instrumentale (Externe)</Link></p>}
                   {song.acapella_version_url && <p><Link href={song.acapella_version_url} className="text-blue-500 hover:underline">Lien Version Acapella (Externe)</Link></p>}
                   <p><strong>Stems Disponibles:</strong> {song.stems_available ? 'Oui' : 'Non'}</p>
                   <p><strong>Est un Remix:</strong> {song.is_remix ? 'Oui' : 'Non'}</p>
                   {song.remix_of && <p><strong>Remix de (ID):</strong> {song.remix_of}</p>}
                   {song.original_song_id && <p><strong>Version de (ID):</strong> {song.original_song_id}</p>}
                 </CardContent>
               </Card>
             </TabsContent>
           </Tabs>

           {/* Comments Section Placeholder */}
           <Card>
             <CardHeader><CardTitle>Commentaires</CardTitle></CardHeader>
             <CardContent>
               <p className="text-muted-foreground">Section commentaires à implémenter.</p>
               {/* Example: <CommentSection resourceId={song.id} resourceType="song" /> */}
             </CardContent>
           </Card>

         </div>
       </div>

       {/* "Vous pourriez aussi aimer" Section */}
       <div className="mt-12">
         <h2 className="text-2xl font-semibold mb-4">Vous pourriez aussi aimer</h2>
         {suggestedSongs && suggestedSongs.length > 0 ? (
           <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
             {suggestedSongs.map((suggestedSong) => (
               <Link key={suggestedSong.id} href={`/songs/${suggestedSong.id}`}>
                 <Card className="overflow-hidden hover:shadow-lg transition-shadow">
                   <div className="aspect-square relative bg-muted">
                     {suggestedSong.cover_url ? (
                       <Image src={suggestedSong.cover_url} alt={suggestedSong.title ?? ''} fill className="object-cover"/>
                     ) : <Music className="h-1/2 w-1/2 text-muted-foreground absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"/>}
                   </div>
                   <CardHeader className="p-3">
                     <CardTitle className="text-base font-medium truncate">{suggestedSong.title}</CardTitle>
                     <CardDescription className="text-xs truncate">{suggestedSong.artist_name}</CardDescription>
                   </CardHeader>
                 </Card>
               </Link>
             ))}
           </div>
         ) : (
           <p className="text-muted-foreground">Aucune suggestion pour le moment.</p>
         )}
       </div>
    </div>
  );
}