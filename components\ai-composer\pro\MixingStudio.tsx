'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Waveform } from 'lucide-react';

interface MixingStudioProps {
  activeTab: string;
  songSections: any[];
  selectedSection: string;
  styleConfig: any;
  setStyleConfig: (config: any) => void;
}

export const MixingStudio: React.FC<MixingStudioProps> = ({
  activeTab,
  songSections,
  selectedSection,
  styleConfig,
  setStyleConfig
}) => {
  
  return (
    <div className="h-full p-6 flex items-center justify-center">
      <div className="text-center">
        <Waveform className="h-16 w-16 text-slate-400 mx-auto mb-4" />
        <h3 className="text-xl font-medium text-white mb-2">Studio de Mixage</h3>
        <p className="text-slate-400 mb-4">Fonctionnalité en développement</p>
        <Card className="bg-slate-700/50 border-slate-600 max-w-md">
          <CardContent className="p-4">
            <div className="text-sm text-slate-300">
              <p className="mb-2">Prochainement disponible :</p>
              <ul className="text-left space-y-1 text-slate-400">
                <li>• Console de mixage virtuelle</li>
                <li>• Égaliseur multi-bandes</li>
                <li>• Effets et processeurs</li>
                <li>• Automation et balance</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
