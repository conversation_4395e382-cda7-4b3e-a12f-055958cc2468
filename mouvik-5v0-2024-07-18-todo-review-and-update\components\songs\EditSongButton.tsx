"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from "@/components/ui/button";
import { createBrowserClient } from '@/lib/supabase/client';
import { Pencil } from 'lucide-react'; // Using Pencil icon for edit

interface EditSongButtonProps {
  songUserId: string; // The user_id of the song's owner
  songId: string;     // The ID of the song
  className?: string;
}

export default function EditSongButton({ songUserId, songId, className }: EditSongButtonProps) {
  const supabase = createBrowserClient();
  const [currentUserId, setCurrentUserId] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCurrentUserId = async () => {
      setIsLoading(true);
      const { data: sessionData, error } = await supabase.auth.getSession();
      if (error) {
        console.error("Error fetching session for edit button:", error);
      }
      setCurrentUserId(sessionData?.session?.user?.id);
      setIsLoading(false);
    };
    fetchCurrentUserId();
  }, [supabase]);

  if (isLoading) {
    return (
      <Button variant="outline" size="sm" className={className} disabled>
        <Pencil className="mr-2 h-4 w-4" /> Modifier...
      </Button>
    );
  }

  if (currentUserId === songUserId) {
    return (
      <Link href={`/manage-songs/${songId}/edit`} passHref>
        <Button variant="outline" size="sm" className={className}>
          <Pencil className="mr-2 h-4 w-4" /> Modifier le morceau
        </Button>
      </Link>
    );
  }

  return null; // Don't render the button if the user is not the owner
}
