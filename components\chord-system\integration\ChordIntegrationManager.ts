/**
 * 🎼 CHORD INTEGRATION MANAGER - Gestionnaire d'Intégration AI Composer
 * 
 * Gestionnaire central pour l'intégration du système d'accords
 * dans l'écosystème AI Composer existant
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

import type { 
  UnifiedChordPosition, 
  ChordProgression,
  ChordGridSection 
} from '../types/chord-system';

// ============================================================================
// TYPES POUR L'INTÉGRATION
// ============================================================================

/** Modes d'intégration disponibles */
export type IntegrationMode = 
  | 'standalone'           // Module indépendant
  | 'text-editor'         // Intégration éditeur de texte
  | 'song-structure'      // Intégration structure de morceau
  | 'timeline'            // Intégration timeline
  | 'ai-suggestions';     // Suggestions IA

/** Formats d'export pour l'éditeur de texte */
export type TextExportFormat = 
  | 'simple'              // "Am - F - C - G"
  | 'detailed'            // "Am (guitare, débutant) - F (guitare, intermédiaire)"
  | 'chord-pro'           // Format ChordPro
  | 'markdown'            // Format Markdown
  | 'json';               // Format JSON

/** Types de sections pour la structure de morceau */
export type SongSectionType = 
  | 'intro' | 'verse' | 'chorus' | 'bridge' 
  | 'solo' | 'outro' | 'pre-chorus' | 'breakdown';

/** Configuration d'intégration */
export interface IntegrationConfig {
  mode: IntegrationMode;
  textEditor?: {
    insertPosition?: number;
    format?: TextExportFormat;
    wrapInTags?: boolean;
    includeMetadata?: boolean;
  };
  songStructure?: {
    sectionType?: SongSectionType;
    autoCreateSection?: boolean;
    mergeWithExisting?: boolean;
  };
  timeline?: {
    startTime?: number;
    duration?: number;
    trackId?: string;
    syncTempo?: boolean;
  };
  aiSuggestions?: {
    contextChords?: UnifiedChordPosition[];
    targetKey?: string;
    suggestionCount?: number;
  };
}

/** Callbacks pour l'intégration */
export interface IntegrationCallbacks {
  // Éditeur de texte
  onInsertToTextEditor?: (text: string, position?: number) => void;
  onReplaceTextSelection?: (text: string) => void;
  onGetTextEditorContent?: () => string;
  onGetTextEditorPosition?: () => number;
  
  // Structure de morceau
  onAddToSongStructure?: (section: ChordGridSection, sectionType: SongSectionType) => void;
  onUpdateSongSection?: (sectionId: string, updates: Partial<ChordGridSection>) => void;
  onGetSongStructure?: () => ChordGridSection[];
  onGetActiveSongSection?: () => string | null;
  
  // Timeline
  onSyncWithTimeline?: (progression: ChordProgression, config: IntegrationConfig['timeline']) => void;
  onGetTimelinePosition?: () => number;
  onSetTimelineMarkers?: (markers: { time: number; chord: UnifiedChordPosition }[]) => void;
  
  // Suggestions IA
  onRequestAISuggestions?: (context: IntegrationConfig['aiSuggestions']) => Promise<UnifiedChordPosition[]>;
  onApplyAISuggestion?: (chord: UnifiedChordPosition) => void;
}

// ============================================================================
// CLASSE PRINCIPALE
// ============================================================================

export class ChordIntegrationManager {
  private config: IntegrationConfig;
  private callbacks: IntegrationCallbacks;
  private isInitialized = false;

  constructor(config: IntegrationConfig, callbacks: IntegrationCallbacks) {
    this.config = config;
    this.callbacks = callbacks;
  }

  // ============================================================================
  // MÉTHODES D'INITIALISATION
  // ============================================================================

  /**
   * Initialise le gestionnaire d'intégration
   */
  initialize(): void {
    if (this.isInitialized) {
      console.warn('ChordIntegrationManager déjà initialisé');
      return;
    }

    this.setupEventListeners();
    this.validateConfiguration();
    this.isInitialized = true;
    
    console.log(`ChordIntegrationManager initialisé en mode: ${this.config.mode}`);
  }

  /**
   * Met à jour la configuration
   */
  updateConfig(newConfig: Partial<IntegrationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.validateConfiguration();
  }

  /**
   * Met à jour les callbacks
   */
  updateCallbacks(newCallbacks: Partial<IntegrationCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...newCallbacks };
  }

  // ============================================================================
  // INTÉGRATION ÉDITEUR DE TEXTE
  // ============================================================================

  /**
   * Insère un accord dans l'éditeur de texte
   */
  insertChordToTextEditor(chord: UnifiedChordPosition): void {
    if (!this.callbacks.onInsertToTextEditor) {
      console.warn('Callback onInsertToTextEditor non défini');
      return;
    }

    const format = this.config.textEditor?.format || 'simple';
    const text = this.formatChordForText(chord, format);
    const position = this.config.textEditor?.insertPosition;

    this.callbacks.onInsertToTextEditor(text, position);
  }

  /**
   * Insère une progression dans l'éditeur de texte
   */
  insertProgressionToTextEditor(progression: ChordProgression): void {
    if (!this.callbacks.onInsertToTextEditor) {
      console.warn('Callback onInsertToTextEditor non défini');
      return;
    }

    const format = this.config.textEditor?.format || 'simple';
    const text = this.formatProgressionForText(progression, format);
    const position = this.config.textEditor?.insertPosition;

    this.callbacks.onInsertToTextEditor(text, position);
  }

  /**
   * Remplace la sélection actuelle dans l'éditeur
   */
  replaceTextSelection(progression: ChordProgression): void {
    if (!this.callbacks.onReplaceTextSelection) {
      console.warn('Callback onReplaceTextSelection non défini');
      return;
    }

    const format = this.config.textEditor?.format || 'simple';
    const text = this.formatProgressionForText(progression, format);

    this.callbacks.onReplaceTextSelection(text);
  }

  // ============================================================================
  // INTÉGRATION STRUCTURE DE MORCEAU
  // ============================================================================

  /**
   * Ajoute une progression à la structure de morceau
   */
  addProgressionToSongStructure(
    progression: ChordProgression, 
    sectionType?: SongSectionType
  ): void {
    if (!this.callbacks.onAddToSongStructure) {
      console.warn('Callback onAddToSongStructure non défini');
      return;
    }

    const targetSectionType = sectionType || this.config.songStructure?.sectionType || 'verse';
    const section = this.convertProgressionToGridSection(progression, targetSectionType);

    this.callbacks.onAddToSongStructure(section, targetSectionType);
  }

  /**
   * Met à jour une section existante
   */
  updateSongSection(sectionId: string, progression: ChordProgression): void {
    if (!this.callbacks.onUpdateSongSection) {
      console.warn('Callback onUpdateSongSection non défini');
      return;
    }

    const updates = {
      measures: this.convertProgressionToMeasures(progression),
      updatedAt: new Date().toISOString()
    };

    this.callbacks.onUpdateSongSection(sectionId, updates);
  }

  // ============================================================================
  // INTÉGRATION TIMELINE
  // ============================================================================

  /**
   * Synchronise une progression avec la timeline
   */
  syncProgressionWithTimeline(progression: ChordProgression): void {
    if (!this.callbacks.onSyncWithTimeline) {
      console.warn('Callback onSyncWithTimeline non défini');
      return;
    }

    const timelineConfig = this.config.timeline || {};
    this.callbacks.onSyncWithTimeline(progression, timelineConfig);

    // Créer des marqueurs pour chaque accord
    if (this.callbacks.onSetTimelineMarkers) {
      const markers = this.createTimelineMarkers(progression);
      this.callbacks.onSetTimelineMarkers(markers);
    }
  }

  /**
   * Crée des marqueurs timeline pour une progression
   */
  private createTimelineMarkers(progression: ChordProgression): { time: number; chord: UnifiedChordPosition }[] {
    const startTime = this.config.timeline?.startTime || 0;
    const chordDuration = 60 / progression.tempo; // Durée d'un accord en secondes
    
    return progression.chords.map((chord, index) => ({
      time: startTime + (index * chordDuration),
      chord
    }));
  }

  // ============================================================================
  // SUGGESTIONS IA
  // ============================================================================

  /**
   * Demande des suggestions IA pour compléter une progression
   */
  async requestAISuggestions(
    contextChords: UnifiedChordPosition[],
    targetKey?: string
  ): Promise<UnifiedChordPosition[]> {
    if (!this.callbacks.onRequestAISuggestions) {
      console.warn('Callback onRequestAISuggestions non défini');
      return [];
    }

    const suggestionConfig = {
      contextChords,
      targetKey: targetKey || this.config.aiSuggestions?.targetKey,
      suggestionCount: this.config.aiSuggestions?.suggestionCount || 5
    };

    try {
      return await this.callbacks.onRequestAISuggestions(suggestionConfig);
    } catch (error) {
      console.error('Erreur lors de la demande de suggestions IA:', error);
      return [];
    }
  }

  /**
   * Applique une suggestion IA
   */
  applyAISuggestion(chord: UnifiedChordPosition): void {
    if (!this.callbacks.onApplyAISuggestion) {
      console.warn('Callback onApplyAISuggestion non défini');
      return;
    }

    this.callbacks.onApplyAISuggestion(chord);
  }

  // ============================================================================
  // FORMATAGE ET CONVERSION
  // ============================================================================

  /**
   * Formate un accord pour l'éditeur de texte
   */
  private formatChordForText(chord: UnifiedChordPosition, format: TextExportFormat): string {
    switch (format) {
      case 'simple':
        return chord.chord;
        
      case 'detailed':
        return `${chord.chord} (${chord.instrument}, ${chord.difficulty})`;
        
      case 'chord-pro':
        return `[${chord.chord}]`;
        
      case 'markdown':
        return `**${chord.chord}**`;
        
      case 'json':
        return JSON.stringify(chord, null, 2);
        
      default:
        return chord.chord;
    }
  }

  /**
   * Formate une progression pour l'éditeur de texte
   */
  private formatProgressionForText(progression: ChordProgression, format: TextExportFormat): string {
    const chords = progression.chords;
    
    switch (format) {
      case 'simple':
        return chords.map(c => c.chord).join(' - ');
        
      case 'detailed':
        return chords.map(c => `${c.chord} (${c.instrument}, ${c.difficulty})`).join(' - ');
        
      case 'chord-pro':
        return chords.map(c => `[${c.chord}]`).join(' ');
        
      case 'markdown':
        return `### ${progression.name}\n\n` +
               `**Tonalité:** ${progression.key} | **Tempo:** ${progression.tempo} BPM\n\n` +
               chords.map(c => `**${c.chord}**`).join(' - ');
        
      case 'json':
        return JSON.stringify(progression, null, 2);
        
      default:
        return chords.map(c => c.chord).join(' - ');
    }
  }

  /**
   * Convertit une progression en section de grille
   */
  private convertProgressionToGridSection(
    progression: ChordProgression, 
    sectionType: SongSectionType
  ): ChordGridSection {
    return {
      id: crypto.randomUUID(),
      name: `${sectionType.charAt(0).toUpperCase() + sectionType.slice(1)} - ${progression.name}`,
      measures: this.convertProgressionToMeasures(progression),
      timeSignature: progression.timeSignature,
      key: progression.key,
      tempo: progression.tempo,
      order: 0 // Sera défini par le système parent
    };
  }

  /**
   * Convertit une progression en mesures
   */
  private convertProgressionToMeasures(progression: ChordProgression): any[] {
    // Simplification : un accord par mesure
    return progression.chords.map((chord, index) => ({
      id: crypto.randomUUID(),
      number: index + 1,
      chords: [{
        chord,
        beat: 1,
        duration: 4, // Toute la mesure
        emphasis: 'strong'
      }],
      beats: 4
    }));
  }

  // ============================================================================
  // UTILITAIRES
  // ============================================================================

  /**
   * Valide la configuration
   */
  private validateConfiguration(): void {
    if (!this.config.mode) {
      throw new Error('Mode d\'intégration requis');
    }

    // Validation spécifique par mode
    switch (this.config.mode) {
      case 'text-editor':
        if (!this.callbacks.onInsertToTextEditor) {
          console.warn('Mode text-editor sans callback onInsertToTextEditor');
        }
        break;
        
      case 'song-structure':
        if (!this.callbacks.onAddToSongStructure) {
          console.warn('Mode song-structure sans callback onAddToSongStructure');
        }
        break;
        
      case 'timeline':
        if (!this.callbacks.onSyncWithTimeline) {
          console.warn('Mode timeline sans callback onSyncWithTimeline');
        }
        break;
        
      case 'ai-suggestions':
        if (!this.callbacks.onRequestAISuggestions) {
          console.warn('Mode ai-suggestions sans callback onRequestAISuggestions');
        }
        break;
    }
  }

  /**
   * Configure les écouteurs d'événements
   */
  private setupEventListeners(): void {
    // Écouter les raccourcis clavier pour l'intégration
    if (typeof window !== 'undefined') {
      window.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    }
  }

  /**
   * Gère les raccourcis clavier
   */
  private handleKeyboardShortcuts(event: KeyboardEvent): void {
    // Ctrl/Cmd + Shift + C : Ouvrir le sélecteur d'accords
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
      event.preventDefault();
      // Émettre un événement personnalisé pour ouvrir le sélecteur
      window.dispatchEvent(new CustomEvent('chord-system:open-picker'));
    }
    
    // Ctrl/Cmd + Shift + P : Ouvrir le constructeur de progression
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'P') {
      event.preventDefault();
      window.dispatchEvent(new CustomEvent('chord-system:open-progression-builder'));
    }
  }

  /**
   * Nettoie les ressources
   */
  dispose(): void {
    if (typeof window !== 'undefined') {
      window.removeEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    }
    
    this.isInitialized = false;
  }
}

// ============================================================================
// FONCTIONS UTILITAIRES EXPORTÉES
// ============================================================================

/**
 * Crée une instance du gestionnaire d'intégration
 */
export function createIntegrationManager(
  config: IntegrationConfig,
  callbacks: IntegrationCallbacks
): ChordIntegrationManager {
  const manager = new ChordIntegrationManager(config, callbacks);
  manager.initialize();
  return manager;
}

/**
 * Détecte le mode d'intégration optimal selon le contexte
 */
export function detectOptimalIntegrationMode(): IntegrationMode {
  // Logique de détection basée sur l'URL, les éléments DOM, etc.
  if (typeof window === 'undefined') return 'standalone';
  
  const path = window.location.pathname;
  
  if (path.includes('/editor') || path.includes('/text')) {
    return 'text-editor';
  }
  
  if (path.includes('/structure') || path.includes('/song')) {
    return 'song-structure';
  }
  
  if (path.includes('/timeline') || path.includes('/arrange')) {
    return 'timeline';
  }
  
  return 'standalone';
}

/**
 * Crée une configuration d'intégration par défaut
 */
export function createDefaultIntegrationConfig(mode: IntegrationMode): IntegrationConfig {
  const baseConfig: IntegrationConfig = { mode };
  
  switch (mode) {
    case 'text-editor':
      baseConfig.textEditor = {
        format: 'simple',
        wrapInTags: false,
        includeMetadata: false
      };
      break;
      
    case 'song-structure':
      baseConfig.songStructure = {
        sectionType: 'verse',
        autoCreateSection: true,
        mergeWithExisting: false
      };
      break;
      
    case 'timeline':
      baseConfig.timeline = {
        startTime: 0,
        syncTempo: true
      };
      break;
      
    case 'ai-suggestions':
      baseConfig.aiSuggestions = {
        suggestionCount: 5
      };
      break;
  }
  
  return baseConfig;
}
