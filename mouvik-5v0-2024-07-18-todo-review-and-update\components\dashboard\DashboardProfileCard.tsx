"use client"

import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, Headphones, Eye, UserCircle } from "lucide-react"

interface UserProfile {
  username: string | null;
  bio: string | null;
  website?: string | null; // Changed from website_url, make it optional if not always present
  avatar_url: string | null;
}

interface DashboardProfileCardProps {
  userProfile: UserProfile | null; // UserProfile here will use the local definition
  totalFollowers: number;
  totalPlays: number;
  totalViews: number;
}

function truncateText(text: string | null | undefined, maxLength: number): string {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
}

export function DashboardProfileCard({
  userProfile,
  totalFollowers,
  totalPlays,
  totalViews,
}: DashboardProfileCardProps) {
  if (!userProfile) {
    return (
      <Card className="w-full md:w-1/3"> {/* Ajustez la largeur au besoin */}
        <CardHeader>
          <CardTitle>Profil Utilisateur</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Impossible de charger les informations du profil.</p>
        </CardContent>
      </Card>
    )
  }

  const usernameDisplay = userProfile.username || "Artiste Anonyme";
  const avatarFallback = usernameDisplay.charAt(0).toUpperCase();

  return (
    <Card className="w-full"> {/* La carte prendra la largeur de son conteneur parent */}
      <CardHeader className="flex flex-row items-center gap-4 pb-4">
        <Avatar className="h-16 w-16">
          <AvatarImage src={userProfile.avatar_url || undefined} alt={usernameDisplay} />
          <AvatarFallback>{avatarFallback}</AvatarFallback>
        </Avatar>
        <div>
          <CardTitle className="text-2xl">{usernameDisplay}</CardTitle>
          {userProfile.bio && (
            <p className="text-sm text-muted-foreground mt-1">
              {truncateText(userProfile.bio, 100)}
            </p>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-2 text-center mb-4">
          <div>
            <p className="text-xs text-muted-foreground">Abonnés</p>
            <p className="text-lg font-bold flex items-center justify-center">
              <Users className="h-4 w-4 mr-1" />
              {totalFollowers.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Écoutes</p>
            <p className="text-lg font-bold flex items-center justify-center">
              <Headphones className="h-4 w-4 mr-1" />
              {totalPlays.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">Vues</p>
            <p className="text-lg font-bold flex items-center justify-center">
              <Eye className="h-4 w-4 mr-1" />
              {totalViews.toLocaleString()}
            </p>
          </div>
        </div>
        <Button variant="outline" className="w-full" asChild>
          <Link href={`/artists/${userProfile.username}`}>
            <UserCircle className="h-4 w-4 mr-2" />
            Voir le Profil Public
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
}
