export const genres = [
  { value: "pop", label: "Pop" },
  { value: "rock", label: "Rock" },
  { value: "hip-hop", label: "Hip-Hop" },
  { value: "rap", label: "Rap" },
  { value: "r-n-b", label: "R&B" },
  { value: "jazz", label: "Jazz" },
  { value: "blues", label: "Blues" },
  { value: "electronic", label: "Électronique" },
  { value: "dance", label: "Dance" },
  { value: "edm", label: "EDM" },
  { value: "house", label: "House" },
  { value: "techno", label: "Techno" },
  { value: "trance", label: "Trance" },
  { value: "dubstep", label: "Dubstep" },
  { value: "drum-and-bass", label: "Drum & Bass" },
  { value: "ambient", label: "Ambient" },
  { value: "classical", label: "Classique" },
  { value: "folk", label: "Folk" },
  { value: "country", label: "Country" },
  { value: "reggae", label: "Reggae" },
  { value: "reggaeton", label: "Reggaeton" },
  { value: "metal", label: "Metal" },
  { value: "punk", label: "Punk" },
  { value: "funk", label: "Funk" },
  { value: "soul", label: "Soul" },
  { value: "disco", label: "Disco" },
  { value: "experimental", label: "Expérimental" },
  { value: "indie", label: "Indie" },
  { value: "trap", label: "Trap" },
  { value: "lo-fi", label: "Lo-Fi" },
  { value: "synthwave", label: "Synthwave" },
  { value: "vaporwave", label: "Vaporwave" },
  { value: "chillwave", label: "Chillwave" },
  { value: "afrobeat", label: "Afrobeat" },
  { value: "latin", label: "Latin" },
  { value: "salsa", label: "Salsa" },
  { value: "flamenco", label: "Flamenco" },
  { value: "bossa-nova", label: "Bossa Nova" },
  { value: "jazz-fusion", label: "Jazz Fusion" },
  { value: "gospel", label: "Gospel" },
  { value: "opera", label: "Opéra" },
  { value: "soundtrack", label: "Bande Originale" },
  { value: "instrumental", label: "Instrumental" },
  { value: "acapella", label: "A Cappella" },
  { value: "world", label: "World" },
  { value: "new-age", label: "New Age" },
  { value: "chanson", label: "Chanson Française" },
  { value: "other", label: "Autre" },
]
