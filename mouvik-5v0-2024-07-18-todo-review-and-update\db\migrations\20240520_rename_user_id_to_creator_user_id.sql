-- Migration pour renommer la colonne user_id en creator_user_id dans la table songs
-- C<PERSON><PERSON> le: 2024-05-20
-- Auteur: <PERSON><PERSON><PERSON>

-- Début de la transaction
BEGIN;

-- 1. Vérifier si la colonne user_id existe toujours
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'songs' AND column_name = 'user_id') THEN
        -- 2. Renommer la colonne user_id en creator_user_id
        ALTER TABLE public.songs RENAME COLUMN user_id TO creator_user_id;
        
        -- 3. Mettre à jour les contraintes de clé étrangère si nécessaire
        -- (généralement, le renommage de la colonne préserve la contrainte)
        
        -- 4. Mettre à jour les index si nécessaire
        -- (les index sur user_id seront automatiquement mis à jour pour référencer creator_user_id)
        
        RAISE NOTICE 'La colonne user_id a été renommée en creator_user_id dans la table songs';
    ELSE
        RAISE NOTICE 'La colonne user_id n''existe plus dans la table songs';
    END IF;
    
    -- Vérifier si la colonne creator_user_id existe déjà
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'songs' AND column_name = 'creator_user_id') THEN
        -- Créer la colonne creator_user_id si elle n'existe pas
        ALTER TABLE public.songs ADD COLUMN creator_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
        
        -- Copier les données de user_id vers creator_user_id si elles n'ont pas encore été copiées
        -- (cette étape ne devrait pas être nécessaire si la colonne a été renommée ci-dessus)
        UPDATE public.songs SET creator_user_id = user_id WHERE creator_user_id IS NULL;
        
        -- Rendre la colonne NOT NULL une fois les données copiées
        ALTER TABLE public.songs ALTER COLUMN creator_user_id SET NOT NULL;
        
        RAISE NOTICE 'La colonne creator_user_id a été ajoutée à la table songs';
    END IF;
END
$$;

-- Fin de la transaction
COMMIT;

-- Instructions post-migration:
-- 1. Vérifier que la colonne a bien été renommée avec: \d+ songs
-- 2. Tester les fonctionnalités qui utilisent cette colonne
-- 3. Vérifier que les contraintes de clé étrangère sont toujours en place
