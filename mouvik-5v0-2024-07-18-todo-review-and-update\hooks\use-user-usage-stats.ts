"use client";

import { useState, useEffect, useCallback } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';
import { useUser } from '@/contexts/user-context';

export interface UserUsageStats {
  monthlyUploads: number;
  iaCredits: number | null; // Can be null if not set on profile
  coinsBalance: number | null; // Can be null if not set on profile
  vaultUsageGB: number;
}

export function useUserUsageStats(): { stats: UserUsageStats | null; isLoading: boolean; refreshUsageStats: () => void } {
  const { user } = useUser();
  const supabase = getSupabaseClient();
  const [stats, setStats] = useState<UserUsageStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchUsageStats = useCallback(async () => {
    if (!user?.id) {
      setIsLoading(false);
      setStats(null);
      return;
    }

    setIsLoading(true);
    try {
      const currentMonthYear = new Date().toISOString().slice(0, 7); // YYYY-MM

      // Fetch monthly uploads
      const { data: uploadData, error: uploadError } = await supabase
        .from('user_monthly_uploads')
        .select('upload_count')
        .eq('user_id', user.id)
        .eq('month_year', currentMonthYear)
        .single();

      if (uploadError && uploadError.code !== 'PGRST116') { // PGRST116: no rows found, which is fine
        console.error("Error fetching monthly uploads:", uploadError);
      }

      // Fetch vault usage via RPC
      const { data: vaultData, error: vaultError } = await supabase
        .rpc('rpc_get_my_vault_usage');
      
      if (vaultError) {
        console.error("Error fetching vault usage:", vaultError);
      }

      setStats({
        monthlyUploads: uploadData?.upload_count || 0,
        iaCredits: user.ia_credits || 0, // From UserContext, which gets it from profiles
        coinsBalance: user.coins_balance || 0, // From UserContext
        vaultUsageGB: vaultData || 0,
      });

    } catch (error) {
      console.error("Error in fetchUsageStats:", error);
      setStats(null); // Or set to default error state
    } finally {
      setIsLoading(false);
    }
  }, [user, supabase]);

  useEffect(() => {
    fetchUsageStats();
  }, [fetchUsageStats]);

  return { stats, isLoading, refreshUsageStats: fetchUsageStats };
}
