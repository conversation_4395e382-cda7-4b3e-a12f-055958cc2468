/**
 * 🎼 CHORD SAVE MANAGER - Gestionnaire de Sauvegarde
 * 
 * Gestionnaire pour la persistance des progressions et grilles
 * Intégration Supabase avec cache local et synchronisation
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Save, Download, Upload, Trash2, Star, StarOff, 
  Cloud, CloudOff, RefreshCw, FolderOpen, Plus,
  Search, Filter, Calendar, User, Music
} from 'lucide-react';
import { useChordSystem } from '../providers/ChordSystemProvider';
import type { 
  ChordProgression, 
  ChordGridSection,
  UnifiedChordPosition 
} from '../types/chord-system';

// ============================================================================
// TYPES POUR LA SAUVEGARDE
// ============================================================================

interface SavedItem {
  id: string;
  name: string;
  type: 'progression' | 'grid' | 'chord';
  data: ChordProgression | ChordGridSection[] | UnifiedChordPosition;
  tags: string[];
  isFavorite: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  userId?: string;
  userName?: string;
  metadata?: {
    key?: string;
    tempo?: number;
    timeSignature?: string;
    instrument?: string;
    difficulty?: string;
    genre?: string;
  };
}

interface ChordSaveManagerProps {
  /** Mode d'affichage */
  viewMode?: 'list' | 'grid' | 'compact';
  /** Afficher les éléments publics */
  showPublicItems?: boolean;
  /** Afficher les contrôles d'import/export */
  showImportExport?: boolean;
  /** Callback lors de la sélection d'un élément */
  onItemSelect?: (item: SavedItem) => void;
  /** Callback lors de la suppression */
  onItemDelete?: (itemId: string) => void;
  /** Classe CSS personnalisée */
  className?: string;
}

interface SaveDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string, tags: string[], isPublic: boolean) => void;
  itemType: 'progression' | 'grid' | 'chord';
  defaultName?: string;
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Dialog de sauvegarde
 */
const SaveDialog: React.FC<SaveDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  itemType,
  defaultName = ''
}) => {
  const [name, setName] = useState(defaultName);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [isPublic, setIsPublic] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setName(defaultName);
      setTags([]);
      setTagInput('');
      setIsPublic(false);
    }
  }, [isOpen, defaultName]);

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = () => {
    if (name.trim()) {
      onSave(name.trim(), tags, isPublic);
      onClose();
    }
  };

  if (!isOpen) return null;

  const typeLabels = {
    progression: 'Progression',
    grid: 'Grille',
    chord: 'Accord'
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Sauvegarder {typeLabels[itemType]}
          </h3>

          {/* Nom */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={`Nom de la ${typeLabels[itemType].toLowerCase()}`}
              autoFocus
            />
          </div>

          {/* Tags */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <div className="flex flex-wrap gap-1 mb-2">
              {tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                >
                  {tag}
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
            <div className="flex">
              <input
                type="text"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ajouter un tag"
              />
              <button
                onClick={handleAddTag}
                className="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg hover:bg-gray-200"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Visibilité */}
          <div className="mb-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">
                Rendre public (visible par tous les utilisateurs)
              </span>
            </label>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Annuler
            </button>
            <button
              onClick={handleSave}
              disabled={!name.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Sauvegarder
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Carte d'élément sauvegardé
 */
const SavedItemCard: React.FC<{
  item: SavedItem;
  viewMode: string;
  onSelect: (item: SavedItem) => void;
  onToggleFavorite: (itemId: string) => void;
  onDelete: (itemId: string) => void;
}> = ({ item, viewMode, onSelect, onToggleFavorite, onDelete }) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'progression': return <Music className="w-4 h-4" />;
      case 'grid': return <FolderOpen className="w-4 h-4" />;
      case 'chord': return <Star className="w-4 h-4" />;
      default: return <Music className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'progression': return 'text-blue-600 bg-blue-50';
      case 'grid': return 'text-green-600 bg-green-50';
      case 'chord': return 'text-purple-600 bg-purple-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (viewMode === 'compact') {
    return (
      <div 
        className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
        onClick={() => onSelect(item)}
      >
        <div className={`p-1 rounded ${getTypeColor(item.type)} mr-3`}>
          {getTypeIcon(item.type)}
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 truncate">{item.name}</h4>
          <p className="text-xs text-gray-500">
            {new Date(item.updatedAt).toLocaleDateString()}
          </p>
        </div>

        <div className="flex items-center space-x-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite(item.id);
            }}
            className="p-1 text-gray-400 hover:text-yellow-500"
          >
            {item.isFavorite ? <Star className="w-4 h-4 fill-current" /> : <StarOff className="w-4 h-4" />}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete(item.id);
            }}
            className="p-1 text-gray-400 hover:text-red-500"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors"
      onClick={() => onSelect(item)}
    >
      {/* En-tête */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <div className={`p-2 rounded ${getTypeColor(item.type)} mr-3`}>
            {getTypeIcon(item.type)}
          </div>
          <div>
            <h4 className="font-medium text-gray-900">{item.name}</h4>
            <p className="text-sm text-gray-500 capitalize">{item.type}</p>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          {item.isPublic && <Cloud className="w-4 h-4 text-blue-500" />}
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite(item.id);
            }}
            className="p-1 text-gray-400 hover:text-yellow-500"
          >
            {item.isFavorite ? <Star className="w-4 h-4 fill-current" /> : <StarOff className="w-4 h-4" />}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete(item.id);
            }}
            className="p-1 text-gray-400 hover:text-red-500"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Métadonnées */}
      {item.metadata && (
        <div className="mb-3 text-sm text-gray-600">
          {item.metadata.key && <span>Tonalité: {item.metadata.key}</span>}
          {item.metadata.tempo && <span className="ml-3">Tempo: {item.metadata.tempo} BPM</span>}
          {item.metadata.instrument && <span className="ml-3">Instrument: {item.metadata.instrument}</span>}
        </div>
      )}

      {/* Tags */}
      {item.tags.length > 0 && (
        <div className="mb-3">
          <div className="flex flex-wrap gap-1">
            {item.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
            {item.tags.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">
                +{item.tags.length - 3}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Pied de page */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <span>Modifié le {new Date(item.updatedAt).toLocaleDateString()}</span>
        {item.userName && <span>Par {item.userName}</span>}
      </div>
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const ChordSaveManager: React.FC<ChordSaveManagerProps> = ({
  viewMode = 'grid',
  showPublicItems = true,
  showImportExport = true,
  onItemSelect,
  onItemDelete,
  className = ''
}) => {
  const { state, actions } = useChordSystem();
  
  // État local
  const [savedItems, setSavedItems] = useState<SavedItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<SavedItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'progression' | 'grid' | 'chord'>('all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveDialogType, setSaveDialogType] = useState<'progression' | 'grid' | 'chord'>('progression');
  const [isLoading, setIsLoading] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [currentViewMode, setCurrentViewMode] = useState(viewMode);

  // ============================================================================
  // EFFETS
  // ============================================================================

  // Charger les éléments sauvegardés
  useEffect(() => {
    loadSavedItems();
  }, []);

  // Filtrer les éléments
  useEffect(() => {
    let filtered = savedItems;

    // Filtre par terme de recherche
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filtre par type
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.type === filterType);
    }

    // Filtre favoris
    if (showFavoritesOnly) {
      filtered = filtered.filter(item => item.isFavorite);
    }

    setFilteredItems(filtered);
  }, [savedItems, searchTerm, filterType, showFavoritesOnly]);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const loadSavedItems = useCallback(async () => {
    setIsLoading(true);
    try {
      // TODO: Implémenter le chargement depuis Supabase
      // Pour l'instant, utiliser le localStorage comme fallback
      const localItems = localStorage.getItem('chord-system-saved-items');
      if (localItems) {
        setSavedItems(JSON.parse(localItems));
      }
    } catch (error) {
      console.error('Erreur chargement éléments sauvegardés:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const saveItem = useCallback(async (
    name: string, 
    tags: string[], 
    isPublic: boolean,
    type: 'progression' | 'grid' | 'chord',
    data: any
  ) => {
    try {
      const newItem: SavedItem = {
        id: crypto.randomUUID(),
        name,
        type,
        data,
        tags,
        isFavorite: false,
        isPublic,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: 'current-user', // TODO: Obtenir l'ID utilisateur réel
        userName: 'Utilisateur', // TODO: Obtenir le nom utilisateur réel
        metadata: extractMetadata(data, type)
      };

      const updatedItems = [...savedItems, newItem];
      setSavedItems(updatedItems);
      
      // Sauvegarder localement
      localStorage.setItem('chord-system-saved-items', JSON.stringify(updatedItems));
      
      // TODO: Sauvegarder sur Supabase
      console.log('Élément sauvegardé:', newItem);
      
    } catch (error) {
      console.error('Erreur sauvegarde:', error);
    }
  }, [savedItems]);

  const extractMetadata = (data: any, type: string) => {
    const metadata: any = {};
    
    if (type === 'progression' && data.key) {
      metadata.key = data.key;
      metadata.tempo = data.tempo;
      metadata.timeSignature = data.timeSignature;
    }
    
    if (type === 'chord' && data.instrument) {
      metadata.instrument = data.instrument;
      metadata.difficulty = data.difficulty;
    }
    
    return metadata;
  };

  const handleSaveCurrentProgression = () => {
    if (state.currentProgression) {
      setSaveDialogType('progression');
      setShowSaveDialog(true);
    }
  };

  const handleSaveCurrentGrid = () => {
    if (state.songSections && state.songSections.length > 0) {
      setSaveDialogType('grid');
      setShowSaveDialog(true);
    }
  };

  const handleToggleFavorite = useCallback((itemId: string) => {
    const updatedItems = savedItems.map(item =>
      item.id === itemId ? { ...item, isFavorite: !item.isFavorite } : item
    );
    setSavedItems(updatedItems);
    localStorage.setItem('chord-system-saved-items', JSON.stringify(updatedItems));
  }, [savedItems]);

  const handleDeleteItem = useCallback((itemId: string) => {
    const updatedItems = savedItems.filter(item => item.id !== itemId);
    setSavedItems(updatedItems);
    localStorage.setItem('chord-system-saved-items', JSON.stringify(updatedItems));
    onItemDelete?.(itemId);
  }, [savedItems, onItemDelete]);

  const handleItemSelect = useCallback((item: SavedItem) => {
    // Charger l'élément dans le système
    if (item.type === 'progression') {
      actions.setCurrentProgression?.(item.data as ChordProgression);
    } else if (item.type === 'grid') {
      actions.setSongSections?.(item.data as ChordGridSection[]);
    } else if (item.type === 'chord') {
      actions.selectChord(item.data as UnifiedChordPosition);
    }
    
    onItemSelect?.(item);
  }, [actions, onItemSelect]);

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <div className={`chord-save-manager ${className}`}>
      {/* En-tête */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Save className="w-6 h-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">Éléments Sauvegardés</h2>
          <div className="ml-3 flex items-center">
            {isOnline ? (
              <Cloud className="w-4 h-4 text-green-500" />
            ) : (
              <CloudOff className="w-4 h-4 text-red-500" />
            )}
            <span className="ml-1 text-sm text-gray-500">
              {isOnline ? 'En ligne' : 'Hors ligne'}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Actions de sauvegarde rapide */}
          <button
            onClick={handleSaveCurrentProgression}
            disabled={!state.currentProgression}
            className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            Sauver Progression
          </button>
          
          <button
            onClick={handleSaveCurrentGrid}
            disabled={!state.songSections || state.songSections.length === 0}
            className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            Sauver Grille
          </button>

          {/* Contrôles d'import/export */}
          {showImportExport && (
            <>
              <button className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg">
                <Upload className="w-4 h-4" />
              </button>
              <button className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg">
                <Download className="w-4 h-4" />
              </button>
            </>
          )}

          <button
            onClick={loadSavedItems}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="mb-6 space-y-4">
        {/* Barre de recherche */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Rechercher par nom ou tag..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filtres */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Type */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
            >
              <option value="all">Tous types</option>
              <option value="progression">Progressions</option>
              <option value="grid">Grilles</option>
              <option value="chord">Accords</option>
            </select>

            {/* Favoris */}
            <button
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
              className={`
                flex items-center px-3 py-2 text-sm rounded-lg border transition-colors
                ${showFavoritesOnly 
                  ? 'bg-yellow-100 text-yellow-800 border-yellow-300' 
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-yellow-50'
                }
              `}
            >
              <Star className="w-4 h-4 mr-1" />
              Favoris
            </button>
          </div>

          {/* Mode d'affichage */}
          <div className="flex items-center border border-gray-300 rounded-lg">
            <button
              onClick={() => setCurrentViewMode('compact')}
              className={`px-3 py-2 text-sm ${currentViewMode === 'compact' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              Compact
            </button>
            <button
              onClick={() => setCurrentViewMode('grid')}
              className={`px-3 py-2 text-sm ${currentViewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              Grille
            </button>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600 mr-2" />
          <span className="text-gray-600">Chargement...</span>
        </div>
      ) : filteredItems.length === 0 ? (
        <div className="text-center py-12">
          <Save className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm || filterType !== 'all' || showFavoritesOnly 
              ? 'Aucun résultat' 
              : 'Aucun élément sauvegardé'
            }
          </h3>
          <p className="text-gray-600">
            {searchTerm || filterType !== 'all' || showFavoritesOnly
              ? 'Essayez de modifier vos critères de recherche.'
              : 'Commencez par sauvegarder une progression ou une grille d\'accords.'
            }
          </p>
        </div>
      ) : (
        <div className={`
          ${currentViewMode === 'compact' 
            ? 'space-y-2' 
            : 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
          }
        `}>
          {filteredItems.map((item) => (
            <SavedItemCard
              key={item.id}
              item={item}
              viewMode={currentViewMode}
              onSelect={handleItemSelect}
              onToggleFavorite={handleToggleFavorite}
              onDelete={handleDeleteItem}
            />
          ))}
        </div>
      )}

      {/* Dialog de sauvegarde */}
      <SaveDialog
        isOpen={showSaveDialog}
        onClose={() => setShowSaveDialog(false)}
        onSave={(name, tags, isPublic) => {
          let data;
          if (saveDialogType === 'progression') {
            data = state.currentProgression;
          } else if (saveDialogType === 'grid') {
            data = state.songSections;
          }
          
          if (data) {
            saveItem(name, tags, isPublic, saveDialogType, data);
          }
        }}
        itemType={saveDialogType}
        defaultName={
          saveDialogType === 'progression' 
            ? state.currentProgression?.name 
            : `Grille ${new Date().toLocaleDateString()}`
        }
      />
    </div>
  );
};
