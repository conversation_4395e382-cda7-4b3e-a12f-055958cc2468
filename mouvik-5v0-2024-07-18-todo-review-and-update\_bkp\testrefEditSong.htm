<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MOUVIK Studio - Interface Conceptuelle</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <style>
        :root {
            --primary: #0d2e33;
            --secondary: #124a54;
            --accent: #00c2cb;
            --text: #e6f4f1;
            --muted: #7c9da3;
            --danger: #ff5f56;
            --success: #27c93f;
            --warning: #ffbd2e;
        }
        
        body {
            background-color: var(--primary);
            color: var(--text);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .bg-primary { background-color: var(--primary); }
        .bg-secondary { background-color: var(--secondary); }
        .bg-accent { background-color: var(--accent); }
        .text-accent { color: var(--accent); }
        .text-muted { color: var(--muted); }
        .border-accent { border-color: var(--accent); }
        
        .btn-primary {
            background-color: var(--accent);
            color: var(--primary);
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: all 0.2s;
        }
        
        .btn-primary:hover {
            opacity: 0.9;
        }
        
        .btn-secondary {
            background-color: var(--secondary);
            color: var(--text);
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: all 0.2s;
        }
        
        .btn-secondary:hover {
            opacity: 0.9;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--secondary);
        }
        
        .tab {
            padding: 0.75rem 1.25rem;
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 2px solid transparent;
        }
        
        .tab.active {
            border-bottom: 2px solid var(--accent);
            color: var(--accent);
        }
        
        .tab:hover:not(.active) {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .dashboard-card {
            background-color: var(--secondary);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-input {
            background-color: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text);
            padding: 0.5rem;
            border-radius: 0.25rem;
            width: 100%;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--accent);
        }
        
        .form-select {
            background-color: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text);
            padding: 0.5rem;
            border-radius: 0.25rem;
            width: 100%;
        }
        
        .waveform-container {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.5rem;
            height: 120px;
            position: relative;
            overflow: hidden;
        }
        
        .waveform {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
        }
        
        .waveform-line {
            width: 3px;
            background-color: var(--accent);
            margin-right: 2px;
            border-radius: 1px;
        }
        
        .chord-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 1rem;
        }
        
        .chord-box {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.25rem;
            padding: 1rem;
            text-align: center;
        }
        
        .chord-diagram {
            width: 70px;
            height: 80px;
            margin: 0 auto;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .chord-string {
            height: 1px;
            background-color: var(--muted);
            margin: 3px 0;
            flex: 1;
        }
        
        .chord-fret {
            width: 1px;
            background-color: var(--muted);
            position: absolute;
            top: 0;
            bottom: 0;
        }
        
        .chord-dot {
            width: 12px;
            height: 12px;
            background-color: var(--accent);
            border-radius: 50%;
            position: absolute;
            transform: translate(-50%, -50%);
        }
        
        .track-preview {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.25rem;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        
        /* AI Suggestions Panel */
        .ai-panel {
            background-color: rgba(0, 194, 203, 0.1);
            border: 1px solid var(--accent);
            border-radius: 0.5rem;
            padding: 1rem;
        }
        
        .suggestion-item {
            padding: 0.75rem;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .suggestion-item:hover {
            background-color: rgba(0, 194, 203, 0.2);
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--primary);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--secondary);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--accent);
        }
        
        /* Lyrics Editor */
        .lyrics-editor {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.5rem;
            padding: 1rem;
            min-height: 300px;
            line-height: 1.7;
        }
        
        .lyrics-line {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .chord-tag {
            background-color: var(--accent);
            color: var(--primary);
            font-weight: 600;
            padding: 0.1rem 0.5rem;
            border-radius: 0.25rem;
            margin-right: 0.5rem;
            font-size: 0.8rem;
        }
        
        /* Version Control */
        .version-item {
            padding: 0.5rem;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .version-active {
            border-left: 3px solid var(--accent);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="bg-primary border-b border-secondary">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <div class="text-accent font-bold text-xl mr-2">
                    <i class="fas fa-wave-square mr-2"></i>MOUVIK Studio
                </div>
                <span class="bg-secondary text-xs px-2 py-1 rounded">BETA</span>
            </div>
            
            <div class="flex items-center">
                <div class="mr-4">
                    <button class="btn-primary"><i class="fas fa-save mr-2"></i>Sauvegarder</button>
                </div>
                <div class="relative">
                    <img src="https://via.placeholder.com/32" class="w-8 h-8 rounded-full" alt="Profile">
                </div>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <div class="container mx-auto px-4 py-4">
        <!-- Project Header -->
        <div class="flex justify-between items-center mb-6">
            <div>
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold">Mon Premier Chef-d'Œuvre</h1>
                    <span class="ml-3 bg-secondary text-xs px-2 py-1 rounded">Draft</span>
                </div>
                <div class="text-muted text-sm mt-1">Dernière modification il y a 5 minutes</div>
            </div>
            
            <div class="flex">
                <button class="btn-secondary mr-2"><i class="fas fa-headphones mr-1"></i> Prévisualiser</button>
                <button class="btn-primary"><i class="fas fa-share-alt mr-1"></i> Partager</button>
            </div>
        </div>
        
        <!-- Tabs -->
        <div class="tabs mb-6">
            <div class="tab active"><i class="fas fa-music mr-2"></i>Édition & Métadonnées</div>
            <div class="tab"><i class="fas fa-microphone mr-2"></i>Audio & Waveform</div>
            <div class="tab"><i class="fas fa-edit mr-2"></i>Paroles & Accords</div>
            <div class="tab"><i class="fas fa-chart-line mr-2"></i>Analytics</div>
        </div>
        
        <!-- Main Tab Content -->
        <div class="flex flex-wrap -mx-3">
            <!-- Left Column -->
            <div class="w-full md:w-8/12 px-3">
                <!-- Audio Waveform Section -->
                <div class="dashboard-card mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold"><i class="fas fa-wave-square mr-2"></i>Audio & Waveform</h2>
                        <button class="btn-secondary text-sm"><i class="fas fa-upload mr-1"></i> Importer Audio</button>
                    </div>
                    
                    <div class="waveform-container mb-4">
                        <div class="waveform">
                            <!-- Generated waveform pattern -->
                            <div class="waveform-line" style="height: 30%;"></div>
                            <div class="waveform-line" style="height: 40%;"></div>
                            <div class="waveform-line" style="height: 60%;"></div>
                            <div class="waveform-line" style="height: 80%;"></div>
                            <div class="waveform-line" style="height: 50%;"></div>
                            <div class="waveform-line" style="height: 20%;"></div>
                            <div class="waveform-line" style="height: 30%;"></div>
                            <div class="waveform-line" style="height: 70%;"></div>
                            <div class="waveform-line" style="height: 90%;"></div>
                            <div class="waveform-line" style="height: 60%;"></div>
                            <div class="waveform-line" style="height: 40%;"></div>
                            <div class="waveform-line" style="height: 30%;"></div>
                            <div class="waveform-line" style="height: 20%;"></div>
                            <div class="waveform-line" style="height: 45%;"></div>
                            <div class="waveform-line" style="height: 65%;"></div>
                            <div class="waveform-line" style="height: 75%;"></div>
                            <div class="waveform-line" style="height: 55%;"></div>
                            <div class="waveform-line" style="height: 35%;"></div>
                            <div class="waveform-line" style="height: 25%;"></div>
                            <div class="waveform-line" style="height: 40%;"></div>
                            <div class="waveform-line" style="height: 30%;"></div>
                            <div class="waveform-line" style="height: 60%;"></div>
                            <div class="waveform-line" style="height: 80%;"></div>
                            <!-- Repeat for more lines -->
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <button class="bg-accent text-primary p-2 rounded-full mr-3"><i class="fas fa-play"></i></button>
                            <span>00:42 / 03:12</span>
                        </div>
                        
                        <div class="flex items-center">
                            <span class="mr-3">120 BPM</span>
                            <button class="text-muted mr-3"><i class="fas fa-volume-up"></i></button>
                            <button class="text-muted"><i class="fas fa-expand"></i></button>
                        </div>
                    </div>
                </div>
                
                <!-- Lyrics & Chords Editor -->
                <div class="dashboard-card mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold"><i class="fas fa-edit mr-2"></i>Éditeur de Paroles et Accords</h2>
                        <div>
                            <button class="btn-secondary text-sm mr-2"><i class="fas fa-magic mr-1"></i> Suggestions IA</button>
                            <button class="btn-secondary text-sm"><i class="fas fa-expand mr-1"></i></button>
                        </div>
                    </div>
                    
                    <div class="lyrics-editor mb-4">
                        <div class="lyrics-line">
                            <span class="chord-tag">C</span>
                            <span>Je me</span>
                            <span class="chord-tag">Am</span>
                            <span>réveille sous un ciel</span>
                        </div>
                        
                        <div class="lyrics-line">
                            <span>Qui n'a</span>
                            <span class="chord-tag">F</span>
                            <span>plus de cou</span>
                            <span class="chord-tag">G</span>
                            <span>leur</span>
                        </div>
                        
                        <div class="lyrics-line">
                            <span class="chord-tag">C</span>
                            <span>Les souvenirs</span>
                            <span class="chord-tag">Am</span>
                            <span>me reviennent comme des ailes</span>
                        </div>
                        
                        <div class="lyrics-line">
                            <span>S'envo</span>
                            <span class="chord-tag">G</span>
                            <span>lent de mon</span>
                            <span class="chord-tag">C</span>
                            <span>cœur</span>
                        </div>
                        
                        <div class="mt-4 font-semibold">REFRAIN:</div>
                        
                        <div class="lyrics-line mt-2">
                            <span class="chord-tag">F</span>
                            <span>Et je</span>
                            <span class="chord-tag">C</span>
                            <span>cours, je</span>
                            <span class="chord-tag">G</span>
                            <span>cours jusqu'au bout</span>
                        </div>
                        
                        <div class="lyrics-line">
                            <span class="chord-tag">F</span>
                            <span>Sans sa</span>
                            <span class="chord-tag">G</span>
                            <span>voir où</span>
                            <span class="chord-tag">C</span>
                            <span>je vais</span>
                        </div>
                    </div>
                </div>
                
                <!-- Chord Visualization -->
                <div class="dashboard-card">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold"><i class="fas fa-guitar mr-2"></i>Visualisation des Accords</h2>
                        <select class="form-select w-auto">
                            <option>Guitare</option>
                            <option>Piano</option>
                            <option>Ukulélé</option>
                        </select>
                    </div>
                    
                    <div class="chord-grid">
                        <!-- C Chord -->
                        <div class="chord-box">
                            <div class="text-lg font-semibold mb-1">C</div>
                            <div class="chord-diagram mb-1">
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                
                                <div class="chord-fret" style="left: 25%;"></div>
                                <div class="chord-fret" style="left: 50%;"></div>
                                <div class="chord-fret" style="left: 75%;"></div>
                                
                                <div class="chord-dot" style="left: 37.5%; top: 30%;"></div>
                                <div class="chord-dot" style="left: 62.5%; top: 50%;"></div>
                                <div class="chord-dot" style="left: 12.5%; top: 70%;"></div>
                            </div>
                            <div class="text-xs text-muted">Do Majeur</div>
                        </div>
                        
                        <!-- Am Chord -->
                        <div class="chord-box">
                            <div class="text-lg font-semibold mb-1">Am</div>
                            <div class="chord-diagram mb-1">
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                
                                <div class="chord-fret" style="left: 25%;"></div>
                                <div class="chord-fret" style="left: 50%;"></div>
                                <div class="chord-fret" style="left: 75%;"></div>
                                
                                <div class="chord-dot" style="left: 37.5%; top: 30%;"></div>
                                <div class="chord-dot" style="left: 12.5%; top: 50%;"></div>
                                <div class="chord-dot" style="left: 37.5%; top: 70%;"></div>
                            </div>
                            <div class="text-xs text-muted">La mineur</div>
                        </div>
                        
                        <!-- F Chord -->
                        <div class="chord-box">
                            <div class="text-lg font-semibold mb-1">F</div>
                            <div class="chord-diagram mb-1">
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                
                                <div class="chord-fret" style="left: 25%;"></div>
                                <div class="chord-fret" style="left: 50%;"></div>
                                <div class="chord-fret" style="left: 75%;"></div>
                                
                                <div class="chord-dot" style="left: 12.5%; top: 10%;"></div>
                                <div class="chord-dot" style="left: 37.5%; top: 30%;"></div>
                                <div class="chord-dot" style="left: 62.5%; top: 50%;"></div>
                            </div>
                            <div class="text-xs text-muted">Fa Majeur</div>
                        </div>
                        
                        <!-- G Chord -->
                        <div class="chord-box">
                            <div class="text-lg font-semibold mb-1">G</div>
                            <div class="chord-diagram mb-1">
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                <div class="chord-string"></div>
                                
                                <div class="chord-fret" style="left: 25%;"></div>
                                <div class="chord-fret" style="left: 50%;"></div>
                                <div class="chord-fret" style="left: 75%;"></div>
                                
                                <div class="chord-dot" style="left: 87.5%; top: 10%;"></div>
                                <div class="chord-dot" style="left: 62.5%; top: 70%;"></div>
                                <div class="chord-dot" style="left: 87.5%; top: 90%;"></div>
                            </div>
                            <div class="text-xs text-muted">Sol Majeur</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Column -->
            <div class="w-full md:w-4/12 px-3">
                <!-- Song Metadata -->
                <div class="dashboard-card">
                    <h2 class="text-lg font-semibold mb-4"><i class="fas fa-info-circle mr-2"></i>Métadonnées</h2>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Titre</label>
                        <input type="text" class="form-input" value="Mon Premier Chef-d'Œuvre">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Artiste</label>
                        <input type="text" class="form-input" value="MouvikUser">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Genre</label>
                        <select class="form-select">
                            <option selected>Pop/Rock</option>
                            <option>Électronique</option>
                            <option>Jazz</option>
                            <option>Classique</option>
                            <option>Hip-Hop</option>
                        </select>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-muted mb-1 text-sm">Tempo</label>
                            <input type="number" class="form-input" value="120">
                        </div>
                        <div>
                            <label class="block text-muted mb-1 text-sm">Tonalité</label>
                            <select class="form-select">
                                <option>A</option>
                                <option>A#/Bb</option>
                                <option>B</option>
                                <option selected>C</option>
                                <option>C#/Db</option>
                                <option>D</option>
                                <option>D#/Eb</option>
                                <option>E</option>
                                <option>F</option>
                                <option>F#/Gb</option>
                                <option>G</option>
                                <option>G#/Ab</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Description</label>
                        <textarea class="form-input" rows="3">Une chanson originale créée avec Mouvik Studio, explorant des thèmes de découverte et d'aventure.</textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Langue</label>
                        <select class="form-select">
                            <option selected>Français</option>
                            <option>Anglais</option>
                            <option>Espagnol</option>
                            <option>Italien</option>
                            <option>Allemand</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Date de sortie</label>
                        <input type="date" class="form-input" value="2023-11-15">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">ISRC</label>
                        <input type="text" class="form-input" placeholder="Ex: FRXXX2300123">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Auteurs</label>
                        <input type="text" class="form-input" placeholder="Séparez les noms par des virgules">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Producteurs</label>
                        <input type="text" class="form-input" placeholder="Séparez les noms par des virgules">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-muted mb-1 text-sm">Featuring</label>
                        <input type="text" class="form-input" placeholder="Séparez les noms par des virgules">
                    </div>
                    
                    <div class="flex mb-4">
                        <div class="mr-4">
                            <label class="block text-muted mb-1 text-sm">Humeur</label>
                            <select class="form-select w-full">
                                <option>Joyeux</option>
                                <option>Mélancolique</option>
                                <option selected>Énergique</option>
                                <option>Calme</option>
                                <option>Intense</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-muted mb-1 text-sm">Visibilité</label>
                            <select class="form-select w-full">
                                <option selected>Public</option>
                                <option>Privé</option>
                                <option>Lien uniquement</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap mb-2">
                        <div class="w-1/2 mb-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="allow_downloads" class="mr-2" checked>
                                <label for="allow_downloads" class="text-sm">Permettre téléchargements</label>
                            </div>
                        </div>
                        <div class="w-1/2 mb-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="allow_comments" class="mr-2" checked>
                                <label for="allow_comments" class="text-sm">Permettre commentaires</label>
                            </div>
                        </div>
                        <div class="w-1/2 mb-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="is_explicit" class="mr-2">
                                <label for="is_explicit" class="text-sm">Contenu explicite</label>
                            </div>
                        </div>
                        <div class="w-1/2 mb-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="stems_available" class="mr-2">
                                <label for="stems_available" class="text-sm">Stems disponibles</label>
                            </div>
                        </div>
                        <div class="w-1/2 mb-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="is_remix" class="mr-2">
                                <label for="is_remix" class="text-sm">C'est un remix</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- AI Assistant -->
                <div class="ai-panel mt-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold"><i class="fas fa-brain mr-2"></i>Assistant IA</h2>
                        <button class="text-xs bg-accent bg-opacity-20 text-accent px-2 py-1 rounded">Connecté</button>
                    </div>
                    
                    <div class="text-sm mb-4">Suggestions contextuelles basées sur votre travail actuel :</div>
                    
                    <div class="suggestion-item">
                        <div class="font-semibold">Progression d'accords alternative</div>
                        <div class="text-sm text-muted">Am - F - C - G (pour une ambiance plus mélancolique)</div>
                    </div>
                    
                    <div class="suggestion-item">
                        <div class="font-semibold">Idée de mélodie</div>
                        <div class="text-sm text-muted">Essayez un motif descendant au refrain pour contraster avec les couplets</div>
                    </div>
                    
                    <div class="suggestion-item">
                        <div class="font-semibold">Amélioration des paroles</div>
                        <div class="text-sm text-muted">Utilisez plus d'images pour renforcer le thème du voyage</div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <button class="btn-secondary text-sm w-full">Demander d'autres suggestions</button>
                    </div>
                </div>
                
                <!-- Version Control -->
                <div class="dashboard-card mt-6">
                    <h2 class="text-lg font-semibold mb-4"><i class="fas fa-code-branch mr-2"></i>Versions</h2>
                    
                    <div class="version-item version-active">
                        <div>
                            <div class="font-semibold">Version 4</div>
                            <div class="text-xs text-muted">Aujourd'hui, 10:25 - Actuelle</div>
                        </div>
                        <button class="text-accent text-sm">Voir</button>
                    </div>
                    
                    <div class="version-item">
                        <div>
                            <div class="font-semibold">Version 3</div>
                            <div class="text-xs text-muted">Hier, 15:32</div>
                        </div>
                        <button class="text-accent text-sm">Voir</button>
                    </div>
                    
                    <div class="version-item">
                        <div>
                            <div class="font-semibold">Version 2</div>
                            <div class="text-xs text-muted">03 Nov, 20:45</div>
                        </div>
                        <button class="text-accent text-sm">Voir</button>
                    </div>
                    
                    <div class="version-item">
                        <div>
                            <div class="font-semibold">Version 1</div>
                            <div class="text-xs text-muted">01 Nov, 15:20</div>
                        </div>
                        <button class="text-accent text-sm">Voir</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Additional Features Section -->
        <div class="mt-6">
            <h2 class="text-lg font-semibold mb-4">Fonctionnalités avancées</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="dashboard-card h-full">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-layer-group text-accent text-xl mr-3"></i>
                        <h3 class="font-semibold">Stems & Versions</h3>
                    </div>
                    <p class="text-sm text-muted mb-3">Gérez les différentes versions de votre morceau : instrumentale, a cappella, ou remix.</p>
                    <div class="mt-auto">
                        <button class="btn-secondary text-sm w-full">Gérer les versions</button>
                    </div>
                </div>
                
                <div class="dashboard-card h-full">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-share-alt text-accent text-xl mr-3"></i>
                        <h3 class="font-semibold">Distribution</h3>
                    </div>
                    <p class="text-sm text-muted mb-3">Préparez votre morceau pour la distribution sur les plateformes de streaming.</p>
                    <div class="mt-auto">
                        <button class="btn-secondary text-sm w-full">Préparer la distribution</button>
                    </div>
                </div>
                
                <div class="dashboard-card h-full">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-magic text-accent text-xl mr-3"></i>
                        <h3 class="font-semibold">Générer avec IA</h3>
                    </div>
                    <p class="text-sm text-muted mb-3">Utilisez l'IA pour générer des paroles, mélodies, ou harmonies complémentaires.</p>
                    <div class="mt-auto">
                        <button class="btn-secondary text-sm w-full">Explorer les options IA</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="bg-primary border-t border-secondary mt-8">
        <div class="container mx-auto px-4 py-4">
            <div class="flex flex-wrap items-center justify-between">
                <div class="text-muted text-sm">
                    MOUVIK Studio © 2023 - Tous droits réservés
                </div>
                
                <div class="flex text-muted text-sm">
                    <a href="#" class="mr-4 hover:text-accent">Aide</a>
                    <a href="#" class="mr-4 hover:text-accent">Conditions</a>
                    <a href="#" class="hover:text-accent">Confidentialité</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Script for interactive elements -->
    <script>
        // Simple tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // Dynamically generate more waveform lines
        const waveform = document.querySelector('.waveform');
        for (let i = 0; i < 80; i++) {
            const height = Math.floor(Math.random() * 80) + 10;
            const line = document.createElement('div');
            line.className = 'waveform-line';
            line.style.height = `${height}%`;
            waveform.appendChild(line);
        }
    </script>
</body>
</html>