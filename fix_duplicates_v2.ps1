# Advanced script to remove duplicate lines
$inputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration.tsx'
$outputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration_fixed.tsx'

# Read all content
$content = Get-Content $inputFile -Encoding UTF8
$fixedLines = @()
$i = 0

while ($i -lt $content.Length) {
    $currentLine = $content[$i]
    
    # Check if this line is duplicated in the next line
    if ($i + 1 -lt $content.Length -and $content[$i] -eq $content[$i + 1]) {
        # Skip the duplicate
        $fixedLines += $currentLine
        $i += 2  # Skip both the original and duplicate
    } else {
        $fixedLines += $currentLine
        $i++
    }
}

# Write the fixed content
$fixedLines | Set-Content $outputFile -Encoding UTF8

Write-Host "Original lines: $($content.Length)"
Write-Host "Fixed lines: $($fixedLines.Length)"
Write-Host "Removed: $($content.Length - $fixedLines.Length) duplicate lines"