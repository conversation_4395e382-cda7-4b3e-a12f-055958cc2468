import { z } from "zod";

// Enums for various status fields
export enum AiAssistanceLevel {
  NONE = "none",
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  FULL = "full"
}

export enum RecordingStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  NEEDS_REVISION = "needs_revision"
}

export enum ArrangementStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  NEEDS_REVISION = "needs_revision"
}

export enum MixingStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  NEEDS_REVISION = "needs_revision"
}

export enum MasteringStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  NEEDS_REVISION = "needs_revision"
}

// Interfaces for AI-related fields
export interface AiConfig {
  model?: string;
  provider?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
}

export interface AiHistoryItem {
  id: string;
  timestamp: string;
  action: string;
  input?: string;
  output?: string;
  model?: string;
  provider?: string;
}

// Main song schema
export const songSchema = z.object({
  // Basic song information
  subgenre: z.array(z.string()).optional(), // Added subgenre
  is_archived: z.boolean().default(false).optional(), // Added is_archived
  featured_artists: z.array(z.string()).optional(), // Added featured_artists as an array of strings
  id: z.string().uuid().optional(),
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist: z.string().min(1, { message: "Le nom de l'artiste est requis." }),
  artist_name: z.string().nullable().default(null).optional(),
  duration_ms: z.number().int().min(0).nullable().optional(), // Changed from duration to duration_ms
  bpm: z.number().nullable().optional(),
  
  // Musical characteristics
  key: z.string().nullable().optional(),
  musical_key: z.string().nullable().optional(), // Added musical_key field
  time_signature: z.string().nullable().optional(),
  scale: z.string().nullable().optional(),
  
  genre: z.string().nullable().default(null).optional(), // Changed from genres:string[] to genre:string
  moods: z.array(z.string().trim().min(1)).default([]),
  lyrics: z.string().nullable().default(null).optional(),
  composer_name: z.string().nullable().default(null).optional(),
  writers: z.array(z.string().trim().min(1)).default([]),
  producers: z.array(z.string().trim().min(1)).default([]),
  bloc_note: z.string().nullable().default(null).optional(),
  right_column_notepad: z.string().nullable().default(null).optional(),
  status: z.string().nullable().default(null).optional(), // Consider z.enum([...]) later
  
  slug: z.string().trim().nullable().default(null).optional(),
  
  tuning_frequency: z.number().positive().nullable().optional(),
  description: z.string().nullable().default(null).optional(), // From backup, distinct from 'notes'
  lyrics_language: z.string().trim().nullable().default(null).optional(),
  custom_css: z.string().nullable().default(null).optional(),
  chords: z.string().nullable().default(null).optional(),
  structure: z.string().nullable().default(null).optional(),
  notes: z.string().nullable().default(null).optional(),
  is_public: z.boolean().default(false),
  is_favorite: z.boolean().default(false),
  is_incomplete: z.boolean().default(true),
  is_cover: z.boolean().default(false),
  is_instrumental: z.boolean().default(false),
  is_explicit: z.boolean().default(false),
  
  audio_url: z.string().url({ message: "URL audio invalide." }).nullable().optional(),
  cover_art_url: z.string().url({ message: "URL de la pochette invalide." }).nullable().optional(),
  cover_art_file_name: z.string().nullable().optional(), // Added
  audio_file_name: z.string().nullable().optional(), // Added
  creator_user_id: z.string().uuid({ message: "ID créateur invalide." }),
  allow_comments: z.boolean().default(true).optional(),
  allow_downloads: z.boolean().default(false).optional(),
  band_id: z.string().uuid().nullable().optional(),
  tags: z.array(z.string().trim().min(1)).default([]),
  themes: z.array(z.string().trim().min(1)).default([]),
  instruments: z.array(z.string().trim().min(1)).default([]),
  contributors: z.array(z.object({
    id: z.string().uuid(),
    name: z.string().min(1, { message: "Nom du contributeur requis."}),
    role: z.string().min(1, { message: "Rôle du contributeur requis."}),
  })).default([]),
  plays: z.number().int().min(0).default(0),
  
  created_at: z.string().datetime({ message: "Date de création invalide." }).optional(),
  updated_at: z.string().datetime({ message: "Date de mise à jour invalide." }).optional(),
  iswc_code: z.string().nullable().default(null).optional(), // Added iswc_code
  
  // Legal and publishing information
  isrc_code: z.string().nullable().default(null).optional(),
  upc_code: z.string().nullable().default(null).optional(),
  album_id: z.string().uuid({ message: "ID d'album invalide." }).nullable().optional(),
  copyright_notice: z.string().nullable().default(null).optional(),
  publisher_name: z.string().nullable().default(null).optional(),
  record_label: z.string().nullable().default(null).optional(),
  licensing_info: z.string().nullable().default(null).optional(),
  language: z.string().nullable().default(null).optional(), // General song language
  parental_advisory: z.string().nullable().default(null).optional(),
  recording_date: z.string().datetime({ message: "Date d'enregistrement invalide." }).nullable().optional(),
  release_date: z.string().datetime({ message: "Date de sortie invalide." }).nullable().optional(), // Added release_date field
  recording_status: z.string().nullable().optional(), // TODO: Consider z.enum with PROGRESS_STATUS_OPTIONS
  mastering_status: z.string().nullable().optional(), // TODO: Consider z.enum with PROGRESS_STATUS_OPTIONS
  mixing_engineer: z.string().nullable().default(null).optional(),
  mastering_engineer: z.string().nullable().default(null).optional(),
  mixing_status: z.string().nullable().optional(), // TODO: Consider z.enum with PROGRESS_STATUS_OPTIONS
  ai_collaboration_level: z.number().int().min(0).max(100).nullable().optional(),
  artwork_credits: z.string().nullable().default(null).optional(),
  tablature: z.string().nullable().default(null).optional(),
  performance_notes: z.string().nullable().default(null).optional(),
  song_versions: z.array(z.any()).optional(), // Placeholder, define specific type later
  audio_file_versions: z.array(z.any()).optional(), // Placeholder, define specific type later
  external_links: z.array(z.object({ type: z.string(), url: z.string().url({ message: "URL externe invalide." }) })).optional(),
  completion_percentage: z.number().int().min(0).max(100).nullable().optional(),
  creation_process_type: z.string().nullable().optional(), // TODO: Consider z.enum with CREATION_PROCESS_OPTIONS
  
  // Progression fields for different song sections
  progression_intro: z.number().int().min(0).max(100).nullable().optional(),
  progression_verse: z.number().int().min(0).max(100).nullable().optional(),
  progression_chorus: z.number().int().min(0).max(100).nullable().optional(),
  progression_bridge: z.number().int().min(0).max(100).nullable().optional(),
  progression_outro: z.number().int().min(0).max(100).nullable().optional(),
  
  custom_fields: z.record(z.any()).optional(), // For user-defined fields
  lyrics_sync_data: z.any().nullable().optional(), // For LRC or other sync formats
  chords_diagrams: z.record(z.unknown()).optional(), // JSONB for chord diagrams
  privacy_settings: z.object({
    allow_embedding: z.boolean(),
    allow_download: z.boolean()
  }).optional(),
  collaborators: z.array(z.any()).optional(), // Array of collaborators, specific structure TBD
  split_sheet: z.any().nullable().optional(),
  last_played_at: z.string().datetime({ message: "Date de dernière lecture invalide." }).nullable().optional(),
  play_count: z.number().int().min(0).optional().default(0),
  rating_average: z.number().min(0).max(5).optional().default(0), // Assuming a 0-5 rating scale
  rating_count: z.number().int().min(0).optional().default(0),
  comments_count: z.number().int().min(0).optional().default(0),
  shares_count: z.number().int().min(0).optional().default(0),
  downloads_count: z.number().int().min(0).optional().default(0),
  stream_sources: z.array(z.any()).optional(), // Placeholder for specific stream source object structure
  purchase_links: z.array(z.any()).optional(), // Placeholder for specific purchase link object structure
  related_songs: z.array(z.string().uuid({ message: "ID de morceau lié invalide." })).optional(), // Assuming array of song UUIDs
  song_story: z.string().nullable().default(null).optional(),
  production_notes: z.string().nullable().default(null).optional(),
  gear_used: z.array(z.string()).optional(), // Array of strings for gear used
  sample_credits: z.array(z.any()).optional(), // Placeholder for specific sample credit object structure
  remix_info: z.string().nullable().default(null).optional(),
  version_of_song_id: z.string().uuid({ message: "ID de la version originale du morceau invalide." }).nullable().optional(),
  ai_config: z.custom<AiConfig>().optional(), // Added AiConfig
  arrangement_status: z.string().nullable().optional(), // TODO: Consider z.enum with PROGRESS_STATUS_OPTIONS
  ai_history: z.array(z.custom<AiHistoryItem>()).optional(), // Added AiHistory
  ai_assistance_level: z.nativeEnum(AiAssistanceLevel).optional().nullable(), // Added for the AI tab
  editor_data: z.record(z.any()).optional(), // Added editor_data field for lyrics editor
  progress_data: z.record(z.any()).optional(), // Added progress_data field for tracking progress
  attribution_type: z.string().optional().nullable(), // Added attribution_type field
  // TODO: Review if any other fields from backup's songFormSchema need to be added or if any existing fields conflict
});

// Type TypeScript déduit du schéma Zod
export type SongFormValues = z.infer<typeof songSchema>;