"use client";

import { useState, useEffect } from 'react';
import { Button, ButtonProps } from "@/components/ui/button";
import { Rss, UserCheck, UserPlus } from "lucide-react"; // Rss for general follow, UserPlus/Check for user-specific follow
import { getSupabaseClient } from '@/lib/supabase/client';
import { useToast } from "@/hooks/use-toast";
import { cn } from '@/lib/utils';

interface FollowPlaylistButtonProps extends Omit<ButtonProps, 'onClick'> {
  playlistId: string;
  userId?: string; // Logged-in user's ID
  initialFollowerCount: number;
  initialIsFollowed: boolean; 
  onFollowToggle?: (newIsFollowed: boolean, newFollowerCount: number) => void;
}

export function FollowPlaylistButton({
  playlistId,
  userId,
  initialFollowerCount,
  initialIsFollowed,
  onFollowToggle,
  className,
  size = "default",
  variant = "outline",
  ...props
}: FollowPlaylistButtonProps) {
  const supabase = getSupabaseClient();
  const { toast } = useToast();
  const [isFollowed, setIsFollowed] = useState(initialIsFollowed);
  const [followerCount, setFollowerCount] = useState(initialFollowerCount);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsFollowed(initialIsFollowed);
    setFollowerCount(initialFollowerCount);
  }, [initialIsFollowed, initialFollowerCount, playlistId]);

  const handleFollow = async () => {
    if (!userId) {
      toast({ title: "Action requise", description: "Veuillez vous connecter pour suivre cette playlist.", variant: "default" });
      return;
    }
    if (isLoading) return;

    setIsLoading(true);
    const optimisticIsFollowed = !isFollowed;
    const optimisticFollowerCount = optimisticIsFollowed ? followerCount + 1 : Math.max(0, followerCount - 1);
    
    setIsFollowed(optimisticIsFollowed);
    setFollowerCount(optimisticFollowerCount);

    try {
      const { data, error } = await supabase.rpc('toggle_playlist_follow', {
        p_playlist_id: playlistId,
        p_user_id: userId,
      });

      if (error) throw error;

      const actualIsFollowed = data.is_following;
      const actualFollowerCount = data.new_follower_count;

      setIsFollowed(actualIsFollowed);
      setFollowerCount(actualFollowerCount);
      
      if (onFollowToggle) {
        onFollowToggle(actualIsFollowed, actualFollowerCount);
      }
      toast({
        title: actualIsFollowed ? "Suivi" : "Non suivi",
        description: actualIsFollowed ? "Vous suivez maintenant cette playlist." : "Vous ne suivez plus cette playlist.",
      });

    } catch (error: any) {
      console.error("Error toggling playlist follow:", error);
      toast({ title: "Erreur", description: "Impossible de traiter votre demande.", variant: "destructive" });
      setIsFollowed(!optimisticIsFollowed); // Revert optimistic
      setFollowerCount(optimisticIsFollowed ? optimisticFollowerCount - 1 : optimisticFollowerCount + 1);
       if (onFollowToggle) {
         onFollowToggle(!optimisticIsFollowed, optimisticIsFollowed ? optimisticFollowerCount -1 : optimisticFollowerCount + 1);
       }
    } finally {
      setIsLoading(false);
    }
  };

  const FollowIcon = isFollowed ? UserCheck : UserPlus; // Or Rss if always Rss

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleFollow}
      disabled={isLoading || !userId}
      className={cn(
        "flex items-center gap-1.5",
        isFollowed && "text-primary border-primary", // Style for followed state
        className
      )}
      {...props}
    >
      <FollowIcon className={cn("h-4 w-4", size !== "icon" && size !== "default" ? "mr-1" : "")} />
      {size !== "icon" && (
        <span>{isFollowed ? "Suivi" : "Suivre"} ({followerCount})</span>
      )}
      {size === "icon" && (
        <span className="sr-only">{isFollowed ? "Ne plus suivre" : "Suivre"}</span>
      )}
    </Button>
  );
}
