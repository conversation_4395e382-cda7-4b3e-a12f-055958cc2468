# Create a completely clean version of AIChordIntegration.tsx
$backupFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration_backup.tsx'
$targetFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration_new.tsx'

if (Test-Path $backupFile) {
    Write-Host "Using backup file..."
    Copy-Item $backupFile $targetFile
} else {
    Write-Host "Backup file not found. Creating minimal version..."
    $minimalContent = @'
import React from 'react';

const AIChordIntegration = () => {
  return (
    <div className="h-full flex flex-col">
      <div className="p-4">
        <h2>AI Chord Integration</h2>
        <p>Component temporarily simplified for build fix.</p>
      </div>
    </div>
  );
};

export default AIChordIntegration;
'@
    
    [System.IO.File]::WriteAllText($targetFile, $minimalContent, [System.Text.Encoding]::UTF8)
}

Write-Host "Clean file created at: $targetFile"