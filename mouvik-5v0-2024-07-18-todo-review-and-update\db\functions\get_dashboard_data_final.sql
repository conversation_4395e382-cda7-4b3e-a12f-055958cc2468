CREATE OR REPLACE FUNCTION public.get_dashboard_data(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_profile JSONB;
    v_songs_summary JSONB;
    v_albums_summary JSONB;
    v_total_plays BIGINT;
    v_total_views BIGINT;
    v_total_likes BIGINT;
    v_total_followers BIGINT;
    v_daily_metrics JSONB;
    v_weekly_metrics JSONB;
    v_monthly_metrics JSONB;
    v_recent_songs JSONB;
    v_recent_albums JSONB;
    v_top_songs JSONB;
    v_recent_comments JSONB; -- Placeholder, not implemented in this version
    v_recent_followers JSONB; -- Placeholder, not implemented in this version
    v_highlight_song JSONB; -- Placeholder, not implemented in this version
    v_result JSONB;
    v_user_song_ids UUID[];
    v_user_album_ids UUID[];
BEGIN
    -- Get all song and album IDs for the user once
    SELECT array_agg(id) INTO v_user_song_ids FROM public.songs WHERE creator_user_id = p_user_id AND is_archived = false;
    SELECT array_agg(id) INTO v_user_album_ids FROM public.albums WHERE creator_user_id = p_user_id AND is_archived = false;

    -- 1. User Profile
    SELECT jsonb_build_object(
        'id', id,
        'username', username,
        'display_name', display_name,
        'avatar_url', profile_picture_url,
        'bio', bio,
        'website', website_url,
        'location', location,
        'coins_balance', 0 -- Placeholder for coins_balance
    )
    INTO v_profile
    FROM public.profiles
    WHERE id = p_user_id;

    -- 2. Songs Summary
    SELECT jsonb_build_object(
        'total_count', COUNT(*),
        'published_count', COUNT(*) FILTER (WHERE visibility = 'public')
    )
    INTO v_songs_summary
    FROM public.songs
    WHERE creator_user_id = p_user_id AND is_archived = false;

    -- 3. Albums Summary
    SELECT jsonb_build_object(
        'total_count', COUNT(*),
        'published_count', COUNT(*) FILTER (WHERE visibility = 'public')
    )
    INTO v_albums_summary
    FROM public.albums
    WHERE creator_user_id = p_user_id AND is_archived = false;

    -- 4. Total Plays (from songs.plays for simplicity, or public.plays for granularity)
    SELECT COALESCE(SUM(s.plays), 0)
    INTO v_total_plays
    FROM public.songs s
    WHERE s.creator_user_id = p_user_id AND s.is_archived = false;
    -- Alternative using public.plays: 
    -- SELECT COUNT(*) INTO v_total_plays FROM public.plays WHERE song_id = ANY(v_user_song_ids);

    -- 5. Total Views (from resource_views for songs and albums)
    SELECT COUNT(*)
    INTO v_total_views
    FROM public.resource_views
    WHERE (resource_type = 'song' AND resource_id = ANY(v_user_song_ids))
       OR (resource_type = 'album' AND resource_id = ANY(v_user_album_ids));

    -- 6. Total Likes
    SELECT COUNT(*)
    INTO v_total_likes
    FROM public.likes
    WHERE (resource_type = 'song' AND resource_id = ANY(v_user_song_ids))
       OR (resource_type = 'album' AND resource_id = ANY(v_user_album_ids));

    -- 7. Total Followers
    SELECT COUNT(*)
    INTO v_total_followers
    FROM public.follows
    WHERE following_id = p_user_id;

    -- 8. Recent Songs
    SELECT COALESCE(jsonb_agg(jsonb_build_object(
        'id', s.id,
        'title', s.title,
        'cover_url', s.cover_art_url,
        'genres', s.genres,
        'plays', s.plays, 
        'created_at', s.created_at,
        'updated_at', s.updated_at,
        'status', CASE WHEN s.visibility = 'public' THEN 'published' ELSE 'draft' END
    ) ORDER BY s.created_at DESC NULLS LAST), '[]'::jsonb)
    INTO v_recent_songs
    FROM public.songs s
    WHERE s.creator_user_id = p_user_id AND s.is_archived = false
    LIMIT 5;

    -- 9. Recent Albums
    SELECT COALESCE(jsonb_agg(jsonb_build_object(
        'id', a.id,
        'title', a.title,
        'cover_url', a.album_art_url,
        'genres', a.genres,
        'created_at', a.created_at,
        'updated_at', a.updated_at,
        'status', CASE WHEN a.visibility = 'public' THEN 'published' ELSE 'draft' END
    ) ORDER BY a.created_at DESC NULLS LAST), '[]'::jsonb)
    INTO v_recent_albums
    FROM public.albums a
    WHERE a.creator_user_id = p_user_id AND a.is_archived = false
    LIMIT 5;

    -- 10. Top Songs (based on songs.plays)
    SELECT COALESCE(jsonb_agg(jsonb_build_object(
        'id', s.id,
        'title', s.title,
        'cover_url', s.cover_art_url,
        'genres', s.genres,
        'plays', s.plays,
        'created_at', s.created_at
    ) ORDER BY s.plays DESC NULLS LAST, s.created_at DESC), '[]'::jsonb)
    INTO v_top_songs
    FROM public.songs s
    WHERE s.creator_user_id = p_user_id AND s.is_archived = false AND s.visibility = 'public'
    LIMIT 5;

    -- 11. Daily Metrics (Plays and Views for the last 30 days)
    WITH date_series AS (
        SELECT generate_series(date_trunc('day', NOW() - INTERVAL '29 days'), date_trunc('day', NOW()), INTERVAL '1 day') AS day_date
    )
    SELECT COALESCE(jsonb_agg(jsonb_build_object(
        'date', TO_CHAR(ds.day_date, 'YYYY-MM-DD'),
        'plays', (
            SELECT COUNT(p.id)
            FROM public.plays p
            WHERE p.song_id = ANY(v_user_song_ids) AND date_trunc('day', p.played_at) = ds.day_date
        ),
        'views', (
            SELECT COUNT(rv.id)
            FROM public.resource_views rv
            WHERE ((rv.resource_type = 'song' AND rv.resource_id = ANY(v_user_song_ids)) 
                   OR (rv.resource_type = 'album' AND rv.resource_id = ANY(v_user_album_ids)))
              AND date_trunc('day', rv.viewed_at) = ds.day_date
        )
    ) ORDER BY ds.day_date ASC), '[]'::jsonb)
    INTO v_daily_metrics
    FROM date_series ds;

    -- 12. Weekly Metrics (Plays and Views for the last 12 weeks)
    WITH date_series AS (
        SELECT generate_series(date_trunc('week', NOW() - INTERVAL '11 weeks'), date_trunc('week', NOW()), INTERVAL '1 week') AS week_date
    )
    SELECT COALESCE(jsonb_agg(jsonb_build_object(
        'date', TO_CHAR(ds.week_date, 'YYYY-MM-DD'),
        'plays', (
            SELECT COUNT(p.id)
            FROM public.plays p
            WHERE p.song_id = ANY(v_user_song_ids) AND date_trunc('week', p.played_at) = ds.week_date
        ),
        'views', (
            SELECT COUNT(rv.id)
            FROM public.resource_views rv
            WHERE ((rv.resource_type = 'song' AND rv.resource_id = ANY(v_user_song_ids)) 
                   OR (rv.resource_type = 'album' AND rv.resource_id = ANY(v_user_album_ids)))
              AND date_trunc('week', rv.viewed_at) = ds.week_date
        )
    ) ORDER BY ds.week_date ASC), '[]'::jsonb)
    INTO v_weekly_metrics
    FROM date_series ds;

    -- 13. Monthly Metrics (Plays and Views for the last 12 months)
    WITH date_series AS (
        SELECT generate_series(date_trunc('month', NOW() - INTERVAL '11 months'), date_trunc('month', NOW()), INTERVAL '1 month') AS month_date
    )
    SELECT COALESCE(jsonb_agg(jsonb_build_object(
        'date', TO_CHAR(ds.month_date, 'YYYY-MM-DD'),
        'plays', (
            SELECT COUNT(p.id)
            FROM public.plays p
            WHERE p.song_id = ANY(v_user_song_ids) AND date_trunc('month', p.played_at) = ds.month_date
        ),
        'views', (
            SELECT COUNT(rv.id)
            FROM public.resource_views rv
            WHERE ((rv.resource_type = 'song' AND rv.resource_id = ANY(v_user_song_ids)) 
                   OR (rv.resource_type = 'album' AND rv.resource_id = ANY(v_user_album_ids)))
              AND date_trunc('month', rv.viewed_at) = ds.month_date
        )
    ) ORDER BY ds.month_date ASC), '[]'::jsonb)
    INTO v_monthly_metrics
    FROM date_series ds;

    -- Build the final JSON result
    v_result := jsonb_build_object(
        'userProfile', COALESCE(v_profile, '{}'::jsonb),
        'songs_summary', COALESCE(v_songs_summary, '{}'::jsonb),
        'albums_summary', COALESCE(v_albums_summary, '{}'::jsonb),
        'totalPlays', COALESCE(v_total_plays, 0),
        'totalViews', COALESCE(v_total_views, 0),
        'totalLikes', COALESCE(v_total_likes, 0),
        'totalFollowers', COALESCE(v_total_followers, 0),
        'daily_metrics_for_dashboard', COALESCE(v_daily_metrics, '[]'::jsonb),
        'weekly_metrics_for_dashboard', COALESCE(v_weekly_metrics, '[]'::jsonb),
        'monthly_metrics_for_dashboard', COALESCE(v_monthly_metrics, '[]'::jsonb),
        'recentSongs', COALESCE(v_recent_songs, '[]'::jsonb),
        'recentAlbums', COALESCE(v_recent_albums, '[]'::jsonb),
        'topSongs', COALESCE(v_top_songs, '[]'::jsonb),
        'recentComments', COALESCE(v_recent_comments, '[]'::jsonb), -- Placeholder
        'recentFollowers', COALESCE(v_recent_followers, '[]'::jsonb), -- Placeholder
        'highlightSong', COALESCE(v_highlight_song, '{}'::jsonb) -- Placeholder
    );

    RETURN v_result;
END;
$$;

