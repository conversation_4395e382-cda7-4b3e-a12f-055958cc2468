/**
 * 🎼 CHORD LIBRARY BROWSER - Navigation Intelligente d'Accords
 * 
 * Composant principal pour naviguer dans la bibliothèque d'accords
 * Interface optimisée pour musiciens avec recherche avancée
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Search, Filter, Music, Volume2, VolumeX, Grid, List, Star } from 'lucide-react';
import { useChordSystem } from '../providers/ChordSystemProvider';
import { useChordLibrary } from '../hooks/useChordLibrary';
import { ChordDiagramViewer } from './ChordDiagramViewer';
import type { 
  UnifiedChordPosition, 
  ChordSearchFilters, 
  InstrumentType,
  DifficultyLevel 
} from '../types/chord-system';

// ============================================================================
// TYPES ET INTERFACES
// ============================================================================

interface ChordLibraryBrowserProps {
  /** Mode d'affichage par défaut */
  defaultViewMode?: 'grid' | 'list';
  /** Nombre d'accords par page */
  itemsPerPage?: number;
  /** Afficher les contrôles audio */
  showAudioControls?: boolean;
  /** Afficher les filtres avancés */
  showAdvancedFilters?: boolean;
  /** Callback lors de la sélection d'un accord */
  onChordSelect?: (chord: UnifiedChordPosition) => void;
  /** Callback lors de l'ajout à la progression */
  onAddToProgression?: (chord: UnifiedChordPosition) => void;
  /** Classe CSS personnalisée */
  className?: string;
}

interface ChordCardProps {
  chord: UnifiedChordPosition;
  viewMode: 'grid' | 'list';
  isSelected: boolean;
  isPlaying: boolean;
  onSelect: (chord: UnifiedChordPosition) => void;
  onPlay: (chord: UnifiedChordPosition) => void;
  onAddToProgression: (chord: UnifiedChordPosition) => void;
  showAudioControls: boolean;
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Carte d'accord individuelle
 */
const ChordCard: React.FC<ChordCardProps> = ({
  chord,
  viewMode,
  isSelected,
  isPlaying,
  onSelect,
  onPlay,
  onAddToProgression,
  showAudioControls
}) => {
  const getDifficultyColor = (difficulty: DifficultyLevel): string => {
    switch (difficulty) {
      case 'beginner': return 'text-green-600 bg-green-50';
      case 'intermediate': return 'text-yellow-600 bg-yellow-50';
      case 'advanced': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getDifficultyLabel = (difficulty: DifficultyLevel): string => {
    switch (difficulty) {
      case 'beginner': return 'Débutant';
      case 'intermediate': return 'Intermédiaire';
      case 'advanced': return 'Avancé';
      default: return 'Non spécifié';
    }
  };

  if (viewMode === 'list') {
    return (
      <div 
        className={`
          flex items-center p-4 border rounded-lg cursor-pointer transition-all
          ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
          ${isPlaying ? 'ring-2 ring-blue-400' : ''}
        `}
        onClick={() => onSelect(chord)}
      >
        {/* Diagramme miniature */}
        <div className="w-16 h-16 mr-4 flex-shrink-0">
          <ChordDiagramViewer 
            chord={chord} 
            size="small" 
            interactive={false}
            showLabels={false}
          />
        </div>

        {/* Informations */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {chord.chord}
            </h3>
            <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(chord.difficulty)}`}>
              {getDifficultyLabel(chord.difficulty)}
            </span>
          </div>
          
          <div className="flex items-center mt-1 text-sm text-gray-500">
            <span className="capitalize">{chord.instrument}</span>
            <span className="mx-2">•</span>
            <span className="capitalize">{chord.tuning}</span>
            {chord.midi && chord.midi.length > 0 && (
              <>
                <span className="mx-2">•</span>
                <Volume2 className="w-4 h-4" />
              </>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 ml-4">
          {showAudioControls && chord.midi && chord.midi.length > 0 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onPlay(chord);
              }}
              className={`
                p-2 rounded-full transition-colors
                ${isPlaying 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }
              `}
              title="Écouter l'accord"
            >
              {isPlaying ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </button>
          )}
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onAddToProgression(chord);
            }}
            className="p-2 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors"
            title="Ajouter à la progression"
          >
            <Star className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  // Mode grille
  return (
    <div 
      className={`
        p-4 border rounded-lg cursor-pointer transition-all
        ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}
        ${isPlaying ? 'ring-2 ring-blue-400' : ''}
      `}
      onClick={() => onSelect(chord)}
    >
      {/* Diagramme */}
      <div className="w-full h-32 mb-3">
        <ChordDiagramViewer 
          chord={chord} 
          size="medium" 
          interactive={false}
          showLabels={true}
        />
      </div>

      {/* Nom de l'accord */}
      <h3 className="text-lg font-semibold text-gray-900 text-center mb-2">
        {chord.chord}
      </h3>

      {/* Métadonnées */}
      <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
        <span className="capitalize">{chord.instrument}</span>
        <span className={`px-2 py-1 rounded-full ${getDifficultyColor(chord.difficulty)}`}>
          {getDifficultyLabel(chord.difficulty)}
        </span>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-center space-x-2">
        {showAudioControls && chord.midi && chord.midi.length > 0 && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onPlay(chord);
            }}
            className={`
              p-2 rounded-full transition-colors
              ${isPlaying 
                ? 'bg-blue-100 text-blue-600' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }
            `}
            title="Écouter l'accord"
          >
            {isPlaying ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
          </button>
        )}
        
        <button
          onClick={(e) => {
            e.stopPropagation();
            onAddToProgression(chord);
          }}
          className="p-2 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors"
          title="Ajouter à la progression"
        >
          <Star className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

/**
 * Barre de recherche et filtres
 */
const SearchAndFilters: React.FC<{
  filters: ChordSearchFilters;
  onFiltersChange: (filters: Partial<ChordSearchFilters>) => void;
  showAdvancedFilters: boolean;
  availableInstruments: InstrumentType[];
}> = ({ filters, onFiltersChange, showAdvancedFilters, availableInstruments }) => {
  const [showFilters, setShowFilters] = useState(false);

  return (
    <div className="space-y-4">
      {/* Barre de recherche principale */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          placeholder="Rechercher un accord (ex: Am, C7, Dmaj7...)"
          value={filters.searchTerm || ''}
          onChange={(e) => onFiltersChange({ searchTerm: e.target.value })}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {showAdvancedFilters && (
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`
              absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded
              ${showFilters ? 'text-blue-600 bg-blue-50' : 'text-gray-400 hover:text-gray-600'}
            `}
          >
            <Filter className="w-5 h-5" />
          </button>
        )}
      </div>

      {/* Filtres rapides */}
      <div className="flex flex-wrap gap-2">
        {/* Instrument */}
        <select
          value={filters.instrument || ''}
          onChange={(e) => onFiltersChange({ instrument: e.target.value as InstrumentType })}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
        >
          {availableInstruments.map(instrument => (
            <option key={instrument} value={instrument}>
              {instrument.charAt(0).toUpperCase() + instrument.slice(1)}
            </option>
          ))}
        </select>

        {/* Difficulté */}
        <select
          value={filters.difficulty || 'all'}
          onChange={(e) => onFiltersChange({ difficulty: e.target.value as DifficultyLevel | 'all' })}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">Toutes difficultés</option>
          <option value="beginner">Débutant</option>
          <option value="intermediate">Intermédiaire</option>
          <option value="advanced">Avancé</option>
        </select>

        {/* Tonalité */}
        <select
          value={filters.key || ''}
          onChange={(e) => onFiltersChange({ key: e.target.value })}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Toutes tonalités</option>
          {['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'].map(key => (
            <option key={key} value={key}>{key}</option>
          ))}
        </select>

        {/* Audio disponible */}
        <label className="flex items-center px-3 py-2 border border-gray-300 rounded-lg cursor-pointer">
          <input
            type="checkbox"
            checked={filters.hasAudio || false}
            onChange={(e) => onFiltersChange({ hasAudio: e.target.checked })}
            className="mr-2"
          />
          <Volume2 className="w-4 h-4 mr-1" />
          Audio
        </label>
      </div>

      {/* Filtres avancés */}
      {showAdvancedFilters && showFilters && (
        <div className="p-4 bg-gray-50 rounded-lg space-y-3">
          <h4 className="font-medium text-gray-900">Filtres avancés</h4>
          
          {/* Accordage */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Accordage
            </label>
            <input
              type="text"
              placeholder="ex: standard, drop_d, open_g..."
              value={filters.tuning || ''}
              onChange={(e) => onFiltersChange({ tuning: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Catégorie */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Catégorie
            </label>
            <select
              value={filters.category || ''}
              onChange={(e) => onFiltersChange({ category: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Toutes catégories</option>
              <option value="major">Majeur</option>
              <option value="minor">Mineur</option>
              <option value="7th">7ème</option>
              <option value="extended">Étendu</option>
              <option value="altered">Altéré</option>
              <option value="suspended">Suspendu</option>
              <option value="diminished">Diminué</option>
              <option value="augmented">Augmenté</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const ChordLibraryBrowser: React.FC<ChordLibraryBrowserProps> = ({
  defaultViewMode = 'grid',
  itemsPerPage = 24,
  showAudioControls = true,
  showAdvancedFilters = true,
  onChordSelect,
  onAddToProgression,
  className = ''
}) => {
  const { state, actions } = useChordSystem();
  const { filteredChords, loading, error, searchChords } = useChordLibrary();
  
  // État local
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(defaultViewMode);
  const [currentPage, setCurrentPage] = useState(1);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleChordSelect = useCallback((chord: UnifiedChordPosition) => {
    actions.selectChord(chord);
    onChordSelect?.(chord);
  }, [actions, onChordSelect]);

  const handleChordPlay = useCallback(async (chord: UnifiedChordPosition) => {
    try {
      await actions.playChord(chord, { mode: state.playMode });
    } catch (error) {
      console.error('Erreur lecture accord:', error);
    }
  }, [actions, state.playMode]);

  const handleAddToProgression = useCallback((chord: UnifiedChordPosition) => {
    actions.addToProgression(chord);
    onAddToProgression?.(chord);
  }, [actions, onAddToProgression]);

  const handleFiltersChange = useCallback((newFilters: Partial<ChordSearchFilters>) => {
    const updatedFilters = { ...state.searchFilters, ...newFilters };
    actions.searchChords(updatedFilters);
    setCurrentPage(1); // Reset à la première page
  }, [state.searchFilters, actions]);

  // ============================================================================
  // DONNÉES CALCULÉES
  // ============================================================================

  const paginatedChords = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredChords.slice(startIndex, endIndex);
  }, [filteredChords, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredChords.length / itemsPerPage);

  const availableInstruments = useMemo(() => {
    return state.availableInstruments.map(config => config.type);
  }, [state.availableInstruments]);

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <div className={`chord-library-browser ${className}`}>
      {/* En-tête avec recherche et filtres */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Music className="w-6 h-6 mr-2" />
            Bibliothèque d'Accords
          </h2>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              <Grid className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              <List className="w-5 h-5" />
            </button>
          </div>
        </div>

        <SearchAndFilters
          filters={state.searchFilters}
          onFiltersChange={handleFiltersChange}
          showAdvancedFilters={showAdvancedFilters}
          availableInstruments={availableInstruments}
        />
      </div>

      {/* Statistiques */}
      <div className="flex items-center justify-between mb-4 text-sm text-gray-600">
        <span>
          {filteredChords.length} accord{filteredChords.length !== 1 ? 's' : ''} trouvé{filteredChords.length !== 1 ? 's' : ''}
          {state.searchFilters.searchTerm && ` pour "${state.searchFilters.searchTerm}"`}
        </span>
        {totalPages > 1 && (
          <span>
            Page {currentPage} sur {totalPages}
          </span>
        )}
      </div>

      {/* Contenu principal */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Chargement des accords...</span>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {!loading && !error && paginatedChords.length === 0 && (
        <div className="text-center py-12">
          <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun accord trouvé</h3>
          <p className="text-gray-600">
            Essayez de modifier vos critères de recherche ou de changer d'instrument.
          </p>
        </div>
      )}

      {!loading && !error && paginatedChords.length > 0 && (
        <>
          {/* Grille/Liste d'accords */}
          <div className={`
            ${viewMode === 'grid' 
              ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4' 
              : 'space-y-3'
            }
          `}>
            {paginatedChords.map((chord) => (
              <ChordCard
                key={chord.id}
                chord={chord}
                viewMode={viewMode}
                isSelected={state.currentChord?.id === chord.id}
                isPlaying={state.isPlaying && state.currentChord?.id === chord.id}
                onSelect={handleChordSelect}
                onPlay={handleChordPlay}
                onAddToProgression={handleAddToProgression}
                showAudioControls={showAudioControls}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center mt-8 space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Précédent
              </button>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + Math.max(1, currentPage - 2);
                if (page > totalPages) return null;
                
                return (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`
                      px-3 py-2 border rounded-lg
                      ${currentPage === page 
                        ? 'bg-blue-600 text-white border-blue-600' 
                        : 'border-gray-300 hover:bg-gray-50'
                      }
                    `}
                  >
                    {page}
                  </button>
                );
              })}
              
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Suivant
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};
