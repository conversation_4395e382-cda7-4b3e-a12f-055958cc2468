# Liste des tâches pour la refactorisation de SongForm.tsx

## Terminé :
- Extraction de la logique de gestion des versions dans `useSongVersioning.ts`.
- Correction des importations de types liées (`LocalSongVersion`, `SongFormValues`, alias de `Song` en `SongData`, et un placeholder pour `SongVersion`) dans `useSongVersioning.ts`.

## À faire :
1.  **Finaliser les types de versioning** : Terminé
    *   `SongData` est un alias de `Song` (depuis `types/index.ts`), ce qui est satisfaisant.
    *   `SongVersion` est défini dans `useSongVersioning.ts` et est adéquat.

2.  **Extraire la logique de gestion des fichiers locaux** :
    *   **Extract Local File Management Logic**: DONE - La logique de gestion des fichiers audio et image (téléchargement, prévisualisation, état local) a été déplacée de `SongForm.tsx` vers le hook `components/songs/hooks/useLocalFileManagement.ts`. Ce hook gère `localCoverArtFile` et `localAudioFile` ainsi que les fonctions associées (`handleCoverArtSelect`, `handleClearCoverArt`, `handleAudioFileSelect`, `handleClearAudio`). `SongForm.tsx` utilise maintenant ce hook.
3.  **Extraire la logique de soumission du formulaire et des modales** : DONE - La logique de soumission principale du formulaire (anciennement `onInternalSubmit` et `onError`) a été déplacée vers le hook `components/songs/hooks/useSongFormActions.ts`. Ce hook gère `isProcessingSubmit`, `handleFormSubmit`, et `handleFormError`. `SongForm.tsx` utilise maintenant ce hook. La gestion des modales (`ConfirmDeleteVersionModal`, etc.) reste principalement assurée par `SongFormModals.tsx` et `useSongVersioning.ts`, car elle était déjà bien découplée.
4.  **Décomposer la structure JSX** : DONE - Les sections pour la pochette et le fichier audio ont été extraites dans les composants `SongFormCoverArtCard.tsx` et `SongFormAudioCard.tsx` respectivement. Les onglets d'informations (`SongFormGeneralInfoTab`, `SongFormLyricsChordTab`, `SongFormStructureTab`, `SongFormAdvancedTab`) étaient déjà des composants séparés ou ont été confirmés comme tels. `SongForm.tsx` utilise maintenant ces nouveaux composants pour une meilleure modularité.
5.  **Gérer les `useEffect` restants** : DONE
    *   Les `useEffect` précédemment identifiés dans `SongForm.tsx` ont été gérés :
        *   Le `useEffect` qui appelait `fetchVersions` a été supprimé ; cette logique est maintenant dans `useSongVersioning`.
        *   Le `useEffect` pour la réinitialisation du formulaire (basé sur `mode` et `loadedVersionData`) a été supprimé ; cette logique est gérée par `useSongVersioning` (via `reset()` et la callback `onVersionLoad` pour les mises à jour spécifiques au formulaire).
        *   Le `useEffect` pour la mise à jour des états locaux (`uploadedAudioUrl`, `localChords`, etc. basés sur les données de version chargées) a été supprimé ; cette logique est gérée par la callback `onVersionLoad` définie dans `SongForm.tsx` et passée à `useSongVersioning`.
    *   Il ne reste plus de `useEffect` majeurs à refactoriser dans `SongForm.tsx` liés à la logique de versioning ou de chargement de données initiales. Une revue générale des `useEffect` restants (s'il y en a pour d'autres fonctionnalités) peut être faite lors de la tâche 6.
6.  **Revue des dépendances et des importations** : DONE
    *   Les importations et dépendances de `SongForm.tsx`, `useSongVersioning.ts`, `useLocalFileManagement.ts`, et `useSongFormActions.ts` ont été revues et optimisées. Les importations inutilisées ont été supprimées et les types ont été clarifiés si nécessaire.
7.  **Tests (si applicable)** :
    *   Envisager d'ajouter des tests unitaires ou d'intégration pour les nouveaux hooks afin de garantir leur bon fonctionnement.