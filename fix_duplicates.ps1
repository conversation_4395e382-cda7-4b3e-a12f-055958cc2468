$inputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration.tsx'
$outputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration_fixed.tsx'

$lines = Get-Content $inputFile
$result = @()
$previousLine = ''

foreach($line in $lines) {
    if($line -ne $previousLine) {
        $result += $line
    }
    $previousLine = $line
}

$result | Out-File $outputFile -Encoding UTF8
Write-Host "Fixed file created: $outputFile"
Write-Host "Original lines: $($lines.Count)"
Write-Host "Fixed lines: $($result.Count)"