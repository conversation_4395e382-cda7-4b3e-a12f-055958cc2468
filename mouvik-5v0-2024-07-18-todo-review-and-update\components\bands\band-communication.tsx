"use client"

import type React from "react"

import { useState } from "react"
import { Send } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"

interface BandCommunicationProps {
  bandId: string
}

export function BandCommunication({ bandId }: BandCommunicationProps) {
  const [message, setMessage] = useState("")

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim()) {
      // Envoyer le message
      setMessage("")
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Communication</CardTitle>
        <CardDescription>Discutez avec les membres du groupe</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="chat">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chat">Chat groupe</TabsTrigger>
            <TabsTrigger value="messages">Messages</TabsTrigger>
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
          </TabsList>
          <TabsContent value="chat" className="h-[300px] flex flex-col">
            <div className="flex-1 overflow-y-auto py-2 space-y-4">
              <div className="flex items-start gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg?height=32&width=32&query=woman musician" />
                  <AvatarFallback>LC</AvatarFallback>
                </Avatar>
                <div className="bg-muted p-2 rounded-md text-sm max-w-[80%]">
                  <p className="text-xs font-medium mb-1">Lela Chen • 14:53</p>
                  <p>
                    J'ai uploadé la nouvelle version des vocaux. Est-ce que quelqu'un peut écouter la partie à 3:14 ? Je
                    ne suis pas sûre de la transition.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-2 justify-end">
                <div className="bg-primary/10 p-2 rounded-md text-sm max-w-[80%]">
                  <p className="text-xs font-medium mb-1">Vous • 16:42</p>
                  <p>Je vais l'écouter tout de suite et te donner mon avis.</p>
                </div>
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder.svg?height=32&width=32&query=man musician" />
                  <AvatarFallback>YO</AvatarFallback>
                </Avatar>
              </div>
            </div>

            <form onSubmit={handleSendMessage} className="mt-2 flex gap-2">
              <Input
                placeholder="Tapez votre message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="flex-1"
              />
              <Button type="submit" size="icon">
                <Send className="h-4 w-4" />
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="messages" className="h-[300px] flex items-center justify-center">
            <p className="text-muted-foreground">Aucun message privé</p>
          </TabsContent>

          <TabsContent value="feedback" className="h-[300px] flex items-center justify-center">
            <p className="text-muted-foreground">Aucun feedback en attente</p>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
