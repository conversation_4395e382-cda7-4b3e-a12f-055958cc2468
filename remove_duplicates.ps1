# Script to remove consecutive duplicate lines
$inputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration.tsx'
$outputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration_clean.tsx'

$content = Get-Content $inputFile
$unique = @()
$previousLine = $null

foreach ($line in $content) {
    if ($line -ne $previousLine) {
        $unique += $line
    }
    $previousLine = $line
}

$unique | Set-Content $outputFile
Write-Host "Removed duplicates. Original: $($content.Count) lines, Clean: $($unique.Count) lines"