import type React from "react"
import { redirect } from "next/navigation"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { AppSidebar, type UserProfileForSidebar } from "@/components/sidebar"; // Import UserProfileForSidebar
import MobileMenuButton from "./mobile-menu-button";
// UserProvider is no longer needed here as it's global in ClientLayout
// import { UserProvider } from "@/contexts/user-context"; 

interface AuthenticatedLayoutProps {
  children: React.ReactNode
}

interface ProfileDataForLayout {
  display_name?: string | null;
  username?: string | null;
  avatar_url?: string | null;
  role_primary?: string | null;
  subscription_tier?: string | null; 
  user_role?: string | null; 
  custom_uploads_per_month?: number | null;
  custom_vault_space_gb?: number | null;
  custom_vault_max_files?: number | null; // Added
  custom_ia_credits_month?: number | null;
  custom_coins_month?: number | null;
  custom_max_playlists?: number | null;
  custom_max_friends?: number | null;
  ia_credits?: number | null; 
  coins_balance?: number | null; 
}

export default async function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const supabase = createSupabaseServerClient()

  const {
    data: { user }, // This is AuthUser
    error: userError
  } = await supabase.auth.getUser()

  if (!user || userError) {
    redirect("/") 
  }

  // Fetch profile data to construct userObj for AppSidebar
  // UserProvider is now in ClientLayout, so this layout doesn't need to provide it.
  // It still needs userObj for AppSidebar.
  const profileFieldsToSelect = [
    "display_name", "username", "avatar_url", 
    "role_primary", "subscription_tier", "user_role",
    "custom_uploads_per_month", "custom_vault_space_gb", "custom_vault_max_files",
    "custom_ia_credits_month", "custom_coins_month",
    "custom_max_playlists", "custom_max_friends",
    "ia_credits", "coins_balance"
  ].join(", ");

  const { data: profile, error: profileError } = await supabase
    .from("profiles")
    .select(profileFieldsToSelect) 
    .eq("id", user.id)
    .single<ProfileDataForLayout>(); 

  let userObjForSidebar: UserProfileForSidebar;

  if (profileError) {
    console.error("Error fetching profile for AuthenticatedLayout/AppSidebar:", profileError);
    userObjForSidebar = {
      id: user.id,
      email: user.email,
      name: user.email?.split('@')[0],
      avatar_url: null,
      username: null, // Added username
      role_primary: null,
      subscription_tier: 'free', 
      user_role: 'user', 
      custom_uploads_per_month: null, custom_vault_space_gb: null, custom_vault_max_files: null,
      custom_ia_credits_month: null, custom_coins_month: null, 
      custom_max_playlists: null, custom_max_friends: null,
      ia_credits: null, coins_balance: null,
    };
  } else {
    userObjForSidebar = {
      id: user.id,
      email: user.email,
      name: profile?.display_name || profile?.username || user.email?.split('@')[0],
      avatar_url: profile?.avatar_url,
      username: profile?.username, // Added username
      role_primary: profile?.role_primary,
      subscription_tier: profile?.subscription_tier,
      user_role: profile?.user_role,
      custom_uploads_per_month: profile?.custom_uploads_per_month,
      custom_vault_space_gb: profile?.custom_vault_space_gb,
      custom_vault_max_files: profile?.custom_vault_max_files,
      custom_ia_credits_month: profile?.custom_ia_credits_month,
      custom_coins_month: profile?.custom_coins_month,
      custom_max_playlists: profile?.custom_max_playlists,
      custom_max_friends: profile?.custom_max_friends,
      ia_credits: profile?.ia_credits, 
      coins_balance: profile?.coins_balance, 
    };
  }
  
  // ClientLayout (via app/layout.tsx) will provide UserProvider globally.
  // This AuthenticatedLayout just renders its specific structure.
  return (
    <div className="flex h-screen main-layout-container"> 
      <MobileMenuButton />
      <AppSidebar user={userObjForSidebar} /> 
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  )
}
