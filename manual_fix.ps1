# Manual fix script
$inputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration.tsx'
$outputFile = 'c:\_DEV_projects\TOOL\mouvik-5v0 - Copie (2)\components\ai-composer\AIChordIntegration_manual.tsx'

# Read the file as raw bytes and convert to string
$bytes = [System.IO.File]::ReadAllBytes($inputFile)
$content = [System.Text.Encoding]::UTF8.GetString($bytes)

# Split into lines and remove empty duplicates
$lines = $content -split "`r?`n"
$cleanLines = @()
$prevLine = ""

for ($i = 0; $i -lt $lines.Length; $i++) {
    $currentLine = $lines[$i]
    
    # Only add line if it's different from previous or if it's not empty
    if ($currentLine -ne $prevLine -or $currentLine.Trim() -ne "") {
        $cleanLines += $currentLine
    }
    $prevLine = $currentLine
}

# Write clean content
$cleanContent = $cleanLines -join "`r`n"
[System.IO.File]::WriteAllText($outputFile, $cleanContent, [System.Text.Encoding]::UTF8)

Write-Host "Manual fix complete. Lines: $($lines.Length) -> $($cleanLines.Length)"