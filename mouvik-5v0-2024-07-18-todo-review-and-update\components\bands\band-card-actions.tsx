"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Edit, Trash2, Loader2, <PERSON>, <PERSON>Off } from "lucide-react"; // Added Eye, EyeOff
import { useState, useTransition } from "react"; // Added useState
import { deleteBand, toggleBandPublicStatus } from "@/lib/actions/band.actions"; // Import actions
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation"; 

interface BandCardActionsProps {
  bandId: string;
  bandName?: string; 
  initialIsPublic?: boolean; // Add prop for initial public status
}

export function BandCardActions({ bandId, bandName, initialIsPublic }: BandCardActionsProps) {
  const [isDeletePending, startDeleteTransition] = useTransition();
  const [isTogglePublicPending, startTogglePublicTransition] = useTransition();
  const [isPublic, setIsPublic] = useState(initialIsPublic ?? true); // Local state for optimistic update
  const { toast } = useToast();
  const router = useRouter();

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation(); 
  };

  const handleDeleteClick = async (e: React.MouseEvent) => {
    e.stopPropagation(); 
    
    const confirmationMessage = bandName 
      ? `Êtes-vous sûr de vouloir supprimer le groupe "${bandName}" ? Cette action est irréversible.`
      : "Êtes-vous sûr de vouloir supprimer ce groupe ? Cette action est irréversible.";

    if (window.confirm(confirmationMessage)) {
      startDeleteTransition(async () => {
        const result = await deleteBand(bandId);
        if (result.success) {
          toast({ title: "Succès", description: result.message || "Groupe supprimé." });
          // Revalidation is handled by the server action, router.refresh() could also be used.
        } else {
          toast({ title: "Erreur", description: result.error || "Impossible de supprimer le groupe.", variant: "destructive" });
        }
      });
    }
  };

  const handleTogglePublicStatus = async (e: React.MouseEvent) => {
    e.stopPropagation();
    startTogglePublicTransition(async () => {
      const result = await toggleBandPublicStatus(bandId);
      if (result.success && typeof result.newStatus === 'boolean') {
        setIsPublic(result.newStatus); // Optimistic update or reflect new status
        toast({ title: "Succès", description: result.message });
      } else {
        toast({ title: "Erreur", description: result.error || "Impossible de changer le statut de visibilité.", variant: "destructive" });
      }
    });
  };

  const isAnyActionPending = isDeletePending || isTogglePublicPending;

  return (
    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col gap-1 items-end md:flex-row">
      <Button
        variant="outline"
        size="icon"
        title={isPublic ? "Rendre Privé" : "Rendre Public"}
        onClick={handleTogglePublicStatus}
        disabled={isAnyActionPending}
      >
        {isTogglePublicPending ? <Loader2 className="h-4 w-4 animate-spin" /> : (isPublic ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />)}
      </Button>
      <Button
        variant="outline"
        size="icon"
        asChild
        title="Modifier le groupe"
        onClick={handleEditClick} 
        disabled={isAnyActionPending}
      >
        <Link href={`/manage-bands/${bandId}/edit`}> {/* Updated link */}
          <Edit className="h-4 w-4" />
        </Link>
      </Button>
      <Button
        variant="destructive"
        size="icon"
        title="Supprimer le groupe"
        onClick={handleDeleteClick}
        disabled={isAnyActionPending}
      >
        {isDeletePending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
      </Button>
    </div>
  );
}
