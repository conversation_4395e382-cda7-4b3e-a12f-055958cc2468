# Guide de Migration : Tracks vers Songs

## 📋 Aperçu

Ce document sert de guide complet pour la migration de la table `tracks` vers `songs` dans l'application Mouvik. Cette migration a pour but d'unifier la terminologie et d'améliorer la structure des données.

## 📅 Plan de Migration

### Phase 1: Préparation (1 jour)
- [ ] Mettre à jour la documentation
- [ ] Sauvegarder la base de données
- [ ] Informer l'équipe de la fenêtre de maintenance

### Phase 2: Exécution (1 jour)
- [ ] Mettre l'application en mode maintenance
- [ ] Exécuter le script de migration
- [ ] Valider l'intégrité des données
- [ ] Mettre à jour les configurations si nécessaire

### Phase 3: Vérification (1 jour)
- [ ] Tester les fonctionnalités critiques
- [ ] Vérifier les performances
- [ ] S'assurer que tous les services fonctionnent correctement

### Phase 4: Finalisation (1 jour)
- [ ] Supprimer la vue de compatibilité (si tout est stable)
- [ ] Nettoyer les anciennes données
- [ ] Mettre à jour la documentation finale

## 📝 Détails Techniques

### Fichiers de Migration

1. **Script de Migration**
   - Emplacement: `db/migrations/20240516_rename_tracks_to_songs.sql`
   - Objectif: Crée la table `songs` et migre les données de `tracks`

2. **Script de Rollback**
   - Emplacement: `db/migrations/rollback_20240516_rename_tracks_to_songs.sql`
   - Objectif: Permet de revenir en arrière en cas de problème

3. **Script de Test**
   - Emplacement: `db/migrations/test_migration.js`
   - Objectif: Vérifie l'intégrité des données avant et après la migration

### Structure de la Nouvelle Table `songs`

| Colonne | Type | Description |
|---------|------|-------------|
| id | UUID | Identifiant unique |
| project_id | UUID | Référence vers le projet parent |
| title | TEXT | Titre de la chanson |
| audio_url | TEXT | URL du fichier audio |
| waveform_data | FLOAT[] | Données de forme d'onde |
| duration | FLOAT | Durée en secondes |
| bpm | INTEGER | Battements par minute |
| key | TEXT | Tonalité musicale |
| status | TEXT | Statut (draft/published/archived) |
| metadata | JSONB | Métadonnées supplémentaires |
| created_at | TIMESTAMPTZ | Date de création |
| updated_at | TIMESTAMPTZ | Date de mise à jour |

## 🛠 Procédure de Migration

### Pré-requis

- Accès à la base de données en lecture/écriture
- Sauvegarde complète de la base de données
- Fenêtre de maintenance planifiée

### Étapes de Migration

1. **Sauvegarde**
   ```bash
   pg_dump -U postgres -d mouvik_prod -f mouvik_backup_$(date +%Y%m%d).sql
   ```

2. **Exécution de la Migration**
   ```bash
   psql -U postgres -d mouvik_prod -f db/migrations/20240516_rename_tracks_to_songs.sql
   ```

3. **Vérification**
   ```bash
   node db/migrations/test_migration.js
   ```

4. **En Cas de Problème**
   ```bash
   psql -U postgres -d mouvik_prod -f db/migrations/rollback_20240516_rename_tracks_to_songs.sql
   ```

## 🔍 Vérifications Post-Migration

- [ ] Tous les services démarrent correctement
- [ ] Les requêtes vers `songs` renvoient les données attendues
- [ ] Les statistiques et tableaux de bord s'affichent correctement
- [ ] Les performances sont conformes aux attentes

## 📊 Métriques de Succès

- Temps d'arrêt total < 1 heure
- Aucune perte de données
- Toutes les fonctionnalités critiques opérationnelles
- Performance égale ou supérieure à la version précédente

## 📞 Support

En cas de problème, contacter :
- [Responsable Technique] - [email] - [téléphone]
- [Support Technique] - [email] - [téléphone]

## 📝 Notes Supplémentaires

- La vue de compatibilité `tracks` sera maintenue pendant 1 mois
- Une revue de performance sera effectuée 1 semaine après la migration
- Les anciennes sauvegardes seront conservées pendant 30 jours
