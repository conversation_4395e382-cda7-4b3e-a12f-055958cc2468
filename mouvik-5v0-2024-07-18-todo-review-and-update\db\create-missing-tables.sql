-- Création de la table des vues si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS views (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('song', 'album', 'profile', 'band')),
  resource_id UUID NOT NULL,
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les recherches rapides de vues
CREATE INDEX IF NOT EXISTS views_resource_idx ON views(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS views_user_idx ON views(user_id);

-- Création de la table des commentaires de groupe si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS band_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  band_id UUID NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  parent_id UUID REFERENCES band_comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les recherches rapides de commentaires de groupe
CREATE INDEX IF NOT EXISTS band_comments_band_idx ON band_comments(band_id);
CREATE INDEX IF NOT EXISTS band_comments_user_idx ON band_comments(user_id);
CREATE INDEX IF NOT EXISTS band_comments_parent_idx ON band_comments(parent_id);

-- Création de la table des conversations si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Création de la table des participants aux conversations si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS conversation_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(conversation_id, user_id)
);

-- Index pour les recherches rapides de participants aux conversations
CREATE INDEX IF NOT EXISTS conversation_participants_conversation_idx ON conversation_participants(conversation_id);
CREATE INDEX IF NOT EXISTS conversation_participants_user_idx ON conversation_participants(user_id);

-- Création de la table des messages si elle n'existe pas déjà (si elle n'a pas la structure dont nous avons besoin)
CREATE TABLE IF NOT EXISTS private_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  read_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les recherches rapides de messages
CREATE INDEX IF NOT EXISTS private_messages_conversation_idx ON private_messages(conversation_id);
CREATE INDEX IF NOT EXISTS private_messages_sender_idx ON private_messages(sender_id);

-- Fonction pour incrémenter le nombre de lectures d'un morceau
CREATE OR REPLACE FUNCTION increment_song_plays(song_id UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO song_plays (song_id, user_id)
  VALUES (song_id, auth.uid());
  
  -- Mettre à jour le compteur de lectures dans la table songs
  UPDATE songs
  SET plays = COALESCE(plays, 0) + 1
  WHERE id = song_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour obtenir les statistiques d'un artiste
CREATE OR REPLACE FUNCTION get_artist_stats(artist_id UUID)
RETURNS TABLE(
  total_plays BIGINT,
  total_followers BIGINT,
  total_albums BIGINT,
  total_songs BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(SUM(s.plays), 0)::BIGINT AS total_plays,
    COALESCE((SELECT COUNT(*) FROM follows WHERE following_id = artist_id AND following_type = 'user'), 0)::BIGINT AS total_followers,
    COALESCE((SELECT COUNT(*) FROM albums WHERE user_id = artist_id), 0)::BIGINT AS total_albums,
    COALESCE((SELECT COUNT(*) FROM songs WHERE user_id = artist_id), 0)::BIGINT AS total_songs;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour obtenir les statistiques d'un groupe
CREATE OR REPLACE FUNCTION get_band_stats(band_id UUID)
RETURNS TABLE(
  total_plays BIGINT,
  total_followers BIGINT,
  total_albums BIGINT,
  total_songs BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE((
      SELECT SUM(s.plays)
      FROM songs s
      JOIN band_projects bp ON s.project_id = bp.id
      WHERE bp.band_id = get_band_stats.band_id
    ), 0)::BIGINT AS total_plays,
    COALESCE((SELECT COUNT(*) FROM follows WHERE following_id = band_id AND following_type = 'band'), 0)::BIGINT AS total_followers,
    COALESCE((
      SELECT COUNT(*)
      FROM albums a
      JOIN band_projects bp ON a.project_id = bp.id
      WHERE bp.band_id = get_band_stats.band_id
    ), 0)::BIGINT AS total_albums,
    COALESCE((
      SELECT COUNT(*)
      FROM songs s
      JOIN band_projects bp ON s.project_id = bp.id
      WHERE bp.band_id = get_band_stats.band_id
    ), 0)::BIGINT AS total_songs;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
