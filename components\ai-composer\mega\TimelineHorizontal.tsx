'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, Pause, Plus, Copy, Trash2, GripVertical, 
  Music, FileText, Layers, Target, Square, Volume2,
  Clock, Hash, Guitar, Piano, SkipForward, RotateCcw
} from 'lucide-react';

interface TimelineHorizontalProps {
  sections: any[];
  setSections: (sections: any[]) => void;
  selectedSection: string;
  setSelectedSection: (sectionId: string) => void;
  isPlaying: boolean;
  setIsPlaying: (playing: boolean) => void;
  currentTime: number;
  duration: number;
}

// Types de sections avec couleurs et icônes
const SECTION_TYPES = {
  intro: { label: 'Intro', icon: Play, color: 'bg-orange-500', textColor: 'text-orange-100' },
  verse: { label: 'Couplet', icon: FileText, color: 'bg-blue-500', textColor: 'text-blue-100' },
  'pre-chorus': { label: 'Pré-refrain', icon: Target, color: 'bg-yellow-500', textColor: 'text-yellow-100' },
  chorus: { label: 'Refrain', icon: Music, color: 'bg-green-500', textColor: 'text-green-100' },
  bridge: { label: 'Pont', icon: Layers, color: 'bg-purple-500', textColor: 'text-purple-100' },
  outro: { label: 'Outro', icon: Square, color: 'bg-red-500', textColor: 'text-red-100' },
  instrumental: { label: 'Instrumental', icon: Guitar, color: 'bg-indigo-500', textColor: 'text-indigo-100' }
};

export const TimelineHorizontal: React.FC<TimelineHorizontalProps> = ({
  sections,
  setSections,
  selectedSection,
  setSelectedSection,
  isPlaying,
  setIsPlaying,
  currentTime,
  duration
}) => {
  
  const [draggedSection, setDraggedSection] = useState<string | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);

  // Calculer la durée totale
  const totalDuration = sections.reduce((total, section) => total + (section.duration || 16), 0);

  // Gestionnaire pour démarrer le drag
  const handleDragStart = useCallback((e: React.DragEvent, sectionId: string) => {
    setDraggedSection(sectionId);
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  // Gestionnaire pour le drag over
  const handleDragOver = useCallback((e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  }, []);

  // Gestionnaire pour le drop
  const handleDrop = useCallback((e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    
    if (!draggedSection) return;
    
    const draggedIndex = sections.findIndex(s => s.id === draggedSection);
    if (draggedIndex === -1 || draggedIndex === dropIndex) return;
    
    const newSections = [...sections];
    const [draggedItem] = newSections.splice(draggedIndex, 1);
    newSections.splice(dropIndex, 0, draggedItem);
    
    // Recalculer les startTime
    let currentTime = 0;
    const updatedSections = newSections.map(section => {
      const updatedSection = { ...section, startTime: currentTime };
      currentTime += section.duration || 16;
      return updatedSection;
    });
    
    setSections(updatedSections);
    setDraggedSection(null);
    setDragOverIndex(null);
  }, [sections, setSections, draggedSection]);

  // Gestionnaire pour ajouter une section
  const handleAddSection = useCallback((type: string = 'verse') => {
    const newSection = {
      id: `section-${Date.now()}`,
      type,
      title: `${SECTION_TYPES[type]?.label || 'Section'} ${sections.filter(s => s.type === type).length + 1}`,
      content: '',
      chords: [],
      duration: type === 'chorus' ? 32 : type === 'intro' || type === 'outro' ? 8 : 16,
      startTime: totalDuration
    };
    
    setSections([...sections, newSection]);
    setSelectedSection(newSection.id);
  }, [sections, setSections, setSelectedSection, totalDuration]);

  // Gestionnaire pour dupliquer une section
  const handleDuplicateSection = useCallback((sectionId: string) => {
    const sectionToDuplicate = sections.find(s => s.id === sectionId);
    if (!sectionToDuplicate) return;
    
    const newSection = {
      ...sectionToDuplicate,
      id: `section-${Date.now()}`,
      title: `${sectionToDuplicate.title} (Copie)`,
      startTime: totalDuration
    };
    
    setSections([...sections, newSection]);
  }, [sections, setSections, totalDuration]);

  // Gestionnaire pour supprimer une section
  const handleDeleteSection = useCallback((sectionId: string) => {
    if (sections.length <= 1) return;
    
    const filteredSections = sections.filter(s => s.id !== sectionId);
    
    // Recalculer les startTime
    let currentTime = 0;
    const updatedSections = filteredSections.map(section => {
      const updatedSection = { ...section, startTime: currentTime };
      currentTime += section.duration || 16;
      return updatedSection;
    });
    
    setSections(updatedSections);
    
    if (selectedSection === sectionId && updatedSections.length > 0) {
      setSelectedSection(updatedSections[0].id);
    }
  }, [sections, setSections, selectedSection, setSelectedSection]);

  // Formater le temps en MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-slate-800/50 border-b border-slate-700">
      {/* En-tête de la timeline */}
      <div className="flex items-center justify-between p-3 border-b border-slate-700">
        <div className="flex items-center gap-3">
          <Clock className="h-5 w-5 text-blue-400" />
          <div>
            <h3 className="font-medium text-white">Timeline Structure</h3>
            <p className="text-sm text-slate-400">
              {sections.length} sections • {formatTime(totalDuration)} total
            </p>
          </div>
        </div>
        
        {/* Contrôles de lecture */}
        <div className="flex items-center gap-2">
          <Button
            variant={isPlaying ? "default" : "outline"}
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
            className="gap-1"
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            {isPlaying ? 'Pause' : 'Play'}
          </Button>
          
          <div className="text-sm text-slate-400 font-mono">
            {formatTime(currentTime)} / {formatTime(duration)}
          </div>
        </div>
      </div>

      {/* Barre de progression globale */}
      <div className="p-3 border-b border-slate-700">
        <div className="relative h-2 bg-slate-700 rounded-full overflow-hidden">
          <div 
            className="absolute top-0 left-0 h-full bg-blue-500 transition-all duration-300"
            style={{ width: `${totalDuration > 0 ? (currentTime / totalDuration) * 100 : 0}%` }}
          />
        </div>
      </div>

      {/* Timeline horizontale avec sections */}
      <div className="p-3">
        <ScrollArea className="w-full">
          <div 
            ref={timelineRef}
            className="flex gap-2 min-w-max pb-2"
            style={{ minWidth: `${sections.length * 200}px` }}
          >
            {sections.map((section, index) => {
              const sectionType = SECTION_TYPES[section.type] || SECTION_TYPES.verse;
              const Icon = sectionType.icon;
              const isSelected = selectedSection === section.id;
              const isDraggedOver = dragOverIndex === index;
              const progress = totalDuration > 0 ? (currentTime - section.startTime) / (section.duration || 16) : 0;
              const isCurrentlyPlaying = progress >= 0 && progress <= 1;
              
              return (
                <div
                  key={section.id}
                  className={`relative transition-all ${isDraggedOver ? 'transform scale-105' : ''}`}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDrop={(e) => handleDrop(e, index)}
                  style={{ minWidth: '180px', maxWidth: '220px' }}
                >
                  <Card 
                    className={`cursor-pointer transition-all h-24 ${
                      isSelected 
                        ? `${sectionType.color} border-white/20` 
                        : 'bg-slate-700/50 border-slate-600 hover:border-slate-500'
                    } ${draggedSection === section.id ? 'opacity-50' : ''}`}
                    onClick={() => setSelectedSection(section.id)}
                  >
                    <CardContent className="p-3 h-full">
                      <div className="flex items-start gap-2 h-full">
                        {/* Handle de drag */}
                        <div
                          className="cursor-grab active:cursor-grabbing text-slate-400 hover:text-white mt-1"
                          draggable
                          onDragStart={(e) => handleDragStart(e, section.id)}
                        >
                          <GripVertical className="h-4 w-4" />
                        </div>

                        {/* Contenu de la section */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <div className={`p-1 rounded ${isSelected ? 'bg-white/20' : 'bg-slate-600'}`}>
                              <Icon className={`h-3 w-3 ${isSelected ? sectionType.textColor : 'text-white'}`} />
                            </div>
                            <span className={`font-medium text-xs truncate ${isSelected ? sectionType.textColor : 'text-white'}`}>
                              {section.title}
                            </span>
                          </div>
                          
                          <div className="text-xs mb-1">
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${isSelected ? 'border-white/30 text-white' : ''}`}
                            >
                              {sectionType.label}
                            </Badge>
                          </div>
                          
                          <div className="flex items-center justify-between text-xs">
                            <span className={isSelected ? sectionType.textColor : 'text-slate-400'}>
                              {section.duration || 16}s
                            </span>
                            {section.chords && section.chords.length > 0 && (
                              <div className="flex items-center gap-1">
                                <Guitar className="h-3 w-3" />
                                <span>{section.chords.length}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex flex-col gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDuplicateSection(section.id);
                            }}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteSection(section.id);
                            }}
                            className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
                            disabled={sections.length <= 1}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Barre de progression de la section */}
                      {isCurrentlyPlaying && (
                        <div className="absolute bottom-1 left-3 right-3 h-1 bg-black/20 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-white transition-all duration-300"
                            style={{ width: `${Math.max(0, Math.min(100, progress * 100))}%` }}
                          />
                        </div>
                      )}

                      {/* Accords de la section */}
                      {section.chords && section.chords.length > 0 && (
                        <div className="absolute top-1 right-1">
                          <div className="flex gap-1">
                            {section.chords.slice(0, 3).map((chord, i) => (
                              <Badge 
                                key={i} 
                                variant="outline" 
                                className="text-xs h-4 px-1"
                              >
                                {chord.name}
                              </Badge>
                            ))}
                            {section.chords.length > 3 && (
                              <Badge variant="outline" className="text-xs h-4 px-1">
                                +{section.chords.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              );
            })}

            {/* Bouton d'ajout de section */}
            <div className="flex items-center" style={{ minWidth: '120px' }}>
              <Button
                variant="outline"
                onClick={() => handleAddSection('verse')}
                className="h-24 w-full border-dashed border-slate-500 hover:border-slate-400 flex flex-col gap-2"
              >
                <Plus className="h-6 w-6" />
                <span className="text-xs">Ajouter Section</span>
              </Button>
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* Actions d'ajout rapide */}
      <div className="border-t border-slate-700 p-3">
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-slate-400 mr-2">Ajouter:</span>
          {Object.entries(SECTION_TYPES).map(([type, config]) => {
            const Icon = config.icon;
            return (
              <Button
                key={type}
                variant="outline"
                size="sm"
                onClick={() => handleAddSection(type)}
                className={`gap-1 text-xs ${config.color} ${config.textColor} border-white/20`}
              >
                <Icon className="h-3 w-3" />
                {config.label}
              </Button>
            );
          })}
        </div>
      </div>
    </div>
  );
};
