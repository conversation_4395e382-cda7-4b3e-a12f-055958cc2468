import React from 'react';

interface ConfirmDeleteVersionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  versionName?: string | null;
  versionNumber?: number;
  createdAt?: string; // Added to support displaying creation date
  isDeleting: boolean;
}

const ConfirmDeleteVersionModal: React.FC<ConfirmDeleteVersionModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  versionName,
  versionNumber,
  isDeleting,
}) => {
  if (!isOpen) return null;

  return (
    <div style={{ position: 'fixed', top: '0', left: '0', right: '0', bottom: '0', backgroundColor: 'rgba(0,0,0,0.5)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <div style={{ background: 'white', padding: '20px', borderRadius: '8px' }}>
        <h2>Confirmer la Suppression</h2>
        <p>
          Êtes-vous sûr de vouloir supprimer la version {versionNumber}
          {versionName ? ` (${versionName})` : ''} ?
        </p>
        <div style={{ marginTop: '20px', display: 'flex', justifyContent: 'flex-end' }}>
          <button onClick={onClose} disabled={isDeleting} style={{ marginRight: '10px' }}>Annuler</button>
          <button onClick={onConfirm} disabled={isDeleting} style={{ backgroundColor: 'red', color: 'white' }}>
            {isDeleting ? 'Suppression...' : 'Confirmer'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDeleteVersionModal;
