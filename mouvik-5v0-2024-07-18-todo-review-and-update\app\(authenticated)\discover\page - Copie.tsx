import { Suspense } from "react"
import Link from "next/link"
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { Music, Play, Filter, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { GenreCard } from "@/components/discover/genre-card"
import { FeaturedArtistCard } from "@/components/discover/featured-artist-card"
import { PlaylistCard } from "@/components/discover/playlist-card"
import { AlbumCard } from "@/components/discover/album-card"
import { TrendingTrack } from "@/components/discover/trending-track"

export default async function DiscoverPage() {
  const supabase = createSupabaseServerClient()

  // Fetch featured playlists
  const { data: featuredPlaylists } = await supabase
    .from("playlists")
    .select("*")
    .eq("is_public", true)
    .order("created_at", { ascending: false })
    .limit(5)

  // Fetch new releases
  const { data: newReleases } = await supabase
    .from("albums")
    .select("*")
    .eq("visibility", "public")
    .order("release_date", { ascending: false })
    .limit(4)

  // Fetch trending tracks
  const { data: trendingTracks } = await supabase
    .from("songs")
    .select("*, profiles(name, avatar_url)")
    .order("plays", { ascending: false })
    .limit(5)

  // Fetch featured artists
  const { data: featuredArtists } = await supabase.from("profiles").select("*, songs(count)").limit(5)

  const genres = [
    { id: 1, name: "Électronique", count: 845, image: "/electronic-music-concert.png" },
    { id: 2, name: "Jazz", count: 523, image: "/lively-jazz-concert.png" },
    { id: 3, name: "Rock", count: 678, image: "/rock-concert.png" },
    { id: 4, name: "Hip-Hop", count: 763, image: "/hip-hop-concert.png" },
    { id: 5, name: "Classique", count: 412, image: "/placeholder.svg?key=no59c" },
    { id: 6, name: "Acoustique", count: 389, image: "/placeholder.svg?key=q4qq3" },
    { id: 7, name: "Indie", count: 476, image: "/placeholder.svg?key=d8pb4" },
    { id: 8, name: "Ambient", count: 342, image: "/placeholder.svg?key=hpda0" },
  ]

  const playlists = [
    {
      id: 1,
      title: "Mix découverte semaine",
      description: "Basé sur vos écoutes récentes",
      image: "/placeholder.svg?key=jk0ly",
    },
    {
      id: 2,
      title: "Jazz moderne",
      description: "Contemporain et progressif",
      image: "/placeholder.svg?height=300&width=300&query=modern jazz",
    },
    {
      id: 3,
      title: "Electro Focus",
      description: "Concentration et productivité",
      image: "/placeholder.svg?height=300&width=300&query=electronic focus music",
    },
    {
      id: 4,
      title: "Acoustic Vibes",
      description: "Ambiance détendue",
      image: "/placeholder.svg?height=300&width=300&query=acoustic vibes",
    },
    {
      id: 5,
      title: "Rock Classics",
      description: "Les grands classiques",
      image: "/placeholder.svg?height=300&width=300&query=rock classics",
    },
    {
      id: 6,
      title: "Hip-Hop Hits",
      description: "Les tubes du moment",
      image: "/placeholder.svg?height=300&width=300&query=hip hop hits",
    },
  ]

  // Filter out error objects for playlists, releases, tracks
  const validPlaylists = Array.isArray(featuredPlaylists) ? featuredPlaylists.filter(p => p && typeof p === 'object' && 'id' in p) : []
  const validReleases = Array.isArray(newReleases) ? newReleases.filter(r => r && typeof r === 'object' && 'id' in r) : []
  const validTracks = Array.isArray(trendingTracks) ? trendingTracks.filter(t => t && typeof t === 'object' && 'id' in t) : []

  return (
    <div className="flex flex-col gap-8 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Découvrir</h1>
          <p className="text-muted-foreground">Explorez de nouvelles musiques adaptées à vos goûts</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Input type="search" placeholder="Rechercher de la musique..." className="w-[250px] pl-8" />
            <Music className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex flex-wrap gap-2">
        <Badge variant="outline" className="bg-primary/10 hover:bg-primary/20">
          Tout
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Pop
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Rock
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Hip Hop
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Électronique
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Jazz
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Classique
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Alternative
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          R&B
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Indie
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Metal
        </Badge>
        <Badge variant="outline" className="hover:bg-primary/10">
          Folk
        </Badge>
      </div>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Créé pour vous</h2>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/discover/for-you">
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
          {playlists.map((playlist) => (
            <PlaylistCard key={playlist.id} playlist={playlist} />
          ))}
        </div>
      </section>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Nouveautés</h2>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/discover/new-releases">
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Suspense fallback={<AlbumCardSkeleton count={4} />}>
            {(validReleases || []).map((album) => (
              <AlbumCard key={album.id} album={album} />
            ))}
          </Suspense>
        </div>
      </section>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Catégories</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {genres.map((genre) => (
            <GenreCard key={genre.id} genre={genre} />
          ))}
        </div>
      </section>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Artistes & Groupes</h2>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/discover/artists">
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Suspense fallback={<ArtistCardSkeleton count={5} />}>
            {(featuredArtists || []).map((artist) => (
              <FeaturedArtistCard key={artist.id} artist={artist} />
            ))}
          </Suspense>
        </div>
      </section>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Tendances</h2>
          <Button variant="ghost" size="sm" asChild>
            <Link href="/discover/trending">
              Voir tout <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
        </div>
        <div className="flex flex-col gap-2">
          <Suspense fallback={<TrendingTrackSkeleton count={5} />}>
            {(validTracks || []).map((track, index) => (
              <TrendingTrack key={track.id} track={track} position={index + 1} />
            ))}
          </Suspense>
        </div>
      </section>

      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Artiste en vedette</h2>
        </div>
        <div className="relative w-full h-[300px] rounded-lg overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent z-10"></div>
          <img
            src="/placeholder.svg?height=600&width=1200&query=electronic music artist performing"
            alt="Artiste en vedette"
            className="w-full h-full object-cover"
          />
          <div className="absolute bottom-0 left-0 p-6 z-20 text-white">
            <h3 className="text-3xl font-bold mb-2">Quantum Echoes</h3>
            <p className="text-lg mb-4 max-w-2xl">
              Quantum Echoes est un groupe de rock électronique formé en 2018 à Paris. Leur musique fusionne des
              éléments de rock alternatif avec des sonorités électroniques modernes, créant une expérience sonore unique
              et immersive.
            </p>
            <div className="flex gap-2">
              <Button>
                <Play className="mr-2 h-4 w-4" />
                Écouter
              </Button>
              <Button variant="outline" className="bg-white/10 hover:bg-white/20 border-white/20">
                Suivre
              </Button>
            </div>
          </div>
        </div>
        <div className="mt-4">
          <h3 className="text-lg font-medium mb-2">Titres populaires</h3>
          <div className="flex flex-col gap-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between p-2 rounded-md hover:bg-accent">
                <div className="flex items-center gap-3">
                  <div className="w-6 text-center text-muted-foreground">{i}</div>
                  <img
                    src="/placeholder.svg?height=40&width=40&query=album cover"
                    alt="Album cover"
                    className="w-10 h-10 rounded"
                  />
                  <div>
                    <p className="font-medium">Digital Horizon</p>
                    <p className="text-sm text-muted-foreground">Quantum Echoes • 2023</p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-muted-foreground">4:32</span>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Play className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
          <Button variant="ghost" size="sm" className="mt-2">
            Voir plus
          </Button>
        </div>
      </section>
    </div>
  )
}

function AlbumCardSkeleton({ count = 4 }) {
  return Array(count)
    .fill(0)
    .map((_, i) => (
      <Card key={i} className="overflow-hidden">
        <Skeleton className="h-[200px]" />
        <CardContent className="p-4">
          <Skeleton className="h-5 w-3/4 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </CardContent>
      </Card>
    ))
}

function ArtistCardSkeleton({ count = 5 }) {
  return Array(count)
    .fill(0)
    .map((_, i) => (
      <div key={i} className="flex flex-col items-center gap-2">
        <Skeleton className="h-24 w-24 rounded-full" />
        <Skeleton className="h-5 w-20" />
        <Skeleton className="h-4 w-16" />
      </div>
    ))
}

function TrendingTrackSkeleton({ count = 5 }) {
  return Array(count)
    .fill(0)
    .map((_, i) => (
      <div key={i} className="flex items-center justify-between p-2">
        <div className="flex items-center gap-3">
          <Skeleton className="h-6 w-6" />
          <Skeleton className="h-12 w-12" />
          <div>
            <Skeleton className="h-5 w-32 mb-1" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
        <Skeleton className="h-8 w-16" />
      </div>
    ))
}
