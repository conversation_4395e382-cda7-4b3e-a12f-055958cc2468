'use client';
import React, { useState, useCallback } from 'react';
import { Control, FieldErrors } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SimplifiedAIAssistant } from './SimplifiedAIAssistant';
import type Quill from 'quill';
import { SongFormValues, AiConfig, AiHistoryItem } from './song-schema';
import {
  Music,
  Wand2,
  ArrowRight,
  Edit3,
  Sparkles,
  FileText,
  Guitar,
  Zap
} from 'lucide-react';
import { useRouter } from 'next/navigation';

// Import du nouveau système Enhanced Lyrics Editor
import { LyricsChordWorkflow, type ChordPlacement } from '@/components/enhanced-lyrics-editor';
import { ChordSystemProvider } from '@/components/chord-system/providers/ChordSystemProvider';


// Simplified chord detection
const parseChords = (text: string): string[] => {
  if (!text) return [];
  const chordRegex = /\b([A-Ga-g][#b]?(m|maj|min|dim|aug|sus|add|\d)*)\b/g;
  const matches = text.match(chordRegex) || [];
  return Array.from(new Set(matches)).filter(chord => chord.length > 0 && chord.length < 7);
};

interface SongFormLyricsChordTabProps {
  control: Control<SongFormValues>;
  errors: FieldErrors<SongFormValues>;
  lyricsContent: string;
  onLyricsChange: (value: string, editorContent?: any) => void;
  quillRef: React.RefObject<Quill | null>;
  aiConfig: AiConfig;
  aiGeneralPrompt: string;
  addAiHistory: (userPrompt: string, assistantResponse: string) => void;
  aiHistory: AiHistoryItem[];
  showAiHistory: boolean;
  setShowAiHistory: (show: boolean) => void;
  formControl: Control<SongFormValues>;
  songId?: string;
}

export const SongFormLyricsChordTab: React.FC<SongFormLyricsChordTabProps> = ({
  control,
  errors,
  lyricsContent,
  onLyricsChange,
  quillRef,
  aiConfig,
  aiGeneralPrompt,
  addAiHistory,
  aiHistory,
  showAiHistory,
  setShowAiHistory,
  formControl,
  songId
}) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('enhanced-editor');
  const [chordPlacements, setChordPlacements] = useState<ChordPlacement[]>([]);

  const detectedChords = React.useMemo(() => parseChords(lyricsContent), [lyricsContent]);

  const handleNavigateToAIComposer = useCallback(() => {
    if (songId) {
      router.push(`/ai-composer-workspace?songId=${songId}`);
    } else {
      router.push('/ai-composer-workspace');
    }
  }, [songId, router]);

  // Gestionnaire pour les changements du nouveau système Enhanced Lyrics Editor
  const handleEnhancedLyricsChange = useCallback((
    newLyrics: string,
    newChordPlacements: ChordPlacement[]
  ) => {
    onLyricsChange(newLyrics);
    setChordPlacements(newChordPlacements);
  }, [onLyricsChange]);


  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Paroles et Accords
          </CardTitle>
          <Button 
            onClick={handleNavigateToAIComposer}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            size="sm"
          >
            <Wand2 className="h-4 w-4 mr-2" />
            AI Composer
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <Edit3 className="h-4 w-4" />
              Éditeur
            </TabsTrigger>
            <TabsTrigger value="ai-assistant" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Assistant IA
            </TabsTrigger>
            <TabsTrigger value="chords" className="flex items-center gap-2">
              <Music className="h-4 w-4" />
              Accords
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="editor" className="mt-6">
            <div className="space-y-4">
              <Textarea
                value={lyricsContent}
                onChange={(e) => onLyricsChange(e.target.value)}
                placeholder="Écrivez vos paroles ici..."
                className="min-h-[400px] font-mono text-sm leading-relaxed"
              />
              {errors.lyrics && (
                <p className="text-sm text-red-500">{errors.lyrics.message}</p>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="ai-assistant" className="mt-6">
            <SimplifiedAIAssistant
              songId={songId}
              currentLyrics={lyricsContent}
              onSuggestionApply={(suggestion, type) => {
                if (type === 'lyrics') {
                  onLyricsChange(suggestion);
                }
              }}
              onLyricsChange={onLyricsChange}
              onChordsChange={(chords) => {
                // Intégrer les accords dans les paroles existantes
                const currentLines = lyricsContent.split('\n');
                const chordsLine = `[${chords}]`;
                const updatedLyrics = currentLines.length > 0 
                  ? `${chordsLine}\n${lyricsContent}`
                  : chordsLine;
                onLyricsChange(updatedLyrics);
              }}
            />
          </TabsContent>
          
          <TabsContent value="chords" className="mt-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Accords détectés</h3>
                <Badge variant="secondary">{detectedChords.length} accords</Badge>
              </div>
              
              {detectedChords.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {detectedChords.map(chord => (
                    <Badge key={chord} variant="outline" className="text-sm">
                      {chord}
                    </Badge>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Music className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucun accord détecté dans les paroles</p>
                  <p className="text-sm">Utilisez l'AI Composer pour générer des accords</p>
                </div>
              )}
              
              <div className="pt-4 border-t">
                <Button 
                  onClick={handleNavigateToAIComposer}
                  variant="outline"
                  className="w-full"
                >
                  <Wand2 className="h-4 w-4 mr-2" />
                  Ouvrir l'AI Composer pour les accords
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};