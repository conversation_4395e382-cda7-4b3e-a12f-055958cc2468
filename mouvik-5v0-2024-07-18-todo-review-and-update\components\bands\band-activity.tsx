import Link from "next/link"
import { <PERSON><PERSON>ronRight, MessageSquare, Heart, Share2, Play } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface BandActivityProps {
  bandId: string
}

export function BandActivity({ bandId }: BandActivityProps) {
  // Exemple d'activités
  const activities = [
    {
      id: 1,
      user: {
        id: "user1",
        name: "<PERSON>",
        avatar: "/placeholder.svg?height=40&width=40&query=woman musician",
      },
      type: "version",
      content: 'A ajouté une nouvelle version de "Nebula Dreams - Cosmic Journey"',
      details: "Version 2.4",
      description:
        "Ajout d'une nouvelle introduction et modification de la progression d'accords du refrain pour plus d'impact",
      time: "2 heures",
      hasAudio: false,
    },
    {
      id: 2,
      user: {
        id: "user2",
        name: "<PERSON>",
        avatar: "/placeholder.svg?height=40&width=40&query=man musician",
      },
      type: "comment",
      content: 'A commenté sur "Lunar Eclipse"',
      details: "Je pense qu'on devrait ajouter plus de reverb sur les vocaux du pont, ça manque d'ambiance spatiale.",
      time: "3 heures",
      hasAudio: false,
    },
    {
      id: 3,
      user: {
        id: "user3",
        name: "Pierre Dubois",
        avatar: "/placeholder.svg?height=40&width=40&query=man producer",
      },
      type: "upload",
      content: 'A publié une maquette pour "Quantum Waves - Particle Dance"',
      details: "Quantum_Waves_v1.2.mp3",
      time: "5 heures",
      hasAudio: true,
      audioLength: "3:45",
      fileSize: "7.2 MB",
    },
  ]

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>Activité récente</CardTitle>
          <CardDescription>Dernières actions des membres</CardDescription>
        </div>
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/bands/${bandId}/activity`}>
            Voir tout <ChevronRight className="ml-1 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {activities.map((activity) => (
            <div key={activity.id} className="border-l-2 border-primary/20 pl-4 relative">
              <div className="absolute w-3 h-3 bg-primary rounded-full -left-[7px] top-1.5" />

              <div className="flex items-center gap-2 mb-1">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={activity.user.avatar || "/placeholder.svg"} />
                  <AvatarFallback>{activity.user.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <span className="font-medium">{activity.user.name}</span>
                <span className="text-xs text-muted-foreground">• il y a {activity.time}</span>
              </div>

              <p className="mb-2">{activity.content}</p>

              {activity.type === "version" && (
                <div className="bg-muted p-3 rounded-md text-sm">
                  <div className="flex justify-between mb-1">
                    <span className="font-medium">{activity.details}</span>
                    <Button variant="ghost" size="sm">
                      Voir les changements
                    </Button>
                  </div>
                  <p>{activity.description}</p>
                </div>
              )}

              {activity.type === "comment" && (
                <div className="bg-muted p-3 rounded-md text-sm">
                  <p>{activity.details}</p>
                  <div className="flex items-center gap-4 mt-2">
                    <Button variant="ghost" size="sm" className="h-7 px-2">
                      <Heart className="h-3.5 w-3.5 mr-1" />
                      <span>J'aime</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="h-7 px-2">
                      <MessageSquare className="h-3.5 w-3.5 mr-1" />
                      <span>Répondre</span>
                    </Button>
                  </div>
                </div>
              )}

              {activity.type === "upload" && activity.hasAudio && (
                <div className="bg-muted p-3 rounded-md text-sm">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
                        <Play className="h-4 w-4" />
                      </Button>
                      <div>
                        <p className="font-medium">{activity.details}</p>
                        <p className="text-xs text-muted-foreground">
                          {activity.audioLength} • {activity.fileSize}
                        </p>
                      </div>
                    </div>
                    <Button variant="ghost" size="icon" className="h-7 w-7">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="h-12 bg-black/10 rounded relative overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <svg width="100%" height="100%" viewBox="0 0 100 30">
                        <path
                          d="M0,15 Q5,5 10,15 T20,15 T30,15 T40,20 T50,10 T60,15 T70,5 T80,15 T90,20 T100,15"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1"
                          className="text-primary"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
