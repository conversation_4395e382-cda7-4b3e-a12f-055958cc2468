"use client";

import type React from "react";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AudioProvider } from "@/contexts/audio-context";
import { UserProvider } from "@/contexts/user-context"; // Added UserProvider
import { AuthPlayerWrapper } from "@/components/auth-player-wrapper";
import type { UserProfileForSidebar } from "@/components/sidebar"; // Import UserProfileForSidebar type

interface ClientLayoutProps {
  children: React.ReactNode;
  initialUser?: UserProfileForSidebar | null; // Added initialUser prop
}

export function ClientLayout({ children, initialUser }: ClientLayoutProps) {
  return (
    <UserProvider value={initialUser || null}>
      <ThemeProvider
        attribute="class"
        defaultTheme="dark"
        enableSystem
        disableTransitionOnChange
      >
        {/* Wrapper principal pour la structure flex-col et pour que ThemeProvider applique ses styles */}
        <div className="flex flex-col flex-1 min-h-0"> {/* min-h-0 est important pour flex item dans un parent flex */}
          <div className="flex-1 overflow-y-auto pb-20"> {/* Zone de contenu principale scrollable with padding for player */}
            <AudioProvider>
  <SidebarProvider>{children}</SidebarProvider>
  <AuthPlayerWrapper /> {/* Lecteur audio en bas */}
</AudioProvider>
        </div>
        </div>
        <Toaster />
      </ThemeProvider>
    </UserProvider>
  );
}
