/**
 * 🎼 CHORD GRID SYSTEM - Grille de Mesures Professionnelle
 * 
 * Système de grille pour organiser les accords en mesures
 * Interface professionnelle pour musiciens avec drag & drop
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useCallback, useMemo, useRef } from 'react';
import { 
  Plus, Play, Pause, Save, Copy, Trash2, RotateCcw, 
  Grid3X3, Music, Clock, Hash, Volume2, Edit3, 
  ChevronLeft, ChevronRight, MoreVertical
} from 'lucide-react';
import { useChordSystem } from '../providers/ChordSystemProvider';
import { ChordDiagramViewer } from './ChordDiagramViewer';
import { ChordPickerModal } from './ChordPickerModal';
import type { 
  UnifiedChordPosition, 
  ChordGridSection,
  ChordMeasure,
  ChordPlacement,
  TimeSignature 
} from '../types/chord-system';

// ============================================================================
// TYPES POUR LE GRID SYSTEM
// ============================================================================

interface ChordGridSystemProps {
  /** Sections initiales */
  initialSections?: ChordGridSection[];
  /** Signature temporelle par défaut */
  defaultTimeSignature?: TimeSignature;
  /** Tonalité par défaut */
  defaultKey?: string;
  /** Tempo par défaut */
  defaultTempo?: number;
  /** Mode d'affichage */
  viewMode?: 'compact' | 'detailed' | 'performance';
  /** Afficher les numéros de mesures */
  showMeasureNumbers?: boolean;
  /** Afficher les contrôles d'édition */
  showEditControls?: boolean;
  /** Callback lors des changements */
  onSectionsChange?: (sections: ChordGridSection[]) => void;
  /** Callback pour intégration AI Composer */
  onExportToAIComposer?: (sections: ChordGridSection[]) => void;
  /** Classe CSS personnalisée */
  className?: string;
}

interface MeasureCellProps {
  measure: ChordMeasure;
  sectionId: string;
  isSelected: boolean;
  isPlaying: boolean;
  viewMode: string;
  showMeasureNumbers: boolean;
  onMeasureClick: (sectionId: string, measureId: string) => void;
  onChordClick: (chord: UnifiedChordPosition) => void;
  onDropChord: (sectionId: string, measureId: string, chord: UnifiedChordPosition, beat: number) => void;
  onRemoveChord: (sectionId: string, measureId: string, chordIndex: number) => void;
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Cellule de mesure individuelle
 */
const MeasureCell: React.FC<MeasureCellProps> = ({
  measure,
  sectionId,
  isSelected,
  isPlaying,
  viewMode,
  showMeasureNumbers,
  onMeasureClick,
  onChordClick,
  onDropChord,
  onRemoveChord
}) => {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const chordData = e.dataTransfer.getData('application/json');
    if (chordData) {
      try {
        const chord = JSON.parse(chordData);
        // Calculer le beat selon la position dans la mesure
        const beat = 1; // Simplification : toujours sur le premier temps
        onDropChord(sectionId, measure.id, chord, beat);
      } catch (error) {
        console.error('Erreur lors du drop:', error);
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  // Organiser les accords par temps
  const chordsByBeat = useMemo(() => {
    const beats: { [beat: number]: ChordPlacement[] } = {};
    measure.chords.forEach(chordPlacement => {
      const beat = chordPlacement.beat || 1;
      if (!beats[beat]) beats[beat] = [];
      beats[beat].push(chordPlacement);
    });
    return beats;
  }, [measure.chords]);

  const totalBeats = measure.beats || 4;

  if (viewMode === 'compact') {
    return (
      <div
        className={`
          relative border rounded-lg p-2 min-h-[60px] cursor-pointer transition-all
          ${isSelected ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' : 'border-gray-300 hover:border-gray-400'}
          ${isPlaying ? 'ring-2 ring-green-400 bg-green-50' : ''}
          ${isDragOver ? 'border-blue-500 bg-blue-100' : ''}
        `}
        onClick={() => onMeasureClick(sectionId, measure.id)}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {/* Numéro de mesure */}
        {showMeasureNumbers && (
          <div className="absolute -top-2 -left-2 w-5 h-5 bg-gray-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
            {measure.number}
          </div>
        )}

        {/* Accords */}
        <div className="flex flex-wrap gap-1">
          {measure.chords.length === 0 ? (
            <div className="text-gray-400 text-sm italic">Vide</div>
          ) : (
            measure.chords.map((chordPlacement, index) => (
              <div
                key={index}
                className="group relative"
                onClick={(e) => {
                  e.stopPropagation();
                  onChordClick(chordPlacement.chord);
                }}
              >
                <span className={`
                  px-2 py-1 text-sm rounded font-medium cursor-pointer
                  ${chordPlacement.emphasis === 'strong' ? 'bg-blue-600 text-white' :
                    chordPlacement.emphasis === 'medium' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-700'
                  }
                `}>
                  {chordPlacement.chord.chord}
                </span>
                
                {/* Bouton de suppression */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onRemoveChord(sectionId, measure.id, index);
                  }}
                  className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
              </div>
            ))
          )}
        </div>
      </div>
    );
  }

  // Mode détaillé avec grille de temps
  return (
    <div
      className={`
        relative border rounded-lg p-3 min-h-[100px] cursor-pointer transition-all
        ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
        ${isPlaying ? 'ring-2 ring-green-400 bg-green-50' : ''}
        ${isDragOver ? 'border-blue-500 bg-blue-100' : ''}
      `}
      onClick={() => onMeasureClick(sectionId, measure.id)}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      {/* En-tête de mesure */}
      <div className="flex items-center justify-between mb-2">
        {showMeasureNumbers && (
          <div className="flex items-center">
            <Hash className="w-3 h-3 text-gray-500 mr-1" />
            <span className="text-sm font-medium text-gray-700">{measure.number}</span>
          </div>
        )}
        <div className="text-xs text-gray-500">
          {totalBeats} temps
        </div>
      </div>

      {/* Grille de temps */}
      <div className="grid grid-cols-4 gap-1 h-16">
        {Array.from({ length: totalBeats }, (_, beatIndex) => {
          const beat = beatIndex + 1;
          const chordsOnBeat = chordsByBeat[beat] || [];
          
          return (
            <div
              key={beat}
              className={`
                border border-dashed border-gray-200 rounded p-1 flex flex-col items-center justify-center
                ${chordsOnBeat.length > 0 ? 'bg-blue-50' : 'bg-gray-50'}
              `}
            >
              {/* Numéro du temps */}
              <div className="text-xs text-gray-400 mb-1">{beat}</div>
              
              {/* Accords sur ce temps */}
              {chordsOnBeat.map((chordPlacement, index) => (
                <div
                  key={index}
                  className="group relative"
                  onClick={(e) => {
                    e.stopPropagation();
                    onChordClick(chordPlacement.chord);
                  }}
                >
                  <span className={`
                    text-xs px-1 rounded font-medium cursor-pointer
                    ${chordPlacement.emphasis === 'strong' ? 'bg-blue-600 text-white' :
                      chordPlacement.emphasis === 'medium' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-700'
                    }
                  `}>
                    {chordPlacement.chord.chord}
                  </span>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemoveChord(sectionId, measure.id, index);
                    }}
                    className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          );
        })}
      </div>
    </div>
  );
};

/**
 * Section de grille avec en-tête
 */
const GridSection: React.FC<{
  section: ChordGridSection;
  isExpanded: boolean;
  selectedMeasure: string | null;
  playingMeasure: string | null;
  viewMode: string;
  showMeasureNumbers: boolean;
  onToggleExpanded: (sectionId: string) => void;
  onSectionUpdate: (sectionId: string, updates: Partial<ChordGridSection>) => void;
  onMeasureClick: (sectionId: string, measureId: string) => void;
  onChordClick: (chord: UnifiedChordPosition) => void;
  onDropChord: (sectionId: string, measureId: string, chord: UnifiedChordPosition, beat: number) => void;
  onRemoveChord: (sectionId: string, measureId: string, chordIndex: number) => void;
  onAddMeasure: (sectionId: string) => void;
  onRemoveSection: (sectionId: string) => void;
}> = ({
  section,
  isExpanded,
  selectedMeasure,
  playingMeasure,
  viewMode,
  showMeasureNumbers,
  onToggleExpanded,
  onSectionUpdate,
  onMeasureClick,
  onChordClick,
  onDropChord,
  onRemoveChord,
  onAddMeasure,
  onRemoveSection
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(section.name);

  const handleSaveName = () => {
    onSectionUpdate(section.id, { name: editName });
    setIsEditing(false);
  };

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      {/* En-tête de section */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => onToggleExpanded(section.id)}
              className="p-1 hover:bg-gray-200 rounded"
            >
              {isExpanded ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
            </button>
            
            {isEditing ? (
              <div className="flex items-center space-x-2">
                <input
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  className="px-2 py-1 border border-gray-300 rounded text-sm"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleSaveName();
                    if (e.key === 'Escape') setIsEditing(false);
                  }}
                  autoFocus
                />
                <button
                  onClick={handleSaveName}
                  className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                >
                  ✓
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <h3 className="font-medium text-gray-900">{section.name}</h3>
                <button
                  onClick={() => setIsEditing(true)}
                  className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded"
                >
                  <Edit3 className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {/* Métadonnées */}
            <div className="flex items-center space-x-3 text-sm text-gray-600">
              <span>{section.key}</span>
              <span>{section.tempo} BPM</span>
              <span>{section.timeSignature}</span>
              <span>{section.measures.length} mesures</span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => onAddMeasure(section.id)}
                className="p-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded"
                title="Ajouter une mesure"
              >
                <Plus className="w-4 h-4" />
              </button>
              
              <button
                onClick={() => onRemoveSection(section.id)}
                className="p-1 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded"
                title="Supprimer la section"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Contenu de section */}
      {isExpanded && (
        <div className="p-4">
          {section.measures.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Grid3X3 className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p>Aucune mesure dans cette section</p>
              <button
                onClick={() => onAddMeasure(section.id)}
                className="mt-2 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
              >
                Ajouter la première mesure
              </button>
            </div>
          ) : (
            <div className={`
              grid gap-3
              ${viewMode === 'compact' 
                ? 'grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8' 
                : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
              }
            `}>
              {section.measures.map((measure) => (
                <MeasureCell
                  key={measure.id}
                  measure={measure}
                  sectionId={section.id}
                  isSelected={selectedMeasure === measure.id}
                  isPlaying={playingMeasure === measure.id}
                  viewMode={viewMode}
                  showMeasureNumbers={showMeasureNumbers}
                  onMeasureClick={onMeasureClick}
                  onChordClick={onChordClick}
                  onDropChord={onDropChord}
                  onRemoveChord={onRemoveChord}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const ChordGridSystem: React.FC<ChordGridSystemProps> = ({
  initialSections = [],
  defaultTimeSignature = '4/4',
  defaultKey = 'C',
  defaultTempo = 120,
  viewMode = 'detailed',
  showMeasureNumbers = true,
  showEditControls = true,
  onSectionsChange,
  onExportToAIComposer,
  className = ''
}) => {
  const { state, actions } = useChordSystem();
  
  // État local
  const [sections, setSections] = useState<ChordGridSection[]>(initialSections);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(initialSections.map(s => s.id))
  );
  const [selectedMeasure, setSelectedMeasure] = useState<string | null>(null);
  const [playingMeasure, setPlayingMeasure] = useState<string | null>(null);
  const [showChordPicker, setShowChordPicker] = useState(false);
  const [currentViewMode, setCurrentViewMode] = useState(viewMode);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleSectionsUpdate = useCallback((newSections: ChordGridSection[]) => {
    setSections(newSections);
    onSectionsChange?.(newSections);
  }, [onSectionsChange]);

  const handleAddSection = useCallback(() => {
    const newSection: ChordGridSection = {
      id: crypto.randomUUID(),
      name: `Section ${sections.length + 1}`,
      measures: [],
      timeSignature: defaultTimeSignature,
      key: defaultKey,
      tempo: defaultTempo,
      order: sections.length
    };
    
    const newSections = [...sections, newSection];
    handleSectionsUpdate(newSections);
    setExpandedSections(prev => new Set([...prev, newSection.id]));
  }, [sections, defaultTimeSignature, defaultKey, defaultTempo, handleSectionsUpdate]);

  const handleRemoveSection = useCallback((sectionId: string) => {
    const newSections = sections.filter(s => s.id !== sectionId);
    handleSectionsUpdate(newSections);
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      newSet.delete(sectionId);
      return newSet;
    });
  }, [sections, handleSectionsUpdate]);

  const handleAddMeasure = useCallback((sectionId: string) => {
    const newSections = sections.map(section => {
      if (section.id === sectionId) {
        const newMeasure: ChordMeasure = {
          id: crypto.randomUUID(),
          number: section.measures.length + 1,
          chords: [],
          beats: parseInt(section.timeSignature.split('/')[0]) || 4
        };
        
        return {
          ...section,
          measures: [...section.measures, newMeasure]
        };
      }
      return section;
    });
    
    handleSectionsUpdate(newSections);
  }, [sections, handleSectionsUpdate]);

  const handleDropChord = useCallback((
    sectionId: string, 
    measureId: string, 
    chord: UnifiedChordPosition, 
    beat: number
  ) => {
    const newSections = sections.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          measures: section.measures.map(measure => {
            if (measure.id === measureId) {
              const newChordPlacement: ChordPlacement = {
                chord,
                beat,
                duration: 1,
                emphasis: 'medium'
              };
              
              return {
                ...measure,
                chords: [...measure.chords, newChordPlacement]
              };
            }
            return measure;
          })
        };
      }
      return section;
    });
    
    handleSectionsUpdate(newSections);
  }, [sections, handleSectionsUpdate]);

  const handleRemoveChord = useCallback((
    sectionId: string, 
    measureId: string, 
    chordIndex: number
  ) => {
    const newSections = sections.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          measures: section.measures.map(measure => {
            if (measure.id === measureId) {
              return {
                ...measure,
                chords: measure.chords.filter((_, index) => index !== chordIndex)
              };
            }
            return measure;
          })
        };
      }
      return section;
    });
    
    handleSectionsUpdate(newSections);
  }, [sections, handleSectionsUpdate]);

  const handleToggleExpanded = useCallback((sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  }, []);

  const handleSectionUpdate = useCallback((sectionId: string, updates: Partial<ChordGridSection>) => {
    const newSections = sections.map(section => 
      section.id === sectionId ? { ...section, ...updates } : section
    );
    handleSectionsUpdate(newSections);
  }, [sections, handleSectionsUpdate]);

  const handleChordClick = useCallback((chord: UnifiedChordPosition) => {
    actions.selectChord(chord);
  }, [actions]);

  const handleMeasureClick = useCallback((sectionId: string, measureId: string) => {
    setSelectedMeasure(selectedMeasure === measureId ? null : measureId);
  }, [selectedMeasure]);

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <div className={`chord-grid-system ${className}`}>
      {/* En-tête */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Grid3X3 className="w-6 h-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">Grille d'Accords</h2>
          <span className="ml-3 px-2 py-1 bg-gray-100 text-gray-600 text-sm rounded-full">
            {sections.length} section{sections.length !== 1 ? 's' : ''}
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {/* Contrôles de vue */}
          <div className="flex items-center border border-gray-300 rounded-lg">
            <button
              onClick={() => setCurrentViewMode('compact')}
              className={`px-3 py-1 text-sm ${currentViewMode === 'compact' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              Compact
            </button>
            <button
              onClick={() => setCurrentViewMode('detailed')}
              className={`px-3 py-1 text-sm ${currentViewMode === 'detailed' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              Détaillé
            </button>
          </div>

          {/* Actions */}
          <button
            onClick={() => setShowChordPicker(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Ajouter Accord
          </button>

          <button
            onClick={handleAddSection}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center"
          >
            <Grid3X3 className="w-4 h-4 mr-2" />
            Nouvelle Section
          </button>

          {onExportToAIComposer && (
            <button
              onClick={() => onExportToAIComposer(sections)}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center"
            >
              <Save className="w-4 h-4 mr-2" />
              Exporter
            </button>
          )}
        </div>
      </div>

      {/* Contenu principal */}
      {sections.length === 0 ? (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <Grid3X3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Grille vide</h3>
          <p className="text-gray-600 mb-4">
            Commencez par créer une section pour organiser vos accords en mesures
          </p>
          <button
            onClick={handleAddSection}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Créer la première section
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {sections.map((section) => (
            <GridSection
              key={section.id}
              section={section}
              isExpanded={expandedSections.has(section.id)}
              selectedMeasure={selectedMeasure}
              playingMeasure={playingMeasure}
              viewMode={currentViewMode}
              showMeasureNumbers={showMeasureNumbers}
              onToggleExpanded={handleToggleExpanded}
              onSectionUpdate={handleSectionUpdate}
              onMeasureClick={handleMeasureClick}
              onChordClick={handleChordClick}
              onDropChord={handleDropChord}
              onRemoveChord={handleRemoveChord}
              onAddMeasure={handleAddMeasure}
              onRemoveSection={handleRemoveSection}
            />
          ))}
        </div>
      )}

      {/* Modal de sélection d'accords */}
      <ChordPickerModal
        isOpen={showChordPicker}
        onClose={() => setShowChordPicker(false)}
        onChordSelect={(chord) => {
          // L'accord sera ajouté par drag & drop ou sélection de mesure
          setShowChordPicker(false);
        }}
        title="Sélectionner un accord pour la grille"
      />
    </div>
  );
};
