-- ============================================
-- MIGRATION SÉCURISÉE : TRACKS VERS SONGS
-- ============================================
-- Ce script doit être exécuté dans l'éditeur SQL de Supabase
-- Date: 2024-05-16
-- Auteur: <PERSON><PERSON><PERSON>
-- ============================================

-- ÉTAPE 0: Vérifications préalables
DO $$
DECLARE
    tracks_count BIGINT;
    songs_exists BOOLEAN;
BEGIN
    -- Vérifier si la table tracks existe
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'tracks'
    ) INTO songs_exists;
    
    IF NOT songs_exists THEN
        RAISE EXCEPTION 'ERREUR: La table tracks n''existe pas. Arrêt de la migration.';
    END IF;
    
    -- Compter le nombre de pistes
    SELECT COUNT(*) INTO tracks_count FROM public.tracks;
    RAISE NOTICE 'Début de la migration de % pistes vers la table songs', tracks_count;
    
    -- Vérifier si la table songs existe déjà
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'songs'
    ) INTO songs_exists;
    
    IF songs_exists THEN
        RAISE NOTICE 'ATTENTION: La table songs existe déjà. Vérifiez son contenu avant de continuer.';
    END IF;
END
$$;

-- ÉTAPE 1: Sauvegarde des données (facultatif mais recommandé)
-- Créer une sauvegarde de la table tracks
CREATE TABLE IF NOT EXISTS backup_tracks_before_migration AS TABLE public.tracks;

-- ÉTAPE 2: Création de la table songs
BEGIN;

-- D'abord, vérifier si la table songs existe déjà
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'songs') THEN
        RAISE NOTICE 'La table songs existe déjà. Suppression en cours...';
        DROP TABLE public.songs CASCADE;
    END IF;
END
$$;

-- Créer la table songs avec la structure mise à jour de tracks
CREATE TABLE public.songs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    creator_user_id UUID NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    genres TEXT[],
    moods TEXT[],
    cover_art_url TEXT,
    audio_file_path TEXT NOT NULL,
    duration_ms INTEGER,
    bpm INTEGER,
    musical_key TEXT,
    is_explicit BOOLEAN DEFAULT false,
    visibility TEXT DEFAULT 'private',
    editor_data JSONB,
    is_archived BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Contraintes
    CONSTRAINT fk_creator_user FOREIGN KEY (creator_user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Désactiver temporairement les triggers pour éviter les problèmes de contraintes
SET session_replication_role = 'replica';

-- Copier les données de tracks vers songs
INSERT INTO public.songs (
    id, creator_user_id, title, description, genres, moods, 
    cover_art_url, audio_file_path, duration_ms, bpm, musical_key, 
    is_explicit, visibility, editor_data, is_archived, created_at, updated_at
)
SELECT 
    id, creator_user_id, title, description, genres, moods, 
    cover_art_url, audio_file_path, duration_ms, bpm, musical_key, 
    is_explicit, visibility, editor_data, is_archived, created_at, updated_at
FROM public.tracks;

-- Réactiver les triggers
SET session_replication_role = 'origin';

-- Créer les index nécessaires
CREATE INDEX IF NOT EXISTS idx_songs_creator_user_id ON public.songs(creator_user_id);
CREATE INDEX IF NOT EXISTS idx_songs_created_at ON public.songs(created_at);

-- Vérifier l'intégrité des données
DO $$
DECLARE
    tracks_count BIGINT;
    songs_count BIGINT;
BEGIN
    SELECT COUNT(*) INTO tracks_count FROM public.tracks;
    SELECT COUNT(*) INTO songs_count FROM public.songs;
    
    IF tracks_count != songs_count THEN
        RAISE WARNING 'Le nombre d''enregistrements ne correspond pas: tracks=%, songs=%', 
                      tracks_count, songs_count;
        RAISE EXCEPTION 'ERREUR: La migration a échoué. Les comptes ne correspondent pas.';
    ELSE
        RAISE NOTICE 'Vérification réussie: % enregistrements migrés avec succès', songs_count;
    END IF;
END
$$;

-- Créer une vue de compatibilité pour une transition en douceur
CREATE OR REPLACE VIEW public.tracks AS
SELECT * FROM public.songs;

-- Si tout s'est bien passé, valider la transaction
COMMIT;

-- ============================================
-- INSTRUCTIONS POST-MIGRATION
-- ============================================
-- 1. Tester soigneusement l'application avec la nouvelle table songs
-- 2. Vérifier que toutes les fonctionnalités fonctionnent comme prévu
-- 3. Une fois validé, exécuter le script suivant pour finaliser la migration:
/*
-- Supprimer la vue de compatibilité (à faire uniquement après validation complète)
DROP VIEW IF EXISTS public.tracks;

-- Supprimer l'ancienne table (à faire uniquement après validation complète)
-- DROP TABLE IF EXISTS public.tracks;

-- Supprimer la sauvegarde (à faire uniquement après validation complète et sauvegarde externe)
-- DROP TABLE IF EXISTS backup_tracks_before_migration;
*/

-- En cas de problème, pour annuler la migration:
-- DROP TABLE IF EXISTS public.songs CASCADE;
