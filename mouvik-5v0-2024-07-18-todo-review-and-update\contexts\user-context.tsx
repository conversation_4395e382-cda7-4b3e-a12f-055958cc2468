"use client";

import React, { createContext, useContext, ReactNode, useMemo } from 'react';
import type { UserProfileForSidebar } from '@/components/sidebar'; // Assuming UserProfileForSidebar is exported

interface UserContextType {
  user: UserProfileForSidebar | null;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider = ({ children, value }: { children: ReactNode; value: UserProfileForSidebar | null }) => {
  const contextValue = useMemo(() => ({ user: value }), [value]);

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
