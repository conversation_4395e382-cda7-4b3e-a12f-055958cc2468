// c:\_DEV_projects\TOOL\mouvik-5v0\components\songs\SongFormHeader.tsx
import React from 'react';
import Image from 'next/image';
import { UseFormSetValue } from 'react-hook-form';
import { SongFormValues } from './song-schema';
import { FileInput } from '@/components/ui/file-input';
import { AudioWaveformPreview } from './AudioWaveformPreview';
import AudioRecorder from '@/components/AudioRecorder';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'; // Removed CardContent

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  History, PanelRightClose, PanelRightOpen,
  ImagePlus, X, 
  Save, // Added Save icon
  Trash2, UploadCloud, Mic, StopCircle, FileAudio // Added audio/recording icons
} from 'lucide-react';
import type { LocalFileState } from './hooks/useLocalFileManagement';

interface SongFormHeaderProps {
  mode: 'create' | 'edit';
  songTitle?: string;
  artistName?: string;
  onSave: () => void; // Prop for save action
  isDirty?: boolean; // Prop to indicate form changes
  onViewHistory?: () => void;
  isVaultPanelCollapsed?: boolean;
  onToggleVaultPanel?: () => void;
  lastSavedTimestamp?: Date | null; // Changed to Date | null

  // Props for cover art and audio
  coverArtUrl?: string | null;
  localCoverArtFile?: LocalFileState;
  onCoverArtSelect?: (file: File | null) => void; // Changed to match useLocalFileManagement signature
  onClearCoverArt?: () => void;
  coverArtInputRef?: React.RefObject<HTMLInputElement>;

  // Audio Management Props
  setValue: UseFormSetValue<SongFormValues>;
  localAudioFile: LocalFileState;
  handleAudioFileSelect: (file: File | null) => void;
  handleClearAudio: () => void;
  handleClearAllAudio: () => void;
  recordedAudioBlob: Blob | null;
  recordedAudioPreviewUrl?: string | null;
  onRecordingComplete: (audioBlob: Blob | null, previewUrl?: string | null) => void;
  onRecordingError: (error: string) => void;
  handleClearRecordedAudio: () => void;
  isSubmitting?: boolean;
}

export const SongFormHeader: React.FC<SongFormHeaderProps> = ({
  mode,
  songTitle,
  artistName,
  onViewHistory,
  isVaultPanelCollapsed,
  onToggleVaultPanel,
  lastSavedTimestamp,
  coverArtUrl,
  localCoverArtFile,
  onCoverArtSelect,
  onClearCoverArt,
  coverArtInputRef,
  // Audio props
  setValue,
  localAudioFile,
  handleAudioFileSelect,
  handleClearAudio,
  handleClearAllAudio,
  recordedAudioBlob,
  recordedAudioPreviewUrl,
  onRecordingComplete,
  onRecordingError,
  handleClearRecordedAudio,
  isSubmitting,
  onSave, // Added from props
  isDirty, // Added from props
}) => {
  console.log('[SongFormHeader] Rendering. isSubmitting:', isSubmitting, 'isDirty:', isDirty);

  const titleText = mode === 'create' ? 'Créer un nouveau morceau' : `Modifier: ${songTitle || 'Morceau'}`;


  return (
    <Card className="mb-8 bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600 text-white shadow-2xl overflow-hidden border-0">
      <div className="p-6 lg:p-8">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 items-start">
          {/* Cover Art Section */}
          <div className="w-full lg:w-64 xl:w-72 flex flex-col items-center space-y-3 flex-shrink-0">
            <div
              className="relative w-full aspect-square rounded-lg overflow-hidden border-2 border-dashed border-white/30 flex items-center justify-center bg-white/10 cursor-pointer group"
              onClick={() => !coverArtUrl && coverArtInputRef?.current?.click()}
              title={coverArtUrl ? "Pochette" : "Cliquer pour ajouter une pochette"}
            >
              {coverArtUrl ? (
                <>
                  <Image
                    src={coverArtUrl}
                    alt="Pochette"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 33vw, 25vw"
                    style={{ objectFit: 'cover' }}
                    priority
                  />
                  {onClearCoverArt && (
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-1 right-1 h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity z-10 p-1"
                      onClick={(e) => { e.stopPropagation(); onClearCoverArt(); }}
                      title="Supprimer la pochette"
                    >
                      <X size={16} />
                    </Button>
                  )}
                </>
              ) : (
                <div className="text-center text-white/70 p-2">
                  <ImagePlus size={32} className="mx-auto mb-2" />
                  <p className="text-sm font-medium">Ajouter Pochette</p>
                </div>
              )}
            </div>
            <Input
              type="file"
              className="hidden"
              ref={coverArtInputRef}
              onChange={(e) => {
                if (onCoverArtSelect && e.target.files && e.target.files[0]) {
                  onCoverArtSelect(e.target.files[0]);
                } else if (onCoverArtSelect) {
                  onCoverArtSelect(null); // Clear if no file is selected
                }
              }}
              accept="image/*"
            />
            {localCoverArtFile?.file?.name && !coverArtUrl && (
              <p className="text-xs text-white/80 truncate w-full text-center" title={localCoverArtFile.file?.name}>
                Prêt: {localCoverArtFile.file?.name}
              </p>
            )}
          </div>

          {/* Main Info and Audio Section */}
          <div className="flex-grow space-y-6 min-w-0">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
              <div className="min-w-0 flex-grow">
                <CardTitle className="text-2xl lg:text-3xl xl:text-4xl font-bold text-shadow mb-2" title={titleText}>
                  <span className="block truncate">{titleText}</span>
                </CardTitle>
                {artistName && (
                  <CardDescription className="text-lg lg:text-xl text-white/90 text-shadow-sm" title={artistName}>
                    <span className="block truncate">par {artistName}</span>
                  </CardDescription>
                )}
              </div>
              <div className="flex-shrink-0">
                <Button
                  onClick={onSave}
                  disabled={isSubmitting || !isDirty}
                  size="lg"
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-500 text-white font-semibold px-6 py-3 text-base shadow-lg transition-all duration-200 hover:shadow-xl"
                  aria-label="Enregistrer les modifications"
                >
                  <Save className="mr-2 h-5 w-5" />
                  Enregistrer
                </Button>
              </div>
            </div>

            {/* Audio Section */}
            <div className="space-y-4 pt-6 border-t border-white/30">
              <div className="flex items-center gap-2">
                <FileAudio className="h-5 w-5 text-white/90" />
                <h3 className="text-lg font-semibold text-white/95">Fichier Audio Principal</h3>
              </div>
              
              {/* Audio Preview avec bouton Supprimer */}
              {(localAudioFile.previewUrl || recordedAudioBlob) && (
                <Card className="bg-white/10 border-white/20 backdrop-blur-sm">
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <FileAudio className="h-4 w-4 text-white/90" />
                        <span className="text-sm font-medium text-white/90">
                          {recordedAudioBlob ? 'Enregistrement audio' : 'Fichier uploadé'}
                        </span>
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="bg-red-500/20 hover:bg-red-500/30 border-red-500/50 text-red-300 hover:text-red-200 h-8 w-8 p-0"
                        onClick={handleClearAllAudio}
                        disabled={isSubmitting}
                        title="Supprimer le fichier audio"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {localAudioFile.previewUrl && (
                      <AudioWaveformPreview 
                        audioUrl={localAudioFile.previewUrl} 
                        audioFileName={localAudioFile.file?.name || recordedAudioBlob ? "Enregistrement" : "Aperçu audio"} 
                        waveColor="#A78BFA"
                        progressColor="#8B5CF6"
                        cursorColor="#FFFFFF"
                        height={60}
                      />
                    )}
                    
                    <p className="text-xs text-white/70 mt-2">
                      {recordedAudioBlob ? 'Enregistrement terminé' : `Fichier: ${localAudioFile.file?.name}`}
                    </p>
                  </div>
                </Card>
              )}

              {/* Upload et Record - Cachés quand un fichier est présent */}
              {!localAudioFile.previewUrl && !recordedAudioBlob && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Card Upload */}
                  <Card className="bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/15 transition-all duration-200 cursor-pointer group">
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <UploadCloud className="h-4 w-4 text-white/90 group-hover:text-white transition-colors" />
                        <span className="text-sm font-medium text-white/90 group-hover:text-white transition-colors">Téléverser</span>
                      </div>
                      <FileInput
                        id="audioFileHeader"
                        label="Choisir un fichier audio"
                        onFileSelect={handleAudioFileSelect}
                        currentFile={localAudioFile.file}
                        onClear={handleClearAudio}
                        accept="audio/*"
                        disabled={isSubmitting}
                        buttonText="Parcourir"
                        clearButtonText="Retirer"
                        className="text-white placeholder-white/50 [&>label]:text-white/90 [&_button]:bg-white/10 [&_button]:hover:bg-white/20 [&_button]:border-white/30 [&_button:disabled]:opacity-70"
                      />
                      <p className="text-xs text-white/60 mt-2">MP3, WAV, FLAC...</p>
                    </div>
                  </Card>

                  {/* Card Record */}
                  <Card className="bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/15 transition-all duration-200 group">
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Mic className="h-4 w-4 text-white/90 group-hover:text-white transition-colors" />
                        <span className="text-sm font-medium text-white/90 group-hover:text-white transition-colors">Enregistrer</span>
                      </div>
                      <AudioRecorder
                        onRecordingComplete={onRecordingComplete}
                        onRecordingError={onRecordingError}
                        disabled={isSubmitting}
                      />
                      <p className="text-xs text-white/60 mt-2">Enregistrement direct</p>
                    </div>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer with actions and info */}
        <div className="mt-6 pt-6 border-t border-white/30 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="text-sm text-white/80 font-medium">
            {lastSavedTimestamp ? (
              <span className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                Dernière sauvegarde: {lastSavedTimestamp.toLocaleString()}
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                {mode === 'edit' ? 'Modifications non enregistrées' : 'Nouveau morceau'}
              </span>
            )}
          </div>
          <div className="flex items-center gap-3">
            {mode === 'edit' && onViewHistory && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onViewHistory} 
                className="bg-white/10 hover:bg-white/20 border-white/40 text-white hover:text-white transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <History className="mr-2 h-4 w-4" />
                Historique
              </Button>
            )}
            {onToggleVaultPanel && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onToggleVaultPanel} 
                title={isVaultPanelCollapsed ? "Ouvrir Panneau Versions" : "Fermer Panneau Versions"} 
                className="bg-white/10 hover:bg-white/20 border-white/40 text-white hover:text-white transition-all duration-200 shadow-md hover:shadow-lg"
              >
                {isVaultPanelCollapsed ? <PanelRightOpen className="h-4 w-4" /> : <PanelRightClose className="h-4 w-4" />}
                <span className="ml-2 hidden sm:inline">{isVaultPanelCollapsed ? "Versions" : "Fermer"}</span>
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SongFormHeader;
