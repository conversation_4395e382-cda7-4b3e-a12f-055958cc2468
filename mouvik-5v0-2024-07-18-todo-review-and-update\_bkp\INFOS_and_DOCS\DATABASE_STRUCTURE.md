# Database Structure Summary

This document outlines the proposed database structure for key entities in the Mouvik v2 application.

## Table: Users (Profiles)

Represents user accounts and profiles.

| Field Name      | Data Type     | Constraints/Notes                      | Description                                  |
|-----------------|---------------|----------------------------------------|----------------------------------------------|
| `user_id`       | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the user               |
| `username`      | VARCHAR(50)   | UNIQUE, NOT NULL                       | User's chosen username                       |
| `email`         | VARCHAR(255)  | UNIQUE, NOT NULL                       | User's email address (for login/contact)     |
| `password_hash` | VARCHAR(255)  | NOT NULL                               | Hashed password                              |
| `display_name`  | VARCHAR(100)  |                                        | Public display name                          |
| `bio`           | TEXT          |                                        | User's biography/description                 |
| `profile_picture_url` | VARCHAR(255) |                                  | URL to the profile picture                   |
| `banner_url`    | VARCHAR(255)  |                                        | URL to the profile banner image              |
| `location`      | VARCHAR(100)  |                                        | User's location                              |
| `website_url`   | VARCHAR(255)  |                                        | Link to user's personal website              |
| `user_type`     | VARCHAR(20)   | DEFAULT 'listener'                     | Type of user (e.g., 'listener', 'artist')    |
| `created_at`    | TIMESTAMPZ    | DEFAULT NOW()                          | Timestamp of account creation                |
| `updated_at`    | TIMESTAMPZ    | DEFAULT NOW()                          | Timestamp of last profile update             |
| `last_login`    | TIMESTAMPZ    |                                        | Timestamp of last login                      |
| `is_verified`   | BOOLEAN       | DEFAULT FALSE                          | Indicates if the account is verified         |
| `settings`      | JSONB         |                                        | User-specific settings (privacy, prefs)    |
| `is_artist`     | BOOLEAN       | DEFAULT FALSE                          | Indicates if the user has an artist profile  |
| `stage_name`    | VARCHAR(100)  |                                        | User's artist name (if applicable)           |
| `genres`        | TEXT[]        |                                        | Genres associated with the artist            |
| `instruments`   | TEXT[]        |                                        | Instruments played by the artist             |
| `social_links`  | JSONB         |                                        | Links to social media profiles (e.g., {'spotify': 'url'}) |
| `privacy_settings` | JSONB     |                                        | User privacy preferences (e.g., {'profile_visibility': 'public'}) |

## Table: Artists

Stores information specifically about artists (can link to Users).

| Field Name      | Data Type     | Constraints/Notes        | Description                                  |
|-----------------|---------------|--------------------------|----------------------------------------------|
| `artist_id`     | UUID          | PRIMARY KEY              | Unique identifier for the artist             |
| `user_id`       | UUID          | FOREIGN KEY (Users)      | Link to the corresponding user account       |
| `artist_name`   | VARCHAR(100)  | NOT NULL                 | Official artist name                         |
| `genre`         | VARCHAR(100)  |                          | Primary music genre                          |
| `label`         | VARCHAR(100)  |                          | Record label affiliation                     |
| `origin_country`| VARCHAR(50)   |                          | Artist's country of origin                   |
| `members`       | TEXT[]        |                          | List of band members (if applicable)         |
| `management_contact` | VARCHAR(255) |                      | Contact info for management                  |
| `booking_contact`    | VARCHAR(255) |                      | Contact info for booking                     |
| `created_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of artist profile creation         |
| `updated_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last artist profile update      |

*Note: An artist might be a specific type of user or a separate entity linked to a user.*

## Table: Albums

Represents a collection of music resources (e.g., an album, EP).

| Field Name      | Data Type     | Constraints/Notes        | Description                                  |
|-----------------|---------------|--------------------------|----------------------------------------------|
| `album_id`      | UUID          | PRIMARY KEY              | Unique identifier for the album              |
| `user_id`       | UUID          | FOREIGN KEY (Users)      | The user who owns the album                   |
| `band_id`       | UUID          | FOREIGN KEY (Bands), NULLABLE | The band associated with the album (if any)    |
| `title`         | TEXT          | NOT NULL                 | Title of the album                            |
| `description`   | TEXT          |                          | Description of the album                      |
| `cover_art_url` | TEXT          |                          | URL for the album's cover artwork            |
| `release_date`  | DATE          |                          | Official release date of the album            |
| `genre`         | TEXT          |                          | Primary genre of the album                    |
| `visibility`    | TEXT          | DEFAULT 'private'        | Visibility status ('public', 'private', 'unlisted') |
| `monetization`  | JSONB         |                          | Monetization settings (e.g., price, tiers)   |
| `created_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of album creation                  |
| `updated_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last update                     |

## Table: Album_Resources

Associates Music_Resources with Albums, defining the tracklist and order.

| Field Name    | Data Type     | Constraints/Notes        | Description                                  |
|---------------|---------------|--------------------------|----------------------------------------------|
| `album_id`    | UUID          | PRIMARY KEY, FOREIGN KEY (Albums) | Identifier for the album                     |
| `resource_id` | UUID          | PRIMARY KEY, FOREIGN KEY (Music_Resources) | Identifier for the music resource (track)    |
| `track_number`| INTEGER       | NOT NULL                 | Order of the track within the album          |
| `added_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp when the track was added to the album |

## Table: Songs (public.songs)

Represents individual songs or musical pieces, consolidating and replacing the previous `Music_Resources` and `Morceaux (Tracks/Songs)` concepts.

| Field Name        | Data Type                 | Constraints/Notes                                      | Description                                                |
|-------------------|---------------------------|--------------------------------------------------------|------------------------------------------------------------|
| `id`              | UUID                      | PRIMARY KEY, DEFAULT uuid_generate_v4()                | Unique identifier for the song                             |
| `creator_user_id` | UUID                      | NULLABLE, FOREIGN KEY (profiles.id) ON DELETE SET NULL | User who created/owns the song                             |
| `album_id`        | UUID                      | NULLABLE, FOREIGN KEY (albums.id) ON DELETE SET NULL   | Album this song belongs to (if any)                        |
| `band_id`         | UUID                      | NULLABLE, FOREIGN KEY (bands.id) ON DELETE SET NULL    | Band associated with the song (if any)                     |
| `title`           | TEXT                      | NOT NULL                                               | Title of the song                                          |
| `artist`          | TEXT                      | NULLABLE                                               | Primary artist(s) of the song (display name)               |
| `duration_ms`     | INTEGER                   | NULLABLE                                               | Duration of the song in milliseconds                       |
| `genre`           | TEXT                      | NULLABLE                                               | Main genre(s) of the song (e.g., "Rock", "Pop")            |
| `subgenre`        | TEXT                      | NULLABLE                                               | Subgenre(s) (e.g., "Indie Rock", "Synth Pop")          |
| `mood`            | TEXT                      | NULLABLE                                               | Mood(s) conveyed by the song (e.g., "Happy", "Sad")        |
| `theme`           | TEXT                      | NULLABLE                                               | Theme(s) of the song (e.g., "Love", "Nature")              |
| `instrumentation` | TEXT[]                    | NULLABLE                                               | Main instruments used (e.g., {"Guitar", "Drums"})          |
| `bpm`             | INTEGER                   | NULLABLE                                               | Beats Per Minute                                           |
| `key`             | TEXT                      | NULLABLE                                               | Musical key of the song (e.g., "C Major", "A minor")       |
| `audio_url`       | TEXT                      | NULLABLE                                               | URL to the main audio file (e.g., in Supabase Storage)     |
| `cover_art_url`   | TEXT                      | NULLABLE                                               | URL to the song's cover artwork                            |
| `lyrics`          | TEXT                      | NULLABLE                                               | Lyrics of the song                                         |
| `slug`           | TEXT                      | NULLABLE, UNIQUE                                       | URL-friendly identifier for the song, used for public access. |
| `credits`         | JSONB                     | NULLABLE                                               | Detailed credits (writers, producers, featured artists)    |
| `tags`            | TEXT[]                    | NULLABLE                                               | Descriptive tags for searching/filtering                   |
| `release_date`    | DATE                      | NULLABLE                                               | Official release date of the song                          |
| `is_public`       | BOOLEAN                   | NULLABLE, DEFAULT TRUE                                 | Is the song publicly visible?                              |
| `is_explicit`     | BOOLEAN                   | NULLABLE, DEFAULT FALSE                                | Does the song contain explicit content?                    |
| `visibility`      | TEXT                      | NULLABLE, DEFAULT 'public'                             | Visibility status ('public', 'private', 'unlisted')        |
| `editor_data`     | JSONB                     | NULLABLE                                               | Data from rich text editor for song description/notes      |
| `is_archived`     | BOOLEAN                   | NULLABLE, DEFAULT FALSE                                | Is the song archived (soft delete)?                        |
| `created_at`      | TIMESTAMP WITH TIME ZONE  | NULLABLE, DEFAULT NOW()                                | Timestamp of song creation                                 |
| `updated_at`      | TIMESTAMP WITH TIME ZONE  | NULLABLE, DEFAULT NOW()                                | Timestamp of last song update                              |
| `description`     | TEXT                      | NULLABLE                                               | Optional description or notes about the song               |

## Table: Resource_Collaborators

Links users or bands as collaborators on a specific music resource.

| Field Name    | Data Type     | Constraints/Notes        | Description                                  |
|---------------|---------------|--------------------------|----------------------------------------------|
| `resource_id` | UUID          | PRIMARY KEY, FOREIGN KEY (Music_Resources) | Identifier for the music resource          |
| `user_id`     | UUID          | PRIMARY KEY, FOREIGN KEY (Users), NULLABLE | Identifier for the collaborating user      |
| `band_id`     | UUID          | PRIMARY KEY, FOREIGN KEY (Bands), NULLABLE | Identifier for the collaborating band      |
| `role`        | TEXT          |                          | Role of the collaborator (e.g., 'Producer', 'Vocals', 'Writer') |
| `added_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp when the collaborator was added   |

## Table: Lyrics

Stores lyrics associated with a music resource.

| Field Name    | Data Type     | Constraints/Notes        | Description                                  |
|---------------|---------------|--------------------------|----------------------------------------------|
| `lyric_id`    | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the lyrics entry    |
| `resource_id` | UUID          | NOT NULL, FOREIGN KEY (Music_Resources), UNIQUE | Identifier for the associated music resource |
| `content`     | TEXT          | NOT NULL                 | The actual lyrics text                       |
| `language`    | TEXT          |                          | Language of the lyrics (e.g., 'en', 'es')    |
| `created_at`  | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of lyrics creation                |
| `updated_at`  | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last update                    |

## Table: Chords

Stores chord information associated with a music resource, potentially linked to lyrics or timestamps.

| Field Name    | Data Type     | Constraints/Notes        | Description                                  |
|---------------|---------------|--------------------------|----------------------------------------------|
| `chord_id`    | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the chord data     |
| `resource_id` | UUID          | NOT NULL, FOREIGN KEY (Music_Resources), UNIQUE | Identifier for the associated music resource |
| `chord_data`  | JSONB         | NOT NULL                 | Structured data for chords (e.g., [{'time': 5.2, 'chord': 'Am'}]) |
| `created_at`  | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of chord data creation           |
| `updated_at`  | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last update                    |

## Table: User_Follows (Social Relationships - RS)

Represents the following relationship between users.

| Field Name      | Data Type | Constraints/Notes                               | Description                               |
|-----------------|-----------|-------------------------------------------------|-------------------------------------------|
| `follower_id`   | UUID      | PRIMARY KEY, FOREIGN KEY (Users)                | The user who is following                   |
| `following_id`  | UUID      | PRIMARY KEY, FOREIGN KEY (Users)                | The user who is being followed              |
| `followed_at`   | TIMESTAMPZ| DEFAULT NOW()                                   | Timestamp when the follow action occurred |

*Note: `PRIMARY KEY (follower_id, following_id)` ensures a user can only follow another user once.*

## Table: User_Likes_Tracks

Represents tracks liked by users.

| Field Name    | Data Type | Constraints/Notes                               | Description                               |
|---------------|-----------|-------------------------------------------------|-------------------------------------------|
| `user_id`     | UUID      | PRIMARY KEY, FOREIGN KEY (Users)                | The user who liked the track              |
| `track_id`    | UUID      | PRIMARY KEY, FOREIGN KEY (Morceaux)             | The track that was liked                  |
| `liked_at`    | TIMESTAMPZ| DEFAULT NOW()                                   | Timestamp when the like action occurred   |

*Note: `PRIMARY KEY (user_id, track_id)`.*

## Table: Playlists

Represents user-created or system-generated playlists.

| Field Name      | Data Type     | Constraints/Notes        | Description                                  |
|-----------------|---------------|--------------------------|----------------------------------------------|
| `playlist_id`   | UUID          | PRIMARY KEY              | Unique identifier for the playlist           |
| `user_id`       | UUID          | FOREIGN KEY (Users)      | The user who owns the playlist               |
| `name`          | VARCHAR(100)  | NOT NULL                 | Name of the playlist                         |
| `description`   | TEXT          |                          | Description of the playlist                  |
| `is_public`     | BOOLEAN       | DEFAULT TRUE             | Visibility of the playlist                   |
| `cover_image_url`| VARCHAR(255) |                          | URL for a custom playlist cover image        |
| `created_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of playlist creation               |
| `updated_at`    | TIMESTAMPZ    | DEFAULT NOW()            | Timestamp of last playlist update            |

## Table: Playlist_Tracks

Associates tracks with playlists (many-to-many).

| Field Name    | Data Type | Constraints/Notes                               | Description                                |
|---------------|-----------|-------------------------------------------------|--------------------------------------------|
| `playlist_id` | UUID      | PRIMARY KEY, FOREIGN KEY (Playlists)            | The playlist                               |
| `track_id`    | UUID      | PRIMARY KEY, FOREIGN KEY (Morceaux)             | The track in the playlist                  |
| `added_at`    | TIMESTAMPZ| DEFAULT NOW()                                   | Timestamp when the track was added         |
| `track_order` | INTEGER   | NOT NULL                                        | Order of the track within the playlist     |

*Note: `PRIMARY KEY (playlist_id, track_id)`. Consider `UNIQUE (playlist_id, track_order)`.*

## Table: Music_Resources (Discover Module)

Represents a musical resource, either uploaded/created within Mouvik or imported from an external platform.

| Field Name         | Data Type     | Constraints/Notes                       | Description                                      |
|--------------------|---------------|-----------------------------------------|--------------------------------------------------|
| `resource_id`      | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the resource             |
| `user_id`          | UUID          | FOREIGN KEY (Users), NULLABLE           | User who added/owns this resource (if internal)  |
| `resource_type`    | VARCHAR(50)   | NOT NULL                                | Type of resource (e.g., 'internal_track', 'ai_composition', 'spotify_track', 'youtube_video', 'suno_ai', 'udio_ai') |
| `external_url`     | VARCHAR(512)  | UNIQUE, NULLABLE                        | URL of the resource on the external platform     |
| `internal_path`    | VARCHAR(512)  | NULLABLE                                | Path to the file in Mouvik storage (if internal) |
| `title`            | VARCHAR(255)  | NOT NULL                                | Title of the resource                            |
| `artist_display_name` | VARCHAR(255)|                                         | Artist name(s) as displayed                      |
| `album_display_name` | VARCHAR(255)|                                         | Album name (if applicable)                       |
| `cover_art_url`    | VARCHAR(512)  |                                         | URL to the cover artwork                         |
| `duration_ms`      | INTEGER       |                                         | Duration in milliseconds (if known)              |
| `platform_data`    | JSONB         |                                         | Raw metadata from the external platform/source   |
| `description`      | TEXT          |                                         | Description or notes                             |
| `is_public`        | BOOLEAN       | DEFAULT TRUE                            | Visibility status                                |
| `added_at`         | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp when the resource was added            |
| `updated_at`       | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of last update                         |

*Note: Link to `Morceaux` or `Albums` if the resource represents an existing internal track/album.* 

## Table: Tags (Discover Module)

Centralized repository for tags (genres, moods, styles, platforms, custom).

| Field Name | Data Type     | Constraints/Notes                       | Description                                    |
|------------|---------------|-----------------------------------------|------------------------------------------------|
| `tag_id`   | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the tag                  |
| `name`     | VARCHAR(100)  | UNIQUE, NOT NULL                        | The tag name (normalized - lowercase)          |
| `tag_type` | VARCHAR(50)   | NOT NULL, DEFAULT 'custom'              | Type of tag (e.g., 'genre', 'mood', 'style', 'platform', 'custom') |
| `description`| TEXT         |                                         | Optional description for the tag               |
| `created_by`| UUID         | FOREIGN KEY (Users), NULLABLE           | User who created the tag (if custom)           |
| `created_at`| TIMESTAMPZ   | DEFAULT NOW()                           | Timestamp of tag creation                      |
| `is_approved`| BOOLEAN    | DEFAULT TRUE                            | Approval status (for custom tags moderation) |

## Table: Resource_Tags (Discover Module)

Associates tags with music resources (many-to-many).

| Field Name    | Data Type | Constraints/Notes                         | Description                               |
|---------------|-----------|-------------------------------------------|-------------------------------------------|
| `resource_id` | UUID      | PRIMARY KEY, FOREIGN KEY (Music_Resources)| The music resource                        |
| `tag_id`      | UUID      | PRIMARY KEY, FOREIGN KEY (Tags)           | The associated tag                        |
| `assigned_at` | TIMESTAMPZ| DEFAULT NOW()                             | Timestamp when the tag was assigned       |

## Table: Playlist_Resources (Discover Module)

Associates music resources with playlists (many-to-many), replacing `Playlist_Tracks` for flexibility.

| Field Name    | Data Type | Constraints/Notes                         | Description                                  |
|---------------|-----------|-------------------------------------------|----------------------------------------------|
| `playlist_id` | UUID      | PRIMARY KEY, FOREIGN KEY (Playlists)      | The playlist                                 |
| `resource_id` | UUID      | PRIMARY KEY, FOREIGN KEY (Music_Resources)| The music resource in the playlist           |
| `added_at`    | TIMESTAMPZ| DEFAULT NOW()                             | Timestamp when the resource was added        |
| `item_order`  | INTEGER   | NOT NULL                                  | Order of the resource within the playlist    |

*Note: The previous `Playlist_Tracks` table should be deprecated or migrated to this structure.* 
*Consider UNIQUE constraint on `(playlist_id, item_order)`.* 

## Table: Activity (Community Module)

Records significant actions performed by users for the community feed.

| Field Name       | Data Type     | Constraints/Notes                       | Description                                      |
|------------------|---------------|-----------------------------------------|--------------------------------------------------|
| `activity_id`    | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the activity record        |
| `user_id`        | UUID          | FOREIGN KEY (Users), NOT NULL           | User who performed the action                    |
| `activity_type`  | VARCHAR(50)   | NOT NULL                                | Type of activity (e.g., 'post_created', 'resource_added', 'comment_added', 'like_added', 'playlist_created', 'user_followed', 'group_joined') |
| `target_type`    | VARCHAR(50)   | NULLABLE                                | Type of the entity the activity relates to (e.g., 'resource', 'user', 'playlist', 'comment', 'post', 'group') |
| `target_id`      | UUID          | NULLABLE                                | ID of the related entity (FK based on target_type) |
| `content`        | TEXT          |                                         | Text content associated with the activity (e.g., post text, comment text) |
| `related_resource_id` | UUID     | FOREIGN KEY (Music_Resources), NULLABLE | Direct link to a music resource, if relevant (e.g., for 'resource_added') |
| `created_at`     | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of the activity                        |
| `metadata`       | JSONB         |                                         | Additional context (e.g., original post ID for a comment) |

## Table: Comments (Community Module)

Stores comments made on activities or specific targets.

| Field Name        | Data Type     | Constraints/Notes                       | Description                                      |
|-------------------|---------------|-----------------------------------------|--------------------------------------------------|
| `comment_id`      | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the comment                |
| `user_id`         | UUID          | FOREIGN KEY (Users), NOT NULL           | User who wrote the comment                     |
| `activity_id`     | UUID          | FOREIGN KEY (Activity), NULLABLE        | Activity this comment belongs to (e.g., a post)  |
| `parent_comment_id`| UUID         | FOREIGN KEY (Comments), NULLABLE        | Link for threaded comments                     |
| `target_type`     | VARCHAR(50)   | NULLABLE                                | Target entity type if not linked via activity    |
| `target_id`       | UUID          | NULLABLE                                | Target entity ID if not linked via activity      |
| `content`         | TEXT          | NOT NULL                                | The text of the comment                          |
| `created_at`      | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of comment creation                  |
| `updated_at`      | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of last comment update                 |
| `is_deleted`      | BOOLEAN       | DEFAULT FALSE                           | Soft delete flag                                 |

## Table: Likes (Community Module)

Stores likes given by users on activities or other targets.

| Field Name   | Data Type | Constraints/Notes                         | Description                                      |
|--------------|-----------|-------------------------------------------|--------------------------------------------------|
| `user_id`    | UUID      | PRIMARY KEY, FOREIGN KEY (Users)          | User who gave the like                         |
| `target_type`| VARCHAR(50)| PRIMARY KEY, NOT NULL                     | Type of entity liked (e.g., 'activity', 'comment', 'resource') |
| `target_id`  | UUID      | PRIMARY KEY, NOT NULL                     | ID of the entity liked                           |
| `liked_at`   | TIMESTAMPZ| DEFAULT NOW()                             | Timestamp when the like was given                |

*Note: `PRIMARY KEY (user_id, target_type, target_id)` ensures unique likes.* 
*This replaces `User_Likes_Tracks` for broader applicability.* 

## Table: Hashtags (Community Module)

Stores unique hashtags identified in activities or comments.

| Field Name    | Data Type     | Constraints/Notes                       | Description                                      |
|---------------|---------------|-----------------------------------------|--------------------------------------------------|
| `hashtag_id`  | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the hashtag                |
| `name`        | VARCHAR(100)  | UNIQUE, NOT NULL                        | The hashtag text (normalized, without '#')       |
| `created_at`  | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp when the hashtag was first recorded    |
| `usage_count` | BIGINT        | DEFAULT 1                               | Counter for hashtag popularity                   |

## Table: Activity_Hashtags (Community Module)

Associates hashtags with activities (many-to-many).

| Field Name    | Data Type | Constraints/Notes                         | Description                               |
|---------------|-----------|-------------------------------------------|-------------------------------------------|
| `activity_id` | UUID      | PRIMARY KEY, FOREIGN KEY (Activity)       | The activity containing the hashtag       |
| `hashtag_id`  | UUID      | PRIMARY KEY, FOREIGN KEY (Hashtags)       | The associated hashtag                    |

## Table: Bands (Community Module)

Represents artist groups or bands.

| Field Name         | Data Type     | Constraints/Notes                       | Description                                  |
|--------------------|---------------|-----------------------------------------|----------------------------------------------|
| `band_id`          | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the band/group         |
| `name`             | VARCHAR(100)  | UNIQUE, NOT NULL                        | Name of the band/group                       |
| `description`      | TEXT          |                                         | Description or bio of the band               |
| `profile_image_url`| VARCHAR(255)  |                                         | URL to the band's profile picture            |
| `banner_url`       | VARCHAR(255)  |                                         | URL to the band's banner image               |
| `genre`            | VARCHAR(100)  |                                         | Primary music genre                          |
| `created_by`       | UUID          | FOREIGN KEY (Users), NOT NULL           | User who created the band page               |
| `created_at`       | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of band page creation              |
| `updated_at`       | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of last band page update           |

## Table: Band_Members (Community Module)

Associates users with bands/groups and defines their roles.

| Field Name | Data Type     | Constraints/Notes                      | Description                                      |
|------------|---------------|----------------------------------------|--------------------------------------------------|
| `band_id`  | UUID          | PRIMARY KEY, FOREIGN KEY (Bands)       | The band/group                                   |
| `user_id`  | UUID          | PRIMARY KEY, FOREIGN KEY (Users)       | The user who is a member                       |
| `role`     | VARCHAR(50)   | NOT NULL, DEFAULT 'member'           | Role within the band (e.g., 'admin', 'member', 'moderator') |
| `joined_at`| TIMESTAMPZ    | DEFAULT NOW()                          | Timestamp when the user joined the band          |

## Table: Notifications (Community Module)

Stores notifications for users.

| Field Name          | Data Type     | Constraints/Notes                       | Description                                      |
|---------------------|---------------|-----------------------------------------|--------------------------------------------------|
| `notification_id`   | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the notification         |
| `user_id`           | UUID          | FOREIGN KEY (Users), NOT NULL           | User receiving the notification                |
| `notification_type` | VARCHAR(50)   | NOT NULL                                | Type of notification (e.g., 'new_comment', 'new_follower', 'mention', 'like', 'group_invite') |
| `actor_user_id`     | UUID          | FOREIGN KEY (Users), NULLABLE           | User who triggered the notification (optional)   |
| `target_type`       | VARCHAR(50)   | NULLABLE                                | Type of the entity related to the notification   |
| `target_id`         | UUID          | NULLABLE                                | ID of the related entity                         |
| `content`         | TEXT          |                                         | Optional additional content for the notification |
| `is_read`           | BOOLEAN       | DEFAULT FALSE, NOT NULL                 | Read status of the notification                |
| `created_at`        | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of notification creation             |

## Table: Groups (New)

Represents user-created communities or interest groups.

| Field Name        | Data Type     | Constraints/Notes                       | Description                                  |
|-------------------|---------------|-----------------------------------------|----------------------------------------------|
| `group_id`        | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the group                |
| `name`            | TEXT          | NOT NULL, UNIQUE                        | Name of the group                              |
| `description`     | TEXT          |                                         | Description of the group                       |
| `cover_image_url` | TEXT          |                                         | URL for the group's cover image                |
| `creator_user_id` | UUID          | NOT NULL, FOREIGN KEY REFERENCES Users(user_id) | User who created the group                     |
| `is_private`      | BOOLEAN       | DEFAULT FALSE                           | Indicates if the group requires approval to join |
| `created_at`      | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of group creation                  |
| `updated_at`      | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of last update                     |

## Table: Group_Members (New)

Associates users with groups they are members of.

| Field Name      | Data Type     | Constraints/Notes                       | Description                                  |
|-----------------|---------------|-----------------------------------------|----------------------------------------------|
| `group_id`      | UUID          | PRIMARY KEY, FOREIGN KEY REFERENCES Groups(group_id) | Identifier for the group                       |
| `user_id`       | UUID          | PRIMARY KEY, FOREIGN KEY REFERENCES Users(user_id) | Identifier for the user member                 |
| `role`          | TEXT          | NOT NULL, DEFAULT 'member'              | Role within the group ('admin', 'moderator', 'member') |
| `joined_at`     | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp when the user joined the group         |

## Table: Follows (New)

Tracks user follow relationships.

| Field Name         | Data Type     | Constraints/Notes                       | Description                                  |
|--------------------|---------------|-----------------------------------------|----------------------------------------------|
| `follower_user_id` | UUID          | PRIMARY KEY, FOREIGN KEY REFERENCES Users(user_id) | User who is following                          |
| `following_user_id`| UUID          | PRIMARY KEY, FOREIGN KEY REFERENCES Users(user_id) | User who is being followed                     |
| `created_at`       | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp when the follow relationship started |

## Table: Conversations (New)

Represents a direct message conversation between users.

| Field Name          | Data Type     | Constraints/Notes                       | Description                                  |
|---------------------|---------------|-----------------------------------------|----------------------------------------------|
| `conversation_id`   | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the conversation         |
| `created_at`        | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp when the conversation started        |
-- Note: Participants managed via Conversation_Participants table

## Table: Conversation_Participants (New)

Links users to conversations.

| Field Name          | Data Type     | Constraints/Notes                       | Description                                  |
|---------------------|---------------|-----------------------------------------|----------------------------------------------|
| `conversation_id`   | UUID          | PRIMARY KEY, FOREIGN KEY REFERENCES Conversations(conversation_id) | Identifier for the conversation                |
| `user_id`           | UUID          | PRIMARY KEY, FOREIGN KEY REFERENCES Users(user_id) | Identifier for the participating user            |
| `joined_at`         | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp when the user joined conversation    |
| `last_read_at`      | TIMESTAMPZ    |                                         | Timestamp of when the user last read messages in this conversation |

## Table: Messages (New)

Stores individual messages within a conversation.

| Field Name        | Data Type     | Constraints/Notes                       | Description                                  |
|-------------------|---------------|-----------------------------------------|----------------------------------------------|
| `message_id`      | UUID          | PRIMARY KEY, DEFAULT uuid_generate_v4() | Unique identifier for the message              |
| `conversation_id` | UUID          | NOT NULL, FOREIGN KEY REFERENCES Conversations(conversation_id) | Conversation the message belongs to            |
| `sender_user_id`  | UUID          | NOT NULL, FOREIGN KEY REFERENCES Users(user_id) | User who sent the message                    |
| `content`         | TEXT          | NOT NULL                                | The content of the message                     |
| `created_at`      | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of message creation                  |
| `updated_at`      | TIMESTAMPZ    | DEFAULT NOW()                           | Timestamp of last message edit (if allowed)    |

## Database Relationships (Mermaid ER Diagram)

```mermaid
erDiagram
    USERS {
        UUID user_id PK
        TEXT email
        TEXT password_hash
        TEXT username
        TEXT display_name
        BOOLEAN is_artist
        TEXT profile_picture_url
        TEXT banner_image_url
        TEXT bio
        TEXT location
        TEXT website_url
        TEXT stage_name
        TEXT[] genres
        TEXT[] instruments
        JSONB social_links
        JSONB privacy_settings
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    ALBUMS {
        UUID album_id PK
        UUID user_id FK
        UUID band_id FK
        TEXT title
        TEXT description
        TEXT cover_art_url
        DATE release_date
        TEXT genre
        TEXT visibility
        JSONB monetization
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    MUSIC_RESOURCES {
        UUID resource_id PK
        UUID user_id FK
        TEXT title
        TEXT resource_type
        TEXT source_type
        TEXT external_platform
        TEXT external_id
        TEXT external_url
        INTEGER duration_ms
        JSONB metadata
        TEXT storage_path
        TEXT cover_art_url
        TEXT description
        TEXT visibility
        TEXT license
        UUID version_of FK
        TEXT version_tag
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    ALBUM_RESOURCES {
        UUID album_id PK, FK
        UUID resource_id PK, FK
        INTEGER track_number
        TIMESTAMPTZ added_at
    }

    RESOURCE_COLLABORATORS {
        UUID resource_id PK, FK
        UUID user_id PK, FK
        UUID band_id PK, FK
        TEXT role
        TIMESTAMPTZ added_at
    }

    LYRICS {
        UUID lyric_id PK
        UUID resource_id FK
        TEXT content
        TEXT language
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    CHORDS {
        UUID chord_id PK
        UUID resource_id FK
        JSONB chord_data
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    TAGS {
        UUID tag_id PK
        TEXT name
        TEXT tag_type
        BOOLEAN is_approved
        TIMESTAMPTZ created_at
    }

    RESOURCE_TAGS {
        UUID resource_id PK, FK
        UUID tag_id PK, FK
        TIMESTAMPTZ created_at
    }

    PLAYLISTS {
        UUID playlist_id PK
        UUID user_id FK
        TEXT name
        TEXT description
        TEXT visibility
        TEXT cover_image_url
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    PLAYLIST_RESOURCES {
        UUID playlist_id PK, FK
        UUID resource_id PK, FK
        INTEGER position
        TIMESTAMPTZ added_at
    }

    ACTIVITY {
        UUID activity_id PK
        UUID user_id FK
        TEXT activity_type
        TEXT target_type
        UUID target_id
        TEXT content
        TEXT target_url
        TEXT[] hashtags
        TIMESTAMPTZ created_at
    }

    COMMENTS {
        UUID comment_id PK
        UUID user_id FK
        TEXT target_type
        UUID target_id
        UUID parent_comment_id FK
        TEXT content
        NUMERIC timestamp
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    LIKES {
        UUID like_id PK
        UUID user_id FK
        TEXT target_type
        UUID target_id
        TIMESTAMPTZ created_at
    }

    BANDS {
        UUID band_id PK
        TEXT name
        TEXT bio
        TEXT profile_picture_url
        TEXT banner_image_url
        UUID created_by_user_id FK
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    BAND_MEMBERS {
        UUID band_id PK, FK
        UUID user_id PK, FK
        TEXT role
        TIMESTAMPTZ joined_at
    }

    NOTIFICATIONS {
        UUID notification_id PK
        UUID user_id FK
        TEXT type
        UUID actor_user_id FK
        TEXT target_type
        UUID target_id
        TEXT content
        BOOLEAN is_read
        TIMESTAMPTZ created_at
    }

    GROUPS {
        UUID group_id PK
        TEXT name
        TEXT description
        TEXT cover_image_url
        UUID creator_user_id FK
        BOOLEAN is_private
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    GROUP_MEMBERS {
        UUID group_id PK, FK
        UUID user_id PK, FK
        TEXT role
        TIMESTAMPTZ joined_at
    }

    FOLLOWS {
        UUID follower_user_id PK, FK
        UUID following_user_id PK, FK
        TIMESTAMPTZ created_at
    }

    CONVERSATIONS {
        UUID conversation_id PK
        TIMESTAMPTZ created_at
    }

    CONVERSATION_PARTICIPANTS {
        UUID conversation_id PK, FK
        UUID user_id PK, FK
        TIMESTAMPTZ joined_at
        TIMESTAMPTZ last_read_at
    }

    MESSAGES {
        UUID message_id PK
        UUID conversation_id FK
        UUID sender_user_id FK
        TEXT content
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    USERS ||--o{ ALBUMS : "Owns/Creates"
    USERS ||--o{ MUSIC_RESOURCES : "Uploads/Creates"
    USERS ||--o{ ACTIVITY : "Performs"
    USERS ||--o{ COMMENTS : "Writes"
    USERS ||--o{ LIKES : "Gives"
    USERS ||--o{ BAND_MEMBERS : "Is Member Of"
    USERS ||--o{ NOTIFICATIONS : "Receives"
    USERS ||--o{ GROUPS : "Creates"
    USERS ||--o{ GROUP_MEMBERS : "Is Member Of"
    USERS ||--|{ FOLLOWS : "following_user_id (Is Followed By)"
    USERS ||--|{ FOLLOWS : "follower_user_id (Follows)"
    USERS ||--o{ CONVERSATION_PARTICIPANTS : "Participates In"
    USERS ||--o{ MESSAGES : "Sends"
    USERS ||--o{ RESOURCE_COLLABORATORS : "Collaborates On"
    USERS ||--o{ BANDS : "Created"
    USERS ||--o{ PLAYLISTS : "Creates"

    ALBUMS }|--|| USERS : "Owned By"
    ALBUMS }o--|| BANDS : "Associated With (Optional)"
    ALBUMS ||--|{ ALBUM_RESOURCES : "Contains"

    MUSIC_RESOURCES }|--|| USERS : "Uploaded/Created By"
    MUSIC_RESOURCES ||--o{ RESOURCE_TAGS : "Has Tag"
    MUSIC_RESOURCES ||--o{ PLAYLIST_RESOURCES : "In Playlist"
    MUSIC_RESOURCES ||--o{ COMMENTS : "Has Comment On"
    MUSIC_RESOURCES ||--o{ LIKES : "Has Like On"
    MUSIC_RESOURCES ||--|{ ALBUM_RESOURCES : "Is Part Of Album"
    MUSIC_RESOURCES ||--|| LYRICS : "Has Lyrics (1-to-1)"
    MUSIC_RESOURCES ||--|| CHORDS : "Has Chords (1-to-1)"
    MUSIC_RESOURCES ||--o{ RESOURCE_COLLABORATORS : "Has Collaborator"
    MUSIC_RESOURCES }o--|| MUSIC_RESOURCES : "version_of (Is Version Of)"

    TAGS ||--o{ RESOURCE_TAGS : "Applied To"

    PLAYLISTS }|--|| USERS : "Created By"
    PLAYLISTS ||--o{ PLAYLIST_RESOURCES : "Contains"

    ACTIVITY }|--|| USERS : "Performed By"
    ACTIVITY ||--o{ COMMENTS : "Has Comment On"
    ACTIVITY ||--o{ LIKES : "Has Like On"

    COMMENTS }|--|| USERS : "Written By"
    COMMENTS }o--|| COMMENTS : "parent_comment_id (Is Reply To)"

    LIKES }|--|| USERS : "Given By"

    BANDS }|--|| USERS : "Created By"
    BANDS ||--o{ BAND_MEMBERS : "Has Member"
    BANDS ||--o{ ALBUMS : "Associated With"
    BANDS ||--o{ RESOURCE_COLLABORATORS : "Collaborates On"

    GROUPS }|--|| USERS : "Created By"
    GROUPS ||--o{ GROUP_MEMBERS : "Has Member"

    CONVERSATIONS ||--o{ CONVERSATION_PARTICIPANTS : "Has Participant"
    CONVERSATIONS ||--o{ MESSAGES : "Contains"

    MESSAGES }|--|| USERS : "Sent By"
