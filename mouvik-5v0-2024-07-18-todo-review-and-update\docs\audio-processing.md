# Audio Processing Documentation

## Overview

MOUVIK includes several audio processing features to enhance the music creation and listening experience. This document outlines the key audio processing components and how they work.

## Waveform Generation

The waveform visualization is generated from audio files to provide a visual representation of the audio content.

### Process

1. Audio file is loaded using the Web Audio API
2. Audio data is processed to extract amplitude information
3. Amplitude data is normalized and scaled
4. Waveform data is stored as JSON for efficient rendering

### Implementation

\`\`\`typescript
async function generateWaveform(audioUrl: string): Promise<number[]> {
  // Create audio context
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  
  // Fetch audio data
  const response = await fetch(audioUrl);
  const arrayBuffer = await response.arrayBuffer();
  
  // Decode audio data
  const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
  
  // Get channel data (mono or first channel of stereo)
  const channelData = audioBuffer.getChannelData(0);
  
  // Process data to create waveform
  const samples = 100; // Number of data points in the waveform
  const blockSize = Math.floor(channelData.length / samples);
  const waveform = [];
  
  for (let i = 0; i < samples; i++) {
    let blockStart = blockSize * i;
    let sum = 0;
    
    for (let j = 0; j < blockSize; j++) {
      sum += Math.abs(channelData[blockStart + j]);
    }
    
    // Average amplitude for this block
    waveform.push(sum / blockSize);
  }
  
  // Normalize waveform data to range 0-1
  const max = Math.max(...waveform);
  return waveform.map(val => val / max);
}
\`\`\`

## Audio Playback

The application includes a custom audio player with advanced playback controls.

### Features

- Play/pause control
- Seek functionality
- Volume control
- Playback rate adjustment
- Looping
- Playlist management

### Implementation

The audio playback system uses the HTML5 Audio API with custom controls and state management.

\`\`\`typescript
class AudioPlayer {
  private audio: HTMLAudioElement;
  private currentSongIndex: number = 0;
  private playlist: Song[] = [];
  
  constructor() {
    this.audio = new Audio();
    this.setupEventListeners();
  }
  
  private setupEventListeners() {
    this.audio.addEventListener('ended', this.handleSongEnd);
    this.audio.addEventListener('timeupdate', this.handleTimeUpdate);
    this.audio.addEventListener('loadedmetadata', this.handleMetadataLoaded);
  }
  
  public play() {
    this.audio.play();
  }
  
  public pause() {
    this.audio.pause();
  }
  
  public seek(time: number) {
    this.audio.currentTime = time;
  }
  
  public setVolume(volume: number) {
    this.audio.volume = Math.max(0, Math.min(1, volume));
  }
  
  public setPlaybackRate(rate: number) {
    this.audio.playbackRate = rate;
  }
  
  public loadSong(song: Song) {
    this.audio.src = song.audio_url;
    this.audio.load();
  }
  
  public loadPlaylist(songs: Song[]) {
    this.playlist = songs;
    this.currentSongIndex = 0;
    if (this.playlist.length > 0) {
      this.loadSong(this.playlist[0]);
    }
  }
  
  public next() {
    if (this.playlist.length === 0) return;
    
    this.currentSongIndex = (this.currentSongIndex + 1) % this.playlist.length;
    this.loadSong(this.playlist[this.currentSongIndex]);
    this.play();
  }
  
  public previous() {
    if (this.playlist.length === 0) return;
    
    this.currentSongIndex = (this.currentSongIndex - 1 + this.playlist.length) % this.playlist.length;
    this.loadSong(this.playlist[this.currentSongIndex]);
    this.play();
  }
  
  private handleSongEnd = () => {
    this.next();
  }
  
  private handleTimeUpdate = () => {
    // Update UI with current time
  }
  
  private handleMetadataLoaded = () => {
    // Update UI with duration
  }
}
\`\`\`

## Audio Upload and Processing

The application includes functionality for uploading and processing audio files.

### Upload Process

1. User selects an audio file
2. File is validated (format, size, etc.)
3. File is uploaded to Supabase storage
4. Audio metadata is extracted
5. Waveform is generated
6. Song record is created in the database

### Audio Metadata Extraction

\`\`\`typescript
async function extractAudioMetadata(file: File): Promise<AudioMetadata> {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    audio.preload = 'metadata';
    
    audio.onloadedmetadata = () => {
      resolve({
        duration: audio.duration,
        // Other metadata can be extracted here
      });
    };
    
    audio.onerror = () => {
      reject(new Error('Failed to load audio metadata'));
    };
    
    audio.src = URL.createObjectURL(file);
  });
}
\`\`\`

## Global Audio Context

The application uses a React context to manage global audio state and playback.

### Features

- Centralized audio state management
- Consistent playback across page navigation
- Queue management
- Play history tracking

### Implementation

\`\`\`typescript
interface AudioContextType {
  currentSong: Song | null;
  isPlaying: boolean;
  volume: number;
  progress: number;
  duration: number;
  queue: Song[];
  history: Song[];
  play: (song: Song) => void;
  pause: () => void;
  resume: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  next: () => void;
  previous: () => void;
  addToQueue: (song: Song) => void;
  removeFromQueue: (songId: string) => void;
  clearQueue: () => void;
}

const AudioContext = React.createContext<AudioContextType | undefined>(undefined);

export const AudioProvider: React.FC = ({ children }) => {
  const [currentSong, setCurrentSong] = useState<Song | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(1);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [queue, setQueue] = useState<Song[]>([]);
  const [history, setHistory] = useState<Song[]>([]);
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  useEffect(() => {
    audioRef.current = new Audio();
    
    const audio = audioRef.current;
    
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleMetadataLoaded);
    audio.addEventListener('ended', handleSongEnd);
    
    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleMetadataLoaded);
      audio.removeEventListener('ended', handleSongEnd);
      audio.pause();
    };
  }, []);
  
  // Implementation of all the audio control functions...
  
  const value = {
    currentSong,
    isPlaying,
    volume,
    progress,
    duration,
    queue,
    history,
    play,
    pause,
    resume,
    seek,
    setVolume,
    next,
    previous,
    addToQueue,
    removeFromQueue,
    clearQueue,
  };
  
  return (
    <AudioContext.Provider value={value}>
      {children}
    </AudioContext.Provider>
  );
};

export const useAudio = () => {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};

