'use client';

import React from 'react';
import { useSearchParams } from 'next/navigation';
import { AppSidebar, type UserProfileForSidebar } from "@/components/sidebar";
// Import de notre version refactorisée
import { AIComposerWorkspaceRefactored } from '@/components/ai-composer/AIComposerWorkspaceRefactored';
import MobileMenuButton from "@/app/(authenticated)/mobile-menu-button";

interface AIComposerClientPageProps {
  userObjForSidebar: UserProfileForSidebar;
}

export function AIComposerClientPage({ userObjForSidebar }: AIComposerClientPageProps) {
  const searchParams = useSearchParams();
  const songId = searchParams.get('songId') || undefined;

  return (
    <div className="flex h-screen main-layout-container bg-zinc-950">
      <MobileMenuButton />
      <AppSidebar user={userObjForSidebar} />
      <div className="flex-1 flex flex-col bg-gradient-to-br from-zinc-900 via-zinc-950 to-black">
        <main className="flex-1 overflow-y-auto">
          <AIComposerWorkspaceRefactored songId={songId} />
        </main>
      </div>
    </div>
  );
}