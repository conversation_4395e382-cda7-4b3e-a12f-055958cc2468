"use client"

import { useState, useEffect } from "react"
import { formatDistanceToNow } from "date-fns"
import { fr } from "date-fns/locale"
import { createBrowserClient } from "@/lib/supabase/client"
import { useToast } from "@/hooks/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Heart, Reply } from "lucide-react"

interface Comment {
  id: string
  content: string
  created_at: string
  user_id: string
  profiles: {
    username?: string
    display_name?: string
    avatar_url?: string
  }
}

interface CommentSectionProps {
  resourceId: string
  resourceType: "song" | "album" | "artist" | "band"
  userId?: string
}

export function CommentSection({ resourceId, resourceType, userId }: CommentSectionProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState("")
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [userProfile, setUserProfile] = useState<any>(null)
  const supabase = createBrowserClient()
  const { toast } = useToast()

  useEffect(() => {
    const fetchComments = async () => {
      try {
        const { data, error } = await supabase
          .from("comments")
          .select(`
            *,
            profiles:user_id (username, display_name, avatar_url)
          `)
          .eq("resource_type", resourceType)
          .eq("resource_id", resourceId)
          .order("created_at", { ascending: false })

        if (error) throw error

        setComments(data || [])
      } catch (error) {
        console.error("Erreur lors de la récupération des commentaires:", error)
      } finally {
        setLoading(false)
      }
    }

    const fetchUserProfile = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession()

      if (session) {
        const { data } = await supabase.from("profiles").select("*").eq("id", session.user.id).single()

        setUserProfile(data)
      }
    }

    fetchComments()
    fetchUserProfile()
  }, [resourceId, resourceType, supabase])

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return

    setSubmitting(true)

    try {
      const {
        data: { session },
      } = await supabase.auth.getSession()

      if (!session) {
        toast({
          title: "Connexion requise",
          description: "Vous devez être connecté pour commenter",
          variant: "destructive",
        })
        return
      }

      const { data, error } = await supabase
        .from("comments")
        .insert({
          content: newComment.trim(),
          resource_type: resourceType,
          resource_id: resourceId,
          user_id: session.user.id,
        })
        .select(`
          *,
          profiles:user_id (username, display_name, avatar_url)
        `)
        .single()

      if (error) throw error

      setComments([data, ...comments])
      setNewComment("")

      toast({
        title: "Commentaire ajouté",
        description: "Votre commentaire a été publié avec succès",
      })
    } catch (error) {
      console.error("Erreur lors de l'ajout du commentaire:", error)
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'ajout du commentaire",
        variant: "destructive",
      })
    } finally {
      setSubmitting(false)
    }
  }

  const formatDate = (date: string) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true, locale: fr })
  }

  return (
    <div className="mt-8">
      {userId && (
        <div className="mb-6">
          <Textarea
            placeholder="Ajouter un commentaire..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            className="min-h-[100px]"
          />
          <div className="flex justify-end mt-2">
            <Button onClick={handleSubmitComment} disabled={submitting || !newComment.trim()}>
              {submitting ? "Envoi en cours..." : "Commenter"}
            </Button>
          </div>
        </div>
      )}
      {!userId && (
        <p className="text-sm text-muted-foreground">
          <a href="/login" className="underline">Connectez-vous</a> pour laisser un commentaire.
        </p>
      )}

      <Separator className="my-4" />

      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex gap-4 animate-pulse">
              <div className="h-10 w-10 rounded-full bg-muted"></div>
              <div className="flex-1">
                <div className="h-4 w-1/4 bg-muted rounded mb-2"></div>
                <div className="h-3 w-full bg-muted rounded mb-2"></div>
                <div className="h-3 w-3/4 bg-muted rounded"></div>
              </div>
            </div>
          ))}
        </div>
      ) : comments.length > 0 ? (
        <div className="space-y-6">
          {comments.map((comment) => (
            <div key={comment.id} className="flex gap-4">
              <Avatar className="h-10 w-10">
                <AvatarImage src={comment.profiles?.avatar_url || "/placeholder.svg"} />
                <AvatarFallback>
                  {(comment.profiles?.display_name || comment.profiles?.username || "U").charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="font-medium">{comment.profiles?.display_name || comment.profiles?.username}</div>
                  <div className="text-xs text-muted-foreground">{formatDate(comment.created_at)}</div>
                </div>
                <p className="mt-1 text-sm">{comment.content}</p>
                <div className="mt-2 flex items-center gap-4">
                  <Button variant="ghost" size="sm" className="h-8 gap-1">
                    <Heart className="h-4 w-4" />
                    <span>J'aime</span>
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 gap-1">
                    <Reply className="h-4 w-4" />
                    <span>Répondre</span>
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          Aucun commentaire pour le moment. Soyez le premier à commenter !
        </div>
      )}
    </div>
  )
}
