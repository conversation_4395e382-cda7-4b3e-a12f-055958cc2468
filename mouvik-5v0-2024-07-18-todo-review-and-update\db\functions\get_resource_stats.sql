-- Function to get like count for a specific resource
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION get_like_count(resource_id_param uuid, resource_type_param text)
RETURNS integer AS $$
DECLARE
  like_count integer;
BEGIN
  SELECT count(*)
  INTO like_count
  FROM likes
  WHERE resource_id = resource_id_param AND resource_type = resource_type_param;
  
  RETURN COALESCE(like_count, 0);
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get view count for a specific resource
CREATE OR REPLACE FUNCTION get_view_count(resource_id_param uuid, resource_type_param text)
RETURNS integer AS $$
DECLARE
  view_count integer;
BEGIN
  SELECT count(*)
  INTO view_count
  FROM resource_views
  WHERE resource_id = resource_id_param AND resource_type = resource_type_param;
  
  RETURN COALESCE(view_count, 0);
END;
$$ LANGUAGE plpgsql STABLE;

-- Note: You might want to add a function for dislike_count as well if needed elsewhere,
-- or adjust the existing queries if you have a combined table for interactions.
-- For now, dislike_count is assumed to be a direct column on 'albums'.

-- Example of how to call these functions (for testing in SQL editor):
-- SELECT get_like_count('your-album-uuid', 'album');
-- SELECT get_view_count('your-album-uuid', 'album');
