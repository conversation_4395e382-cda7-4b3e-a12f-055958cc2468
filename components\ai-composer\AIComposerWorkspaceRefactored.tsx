'use client';

import React from 'react';

// Import des modules refactorisés
import { useAIComposerCore } from './core/AIComposerCore';
import { AIComposerHeader } from './core/AIComposerHeader';
import {
  useChordManagement,
  useSectionManagement,
  useAIFunctions,
  useInstruments
} from './core/AIComposerHooks';

// Import du nouveau système Mega Pro
import { AIComposerLayoutMegaPro } from './unified/AIComposerLayoutMegaPro';

interface AIComposerWorkspaceProps {
  songId?: string;
  onNavigateToEditor?: (songId: string) => void;
}

export const AIComposerWorkspaceRefactored: React.FC<AIComposerWorkspaceProps> = ({ 
  songId, 
  onNavigateToEditor 
}) => {
  // Hook principal pour l'état
  const {
    // États
    activeTab,
    setActiveTab,
    currentSong,
    setCurrentSong,
    styleConfig,
    setStyleConfig,
    lyricsContent,
    setLyricsContent,
    songSections,
    setSongSections,
    selectedSection,
    setSelectedSection,
    isPlaying,
    setIsPlaying,
    saving,
    setSaving,
    aiHistory,
    setAiHistory,
    lastAiResult,
    setLastAiResult,
    generalPrompt,
    setGeneralPrompt,
    
    // Configuration IA
    aiConfig,
    saveAiConfig,
    callAI,
    aiLoading,
    aiError,
    isConfigured,
    
    // Utilitaires
    parseLyricsIntoSections,
    getSectionType,
    extractChordProgressions,
    extractSongStructure,
    
    // Contexte
    router,
    supabase,
    user
  } = useAIComposerCore(songId);

  // Hooks pour la gestion des accords
  const {
    handleChordInsert,
    handleChordAdd,
    handleChordRemove,
    handleChordUpdate,
    handlePlayChord
  } = useChordManagement(songSections, setSongSections, selectedSection);

  // Hooks pour la gestion des sections
  const {
    handleLyricsChange,
    handleSectionAdd,
    handleStructureChange,
    formatLyricsWithChords
  } = useSectionManagement(
    songSections, 
    setSongSections, 
    selectedSection, 
    setSelectedSection, 
    lyricsContent, 
    setLyricsContent
  );

  // Hooks pour les fonctions IA
  const {
    handleAIGenerate
  } = useAIFunctions(
    isConfigured,
    callAI,
    songSections,
    setLastAiResult,
    setAiHistory,
    aiHistory
  );

  // Hook pour les instruments
  const { AVAILABLE_INSTRUMENTS } = useInstruments();

  return (
    <div className="h-full flex flex-col bg-background">
      {/* En-tête */}
      <AIComposerHeader
        songId={songId}
        currentSong={currentSong}
        styleConfig={styleConfig}
        songSections={songSections}
        lyricsContent={lyricsContent}
        isPlaying={isPlaying}
        setIsPlaying={setIsPlaying}
        saving={saving}
        setSaving={setSaving}
        router={router}
        supabase={supabase}
        user={user}
        extractChordProgressions={extractChordProgressions}
        extractSongStructure={extractSongStructure}
      />

      {/* Layout Mega Pro */}
      <div className="flex-1 overflow-hidden">
        <AIComposerLayoutMegaPro
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          lyricsContent={lyricsContent}
          setLyricsContent={setLyricsContent}
          songSections={songSections}
          setSongSections={setSongSections}
          selectedSection={selectedSection}
          setSelectedSection={setSelectedSection}
          styleConfig={styleConfig}
          setStyleConfig={setStyleConfig}
          currentSong={currentSong}
          setCurrentSong={setCurrentSong}
          isConfigured={isConfigured}
          aiHistory={aiHistory}
          setAiHistory={setAiHistory}
          lastAiResult={lastAiResult}
          setLastAiResult={setLastAiResult}
          aiLoading={aiLoading}
          aiError={aiError}
          aiConfig={aiConfig}
          setAiConfig={saveAiConfig}
          handleAIGenerate={handleAIGenerate}
          handleLyricsChange={handleLyricsChange}
          handleChordAdd={handleChordAdd}
          handleChordRemove={handleChordRemove}
          handleChordUpdate={handleChordUpdate}
          availableInstruments={AVAILABLE_INSTRUMENTS}
        />
      </div>
    </div>
  );
};

// Export par défaut pour compatibilité
export default AIComposerWorkspaceRefactored;
