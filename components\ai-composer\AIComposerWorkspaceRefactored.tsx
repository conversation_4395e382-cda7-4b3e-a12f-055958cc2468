'use client';

import React from 'react';

// Import des modules refactorisés
import { useAIComposerCore } from './core/AIComposerCore';
import { AIComposerHeader } from './core/AIComposerHeader';

// Import du nouveau système Mega Unified
import { AIComposerMegaUnified } from './mega/AIComposerMegaUnified';

interface AIComposerWorkspaceProps {
  songId?: string;
  onNavigateToEditor?: (songId: string) => void;
}

export const AIComposerWorkspaceRefactored: React.FC<AIComposerWorkspaceProps> = ({
  songId
}) => {
  // Hook principal pour l'état
  const {
    // États
    currentSong,
    setCurrentSong,
    styleConfig,
    setStyleConfig,
    lyricsContent,
    setLyricsContent,
    songSections,
    setSongSections,
    selectedSection,
    setSelectedSection,
    isPlaying,
    setIsPlaying,
    saving,
    setSaving,
    aiHistory,
    setAiHistory,
    lastAiResult,
    setLastAiResult,

    // Configuration IA
    aiConfig,
    saveAiConfig,
    aiLoading,
    aiError,
    isConfigured,

    // Utilitaires
    extractChordProgressions,
    extractSongStructure,

    // Contexte
    router,
    supabase,
    user
  } = useAIComposerCore(songId);

  // Fonction simple pour gérer les changements de paroles
  const handleLyricsChange = (content: string) => {
    setLyricsContent(content);
  };

  // Fonction simple pour générer avec l'IA
  const handleAIGenerate = async (prompt: string, type: string) => {
    if (!isConfigured) return;

    try {
      // Logique IA simplifiée pour le Studio Pro
      console.log('Génération IA:', { prompt, type });
      setLastAiResult(`Résultat IA pour: ${prompt.substring(0, 50)}...`);
      setAiHistory(prev => [...prev,
        { role: 'user', content: prompt },
        { role: 'assistant', content: `Résultat pour ${type}` }
      ]);
    } catch (error) {
      console.error('Erreur IA:', error);
    }
  };

  // Instruments disponibles
  const AVAILABLE_INSTRUMENTS = [
    {
      id: 'guitar',
      name: 'Guitare',
      tunings: [
        { id: 'standard', name: 'Standard', notes: ['E', 'A', 'D', 'G', 'B', 'E'] }
      ]
    },
    {
      id: 'piano',
      name: 'Piano',
      tunings: [
        { id: 'standard', name: 'Standard', notes: [] }
      ]
    }
  ];

  return (
    <div className="h-full flex flex-col bg-background">
      {/* En-tête */}
      <AIComposerHeader
        songId={songId}
        currentSong={currentSong}
        styleConfig={styleConfig}
        songSections={songSections}
        lyricsContent={lyricsContent}
        isPlaying={isPlaying}
        setIsPlaying={setIsPlaying}
        saving={saving}
        setSaving={setSaving}
        router={router}
        supabase={supabase}
        user={user}
        extractChordProgressions={extractChordProgressions}
        extractSongStructure={extractSongStructure}
      />

      {/* Mega Unified */}
      <div className="flex-1 overflow-hidden">
        <AIComposerMegaUnified
          songId={songId}
          currentSong={currentSong}
          setCurrentSong={setCurrentSong}
          lyricsContent={lyricsContent}
          setLyricsContent={setLyricsContent}
          songSections={songSections}
          setSongSections={setSongSections}
          selectedSection={selectedSection}
          setSelectedSection={setSelectedSection}
          styleConfig={styleConfig}
          setStyleConfig={setStyleConfig}
          isConfigured={isConfigured}
          aiHistory={aiHistory}
          setAiHistory={setAiHistory}
          lastAiResult={lastAiResult}
          setLastAiResult={setLastAiResult}
          aiLoading={aiLoading}
          aiError={aiError}
          aiConfig={aiConfig}
          setAiConfig={saveAiConfig}
          handleAIGenerate={handleAIGenerate}
          handleLyricsChange={handleLyricsChange}
          handleSave={async () => {
            // Logique de sauvegarde depuis le header
            console.log('Sauvegarde depuis Mega Unified');
          }}
          availableInstruments={AVAILABLE_INSTRUMENTS}
        />
      </div>
    </div>
  );
};

// Export par défaut pour compatibilité
export default AIComposerWorkspaceRefactored;
