'use client';

import React from 'react';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { UnifiedSongStructureTimeline } from './UnifiedSongStructureTimeline';

// Import des modules refactorisés
import { useAIComposerCore } from './core/AIComposerCore';
import { AIComposerHeader } from './core/AIComposerHeader';
import { AIComposerTabs } from './core/AIComposerTabs';
import { 
  useChordManagement, 
  useSectionManagement, 
  useAIFunctions, 
  useInstruments 
} from './core/AIComposerHooks';

interface AIComposerWorkspaceProps {
  songId?: string;
  onNavigateToEditor?: (songId: string) => void;
}

export const AIComposerWorkspaceRefactored: React.FC<AIComposerWorkspaceProps> = ({ 
  songId, 
  onNavigateToEditor 
}) => {
  // Hook principal pour l'état
  const {
    // États
    activeTab,
    setActiveTab,
    currentSong,
    setCurrentSong,
    styleConfig,
    setStyleConfig,
    lyricsContent,
    setLyricsContent,
    songSections,
    setSongSections,
    selectedSection,
    setSelectedSection,
    isPlaying,
    setIsPlaying,
    saving,
    setSaving,
    aiHistory,
    setAiHistory,
    lastAiResult,
    setLastAiResult,
    generalPrompt,
    setGeneralPrompt,
    
    // Configuration IA
    aiConfig,
    saveAiConfig,
    callAI,
    aiLoading,
    aiError,
    isConfigured,
    
    // Utilitaires
    parseLyricsIntoSections,
    getSectionType,
    extractChordProgressions,
    extractSongStructure,
    
    // Contexte
    router,
    supabase,
    user
  } = useAIComposerCore(songId);

  // Hooks pour la gestion des accords
  const {
    handleChordInsert,
    handleChordAdd,
    handleChordRemove,
    handleChordUpdate,
    handlePlayChord
  } = useChordManagement(songSections, setSongSections, selectedSection);

  // Hooks pour la gestion des sections
  const {
    handleLyricsChange,
    handleSectionAdd,
    handleStructureChange,
    formatLyricsWithChords
  } = useSectionManagement(
    songSections, 
    setSongSections, 
    selectedSection, 
    setSelectedSection, 
    lyricsContent, 
    setLyricsContent
  );

  // Hooks pour les fonctions IA
  const {
    handleAIGenerate
  } = useAIFunctions(
    isConfigured,
    callAI,
    songSections,
    setLastAiResult,
    setAiHistory,
    aiHistory
  );

  // Hook pour les instruments
  const { AVAILABLE_INSTRUMENTS } = useInstruments();

  return (
    <div className="h-full flex flex-col bg-background pb-20">
      {/* En-tête */}
      <AIComposerHeader
        songId={songId}
        currentSong={currentSong}
        styleConfig={styleConfig}
        songSections={songSections}
        lyricsContent={lyricsContent}
        isPlaying={isPlaying}
        setIsPlaying={setIsPlaying}
        saving={saving}
        setSaving={setSaving}
        router={router}
        supabase={supabase}
        user={user}
        extractChordProgressions={extractChordProgressions}
        extractSongStructure={extractSongStructure}
      />
      
      {/* Contenu principal */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Panneau gauche - Structure et navigation */}
          <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
            <div className="h-full border-r bg-card/50">
              <UnifiedSongStructureTimeline
                structure={{
                  sections: songSections.map(s => ({
                    id: s.id,
                    type: s.type,
                    title: s.title,
                    duration: s.duration || 16,
                    startTime: s.startTime || 0,
                    key: styleConfig.key,
                    tempo: styleConfig.bpm
                  })),
                  totalDuration: songSections.reduce((total, section) => total + (section.duration || 16), 0),
                  key: styleConfig.key,
                  tempo: styleConfig.bpm,
                  timeSignature: styleConfig.timeSignature
                }}
                onStructureChange={(structure) => {
                  const newSections = structure.sections.map(s => {
                    const existing = songSections.find(existing => existing.id === s.id);
                    return existing ? { 
                      ...existing, 
                      type: s.type, 
                      title: s.title,
                      duration: s.duration,
                      startTime: s.startTime
                    } : {
                      id: s.id,
                      type: s.type,
                      title: s.title,
                      duration: s.duration,
                      startTime: s.startTime,
                      content: '',
                      chords: []
                    };
                  });
                  setSongSections(newSections);
                }}
                onSectionSelect={setSelectedSection}
                selectedSectionId={selectedSection}
                viewMode="list"
              />
            </div>
          </ResizablePanel>
          
          <ResizableHandle withHandle />
          
          {/* Panneau central - Éditeur principal */}
          <ResizablePanel defaultSize={80} minSize={60}>
            <div className="h-full flex flex-col">
              <AIComposerTabs
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                lyricsContent={lyricsContent}
                setLyricsContent={setLyricsContent}
                songSections={songSections}
                setSongSections={setSongSections}
                selectedSection={selectedSection}
                setSelectedSection={setSelectedSection}
                styleConfig={styleConfig}
                setStyleConfig={setStyleConfig}
                currentSong={currentSong}
                setCurrentSong={setCurrentSong}
                isConfigured={isConfigured}
                aiHistory={aiHistory}
                setAiHistory={setAiHistory}
                lastAiResult={lastAiResult}
                setLastAiResult={setLastAiResult}
                handleAIGenerate={handleAIGenerate}
                handleLyricsChange={handleLyricsChange}
                handleChordAdd={handleChordAdd}
                handleChordRemove={handleChordRemove}
                handleChordUpdate={handleChordUpdate}
                availableInstruments={AVAILABLE_INSTRUMENTS}
              />
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
};

// Export par défaut pour compatibilité
export default AIComposerWorkspaceRefactored;
