"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import WaveSurfer from 'wavesurfer.js';
import { Loader2, Play, Pause, AlertCircle, Volume2, Download as DownloadIcon, VolumeX, Volume1, Trash2 } from 'lucide-react';
import { useAudioPlayerStore } from '@/lib/stores/audioPlayerStore'; 
import type { Song } from '@/types';
import { Button } from '@/components/ui/button';
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Slider } from "@/components/ui/slider";

interface AudioWaveformPreviewProps {
  audioUrl: string | null | undefined;
  song?: Song; 
  height?: number;
  onReady?: (duration: number) => void;
  allowDownload?: boolean;
  onClear?: () => void; // Prop for parent to clear the audio source
  onError?: (errorType: string, errorMessage: string) => void; // Prop to handle errors
}

const AudioWaveformPreviewInternal: React.FC<AudioWaveformPreviewProps> = ({ 
  audioUrl,
  song, 
  height = 80,
  onReady,
  allowDownload = !!audioUrl,
  onClear, // This prop is for the parent to call, not for this component to use internally to clear itself
  onError
}) => {
  console.log(`AudioWaveformPreview: PROPS RECEIVED AT COMPONENT ENTRY - audioUrl: '${audioUrl}', songId: ${song?.id}`);
  const waveformContainerRef = useRef<HTMLDivElement | null>(null);
  const waveformRef = useRef<HTMLDivElement | null>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const loadedAudioUrlRef = useRef<string | null | undefined>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isWaveSurferReady, setIsWaveSurferReady] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlayingState, setIsPlayingState] = useState(false);
  const [volume, setVolume] = useState(0.75);
  const [isMuted, setIsMuted] = useState(false);
  const [hoverTime, setHoverTime] = useState<string | null>(null);
  const [hoverPositionX, setHoverPositionX] = useState(0);

  const globalPlaySong = useAudioPlayerStore(state => state.playSong);
  const globalPauseSong = useAudioPlayerStore(state => state.pauseSong);
  // const globalResumeSong = useAudioPlayerStore(state => state.resumeSong); // resumeSong is handled by playSong or togglePlayPause
  const globalTogglePlayPause = useAudioPlayerStore(state => state.togglePlayPause);
  const globalCurrentSong = useAudioPlayerStore(state => state.currentSong);
  const isGlobalPlaying = useAudioPlayerStore(state => state.isPlaying);

  const canSyncWithGlobalPlayer = !!song;
  const isCurrentGlobalSong = canSyncWithGlobalPlayer && globalCurrentSong?.id === song?.id;

  const formatTime = (seconds: number): string => {
    if (isNaN(seconds) || !isFinite(seconds)) return "0:00";
    const minutes = Math.floor(seconds / 60);
    const secondsRemainder = Math.floor(seconds) % 60;
    const paddedSeconds = `0${secondsRemainder}`.slice(-2);
    return `${minutes}:${paddedSeconds}`;
  };

  useEffect(() => {
    if (audioUrl === '') { // Explicitly handle empty string
      if (wavesurferRef.current) {
        if (wavesurferRef.current.isPlaying()) wavesurferRef.current.stop();
        wavesurferRef.current.empty();
        wavesurferRef.current.destroy();
        wavesurferRef.current = null;
      }
      setIsLoading(false);
      setError(null); // No error for an intentionally empty URL
      if (onError) onError('info', 'Audio URL is empty, waveform cleared.');
      setIsWaveSurferReady(false);
      setDuration(0); setCurrentTime(0);
      if (onReady) onReady(0);
      return;
    }
    const isValidUrl = typeof audioUrl === 'string' && audioUrl.trim() !== '' && (audioUrl.startsWith('http://') || audioUrl.startsWith('https://') || audioUrl.startsWith('blob:http'));

    console.log(`AudioWaveformPreview: VALIDATION - audioUrl: '${audioUrl}', isValidUrl: ${isValidUrl}`);

    if (!waveformRef.current || !isValidUrl) {
      setIsLoading(false);
      const newError = audioUrl && !isValidUrl ? 'Audio URL invalide.' : null;
      setError(newError);
      if (newError && onError) onError('validation', newError);
      console.warn(`AudioWaveformPreview: SKIPPING WaveSurfer.create for '${audioUrl}'. Reason: No waveformRef or invalid URL. Error: ${newError}`);
      if (wavesurferRef.current) {
        if (wavesurferRef.current.isPlaying()) {
          wavesurferRef.current.stop();
        }
        wavesurferRef.current.empty(); // Clear the waveform data
        wavesurferRef.current.destroy();
        wavesurferRef.current = null;
      }
      setIsWaveSurferReady(false);
      setDuration(0); setCurrentTime(0);
      if (onReady) onReady(0);
      return;
    }

    setIsLoading(true); setError(null);
    if (wavesurferRef.current) {
      if (wavesurferRef.current.isPlaying()) {
        wavesurferRef.current.stop();
      }
      wavesurferRef.current.empty();
      wavesurferRef.current.destroy();
      wavesurferRef.current = null; // Ensure it's nulled before creating a new one
    }

    console.log(`AudioWaveformPreview: ATTEMPTING WaveSurfer.create for '${audioUrl}'`);
    const ws = WaveSurfer.create({
      container: waveformRef.current, url: audioUrl, height: height,
      waveColor: 'lightsteelblue', // Lighter unplayed part
      progressColor: 'deepskyblue',           // Played part
      cursorColor: 'hsl(var(--primary))',             // Cursor matches played part
      barWidth: 2, 
      barGap: 1, 
      interact: true,
    });
    wavesurferRef.current = ws;
    loadedAudioUrlRef.current = audioUrl; // Store the URL that was loaded
    ws.setVolume(isMuted ? 0 : volume);

    ws.on('ready', () => {
      setIsLoading(false); setIsWaveSurferReady(true);
      const dur = ws.getDuration(); setDuration(dur); setCurrentTime(0);
      if (onReady) onReady(dur);
    });
    ws.on('audioprocess', (time) => setCurrentTime(time));
    ws.on('play', () => setIsPlayingState(true));
    ws.on('pause', () => setIsPlayingState(false));
    ws.on('finish', () => { setIsPlayingState(false); setCurrentTime(0); ws.seekTo(0); });
    ws.on('error', (err) => {
      // Check if the error is for a URL that is no longer current for this component instance
      // 'audioUrl' is the current prop, 'loadedAudioUrlRef.current' was the URL this 'ws' instance tried to load.
      if (loadedAudioUrlRef.current !== audioUrl && wavesurferRef.current !== ws) {
        console.warn("Wavesurfer error for a stale URL/instance, likely already being cleaned up:", loadedAudioUrlRef.current, err);
        // This instance is stale and a new one is likely being created or it's being cleared.
        // We can destroy this specific 'ws' instance if it hasn't been already by the main effect.
        // However, the main useEffect should handle the destruction of 'wavesurferRef.current'.
        // This 'ws' might be an old instance if wavesurferRef.current was reassigned.
        // Safest to just ensure its state is reset and not propagate error for stale instances.
        ws.destroy(); // Destroy this specific old instance if it's still somehow active
        setIsLoading(false); // Reset loading for this specific instance context if needed
        setIsWaveSurferReady(false); // Reset ready for this specific instance context if needed
        return; // Don't propagate error for an old/stale URL
      }

      console.error("Wavesurfer error:", err);
      const errorMessage = `Erreur chargement audio: ${typeof err === 'string' ? err : (err as Error).message || 'Inconnue'}`;
      setError(errorMessage); // Set local error state for the current active instance
      if (onError) onError('wavesurfer', errorMessage); // Propagate to parent
      setIsLoading(false);
      setIsWaveSurferReady(false);
    });
    ws.on('click', (relativeX) => {
      if (wavesurferRef.current) {
        if (!canSyncWithGlobalPlayer || (isCurrentGlobalSong && !isGlobalPlaying)) {
            wavesurferRef.current.play(); 
        } else if (canSyncWithGlobalPlayer && isCurrentGlobalSong && isGlobalPlaying) {
            // Already playing via global, click just seeks.
        } else if (canSyncWithGlobalPlayer && !isCurrentGlobalSong && song) {
            globalPlaySong(song);
        }
      }
    });

    const waveformEl = waveformRef.current;
    const handleMouseMove = (event: MouseEvent) => {
      if (!ws.getDuration() || !waveformEl) return;
      const bbox = waveformEl.getBoundingClientRect();
      const progress = (event.clientX - bbox.left) / bbox.width;
      setHoverTime(formatTime(progress * ws.getDuration()));
      setHoverPositionX(event.clientX - bbox.left);
    };
    const handleMouseLeave = () => setHoverTime(null);
    waveformEl.addEventListener('mousemove', handleMouseMove);
    waveformEl.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      // URL.revokeObjectURL should be handled by the component that created the blob URL (e.g., SongForm)
      // if (loadedAudioUrlRef.current && loadedAudioUrlRef.current.startsWith('blob:')) {
      //   URL.revokeObjectURL(loadedAudioUrlRef.current);
      // }
      ws.unAll(); // Remove all event listeners for this instance
      ws.destroy(); 
      // wavesurferRef.current = null; // Main effect body handles this based on new audioUrl
      loadedAudioUrlRef.current = null;
      setIsWaveSurferReady(false); // State for the instance being destroyed
      waveformEl?.removeEventListener('mousemove', handleMouseMove);
      waveformEl?.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [audioUrl, height, onReady]);

  useEffect(() => { 
    if (canSyncWithGlobalPlayer && isCurrentGlobalSong && wavesurferRef.current && isWaveSurferReady) {
      if (isGlobalPlaying && !wavesurferRef.current.isPlaying()) wavesurferRef.current.play();
      else if (!isGlobalPlaying && wavesurferRef.current.isPlaying()) wavesurferRef.current.pause();
    } else if (canSyncWithGlobalPlayer && !isCurrentGlobalSong && wavesurferRef.current && wavesurferRef.current.isPlaying()) {
      wavesurferRef.current.pause();
    }
  }, [isGlobalPlaying, isCurrentGlobalSong, canSyncWithGlobalPlayer, isWaveSurferReady, song]);

  const handlePlayPauseClick = useCallback(() => {
    if (!wavesurferRef.current || !isWaveSurferReady) return;
    if (canSyncWithGlobalPlayer && song) {
      if (isCurrentGlobalSong) {
        // If it's the current song, just toggle play/pause
        globalTogglePlayPause();
      } else {
        // If it's a different song, play it (this will also set it as current)
        globalPlaySong(song); 
      }
    } else {
      // If not syncing with global player (e.g., just a URL preview without a full Song object)
      wavesurferRef.current.playPause();
    }
  }, [isWaveSurferReady, canSyncWithGlobalPlayer, song, isCurrentGlobalSong, globalPlaySong, globalTogglePlayPause]);

  const handleVolumeChange = (newVolume: number[]) => {
    const vol = newVolume[0] / 100;
    setVolume(vol);
    setIsMuted(vol === 0);
    if (wavesurferRef.current) wavesurferRef.current.setVolume(vol);
  };

  const toggleMute = () => {
    if (!wavesurferRef.current) return;
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    wavesurferRef.current.setVolume(newMuted ? 0 : volume);
  };
  
  const handleDownload = () => {
    if (audioUrl && allowDownload) {
      const link = document.createElement('a');
      link.href = audioUrl;
      link.download = song?.title || 'audio-download'; 
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const displayPlaying = canSyncWithGlobalPlayer && isCurrentGlobalSong ? isGlobalPlaying : isPlayingState;

  return (
    <div className="space-y-2">
      <div 
        ref={waveformContainerRef} 
        className="waveform-container group relative overflow-hidden bg-muted/90 dark:bg-zinc-800/90 backdrop-blur-sm shadow-lg rounded-md" 
        style={{ minHeight: `${height}px` }}
      >
        <div ref={waveformRef} className="w-full h-full" />
        {hoverTime && (
          <div className="absolute pointer-events-none z-20 -top-6 transform -translate-x-1/2 px-1.5 py-0.5 bg-black/70 text-white text-xs rounded shadow" style={{ left: `${hoverPositionX}px`}}>
            {hoverTime}
          </div>
        )}
        {!isLoading && !error && isWaveSurferReady && (
          <div className="absolute top-1/2 left-3 -translate-y-1/2 z-20 flex items-center gap-1">
            <Button variant="ghost" size="icon" onClick={handlePlayPauseClick} className="h-9 w-9 bg-black/50 text-white hover:bg-black/70 rounded-full" aria-label={displayPlaying ? "Pause" : "Play"}>
              {displayPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            </Button>
          </div>
        )}
        {!isLoading && !error && isWaveSurferReady && (
          <div className="absolute top-1/2 right-3 -translate-y-1/2 z-20 flex items-center gap-1">
            {allowDownload && audioUrl && (
              <Button variant="ghost" size="icon" onClick={handleDownload} className="h-8 w-8 bg-black/40 text-white hover:bg-black/60 rounded-full" title="Télécharger">
                <DownloadIcon className="h-4 w-4" />
              </Button>
            )}
            {onClear && audioUrl && (
              <Button variant="ghost" size="icon" onClick={onClear} className="h-8 w-8 bg-black/40 text-white hover:bg-black/60 rounded-full" title="Effacer">
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 bg-black/40 text-white hover:bg-black/60 rounded-full" title="Volume">
                  {isMuted ? <VolumeX className="h-4 w-4" /> : volume > 0.5 ? <Volume2 className="h-4 w-4" /> : <Volume1 className="h-4 w-4" />}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-32 p-2">
                <Slider defaultValue={[volume * 100]} max={100} step={1} onValueChange={handleVolumeChange} />
              </PopoverContent>
            </Popover>
            <div className="waveform-time text-xs bg-black/50 text-white px-1.5 py-0.5 rounded tabular-nums">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>
        )}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 rounded-[inherit]">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        )}
        {error && !isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-destructive/80 p-2 rounded-[inherit]">
            <AlertCircle className="h-5 w-5 text-destructive-foreground mr-2"/>
            <p className="text-destructive-foreground text-xs text-center font-medium">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

const AudioWaveformPreview: React.FC<AudioWaveformPreviewProps> = React.memo(AudioWaveformPreviewInternal);
export default AudioWaveformPreview;
