<#
.SYNOPSIS
    Exemple de fichier de configuration locale pour les migrations
.DESCRIPTION
    Copiez ce fichier en tant que 'config.local.ps1' et modifiez les valeurs selon votre environnement.
    Ce fichier est ignoré par git pour des raisons de sécurité.
#>

@{
    Database = @{
        Server = "localhost"
        Port = 5432
        Name = "mouvik"
        User = "postgres"
        # Définissez votre mot de passe ici ou utilisez la variable d'environnement DB_PASSWORD
        Password = $env:DB_PASSWORD ?? "votre_mot_de_passe"
    }
    
    # Paramètres de sauvegarde
    Backup = @{
        Path = "$PSScriptRoot\..\backup"
        KeepDays = 7
    }
    
    # Paramètres de journalisation
    Logging = @{
        Level = "INFO"  # DEBUG, INFO, WARN, ERROR
        File = "$PSScriptRoot\migration.log"
    }
}
