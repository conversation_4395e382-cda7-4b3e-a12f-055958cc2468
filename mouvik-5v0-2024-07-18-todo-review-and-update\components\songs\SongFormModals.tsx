import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Save } from 'lucide-react'; // Assuming lucide-react for icons
import ConfirmDeleteVersionModal from './ConfirmDeleteVersionModal'; // Adjust path as needed
import { LocalSongVersion } from './SongForm'; // Corrected import path

interface SongFormModalsProps {
  isSaveVersionModalOpen: boolean;
  setIsSaveVersionModalOpen: (isOpen: boolean) => void;
  newVersionName: string;
  setNewVersionName: (name: string) => void;
  newVersionNotes: string;
  setNewVersionNotes: (notes: string) => void;
  currentSongVersionsLength: number;
  handleSaveNewVersion: () => Promise<void>;
  isSubmittingVersion: boolean;

  isConfirmDeleteVersionModalOpen: boolean;
  setIsConfirmDeleteVersionModalOpen: (isOpen: boolean) => void;
  versionToDelete: LocalSongVersion | null;
  setVersionToDelete: (version: LocalSongVersion | null) => void;
  handleDeleteVersion: () => Promise<void>;
  isLoadingDeleteVersion: boolean;
}

export const SongFormModals: React.FC<SongFormModalsProps> = ({
  isSaveVersionModalOpen,
  setIsSaveVersionModalOpen,
  newVersionName,
  setNewVersionName,
  newVersionNotes,
  setNewVersionNotes,
  currentSongVersionsLength,
  handleSaveNewVersion,
  isSubmittingVersion,
  isConfirmDeleteVersionModalOpen,
  setIsConfirmDeleteVersionModalOpen,
  versionToDelete,
  setVersionToDelete,
  handleDeleteVersion,
  isLoadingDeleteVersion,
}) => {
  return (
    <>
      <Dialog open={isSaveVersionModalOpen} onOpenChange={setIsSaveVersionModalOpen}>
        <DialogContent className="sm:max-w-[480px]">
          <DialogHeader>
            <DialogTitle>Sauvegarder une Nouvelle Version</DialogTitle>
            <DialogDescription>
              Donnez un nom et des notes optionnelles pour cette version. Cela créera un instantané de l'état actuel du formulaire.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-3">
            <div className="space-y-1.5">
              <Label htmlFor={`version-name-${currentSongVersionsLength}`}>Nom de la version (optionnel)</Label>
              <Input id={`version-name-${currentSongVersionsLength}`} value={newVersionName} onChange={(e) => setNewVersionName(e.target.value)} placeholder={`Ex: Version ${currentSongVersionsLength + 1}, Arrangement acoustique`}/>
            </div>
            <div className="space-y-1">
              <Label htmlFor={`version-notes-${currentSongVersionsLength}`}>Notes (optionnel)</Label>
              <Textarea id={`version-notes-${currentSongVersionsLength}`} value={newVersionNotes} onChange={(e) => setNewVersionNotes(e.target.value)} placeholder="Ajoutez des notes sur les changements apportés dans cette version..." />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSaveVersionModalOpen(false)}>Annuler</Button>
            <Button type="button" onClick={handleSaveNewVersion} disabled={isSubmittingVersion}>
              {isSubmittingVersion ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Sauvegarder Version
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {isConfirmDeleteVersionModalOpen && versionToDelete && (
        <ConfirmDeleteVersionModal
          isOpen={isConfirmDeleteVersionModalOpen}
          onClose={() => {
            setIsConfirmDeleteVersionModalOpen(false);
            setVersionToDelete(null);
          }}
          onConfirm={handleDeleteVersion}
          versionName={versionToDelete?.version_name || (versionToDelete as any)?.name || versionToDelete?.id}
          createdAt={versionToDelete?.created_at}
          isDeleting={isLoadingDeleteVersion}
        />
      )}
    </>
  );
};