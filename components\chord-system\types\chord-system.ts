/**
 * 🎼 SYSTÈME D'ACCORDS UNIFIÉ - Types et Interfaces
 * 
 * Types unifiés pour le système d'accords de MOUVIK
 * Compatible avec les données JSON existantes et les composants actuels
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

// ============================================================================
// TYPES DE BASE
// ============================================================================

/** Instruments supportés par le système */
export type InstrumentType = 
  | 'guitar' 
  | 'piano' 
  | 'ukulele' 
  | 'mandolin' 
  | 'banjo' 
  | 'bass';

/** Niveaux de difficulté pour les accords */
export type DifficultyLevel = 
  | 'beginner' 
  | 'intermediate' 
  | 'advanced';

/** Catégories d'accords */
export type ChordCategory = 
  | 'major' 
  | 'minor' 
  | '7th' 
  | 'extended' 
  | 'altered' 
  | 'suspended' 
  | 'diminished' 
  | 'augmented';

/** Modes de lecture audio */
export type PlaybackMode = 
  | 'chord' 
  | 'arpeggio';

/** Patterns d'arpège */
export type ArpeggioPattern = 
  | 'ascending' 
  | 'descending' 
  | 'fingerpicking' 
  | 'strum_down' 
  | 'strum_up';

// ============================================================================
// INTERFACES POUR DONNÉES JSON (Compatibilité existante)
// ============================================================================

/** Structure des barrés dans les JSON */
export interface BarrePosition {
  fret: number;
  fromString: number;
  toString: number;
}

/** Position d'accord dans les fichiers JSON */
export interface ChordJsonPosition {
  frets: (number | string)[]; // Permet 'x' pour cordes muettes
  fingers?: (number | string)[];
  barres?: BarrePosition[];
  midi?: number[];
  baseFret?: number;
  difficulty?: DifficultyLevel;
}

/** Variation d'accord dans les JSON */
export interface ChordJsonVariation {
  suffix: string;
  name: string;
  positions: ChordJsonPosition[];
}

/** Structure complète d'un fichier JSON d'instrument */
export interface ChordJsonDefinition {
  instrument: string;
  tuning: string[];
  strings: number;
  fretRange?: [number, number];
  keys: string[];
  suffixes: string[];
  chords: {
    [rootNote: string]: ChordJsonVariation[];
  };
}

// ============================================================================
// INTERFACES UNIFIÉES (Nouveau système)
// ============================================================================

/** Position d'accord unifiée - Interface principale */
export interface UnifiedChordPosition {
  // Identification unique
  id: string;
  
  // Informations de base
  chord: string;                    // "Am", "C7", "Dmaj7"
  instrument: InstrumentType;
  tuning: string;                   // "standard", "drop_d", "open_g"
  
  // Données de position (compatibles JSON existants)
  frets: (string | number)[];       // [0, 2, 2, 1, 0, 0] ou ['x', 3, 2, 0, 1, 0]
  fingers?: number[];               // [0, 2, 3, 1, 0, 0]
  baseFret: number;                 // Position de base sur le manche
  barres?: BarrePosition[];         // Barrés
  
  // Données audio
  midi?: number[];                  // Notes MIDI [60, 64, 67]
  notes?: string[];                 // Notes ["C", "E", "G"]
  
  // Métadonnées
  difficulty: DifficultyLevel;
  category?: ChordCategory;
  tags?: string[];
  
  // Données visuelles et audio
  preview?: {
    svg?: string;                   // SVG du diagramme
    audio?: string;                 // URL audio preview
  };
  
  // Métadonnées système
  createdAt?: string;
  updatedAt?: string;
}

/** Configuration d'instrument */
export interface InstrumentConfig {
  type: InstrumentType;
  name: string;                     // "Guitare", "Piano", "Ukulélé"
  strings?: number;                 // Nombre de cordes (instruments à cordes)
  tunings: {
    [tuningName: string]: {
      name: string;                 // "Standard", "Drop D"
      notes: string[];              // ["E", "A", "D", "G", "B", "E"]
    };
  };
  fretRange?: [number, number];     // [0, 24] pour guitare
  defaultTuning: string;
}

/** Progression d'accords */
export interface ChordProgression {
  id: string;
  name: string;
  description?: string;
  chords: UnifiedChordPosition[];
  key: string;                      // Tonalité principale
  tempo: number;
  timeSignature: string;            // "4/4", "3/4", "6/8"
  genre?: string;
  mood?: string;
  tags?: string[];
  
  // Métadonnées
  createdAt: string;
  updatedAt: string;
  userId?: string;
}

/** Section de chanson avec grille d'accords */
export interface ChordGridSection {
  id: string;
  name: string;                     // "Verse 1", "Chorus", "Bridge"
  measures: ChordMeasure[];
  timeSignature: string;
  key: string;
  tempo: number;
  repeats?: number;
  
  // Métadonnées
  order: number;                    // Ordre dans la chanson
  color?: string;                   // Couleur pour l'interface
}

/** Mesure dans une grille d'accords */
export interface ChordMeasure {
  id: string;
  number: number;                   // Position dans la section (1, 2, 3, 4...)
  chords: ChordPlacement[];
  beats: number;                    // Nombre de temps dans la mesure
}

/** Placement d'accord dans une mesure */
export interface ChordPlacement {
  chord: UnifiedChordPosition;
  beat: number;                     // Sur quel temps (1, 2, 3, 4)
  duration: number;                 // Durée en beats
  emphasis?: 'strong' | 'weak';     // Accentuation
  strum?: ArpeggioPattern;          // Pattern de jeu
}

// ============================================================================
// INTERFACES POUR RECHERCHE ET FILTRAGE
// ============================================================================

/** Filtres de recherche d'accords */
export interface ChordSearchFilters {
  instrument?: InstrumentType;
  tuning?: string;
  key?: string;                     // Tonalité "C", "Am", etc.
  difficulty?: DifficultyLevel | 'all';
  category?: ChordCategory;
  hasAudio?: boolean;
  tags?: string[];
  searchTerm?: string;              // Recherche textuelle
}

/** Résultat de recherche d'accords */
export interface ChordSearchResult {
  chord: UnifiedChordPosition;
  relevance: number;                // Score de pertinence 0-100
  matchedFields: string[];          // Champs qui matchent la recherche
}

/** Suggestion d'accord par l'IA */
export interface ChordSuggestion {
  chord: UnifiedChordPosition;
  reason: string;                   // "Complète la progression I-V-vi-IV"
  confidence: number;               // 0-100%
  musicTheory: string;              // "Dominante de la tonalité"
  alternatives?: UnifiedChordPosition[]; // Alternatives possibles
}

// ============================================================================
// INTERFACES POUR L'ÉTAT DU SYSTÈME
// ============================================================================

/** État global du système d'accords */
export interface ChordSystemState {
  // Configuration actuelle
  selectedInstrument: InstrumentType;
  selectedTuning: string;
  availableInstruments: InstrumentConfig[];
  
  // Accords et progressions
  selectedChords: UnifiedChordPosition[];
  currentProgression: ChordProgression | null;
  savedProgressions: ChordProgression[];
  
  // Interface utilisateur
  isPlaying: boolean;
  currentChord: UnifiedChordPosition | null;
  searchFilters: ChordSearchFilters;
  
  // Grille de composition
  songSections: ChordGridSection[];
  selectedSection: string | null;
  
  // Audio
  volume: number;
  isMuted: boolean;
  playMode: PlaybackMode;
  
  // Cache et performance
  loadedInstruments: Set<string>;
  chordCache: Map<string, UnifiedChordPosition[]>;
  
  // Erreurs et chargement
  loading: boolean;
  error: string | null;
}

/** Actions disponibles dans le système */
export interface ChordSystemActions {
  // Configuration
  selectInstrument: (instrument: InstrumentType) => void;
  selectTuning: (tuning: string) => void;
  
  // Gestion des accords
  selectChord: (chord: UnifiedChordPosition) => void;
  addToProgression: (chord: UnifiedChordPosition) => void;
  removeFromProgression: (chordId: string) => void;
  reorderProgression: (fromIndex: number, toIndex: number) => void;
  
  // Progressions
  saveProgression: (progression: ChordProgression) => Promise<void>;
  loadProgression: (progressionId: string) => Promise<void>;
  deleteProgression: (progressionId: string) => Promise<void>;
  
  // Grille de composition
  addSection: (section: Omit<ChordGridSection, 'id'>) => void;
  updateSection: (sectionId: string, updates: Partial<ChordGridSection>) => void;
  deleteSection: (sectionId: string) => void;
  addChordToMeasure: (sectionId: string, measureId: string, chord: ChordPlacement) => void;
  
  // Audio
  playChord: (chord: UnifiedChordPosition, options?: PlaybackOptions) => Promise<void>;
  playProgression: (progression: ChordProgression) => Promise<void>;
  stopPlayback: () => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  
  // Recherche
  searchChords: (filters: ChordSearchFilters) => Promise<ChordSearchResult[]>;
  getSuggestions: (context: ChordProgression) => Promise<ChordSuggestion[]>;
  
  // Utilitaires
  clearError: () => void;
  resetState: () => void;
}

/** Options de lecture audio */
export interface PlaybackOptions {
  mode?: PlaybackMode;
  pattern?: ArpeggioPattern;
  duration?: number;                // en millisecondes
  volume?: number;                  // 0-1
  reverb?: number;                  // 0-1
}

// ============================================================================
// TYPES UTILITAIRES
// ============================================================================

/** Type pour les fonctions de transformation de données */
export type ChordDataTransformer<T> = (data: ChordJsonDefinition) => T[];

/** Type pour les validateurs de données */
export type ChordDataValidator = (data: unknown) => data is ChordJsonDefinition;

/** Type pour les gestionnaires d'erreur */
export type ErrorHandler = (error: Error, context?: string) => void;

// ============================================================================
// CONSTANTES ET ENUMS
// ============================================================================

/** Instruments supportés avec métadonnées */
export const SUPPORTED_INSTRUMENTS: Record<InstrumentType, InstrumentConfig> = {
  guitar: {
    type: 'guitar',
    name: 'Guitare',
    strings: 6,
    tunings: {
      standard: { name: 'Standard', notes: ['E', 'A', 'D', 'G', 'B', 'E'] },
      drop_d: { name: 'Drop D', notes: ['D', 'A', 'D', 'G', 'B', 'E'] },
      open_g: { name: 'Open G', notes: ['D', 'G', 'D', 'G', 'B', 'D'] }
    },
    fretRange: [0, 24],
    defaultTuning: 'standard'
  },
  piano: {
    type: 'piano',
    name: 'Piano',
    tunings: {
      standard: { name: 'Standard', notes: [] }
    },
    defaultTuning: 'standard'
  },
  ukulele: {
    type: 'ukulele',
    name: 'Ukulélé',
    strings: 4,
    tunings: {
      gcea: { name: 'GCEA Standard', notes: ['G', 'C', 'E', 'A'] }
    },
    fretRange: [0, 15],
    defaultTuning: 'gcea'
  },
  mandolin: {
    type: 'mandolin',
    name: 'Mandoline',
    strings: 4,
    tunings: {
      gdae: { name: 'GDAE Standard', notes: ['G', 'D', 'A', 'E'] }
    },
    fretRange: [0, 20],
    defaultTuning: 'gdae'
  },
  banjo: {
    type: 'banjo',
    name: 'Banjo',
    strings: 5,
    tunings: {
      open_g: { name: 'Open G', notes: ['D', 'G', 'B', 'D'] }
    },
    fretRange: [0, 22],
    defaultTuning: 'open_g'
  },
  bass: {
    type: 'bass',
    name: 'Basse',
    strings: 4,
    tunings: {
      standard: { name: 'Standard', notes: ['E', 'A', 'D', 'G'] }
    },
    fretRange: [0, 24],
    defaultTuning: 'standard'
  }
};

/** Niveaux de difficulté avec descriptions */
export const CHORD_DIFFICULTIES: Record<DifficultyLevel, { name: string; description: string }> = {
  beginner: {
    name: 'Débutant',
    description: 'Accords simples, positions faciles'
  },
  intermediate: {
    name: 'Intermédiaire',
    description: 'Accords avec barrés, positions moyennes'
  },
  advanced: {
    name: 'Avancé',
    description: 'Accords complexes, positions difficiles'
  }
};

/** Accordages par défaut pour chaque instrument */
export const DEFAULT_TUNINGS: Record<InstrumentType, string> = {
  guitar: 'standard',
  piano: 'standard',
  ukulele: 'gcea',
  mandolin: 'gdae',
  banjo: 'open_g',
  bass: 'standard'
};
