# Deployment Guide

## Overview

This guide explains how to deploy the MOUVIK application to production environments. The application is designed to be deployed on Vercel for the frontend and uses Supabase for the backend services.

## Prerequisites

- A Vercel account
- A Supabase account
- A domain name (optional)

## Environment Variables

The following environment variables are required for deployment:

\`\`\`
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Application
NEXT_PUBLIC_APP_URL=your-app-url
NEXT_PUBLIC_APP_NAME=MOUVIK

# Storage
NEXT_PUBLIC_STORAGE_URL=your-storage-url
\`\`\`

## Deploying to Vercel

1. **Connect your repository to Vercel**

   - Go to [Vercel](https://vercel.com) and sign in
   - Click "New Project"
   - Import your repository
   - Configure the project settings

2. **Configure environment variables**

   - Add all the required environment variables in the Vercel project settings

3. **Deploy the project**

   - Click "Deploy"
   - Vercel will build and deploy your application

4. **Set up a custom domain (optional)**

   - Go to the project settings
   - Navigate to the "Domains" section
   - Add your custom domain
   - Follow the instructions to configure DNS settings

## Supabase Setup

1. **Create a new Supabase project**

   - Go to [Supabase](https://supabase.com) and sign in
   - Create a new project
   - Note the project URL and API keys

2. **Set up the database schema**

   - Use the SQL scripts in the `db` directory to set up the database schema
   - You can run these scripts in the Supabase SQL editor
   - Start with the base schema and then apply any migrations

3. **Configure authentication**

   - Go to the Authentication settings in your Supabase project
   - Configure email authentication
   - Set up any additional authentication providers as needed
   - Configure email templates for verification, password reset, etc.

4. **Set up storage buckets**

   - Go to the Storage section in your Supabase project
   - Create the following buckets:
     - `avatars` - for user profile images
     - `album-covers` - for album cover images
     - `song-covers` - for song cover images
     - `audio` - for audio files
     - `waveforms` - for waveform data files
   - Configure appropriate permissions for each bucket

5. **Set up database triggers and functions**

   - Implement the necessary database triggers and functions for:
     - Updating search indexes
     - Incrementing play and view counts
     - Managing user relationships
     - Handling content moderation

## Continuous Deployment

To set up continuous deployment:

1. **Configure GitHub Actions**

   - Create a `.github/workflows/deploy.yml` file
   - Set up the workflow to deploy to Vercel on push to the main branch

2. **Set up preview deployments**

   - Vercel automatically creates preview deployments for pull requests
   - Use these previews to test changes before merging to main

## Monitoring and Maintenance

1. **Set up monitoring**

   - Use Vercel Analytics to monitor frontend performance
   - Set up Supabase monitoring for backend services
   - Configure error tracking with a service like Sentry

2. **Regular backups**

   - Configure regular database backups in Supabase
   - Store backups securely

3. **Update dependencies**

   - Regularly update dependencies to ensure security and performance
   - Test thoroughly after updates

## Scaling Considerations

As your application grows, consider the following scaling strategies:

1. **Database optimization**

   - Add indexes for frequently queried columns
   - Optimize complex queries
   - Consider using materialized views for complex reports

2. **Content delivery**

   - Use a CDN for static assets
   - Consider regional deployments for global audiences

3. **Storage optimization**

   - Implement file compression for audio files
   - Use appropriate file formats for different use cases
   - Consider tiered storage for less frequently accessed content

## Troubleshooting

Common deployment issues and their solutions:

1. **Build failures**

   - Check build logs for errors
   - Ensure all dependencies are correctly installed
   - Verify environment variables are correctly set

2. **Database connection issues**

   - Check network settings in Supabase
   - Verify connection strings
   - Check for IP restrictions

3. **Storage access problems**

   - Verify bucket permissions
   - Check storage policies
   - Ensure correct authentication for storage operations

For additional help, refer to the Vercel and Supabase documentation or reach out to the community.
