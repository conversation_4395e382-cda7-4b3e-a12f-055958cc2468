"use client";

import { useEffect, useState, useRef } from 'react'; // Removed useCallback, added useRef
import { useRouter } from 'next/navigation'; 
import { createBrowserClient } from '@supabase/ssr'; 
import { SupabaseClient } from '@supabase/supabase-js'; 
// Removed useForm, SubmitHandler, zodResolver, z as SongForm handles this
import { toast } from "@/hooks/use-toast"; 

import { SongForm, SongFormValues, SongFormHandle } from '@/components/songs/SongForm'; // Import SongForm, SongFormValues, SongFormHandle
import { Button } from "@/components/ui/button"; 
import { Loader2 as LoadingSpinner } from 'lucide-react'; 

// Removed musicalKeys, timeSignatures, songFormSchema, and local SongFormValues interface

interface Album {
  id: string;
  title: string;
}

// Removed constants for localStorage keys related to AI config, as SongForm and its children handle this.
// Removed NO_ALBUM_SELECTED_VALUE if SongForm handles this logic internally (it does)

export default function CreateSongPage() {
  const songFormRef = useRef<SongFormHandle>(null); // Ref for SongForm component
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
  const router = useRouter();

  // Removed stripHtml utility if not used elsewhere; SongForm handles lyrics stripping.

  const [isSubmitting, setIsSubmitting] = useState(false); // Renamed from isLoading
  const [albums, setAlbums] = useState<Album[]>([]);
  const [isLoadingAlbums, setIsLoadingAlbums] = useState(true);

  // Removed states: uploadingAudio, uploadedAudioUrl, uploadingImage, uploadedImageUrl, lyricsContent, 
  // aiConfig, aiLoading, aiLastResult, aiError, aiHistory, aiMaxOutputTokens, aiGeneralPrompt
  // Removed refs: audioFileInputRef, imageFileInputRef
  // Removed form hook: const form = useForm(...)
  // Removed handleLyricsChange and other specific field handlers

  // Fetch albums - remains the same
  useEffect(() => {
    const fetchAlbums = async () => {
      setIsLoadingAlbums(true);
      const { data, error } = await supabase.from('albums').select('id, title');
      if (error) {
        console.error('Error fetching albums:', error);
        toast({
          title: "Erreur lors du chargement des albums",
          description: `Erreur lors du chargement des albums: ${error.message}`,
          variant: "destructive",
        });
      } else {
        setAlbums(data || []);
      }
      setIsLoadingAlbums(false);
    };
    fetchAlbums();
  }, [supabase]);

  // Removed useEffect for loading AI config from localStorage - SongForm/AiConfigMenu handles this.
  // Removed AI interaction handlers (handleAiGenerate, handleAiCorrect, handleAiTranslate, handleAiStream)
  // Removed file upload handlers (handleAudioUpload, handleImageUpload)

  const onFormSubmit = async (data: SongFormValues) => {
    setIsSubmitting(true); // Use setIsSubmitting
    try {
      const { data: userSessionData, error: sessionError } = await supabase.auth.getUser();
      if (sessionError || !userSessionData?.user) {
        toast({ title: "Erreur d'authentification", description: "Impossible de récupérer l'utilisateur. Veuillez vous reconnecter.", variant: "destructive" });
        router.push('/login');
        return;
      }
      const userId = userSessionData.user.id;

      const { 
          // Destructure all individual credit fields from data
          featured_artists, composer_name, writers, producers, record_label, isrc, upc,
          // Destructure the 'credits' field (expected to be an object from SongForm)
          credits: advancedCreditsInput, 
          // Destructure other SongFormValues
          ...restOfData 
      } = data;

      let combinedCredits: Record<string, any> = {}; // Use Record<string, any> for better type safety

      // Populate from individual string fields, converting comma-separated to arrays if applicable
      if (featured_artists) combinedCredits.featured_artists = featured_artists.split(',').map(s => s.trim()).filter(s => s);
      if (composer_name) combinedCredits.composer_name = composer_name;
      if (writers) combinedCredits.writers = writers.split(',').map(s => s.trim()).filter(s => s);
      if (producers) combinedCredits.producers = producers.split(',').map(s => s.trim()).filter(s => s);
      if (record_label) combinedCredits.record_label = record_label;
      if (isrc) combinedCredits.isrc = isrc;
      if (upc) combinedCredits.upc = upc;
      
      // Merge with advanced credits input (assuming it's an object or null/undefined)
      // Properties in advancedCreditsInput will overwrite individual fields if names clash.
      if (advancedCreditsInput && typeof advancedCreditsInput === 'object') {
          combinedCredits = { ...combinedCredits, ...advancedCreditsInput };
      }
      
      let editorDataObject = null;
      if (restOfData.editor_data) { // editor_data is part of restOfData
         if (typeof restOfData.editor_data === 'string') {
          try { 
            if (restOfData.editor_data.trim() === "") editorDataObject = null;
            else editorDataObject = JSON.parse(restOfData.editor_data); 
          } catch (e) { 
            console.error("Error parsing editor_data JSON:", e); 
            toast({ title: "Erreur JSON Données Éditeur", description: "Format JSON invalide pour les données éditeur.", variant: "destructive" });
            setIsSubmitting(false); return;
          }
        } else if (typeof restOfData.editor_data === 'object') {
          editorDataObject = restOfData.editor_data;
        }
      }

      const songDataToInsert = {
        ...restOfData, // Contains all fields from SongFormValues except the explicitly destructured credit fields and 'credits' (advancedCreditsInput)
        creator_user_id: userId,
        bpm: restOfData.bpm ? Number(restOfData.bpm) : null,
        credits: Object.keys(combinedCredits).length > 0 ? combinedCredits : null,
        editor_data: editorDataObject,
        // Ensure other potentially modified fields are correctly set if needed
        // For example, if any boolean fields from restOfData need explicit undefined checks:
        is_public: restOfData.is_public === undefined ? false : restOfData.is_public,
        is_explicit: restOfData.is_explicit === undefined ? false : restOfData.is_explicit,
        is_archived: restOfData.is_archived === undefined ? false : restOfData.is_archived,
      };
      
      // Whitelist of real columns in the public.songs table
      const SONGS_TABLE_COLUMNS = [
        'id', 'creator_user_id', 'album_id', 'band_id', 'title', 'artist', 
        'duration_ms', 'genre', 'subgenre', 'mood', 'theme', 'instrumentation', 
        'bpm', 'key', 'audio_url', 'cover_art_url', 'lyrics', 'credits', 
        'tags', 'release_date', 'is_public', 'is_explicit', 'visibility', 
        'editor_data', 'is_archived', 'created_at', 'updated_at', 'description' // Added description to whitelist
      ];
      
      // Filter the songDataToInsert based on the whitelist
      const filteredData = Object.fromEntries(
        Object.entries(songDataToInsert).filter(([key]) => SONGS_TABLE_COLUMNS.includes(key))
      );

      // Remove only undefined values from the filtered data before inserting. Null is acceptable for nullable columns.
      const cleanedPayload = Object.fromEntries(
        Object.entries(filteredData).filter(([_, value]) => value !== undefined)
      );
      
      // Ensure essential fields like creator_user_id and title are present
      if (!cleanedPayload.creator_user_id && songDataToInsert.creator_user_id) {
         cleanedPayload.creator_user_id = songDataToInsert.creator_user_id;
      }
       if (!cleanedPayload.title && songDataToInsert.title) {
         cleanedPayload.title = songDataToInsert.title;
       }
       // 'artist' is nullable, so no need to force add it if it's not provided.

      console.log("Cleaned Payload for Insert:", cleanedPayload); // Log the final payload

      const { data: newSong, error } = await supabase
        .from('songs')
        .insert(cleanedPayload)
        .select()
        .maybeSingle();

      if (error || !newSong) {
        console.error('Error inserting song:', error);
        toast({
          title: "Erreur lors de la création",
          description: error ? `La chanson n'a pas pu être créée: ${error.message}` : "Aucune donnée de chanson retournée.",
          variant: "destructive",
        });
        setIsSubmitting(false); // Ensure isSubmitting is reset on error
        return; // Do not proceed if song creation failed
      }
      // If successful:
      toast({ title: "Chanson Créée!", description: `"${newSong.title}" a été ajoutée.` });
      // Save pending vault items
      if (songFormRef.current) {
        const vaultActions = songFormRef.current.getVaultActions();
        if (vaultActions && newSong.id && userId) {
          try {
            await vaultActions.savePendingItems(newSong.id, userId);
            // Toast for vault save success is handled within savePendingItems
          } catch (vaultError: any) {
            console.error("Error saving vault items:", vaultError);
            toast({ title: "Erreur Vault", description: `Les fichiers du vault n'ont pas pu être sauvegardés: ${vaultError.message}`, variant: "destructive"});
          }
        }
      }
      router.push(`/manage-songs/${newSong.id}/edit`); 
      // setIsSubmitting(false); // Already handled in finally block, but good to be explicit if needed before async ops like router.push
      
    } catch (e: any) {
      console.error('Unexpected error during song creation:', e);
      toast({ title: "Erreur Inattendue", description: e.message || "Une erreur inconnue s'est produite.", variant: "destructive" });
    } finally {
      setIsSubmitting(false); 
    }
  };

  if (isLoadingAlbums) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner className="h-8 w-8 animate-spin" />
        <p className="ml-2">Chargement des albums...</p>
      </div>
    );
  }
  
  return (
    <main className="w-full min-h-screen bg-background flex flex-col items-stretch">
      {/* Removed max-w-[1600px] and mx-auto for full width */}
      <div className="w-full px-2 sm:px-6 lg:px-12 py-6">
        <h1 className="text-3xl sm:text-4xl font-bold tracking-tight text-gray-900 dark:text-white mb-2">
          Créer un morceau
        </h1>
        <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400 mb-8">
          Partagez votre musique avec le monde.
        </p>
        <SongForm
          mode="create"
          onFormSubmit={onFormSubmit}
          isSubmitting={isSubmitting}
          albums={albums}
          isLoadingAlbums={isLoadingAlbums}
          supabaseClient={supabase as SupabaseClient}
          ref={songFormRef} // Pass the ref to SongForm
        />
      </div>
    </main>
  );
}
