import React from 'react';
import { Control, FieldValues, FieldPath, useFormContext } from 'react-hook-form';
import { DatePicker, DatePickerProps } from '@/components/ui/date-picker'; // Adjust path as necessary
import { FormField, FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

// Define RHFDatePickerProps, extending DatePickerProps but omitting RHF-controlled props
interface RHFDatePickerProps<TFieldValues extends FieldValues = FieldValues>
  extends Omit<DatePickerProps, 'date' | 'onSelect'> { // 'date' is the prop for selected date in DatePicker
  name: FieldPath<TFieldValues>;
  label?: string;
  description?: React.ReactNode; // Corresponds to original helperText and new signature's description
  className?: string;           // From new signature
  placeholder?: string;         // From new signature
  control?: Control<TFieldValues>;
}

export const RHFDatePicker = <TFieldValues extends FieldValues = FieldValues>({
  control: controlProp,
  name,
  label,
  description,
  className,
  placeholder,
  ...rest
}: RHFDatePickerProps<TFieldValues>) => {
  const { control: contextControl } = useFormContext<TFieldValues>() || {};
  const finalControl = controlProp || contextControl;

  if (!finalControl) {
    console.error('RHFDatePicker requires control prop or to be used within a FormProvider.');
    return <FormItem className={className}><FormLabel>{label}</FormLabel><FormMessage>Control not found or FormProvider is missing.</FormMessage></FormItem>;
  }

  return (
    <FormField
      control={finalControl}
      name={name}
      render={({ field, fieldState: { error } }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <DatePicker
              {...rest}
              date={field.value ? new Date(field.value) : undefined} // DatePicker expects 'date' prop, not 'selected'
              onSelect={(selectedDate) => { // Renaming for clarity, DatePicker's onSelect provides the selected date
                field.onChange(selectedDate ? selectedDate.toISOString() : null);
              }}
            />
          </FormControl>
          {description && !error && <FormDescription>{description}</FormDescription>}
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormItem>
      )}
    />
  );
}