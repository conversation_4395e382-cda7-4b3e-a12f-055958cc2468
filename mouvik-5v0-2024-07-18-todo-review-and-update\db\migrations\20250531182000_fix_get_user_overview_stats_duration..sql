DROP FUNCTION IF EXISTS public.get_user_overview_stats(uuid, text);

CREATE OR REPLACE FUNCTION public.get_user_overview_stats(p_user_id uuid, p_time_range text)
 RETURNS TABLE(total_plays bigint, total_songs bigint, total_albums bigint, total_followers bigint, total_views bigint, total_likes bigint, total_comments bigint, total_duration_seconds numeric)
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_start_date DATE;
    v_end_date DATE := CURRENT_DATE + INTERVAL '1 day';
BEGIN
    IF p_time_range = '7d' THEN
        v_start_date := CURRENT_DATE - INTERVAL '7 days';
    ELSIF p_time_range = '30d' THEN
        v_start_date := CURRENT_DATE - INTERVAL '30 days';
    ELSIF p_time_range = '90d' THEN
        v_start_date := CURRENT_DATE - INTERVAL '90 days';
    ELSIF p_time_range = 'all' THEN
        v_start_date := '1970-01-01'::DATE;
    ELSE
        v_start_date := CURRENT_DATE - INTERVAL '30 days'; -- Default
    END IF;

    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM song_plays sp JOIN songs s_plays ON sp.song_id = s_plays.id WHERE s_plays.creator_user_id = p_user_id AND sp.played_at >= v_start_date AND sp.played_at < v_end_date) AS total_plays,
        (SELECT COUNT(*) FROM songs s_songs WHERE s_songs.creator_user_id = p_user_id AND s_songs.created_at >= v_start_date AND s_songs.created_at < v_end_date) AS total_songs,
        (SELECT COUNT(DISTINCT alb.id) FROM albums alb WHERE alb.user_id = p_user_id AND alb.created_at >= v_start_date AND alb.created_at < v_end_date) AS total_albums,
        (SELECT COUNT(*) FROM public.profile_followers pf WHERE pf.followed_profile_id = p_user_id AND pf.created_at >= v_start_date AND pf.created_at < v_end_date) AS total_followers,
        (
            (SELECT COUNT(rv.id)
             FROM resource_views rv
             JOIN songs s_view ON rv.resource_id = s_view.id AND rv.resource_type = 'song'
             WHERE s_view.creator_user_id = p_user_id AND rv.viewed_at >= v_start_date AND rv.viewed_at < v_end_date)
            +
            (SELECT COUNT(rv.id)
             FROM resource_views rv
             JOIN albums a_view ON rv.resource_id = a_view.id AND rv.resource_type = 'album'
             WHERE a_view.user_id = p_user_id AND rv.viewed_at >= v_start_date AND rv.viewed_at < v_end_date)
        ) AS total_views,
        (SELECT COUNT(*) FROM song_likes sl JOIN songs s_likes ON sl.song_id = s_likes.id WHERE s_likes.creator_user_id = p_user_id AND sl.created_at >= v_start_date AND sl.created_at < v_end_date) AS total_likes,
        (SELECT COUNT(*) FROM song_comments sc JOIN songs s_comments ON sc.song_id = s_comments.id WHERE s_comments.creator_user_id = p_user_id AND sc.created_at >= v_start_date AND sc.created_at < v_end_date) AS total_comments,
        (SELECT COALESCE(SUM(s_duration.duration)::numeric, 0) FROM songs s_duration WHERE s_duration.creator_user_id = p_user_id AND s_duration.created_at >= v_start_date AND s_duration.created_at < v_end_date) AS total_duration_seconds;
END;
$function$;

COMMENT ON FUNCTION public.get_user_overview_stats(uuid, text) IS 'Retrieves overview statistics for a user based on a specified time range. Corrected duration calculation.';
