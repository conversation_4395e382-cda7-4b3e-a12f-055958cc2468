"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase/client"
import { useToast } from "@/hooks/use-toast"

const genres = [
  "Pop",
  "Rock",
  "Hip-Hop",
  "R&B",
  "Jazz",
  "Électronique",
  "Classique",
  "Folk",
  "Country",
  "Blues",
  "Reggae",
  "Metal",
  "Punk",
  "Funk",
  "Soul",
  "Ambient",
  "Expérimental",
  "Indie",
  "Dance",
  "Trap",
  "Lo-fi",
  "House",
  "Techno",
  "Autre",
]

const albumTypes = ["Album", "EP", "Single", "Compilation", "Live", "Remix", "Autre"]

export default function EditAlbumPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [activeTab, setActiveTab] = useState("info")

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    genre: "",
    albumType: "Album",
    releaseDate: "",
    isExplicit: false,
    status: "draft",
    coverUrl: "",
    label: "",
    upc: "",
    copyright: "",
    recordingYear: "",
    language: "fr",
    tags: [] as string[],
    songs: [] as string[],
    isPublic: true,
    notifyFollowers: true,
    addToDiscovery: true,
    collaborators: [] as {id: string, role: string}[],
    notes: ""
  })

  const [currentTag, setCurrentTag] = useState("")
  const [availableSongs, setAvailableSongs] = useState<any[]>([])
  const [selectedSongs, setSelectedSongs] = useState<any[]>([])
  const [availableUsers, setAvailableUsers] = useState<any[]>([])
  const [collaboratorRole, setCollaboratorRole] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [existingCollaborators, setExistingCollaborators] = useState<any[]>([])

  useEffect(() => {
    const fetchAlbumData = async () => {
      try {
        const supabase = getSupabaseClient()

        // Récupérer les données de l'album
        const { data: album, error } = await supabase
          .from("albums")
          .select("*")
          .eq("id", params.id)
          .single()

        if (error) {
          throw error
        }

        if (!album) {
          toast({
            title: "Erreur",
            description: "Album non trouvé",
            variant: "destructive",
          })
          router.push("/dashboard/albums")
          return
        }

        // Récupérer les morceaux associés à l'album
        const { data: albumSongs } = await supabase
          .from("album_songs")
          .select("song_id, track_number, songs(id, title, cover_url, duration, status, user_id, profiles(username, avatar_url))")
          .eq("album_id", params.id)
          .order("track_number", { ascending: true })

        // Récupérer les tags associés à l'album
        const { data: resourceTags } = await supabase
          .from("resource_tags")
          .select("tags(id, name)")
          .eq("resource_type", "album")
          .eq("resource_id", params.id)

        // Récupérer les collaborateurs
        const { data: collaborations } = await supabase
          .from("collaborations")
          .select("user_id, role, profiles(id, username, display_name, avatar_url)")
          .eq("resource_type", "album")
          .eq("resource_id", params.id)

        const albumTags = resourceTags?.map((rt) => (rt.tags as any).name) || []
        const albumCollaborators = collaborations?.map((collab) => ({
          id: collab.user_id,
          role: collab.role,
          profile: collab.profiles
        })) || []

        setFormData({
          title: typeof album.title === 'string' ? album.title : '',
          description: typeof album.description === 'string' ? album.description : '',
          genre: typeof album.genre === 'string' ? album.genre : '',
          albumType: typeof album.album_type === 'string' ? album.album_type : 'Album',
          releaseDate: typeof album.release_date === 'string' || typeof album.release_date === 'number'
            ? new Date(album.release_date).toISOString().split('T')[0]
            : '',
          isExplicit: typeof album.is_explicit === 'boolean' ? album.is_explicit : false,
          status: typeof album.status === 'string' ? album.status : 'draft',
          coverUrl: typeof album.cover_url === 'string' ? album.cover_url : '',
          label: typeof album.label === 'string' ? album.label : '',
          upc: typeof album.upc === 'string' ? album.upc : '',
          copyright: typeof album.copyright === 'string' ? album.copyright : `© ${new Date().getFullYear()} Tous droits réservés`,
          recordingYear: typeof album.recording_year === 'string' ? album.recording_year : new Date().getFullYear().toString(),
          language: typeof album.language === 'string' ? album.language : 'fr',
          tags: Array.isArray(albumTags) ? albumTags.filter(tag => typeof tag === 'string') : [],
          songs: Array.isArray(albumSongs) ? albumSongs.map(as => (as.songs as any).id).filter(id => typeof id === 'string') : [],
          isPublic: true,
          notifyFollowers: true,
          addToDiscovery: true,
          collaborators: Array.isArray(albumCollaborators)
            ? albumCollaborators
                .filter(c => c && typeof c.id === 'string' && typeof c.role === 'string')
                .map(c => ({ id: c.id as string, role: c.role as string }))
            : [],
          notes: typeof album.notes === 'string' ? album.notes : ''
        })

        setExistingCollaborators(Array.isArray(albumCollaborators)
          ? albumCollaborators.filter(c => c && typeof c.id === 'string' && typeof c.role === 'string')
          : [])

        if (albumSongs) {
          const formattedTracks = albumSongs.map((as) => ({
            id: (as.songs as any).id,
            title: (as.songs as any).title,
            duration: (as.songs as any).duration || 0,
            cover_url: (as.songs as any).cover_url,
            user_id: (as.songs as any).user_id,
            profiles: (as.songs as any).profiles
          }))
          setSelectedSongs(formattedTracks)
        }

        // Charger les morceaux disponibles et les utilisateurs
        // loadAvailableSongs()
        // loadAvailableUsers()
      } catch (error: any) {
        toast({
          title: "Erreur",
          description: error.message || "Une erreur s'est produite",
          variant: "destructive"
        })
      }
    }
    fetchAlbumData()
  }, [params.id])
}
