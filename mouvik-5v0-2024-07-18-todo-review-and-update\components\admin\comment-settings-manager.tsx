"use client";

import React, { useState, useEffect, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';
import { getCommentsPerPageSetting, updateAppSetting } from '@/lib/actions/comment.actions';
import { Loader2 } from 'lucide-react';

/**
 * Component for managing comment-related settings, specifically comments per page.
 * Allows administrators to view and update the pagination limit for comment sections.
 */
export function CommentSettingsManager() {
  const [limit, setLimit] = useState<number>(10); // Stores the current value of comments per page
  const [isLoading, setIsLoading] = useState<boolean>(true); // Tracks loading state for initial fetch
  const [isPending, startTransition] = useTransition(); // For managing pending state of form submission

  /**
   * Fetches the current comments_per_page setting when the component mounts.
   */
  useEffect(() => {
    const fetchLimit = async () => {
      setIsLoading(true);
      try {
        const currentLimit = await getCommentsPerPageSetting();
        setLimit(currentLimit);
      } catch (error) {
        console.error("Failed to fetch comments per page setting:", error);
        toast({ title: "Erreur de chargement", description: "Impossible de charger la limite de pagination.", variant: "destructive" });
        // Keep default limit or handle error appropriately
      } finally {
        setIsLoading(false);
      }
    };
    fetchLimit();
  }, []); // Empty dependency array ensures this runs only on mount

  /**
   * Handles form submission to update the comments_per_page setting.
   * @param event The form submission event.
   */
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    startTransition(async () => {
      try {
        const result = await updateAppSetting('comments_per_page', limit.toString());
        if (result.success) {
          toast({ title: "Succès", description: "Limite de pagination des commentaires mise à jour." });
        } else {
          toast({ title: "Erreur", description: result.error || "Impossible de mettre à jour la limite.", variant: "destructive" });
        }
      } catch (error) {
        console.error("Failed to update comments per page setting:", error);
        toast({ title: "Erreur", description: "Une erreur inattendue s'est produite.", variant: "destructive" });
      }
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Chargement des paramètres...</span>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-medium mb-4">Paramètres des Commentaires</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Label htmlFor="commentsPerPage">Commentaires par page</Label>
          <Input
            id="commentsPerPage"
            type="number"
            value={limit}
            onChange={(e) => setLimit(Math.max(1, parseInt(e.target.value, 10) || 1))}
            min="1"
            className="mt-1"
            disabled={isPending}
          />
          <p className="text-sm text-muted-foreground mt-1">
            Nombre de commentaires à afficher par page dans les sections de commentaires.
          </p>
        </div>
        <Button type="submit" disabled={isPending}>
          {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Enregistrer la limite
        </Button>
      </form>
    </div>
  );
}
