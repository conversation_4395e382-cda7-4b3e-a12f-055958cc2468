'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Music, User, Clock, Hash, Tag, Palette, 
  Save, Edit3, Settings, BarChart3, Star,
  Calendar, Globe, Headphones, Mic2, TrendingUp,
  FileText, <PERSON>, Piano, Volume2, Zap, Upload,
  Play, Pause, Skip<PERSON>or<PERSON>, RotateCcw, Download
} from 'lucide-react';

interface SongInfoHeaderCompleteProps {
  currentSong: any;
  setCurrentSong: (song: any) => void;
  styleConfig: any;
  setStyleConfig: (config: any) => void;
  onSave?: () => Promise<void>;
  onLoadAudio?: (file: File) => void;
  onRecordAudio?: () => void;
  isPlaying?: boolean;
  setIsPlaying?: (playing: boolean) => void;
  currentTime?: number;
  duration?: number;
  audioUrl?: string;
}

// Options de configuration
const MUSICAL_KEYS = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
const TIME_SIGNATURES = ['4/4', '3/4', '2/4', '6/8', '5/4', '7/8'];
const GENRES = ['Pop', 'Rock', 'Jazz', 'Blues', 'Folk', 'Electronic', 'Hip-Hop', 'Country', 'Classical', 'R&B'];
const MOODS = ['Joyeux', 'Mélancolique', 'Énergique', 'Relaxant', 'Romantique', 'Mystérieux', 'Dramatique', 'Nostalgique'];

export const SongInfoHeaderComplete: React.FC<SongInfoHeaderCompleteProps> = ({
  currentSong,
  setCurrentSong,
  styleConfig,
  setStyleConfig,
  onSave,
  onLoadAudio,
  onRecordAudio,
  isPlaying = false,
  setIsPlaying,
  currentTime = 0,
  duration = 0,
  audioUrl
}) => {
  
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  // Gestionnaire pour sauvegarder
  const handleSave = useCallback(async () => {
    if (!onSave) return;
    
    setIsSaving(true);
    try {
      await onSave();
      setIsEditing(false);
    } catch (error) {
      console.error('Erreur sauvegarde:', error);
    } finally {
      setIsSaving(false);
    }
  }, [onSave]);

  // Gestionnaire pour les changements de chanson
  const handleSongChange = useCallback((field: string, value: any) => {
    setCurrentSong({
      ...currentSong,
      [field]: value
    });
  }, [currentSong, setCurrentSong]);

  // Gestionnaire pour les changements de style
  const handleStyleChange = useCallback((field: string, value: any) => {
    setStyleConfig({
      ...styleConfig,
      [field]: value
    });
  }, [styleConfig, setStyleConfig]);

  // Gestionnaire pour l'upload de fichier audio
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onLoadAudio) {
      onLoadAudio(file);
    }
  }, [onLoadAudio]);

  // Formater le temps
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculer la progression globale
  const calculateProgress = () => {
    const fields = [
      currentSong?.title,
      currentSong?.artist,
      styleConfig?.key,
      styleConfig?.bpm,
      styleConfig?.genres?.length > 0
    ];
    const completed = fields.filter(Boolean).length;
    return Math.round((completed / fields.length) * 100);
  };

  return (
    <Card className="bg-slate-800/50 border-slate-700">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Music className="h-5 w-5 text-blue-400" />
            Informations du Morceau
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {calculateProgress()}% complété
            </Badge>
            
            {isEditing ? (
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(false)}
                  className="gap-1"
                >
                  Annuler
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleSave}
                  disabled={isSaving}
                  className="gap-1"
                >
                  <Save className="h-3 w-3" />
                  {isSaving ? 'Sauvegarde...' : 'Sauvegarder'}
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="gap-1"
              >
                <Edit3 className="h-3 w-3" />
                Modifier
              </Button>
            )}
          </div>
        </div>
        
        {/* Barre de progression */}
        <Progress value={calculateProgress()} className="h-2 mt-2" />
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-slate-700/50">
            <TabsTrigger value="general" className="gap-1 text-xs">
              <FileText className="h-3 w-3" />
              Général
            </TabsTrigger>
            <TabsTrigger value="musical" className="gap-1 text-xs">
              <Music className="h-3 w-3" />
              Musical
            </TabsTrigger>
            <TabsTrigger value="audio" className="gap-1 text-xs">
              <Volume2 className="h-3 w-3" />
              Audio
            </TabsTrigger>
            <TabsTrigger value="meta" className="gap-1 text-xs">
              <Tag className="h-3 w-3" />
              Métadonnées
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4 mt-4">
            {/* Informations de base */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title" className="text-white text-sm">Titre</Label>
                <Input
                  id="title"
                  value={currentSong?.title || ''}
                  onChange={(e) => handleSongChange('title', e.target.value)}
                  placeholder="Titre du morceau..."
                  disabled={!isEditing}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
              
              <div>
                <Label htmlFor="artist" className="text-white text-sm">Artiste</Label>
                <Input
                  id="artist"
                  value={currentSong?.artist || ''}
                  onChange={(e) => handleSongChange('artist', e.target.value)}
                  placeholder="Nom de l'artiste..."
                  disabled={!isEditing}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description" className="text-white text-sm">Description</Label>
              <Textarea
                id="description"
                value={currentSong?.description || ''}
                onChange={(e) => handleSongChange('description', e.target.value)}
                placeholder="Description du morceau..."
                disabled={!isEditing}
                rows={3}
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
          </TabsContent>

          <TabsContent value="musical" className="space-y-4 mt-4">
            {/* Configuration musicale */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <Label className="text-white text-sm">Tonalité</Label>
                <Select
                  value={styleConfig?.key || 'C'}
                  onValueChange={(value) => handleStyleChange('key', value)}
                  disabled={!isEditing}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {MUSICAL_KEYS.map(key => (
                      <SelectItem key={key} value={key}>{key}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-white text-sm">Tempo (BPM)</Label>
                <Input
                  type="number"
                  value={styleConfig?.bpm || 120}
                  onChange={(e) => handleStyleChange('bpm', parseInt(e.target.value))}
                  disabled={!isEditing}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>

              <div>
                <Label className="text-white text-sm">Signature</Label>
                <Select
                  value={styleConfig?.timeSignature || '4/4'}
                  onValueChange={(value) => handleStyleChange('timeSignature', value)}
                  disabled={!isEditing}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {TIME_SIGNATURES.map(sig => (
                      <SelectItem key={sig} value={sig}>{sig}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-white text-sm">Capo</Label>
                <Input
                  type="number"
                  min="0"
                  max="12"
                  value={styleConfig?.capo || 0}
                  onChange={(e) => handleStyleChange('capo', parseInt(e.target.value))}
                  disabled={!isEditing}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-white text-sm">Genre</Label>
                <Select
                  value={styleConfig?.genres?.[0] || ''}
                  onValueChange={(value) => handleStyleChange('genres', [value])}
                  disabled={!isEditing}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Sélectionner un genre" />
                  </SelectTrigger>
                  <SelectContent>
                    {GENRES.map(genre => (
                      <SelectItem key={genre} value={genre}>{genre}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-white text-sm">Ambiance</Label>
                <Select
                  value={styleConfig?.mood || ''}
                  onValueChange={(value) => handleStyleChange('mood', value)}
                  disabled={!isEditing}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Sélectionner une ambiance" />
                  </SelectTrigger>
                  <SelectContent>
                    {MOODS.map(mood => (
                      <SelectItem key={mood} value={mood}>{mood}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="audio" className="space-y-4 mt-4">
            {/* Gestion audio */}
            <div className="space-y-4">
              {/* Contrôles de lecture */}
              {audioUrl && (
                <div className="bg-slate-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-white font-medium">Lecture Audio</h4>
                    <div className="text-sm text-slate-400">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 mb-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsPlaying?.(!isPlaying)}
                      className="gap-1"
                    >
                      {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      {isPlaying ? 'Pause' : 'Play'}
                    </Button>
                    
                    <Button variant="outline" size="sm" className="gap-1">
                      <RotateCcw className="h-4 w-4" />
                      Restart
                    </Button>
                  </div>
                  
                  <Progress value={duration > 0 ? (currentTime / duration) * 100 : 0} className="h-2" />
                </div>
              )}

              {/* Upload et enregistrement */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-white text-sm mb-2 block">Importer un fichier audio</Label>
                  <div className="relative">
                    <input
                      type="file"
                      accept="audio/*"
                      onChange={handleFileUpload}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      disabled={!isEditing}
                    />
                    <Button
                      variant="outline"
                      className="w-full gap-2"
                      disabled={!isEditing}
                    >
                      <Upload className="h-4 w-4" />
                      Choisir un fichier
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="text-white text-sm mb-2 block">Enregistrer</Label>
                  <Button
                    variant="outline"
                    onClick={onRecordAudio}
                    className="w-full gap-2"
                    disabled={!isEditing}
                  >
                    <Mic2 className="h-4 w-4" />
                    Enregistrer
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="meta" className="space-y-4 mt-4">
            {/* Métadonnées */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-white text-sm">Date de création</Label>
                <Input
                  type="date"
                  value={currentSong?.created_at?.split('T')[0] || ''}
                  onChange={(e) => handleSongChange('created_at', e.target.value)}
                  disabled={!isEditing}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>

              <div>
                <Label className="text-white text-sm">Durée (secondes)</Label>
                <Input
                  type="number"
                  value={currentSong?.duration || 0}
                  onChange={(e) => handleSongChange('duration', parseInt(e.target.value))}
                  disabled={!isEditing}
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>

            <div>
              <Label className="text-white text-sm">Tags</Label>
              <Input
                value={currentSong?.tags?.join(', ') || ''}
                onChange={(e) => handleSongChange('tags', e.target.value.split(',').map(t => t.trim()))}
                placeholder="rock, guitare, énergique..."
                disabled={!isEditing}
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
