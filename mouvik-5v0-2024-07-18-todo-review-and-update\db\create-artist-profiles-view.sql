-- Création d'une vue pour les profils d'artistes avec leurs statistiques
CREATE OR REPLACE VIEW artist_profiles AS
SELECT 
  p.id,
  p.name,
  p.email,
  p.avatar_url,
  p.bio,
  p.created_at,
  p.updated_at,
  p.initials,
  COUNT(DISTINCT a.id) AS album_count,
  COUNT(DISTINCT s.id) AS song_count,
  COUNT(DISTINCT pl.id) AS playlist_count,
  COALESCE(SUM(s.plays), 0) AS total_plays,
  COUNT(DISTINCT f.follower_id) AS follower_count
FROM 
  profiles p
LEFT JOIN 
  albums a ON p.id = a.artist_id
LEFT JOIN 
  songs s ON p.id = s.user_id
LEFT JOIN 
  playlists pl ON p.id = pl.user_id
LEFT JOIN 
  followers f ON p.id = f.following_id
GROUP BY 
  p.id, p.name, p.email, p.avatar_url, p.bio, p.created_at, p.updated_at, p.initials;
