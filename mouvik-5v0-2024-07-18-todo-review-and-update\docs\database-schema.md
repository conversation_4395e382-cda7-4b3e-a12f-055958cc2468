# Database Schema Documentation

## Changelog (Recent)

---
**2025-06-04**:
- Major documentation update to align with current Supabase schema and enhance clarity.
  - Added detailed definition for `song_user_views` table under "Analytics Tables".
  - Created new section "Key Database Views" with documentation for `artist_stats`.
  - Created new section "Key Database Functions (RPC)" with documentation for `increment_song_plays`, `get_artist_stats`, `get_trending_songs`, `get_recommended_songs`, `search_songs_and_artists`, and `toggle_song_public_status`.
  - Reviewed table definitions for coherence regarding statistics, discovery, and song recording.
  - Updated ERD notes to include `song_user_views`.
  - Suggested future refactoring for placement of existing views and RPCs into new dedicated sections.
- Designed and documented a comprehensive song versioning system.
  - Added `public.song_versions` table schema for storing song snapshots.
  - Clarified the role of the `public.songs` table in relation to versioning.
  - Added RPC definitions for `rpc_save_song_version`, `rpc_get_song_versions`, and `rpc_load_song_version_data`.
  - Updated ERD notes to include `song_versions`.
---

**2025-05-30** :
- Clarification : La colonne `is_public` (boolean) est désormais la référence pour déterminer la publication des `songs` et `albums`.
- Note : La colonne `status` existe toujours dans ces tables mais `is_public` doit être priorisée pour la logique de publication.
- Confirmation : `creator_user_id` est utilisé pour la table `songs`, et `user_id` pour la table `albums` comme identifiant du propriétaire.
---

## Diagramme Entité-Relation (Vue d'ensemble)

```mermaid
erDiagram
    USERS ||--o{ SONGS : creates
    USERS ||--o{ ALBUMS : creates
    USERS ||--o{ PLAYLISTS : creates
    USERS ||--o{ COMMENTS : writes
    USERS ||--o{ LIKES : gives
    USERS ||--o{ FOLLOWS : follows
    SONGS ||--o{ ALBUM_SONGS : included_in
    ALBUMS ||--o{ ALBUM_SONGS : contains
    SONGS ||--o{ PLAYLIST_SONGS : included_in
    PLAYLISTS ||--o{ PLAYLIST_SONGS : contains
    SONGS ||--o{ COMMENTS : has
    ALBUMS ||--o{ COMMENTS : has
    SONGS ||--o{ LIKES : receives
    ALBUMS ||--o{ LIKES : receives
    SONGS ||--o{ RESOURCE_TAGS : has
    ALBUMS ||--o{ RESOURCE_TAGS : has
    TAGS ||--o{ RESOURCE_TAGS : used_in
    USERS ||--o{ ACTIVITIES : generates
    USERS ||--o{ PROFILES : has
    USERS ||--o{ BANDS : creates
    BANDS ||--o{ BAND_MEMBERS : has
    USERS ||--o{ BAND_MEMBERS : joins
    BANDS ||--o{ PROJECTS : has
    USERS ||--o{ MESSAGES : sends
    USERS ||--o{ CONVERSATIONS : participates
```

## Overview

MOUVIK uses a PostgreSQL database managed by Supabase. The schema is designed to support music creation, social features, and content management.

## Enumerated Types

```sql
-- For subscription plans
CREATE TYPE public.subscription_enum AS ENUM ('free', 'pro', 'studio');

-- For user roles within the application
CREATE TYPE public.user_role_enum AS ENUM ('user', 'moderator', 'admin');

-- For analytics detail levels based on plans
CREATE TYPE public.analytics_level_enum AS ENUM ('basic', 'extended', 'pro');

-- For AI Content Origin Flag (EU AI Act 2025 compliance)
CREATE TYPE public.ai_content_origin_enum AS ENUM ('100%_ia', 'hybrid', 'full_human');
```

## Core Tables

### Users (`profiles`)
```sql
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY, 
    email TEXT, 
    username TEXT UNIQUE NOT NULL,
    full_name TEXT,
    display_name TEXT,
    avatar_url TEXT,
    header_url TEXT,
    bio TEXT,
    website TEXT,
    location_city TEXT,
    location_country TEXT, 
    social_links JSONB, 
    genres TEXT[],
    influences TEXT[],
    tags TEXT[],
    is_artist BOOLEAN DEFAULT false,
    record_label TEXT,
    equipment TEXT,
    spoken_languages TEXT[],
    instruments_played JSONB, 
    is_profile_public BOOLEAN DEFAULT true,
    show_stats_publicly BOOLEAN DEFAULT true,
    allow_collaboration_requests BOOLEAN DEFAULT true,
    receive_email_notifications BOOLEAN DEFAULT true,
    role_primary TEXT DEFAULT 'listener', 
    roles_secondary TEXT[],
    subscription_tier public.subscription_enum DEFAULT 'free', 
    user_role public.user_role_enum DEFAULT 'user', 
    status_badges TEXT[],
    main_instruments TEXT[],
    monetization_goals TEXT DEFAULT 'hobby', 
    open_to_collab BOOLEAN DEFAULT false,
    primary_daw TEXT, 
    other_daws TEXT[],
    operating_systems TEXT[],
    ai_usage_level TEXT DEFAULT 'none', 
    ai_usage_percent NUMERIC, 
    ai_tools TEXT[],
    coins_balance INT DEFAULT 0,
    ia_credits INT DEFAULT 0,
    marketing_opt_in BOOLEAN DEFAULT false,
    custom_uploads_per_month SMALLINT NULL,
    custom_vault_space_gb NUMERIC(5,2) NULL,
    custom_vault_max_files SMALLINT NULL, -- Added custom max files for vault
    custom_ia_credits_month INTEGER NULL,
    custom_coins_month INTEGER NULL,
    custom_max_playlists SMALLINT NULL,
    custom_max_friends SMALLINT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    follower_count INTEGER DEFAULT 0 -- Added May 2025
);
-- RLS Policies for profiles...
```

### Bands (`bands`)
```sql
CREATE TABLE public.bands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    genres TEXT[], 
    moods TEXT[], 
    instrumentation TEXT[], 
    countries TEXT[], 
    location TEXT, 
    avatar_url TEXT, 
    banner_url TEXT, 
    cover_url TEXT, 
    creator_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT TRUE, 
    are_comments_public BOOLEAN DEFAULT FALSE, -- Added May 2025
    dislike_count INTEGER DEFAULT 0, -- Added May 2025
    follower_count INTEGER DEFAULT 0, -- Added May 2025
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
-- RLS Policies for bands...
```

### Band Members (`band_members`)
```sql
CREATE TABLE public.band_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    band_id UUID NOT NULL REFERENCES public.bands(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE, 
    role TEXT, 
    is_admin BOOLEAN DEFAULT FALSE, 
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE (band_id, user_id)
);
-- RLS Policies for band_members...
```

### Albums (`albums`)
```sql
CREATE TABLE public.albums (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    band_id UUID REFERENCES public.bands(id) ON DELETE SET NULL, 
    title TEXT NOT NULL,
    description TEXT,
    genres TEXT[], 
    moods TEXT[], 
    instrumentation TEXT[], 
    album_type TEXT, 
    release_date DATE,
    cover_url TEXT,
    status TEXT DEFAULT 'draft', 
    is_explicit BOOLEAN DEFAULT false,
    label TEXT,
    upc TEXT,
    ai_content_origin public.ai_content_origin_enum NULL, -- Added AI Content Origin
    header_banner_url TEXT,
    is_embeddable BOOLEAN DEFAULT false,
    style_circle_map JSONB,
    notes TEXT,
    -- plays INTEGER DEFAULT 0, -- Prévu mais non implémenté (confirmé le 2025-05-30)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
-- RLS Policies for albums...
```

### `conversation_participants`

- **Description:** Links users to conversations.
- **Columns:**
  - `conversation_id` (uuid): Foreign key to `conversations` table.
  - `user_id` (uuid): Foreign key to `profiles` table.
  - `joined_at` (timestamp with time zone): Timestamp when the user joined the conversation.
- **Primary Key:** (`conversation_id`, `user_id`)
- **Relationships:**
  - `conversation_id` -> `conversations.id`
  - `user_id` -> `profiles.id`

```sql
CREATE TABLE public.conversation_participants (
  conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE, -- Assuming profiles.id maps to auth.users.id
  joined_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  PRIMARY KEY (conversation_id, user_id)
);

COMMENT ON TABLE public.conversation_participants IS 'Links users to conversations, recording when they joined.';
-- RLS Policies: Users can see participants of conversations they are part of. Service role for inserts/deletes.
```

### `song_tags`

- **Description:** Associates tags with songs.
- **Columns:**
  - `song_id` (uuid): Foreign key to `songs` table.
  - `tag_id` (uuid): Foreign key to `tags` table.
- **Primary Key:** (`song_id`, `tag_id`)
- **Relationships:**
  - `song_id` -> `songs.id`
  - `tag_id` -> `tags.id`

```sql
CREATE TABLE public.song_tags (
  song_id UUID NOT NULL REFERENCES public.songs(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES public.tags(id) ON DELETE CASCADE,
  PRIMARY KEY (song_id, tag_id)
);

COMMENT ON TABLE public.song_tags IS 'Associates tags with songs in a many-to-many relationship.';
-- RLS Policies: Publicly readable. Song creators or admins can manage tags for a song.
```

### `project_comments`

- **Description:** Stores comments related to projects.
- **Columns:**
  - `id` (uuid): Primary key.
  - `project_id` (uuid): Foreign key to `projects` table.
  - `user_id` (uuid): Foreign key to `profiles` table.
  - `content` (text): The comment content.
  - `created_at` (timestamp with time zone): Timestamp when the comment was created.
- **Primary Key:** `id`
- **Relationships:**
  - `project_id` -> `projects.id`
  - `user_id` -> `profiles.id`

```sql
CREATE TABLE public.project_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL CHECK (char_length(content) > 0),
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.project_comments IS 'Stores user comments on projects.';
-- RLS Policies: Users can create comments. Users can read comments on projects they have access to. Comment authors can edit/delete their own comments.
```

### `song_comments`

- **Description:** Stores comments related to songs.
- **Columns:**
  - `id` (uuid): Primary key.
  - `song_id` (uuid): Foreign key to `songs` table.
  - `user_id` (uuid): Foreign key to `profiles` table.
  - `content` (text): The comment content.
  - `created_at` (timestamp with time zone): Timestamp when the comment was created.
- **Primary Key:** `id`
- **Relationships:**
  - `song_id` -> `songs.id`
  - `user_id` -> `profiles.id`

```sql
CREATE TABLE public.song_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  song_id UUID NOT NULL REFERENCES public.songs(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL CHECK (char_length(content) > 0),
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.song_comments IS 'Stores user comments on songs.';
-- RLS Policies: Users can create comments on public songs or songs they have access to. Comment authors can edit/delete their own comments.
```

### `project_versions`

- **Description:** Stores different versions of projects.
- **Columns:**
  - `id` (uuid): Primary key.
  - `project_id` (uuid): Foreign key to `projects` table.
  - `version_number` (int): The version number.
  - `created_at` (timestamp with time zone): Timestamp when the version was created.
  - `data` (jsonb): JSON data representing the project version.
- **Primary Key:** `id`
- **Relationships:**
  - `project_id` -> `projects.id`

```sql
CREATE TABLE public.project_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  data JSONB, -- Can store project-specific data like DAW session info, stems, etc.
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  notes TEXT, -- Optional notes for this version
  CONSTRAINT uq_project_version_number UNIQUE (project_id, version_number)
);

COMMENT ON TABLE public.project_versions IS 'Stores version history for projects, allowing rollback and tracking changes.';
-- RLS Policies: Project members can create and view versions. Project owners can manage versions.
```


### Songs (`songs`)
```sql
CREATE TABLE public.songs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  creator_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE, -- FK to profiles.id (auth.users.id)
  band_id UUID REFERENCES public.bands(id) ON DELETE SET NULL,
  title VARCHAR(255) NOT NULL,
  artist VARCHAR(255), -- Denormalized artist name, used if no profile or for overrides
  description TEXT,
  genre TEXT, -- Single TEXT value (e.g., "Electronic"). Transformed to array in frontend.
  subgenre TEXT, -- Single TEXT value. Transformed to array in frontend.
  mood TEXT, -- Single TEXT value. Transformed to array in frontend.
  theme TEXT, -- Single TEXT value. Transformed to array in frontend.
  instrumentation TEXT, -- Single TEXT value. Transformed to array in frontend.
  tags TEXT[], -- Array of TEXT values (e.g., {"synth", "80s"})
  album_id UUID REFERENCES public.albums(id) ON DELETE SET NULL,
  duration_ms INTEGER, -- Duration in milliseconds
  cover_art_url TEXT, -- URL for the cover art (maps to 'cover_url' in SongType)
  audio_url TEXT, -- URL for the audio file
  lyrics TEXT,
  bpm INTEGER,
  key VARCHAR(10),
  is_public BOOLEAN DEFAULT false, -- Controls visibility on public listings
  slug TEXT UNIQUE, -- URL-friendly identifier for public song pages
  credits TEXT, -- Free-form text for song credits
  is_explicit BOOLEAN DEFAULT false,
  plays INTEGER DEFAULT 0,
  dislike_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  release_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  visibility TEXT DEFAULT 'public' CHECK (visibility IN ('public', 'private', 'unlisted', 'followers_only')), -- Granular visibility
  is_archived BOOLEAN DEFAULT false,

  -- Fields below are part of the broader song schema but not directly queried by 'manage-songs' page as of May 2025:
  featured_artists TEXT[],
  waveform_url TEXT,
  chords TEXT,
  time_signature VARCHAR(10),
  capo INTEGER,
  tuning_frequency NUMERIC,
  composer_name VARCHAR(255),
  -- record_label VARCHAR(255), -- Removed, not in use
  distributor VARCHAR(255),
  -- isrc VARCHAR(50), -- Removed, not in use
  -- upc VARCHAR(50), -- Removed, not in use
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')), -- Workflow status
  is_ai_generated BOOLEAN DEFAULT FALSE,
  ai_content_origin public.ai_content_origin_enum NULL,
  lyrics_language TEXT[],
  recording_date DATE,
  recording_location TEXT,
  writers TEXT[],
  producers TEXT[],
  scheduled_for TIMESTAMP WITH TIME ZONE,
  stems_available BOOLEAN DEFAULT false,
  instrumental_version_url TEXT,
  acapella_version_url TEXT,
  original_song_id UUID REFERENCES public.songs(id) ON DELETE SET NULL,
  is_remix BOOLEAN DEFAULT false,
  remix_of UUID REFERENCES public.songs(id) ON DELETE SET NULL, -- If this song is a remix, this points to the original
  allow_downloads BOOLEAN DEFAULT false, -- Whether the song can be downloaded
  allow_comments BOOLEAN DEFAULT true, -- Whether comments are enabled for the song
  bloc_note TEXT, -- Internal notes for the creator
  right_column_notepad TEXT, -- Additional internal notes
  progress_data JSONB, -- Data related to song creation progress (e.g., for a DAW-like interface)
  custom_css TEXT, -- Custom CSS for the song's public page (if applicable)
  analytics_public BOOLEAN DEFAULT false, -- Whether song analytics are publicly visible
  paywall_price NUMERIC(10,2) -- Price if the song is behind a paywall
);

COMMENT ON TABLE public.songs IS 'Stores information about individual songs. This table typically represents the current working version or the primary published version of a song. For detailed version history and snapshots, see the `public.song_versions` table.';
-- RLS Policies for songs...
```

### Song Versions (`public.song_versions`)
Stores snapshots of songs at different points in time, enabling version history, reverting to previous states, and managing distinct iterations of a song.

```sql
CREATE TABLE public.song_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    song_id UUID NOT NULL REFERENCES public.songs(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    version_name TEXT, -- e.g., "Initial Draft", "Radio Edit v2.1"
    song_data JSONB NOT NULL, -- Snapshot of all relevant song fields (metadata, lyrics, chords, file URLs, musical info, etc.)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    creator_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_version_id UUID REFERENCES public.song_versions(id) ON DELETE SET NULL, -- Links to the version this was derived from, if any
    is_major_version BOOLEAN DEFAULT true, -- Distinguishes significant user-initiated saves
    user_notes TEXT, -- User's notes specific to this version
    CONSTRAINT uq_song_version_number UNIQUE (song_id, version_number) -- Ensures version numbers are unique per song
);

COMMENT ON TABLE public.song_versions IS 'Stores snapshots of songs, enabling version history and reverting capabilities. Each record is a full state of a song at a point in time.';
COMMENT ON COLUMN public.song_versions.song_id IS 'Link to the main song entity in the public.songs table.';
COMMENT ON COLUMN public.song_versions.version_number IS 'Sequential version number for this specific song.';
COMMENT ON COLUMN public.song_versions.version_name IS 'Optional user-friendly name for the version.';
COMMENT ON COLUMN public.song_versions.song_data IS 'JSONB blob containing a complete snapshot of all relevant song fields at the time of version creation.';
COMMENT ON COLUMN public.song_versions.parent_version_id IS 'If this version was created from a previous version, this links them, allowing for a version tree.';
COMMENT ON COLUMN public.song_versions.is_major_version IS 'Indicates if this was a significant, user-initiated save.';
COMMENT ON COLUMN public.song_versions.user_notes IS 'Specific notes from the user about this particular version.';

-- RLS Policies for song_versions:
-- - Creators can insert new versions for their own songs (via RPC).
-- - Creators can read versions of their own songs.
-- - Potentially, collaborators with appropriate permissions might also read versions.
```

### Album Songs (`album_songs`)
```sql
CREATE TABLE public.album_songs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    album_id UUID NOT NULL REFERENCES public.albums(id) ON DELETE CASCADE,
    song_id UUID NOT NULL REFERENCES public.songs(id) ON DELETE CASCADE,
    track_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(album_id, song_id),
    UNIQUE(album_id, track_number)
);
```

## Subscription and Limits Tables

### Plan Limits (`plan_limits`)
Defines features and quotas for each subscription tier.
```sql
CREATE TABLE public.plan_limits (
  tier public.subscription_enum PRIMARY KEY,
  uploads_per_month smallint,
  vault_space_gb numeric(5,2),
  vault_max_files SMALLINT NULL,         -- Max number of files in Song Vault (NULL for unlimited)
  ia_credits_month integer,
  coins_month integer,
  page_customisation boolean DEFAULT false,
  analytics_level public.analytics_level_enum DEFAULT 'basic',
  max_playlists smallint,
  max_friends smallint
);

-- Example INSERTs with vault_max_files
-- INSERT INTO public.plan_limits (tier, uploads_per_month, vault_space_gb, vault_max_files, ia_credits_month, coins_month, page_customisation, analytics_level, max_playlists, max_friends) VALUES
--  ('free',   2,   0.2, 5,    5,   10, false, 'basic',    5,  50),
--  ('pro',   40,   5.0, 25,   50,  100, true,  'extended', 50, 200),
--  ('studio',NULL, 25.0, NULL, 250, 500, true,  'pro',     200, NULL);
```

### User Monthly Uploads (`user_monthly_uploads`)
(Definition is correct)
...

## Database Functions and Triggers for Plan Limits
(Definitions are correct)
...

## Social Features Tables

### Likes (`likes`)
(Definition is correct)
...

### Dislikes (`dislikes`)
Stores user dislikes for various resources.
```sql
CREATE TABLE public.dislikes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  resource_id UUID NOT NULL,
  resource_type TEXT NOT NULL, -- e.g., 'song', 'album', 'band', 'playlist'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  CONSTRAINT unique_dislike UNIQUE (user_id, resource_id, resource_type)
);
-- RLS Policies for dislikes...
```

### Comments (`comments`)
(Definition is correct)
...

### Follows (`follows`)
(Definition is correct)
...

## Column Additions for Dislike Counts
- `public.songs`: Added `dislike_count INTEGER DEFAULT 0;`
- `public.albums`: Added `dislike_count INTEGER DEFAULT 0;`
- `public.bands`: Added `dislike_count INTEGER DEFAULT 0;`
- `public.playlists`: Added `dislike_count INTEGER DEFAULT 0;` 
- `public.playlists`: Added `follower_count INTEGER DEFAULT 0;` 
- `public.playlists`: Added `plays INTEGER DEFAULT 0;` 
- `public.playlists`: Added `banner_url TEXT NULL;`
- `public.playlists`: Added `genres TEXT[] NULL;`
- `public.playlists`: Added `moods TEXT[] NULL;`
- `public.playlists`: Added `instrumentation TEXT[] NULL;`

### Playlist Followers (`playlist_followers`)
Stores user follows for playlists.
```sql
CREATE TABLE public.playlist_followers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  playlist_id UUID NOT NULL REFERENCES public.playlists(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  CONSTRAINT unique_playlist_follower UNIQUE (playlist_id, user_id)
);
-- RLS Policies for playlist_followers...
```

### Band Followers (`band_followers`)
Stores user follows for bands.
```sql
CREATE TABLE public.band_followers (
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    band_id UUID NOT NULL REFERENCES public.bands(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (user_id, band_id)
);
-- RLS Policies for band_followers...
```

### Profile Followers (`profile_followers`)
Stores user follows for profiles.
```sql
CREATE TABLE public.profile_followers (
    follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    followed_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (follower_id, followed_profile_id)
);
-- RLS Policies for profile_followers...
```

## Analytics Tables

### Song User Views (`song_user_views`)
This table tracks individual song views by users, crucial for accurate play counts, detailed analytics (e.g., identifying unique listeners vs. total plays), and potentially for features like "recently played" or personalized recommendations.

```sql
CREATE TABLE public.song_user_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  song_id UUID NOT NULL REFERENCES public.songs(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  last_viewed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  CONSTRAINT unique_song_user_view UNIQUE (song_id, user_id) -- Ensures one record per user per song for unique view tracking
);

COMMENT ON TABLE public.song_user_views IS 'Tracks individual song views by users. Used for detailed analytics and potentially for "recently played" features.';
COMMENT ON COLUMN public.song_user_views.last_viewed_at IS 'The timestamp of the last recorded view by the user for the song. This can be updated on subsequent views by the same user to reflect the latest interaction.';
```
-- RLS Policies for song_user_views:
-- - Users should be able to insert their own view records (e.g., via a function like increment_song_plays).
-- - Users might be able to select their own view history.
-- - Access to aggregated view data should be controlled, possibly restricted to song creators or admins.

## Key Database Views

This section outlines important database views used for aggregated data and simplifying complex queries related to core application features.

### Artist Statistics (`artist_stats`)
Provides aggregated statistics for artists, such as total public songs, public albums, total plays on their songs, and follower counts. This view is essential for artist dashboards and public artist profiles.

```sql
-- Example structure (actual view definition in Supabase might vary based on specific aggregations)
CREATE OR REPLACE VIEW public.artist_stats AS
SELECT
    p.id AS artist_id,
    p.username,
    p.display_name,
    p.avatar_url,
    (SELECT COUNT(*) FROM public.songs s WHERE s.creator_user_id = p.id AND s.is_public = true AND s.status = 'published') AS public_songs_count,
    (SELECT COUNT(*) FROM public.albums a WHERE a.user_id = p.id AND a.status = 'published' AND EXISTS (SELECT 1 FROM public.album_songs als JOIN public.songs s_on ON als.song_id = s_on.id WHERE als.album_id = a.id AND s_on.is_public = true AND s_on.status = 'published')) AS public_albums_count,
    COALESCE((SELECT SUM(s.plays) FROM public.songs s WHERE s.creator_user_id = p.id), 0) AS total_plays,
    (SELECT COUNT(*) FROM public.profile_followers pf WHERE pf.followed_profile_id = p.id) AS followers_count
FROM
    public.profiles p
WHERE
    p.is_artist = true;

COMMENT ON VIEW public.artist_stats IS 'Aggregated statistics for artists, including counts for public songs, public albums, total plays, and followers. Useful for artist dashboards and profiles.';
```

-- *Note: The existing views `user_playlist_details` and `playlist_songs_view` are currently documented under the "Recent Additions (May 2025)" section. For better structural clarity in future documentation revisions, consider consolidating all key view definitions under this "Key Database Views" section.*

## Key Database Functions (RPC)

This section details important Remote Procedure Calls (RPC) used for various backend operations, encapsulating business logic and ensuring data integrity. RPCs are grouped by their primary functional area.

### 1. RPCs liés aux Morceaux

#### Increment Song Plays (`increment_song_plays`)
Atomically increments the play count for a given song in the `songs` table and records/updates the user's view in the `song_user_views` table. This function is critical for accurate song statistics.

```sql
-- FUNCTION: public.increment_song_plays(song_id_param uuid, p_user_id uuid)
-- Purpose: Atomically increments the 'plays' counter on the 'songs' table and records/updates a view in 'song_user_views'.
-- Parameters:
--   song_id_param UUID: The ID of the song for which to increment plays.
--   p_user_id UUID: The ID of the user who played the song.
-- Returns: Potentially void, or the new play count, or a status.
-- Security: Should be callable by authenticated users, possibly with rate limiting.
COMMENT ON FUNCTION public.increment_song_plays(song_id_param uuid, p_user_id uuid) IS 'Increments the play count for a song and records/updates the view in song_user_views. Ensures play counts are accurately tracked per user interaction.';
```

#### Toggle Song Public Status (`toggle_song_public_status`)
Allows a song's creator to change the `is_public` status of their song. This function should verify that the calling user is indeed the `creator_user_id` of the song. It might also handle the generation or update of a public `slug` for the song if it's being made public.

```sql
-- FUNCTION: public.toggle_song_public_status(p_song_id uuid, p_user_id uuid)
-- Purpose: Toggles the public visibility (is_public flag) of a song. Ensures only the song creator can perform this action.
-- Parameters:
--   p_song_id UUID: The ID of the song to update.
--   p_user_id UUID: The ID of the user attempting the action (for authorization against songs.creator_user_id).
-- Returns: JSONB object indicating success and the new status, e.g., { success: boolean, new_is_public: boolean, new_slug: text }.
COMMENT ON FUNCTION public.toggle_song_public_status(p_song_id uuid, p_user_id uuid) IS 'Allows the song creator to toggle the is_public status of their song. May also handle slug generation/update if the song becomes public.';
```

#### Save Song Version (`rpc_save_song_version`)
Creates a new snapshot (version) of a song in the `song_versions` table.

```sql
-- FUNCTION: public.rpc_save_song_version(p_song_id uuid, p_version_name text, p_song_data_snapshot jsonb, p_user_notes text, p_parent_version_id uuid DEFAULT NULL)
-- Purpose: Saves a new version of a song.
-- Parameters:
--   p_song_id UUID: The ID of the main song entry.
--   p_version_name TEXT (Optional): A user-friendly name for this version.
--   p_song_data_snapshot JSONB: The complete data snapshot of the song to be saved.
--   p_user_notes TEXT (Optional): User's notes for this version.
--   p_parent_version_id UUID (Optional): The ID of the version this new version is derived from.
-- Returns: JSONB object with the new song_version_id and version_number.
-- Security: Should only be callable by the song's creator or authorized collaborators.
COMMENT ON FUNCTION public.rpc_save_song_version(p_song_id uuid, p_version_name text, p_song_data_snapshot jsonb, p_user_notes text, p_parent_version_id uuid) IS 'Creates a new version snapshot of a song in the song_versions table.';
```

#### Get Song Versions (`rpc_get_song_versions`)
Retrieves all saved versions for a specific song, ordered by version number or creation date.

```sql
-- FUNCTION: public.rpc_get_song_versions(p_song_id uuid)
-- Purpose: Fetches all versions for a given song.
-- Parameters:
--   p_song_id UUID: The ID of the song for which to retrieve versions.
-- Returns: SETOF public.song_versions or JSONB array of version objects (id, version_number, version_name, created_at, user_notes).
-- Security: Should only be callable by the song's creator or authorized collaborators.
COMMENT ON FUNCTION public.rpc_get_song_versions(p_song_id uuid) IS 'Retrieves all saved versions for a specific song.';
```

#### Load Song Version Data (`rpc_load_song_version_data`)
Fetches the complete `song_data` JSONB for a specific song version, allowing the UI to populate with that version's state.

```sql
-- FUNCTION: public.rpc_load_song_version_data(p_song_version_id uuid)
-- Purpose: Fetches the song_data snapshot for a specific version.
-- Parameters:
--   p_song_version_id UUID: The ID of the song_version to load.
-- Returns: JSONB (the song_data field from the specified song_versions record).
-- Security: Should only be callable by the song's creator or authorized collaborators.
COMMENT ON FUNCTION public.rpc_load_song_version_data(p_song_version_id uuid) IS 'Fetches the complete song_data JSONB for a specific song version.';
```

### 2. RPCs liés aux Utilisateurs et Profils (y compris le suivi)

#### Toggle Profile Follow (`toggle_profile_follow`)
Allows a user to follow or unfollow another user's profile. Updates follower counts accordingly.

```sql
-- FUNCTION: public.toggle_profile_follow(p_profile_id UUID, p_user_id UUID)
-- Purpose: Allows a user to follow or unfollow another user profile. Manages entries in `profile_followers` and updates `follower_count` on `profiles`.
-- Parameters:
--   p_profile_id UUID: The ID of the profile to be followed/unfollowed.
--   p_user_id UUID: The ID of the user performing the action.
-- Returns: TABLE (is_following BOOLEAN, new_follower_count INTEGER)
-- Security: SECURITY DEFINER. Ensures user cannot follow themselves.
COMMENT ON FUNCTION public.toggle_profile_follow(p_profile_id uuid, p_user_id uuid) IS 'Toggles the follow status between two user profiles and updates follower counts.';
```

#### Toggle Band Follow (`toggle_band_follow`)
Allows a user to follow or unfollow a band. Updates follower counts accordingly.

```sql
-- FUNCTION: public.toggle_band_follow(p_band_id UUID, p_user_id UUID)
-- Purpose: Allows a user to follow or unfollow a band. Manages entries in `band_followers` and updates `follower_count` on `bands`.
-- Parameters:
--   p_band_id UUID: The ID of the band to be followed/unfollowed.
--   p_user_id UUID: The ID of the user performing the action.
-- Returns: TABLE (is_following BOOLEAN, new_follower_count INTEGER)
-- Security: SECURITY DEFINER.
COMMENT ON FUNCTION public.toggle_band_follow(p_band_id uuid, p_user_id uuid) IS 'Toggles the follow status for a user and a band, updating follower counts.';
```

### 3. RPCs liés aux Playlists

#### Create Playlist With Coin Deduction (`create_playlist_with_coin_deduction`)
Creates a new playlist for a user, deducting coins based on the cost defined in `creation_costs`. Administrators are exempt from costs.

```sql
-- FUNCTION: public.create_playlist_with_coin_deduction(...)
-- Purpose: Creates a new playlist, handles coin deduction based on `creation_costs` and user role.
-- Parameters: (See full definition in "Recent Additions (May 2025)" section as it's extensive)
-- Returns: JSONB indicating success or failure, new playlist ID, and updated coin balance.
-- Security: SECURITY DEFINER.
COMMENT ON FUNCTION public.create_playlist_with_coin_deduction(UUID,TEXT,TEXT,BOOLEAN,TEXT,TEXT,TEXT,TEXT[],TEXT[],TEXT[],BOOLEAN) IS 'Creates a playlist, deducting coins if applicable. Admins pay 0. Returns status and new playlist ID.';
```

#### Get Playlist Details for View (`get_playlist_details_for_view`)
Fetches comprehensive details for a single playlist, intended for display on a playlist detail page. Includes permission checks to ensure private playlists are only accessible by their owners.

```sql
-- FUNCTION: public.get_playlist_details_for_view(p_playlist_id UUID, p_requesting_user_id UUID DEFAULT NULL)
-- Purpose: Fetches all necessary details for displaying a playlist, including songs and creator info. Respects playlist privacy.
-- Parameters:
--  p_playlist_id UUID: The ID of the playlist to fetch.
--  p_requesting_user_id UUID (Optional): The ID of the user requesting the details, for privacy checks.
-- Returns: JSONB containing playlist details, or NULL if not found or not authorized.
COMMENT ON FUNCTION public.get_playlist_details_for_view(p_playlist_id uuid, p_requesting_user_id uuid) IS 'Retrieves detailed information for a specific playlist, suitable for a playlist view page, with privacy checks.';
```

### 4. RPCs liés à la Découverte et aux Statistiques

#### Get Artist Statistics (`get_artist_stats`)
Retrieves a comprehensive set of statistics for a specific artist profile, often utilizing the `artist_stats` view or performing direct aggregations for more detailed or real-time data.

```sql
-- FUNCTION: public.get_artist_stats(p_artist_id uuid)
-- Purpose: Fetches a comprehensive set of statistics for a given artist profile (user marked as is_artist).
-- Parameters:
--   p_artist_id UUID: The ID of the artist (references profiles.id).
-- Returns: JSONB or a custom record type containing various stats (e.g., song count, album count, total plays, follower count, listener trends, etc.).
COMMENT ON FUNCTION public.get_artist_stats(p_artist_id uuid) IS 'Retrieves detailed statistics for a given artist, often used for artist dashboards or public profiles. Leverages the artist_stats view and potentially other aggregations.';
```

#### Get Trending Songs (`get_trending_songs`)
Fetches a list of songs that are currently trending. The trending algorithm could be based on recent play velocity (plays in `song_user_views` over a time period), like counts, or other engagement metrics.

```sql
-- FUNCTION: public.get_trending_songs(limit_count integer, time_period_hours integer DEFAULT 24)
-- Purpose: Identifies and returns songs that are currently popular or gaining traction based on recent activity.
-- Parameters:
--   limit_count INTEGER: The maximum number of trending songs to return.
--   time_period_hours INTEGER: The period in hours (e.g., 24 for last day, 168 for last 7 days) to consider for trending calculation.
-- Returns: SETOF songs or JSONB array of song objects, ordered by trending score.
COMMENT ON FUNCTION public.get_trending_songs(limit_count integer, time_period_hours integer) IS 'Returns a list of trending songs based on recent engagement metrics (e.g., play counts from song_user_views) within a specified time period.';
```

#### Get Recommended Songs (`get_recommended_songs`)
Provides personalized song recommendations for a user. This could be based on their listening history (from `song_user_views`), liked songs, followed artists, or more complex collaborative filtering techniques.

```sql
-- FUNCTION: public.get_recommended_songs(p_user_id uuid, limit_count integer)
-- Purpose: Generates and returns personalized song recommendations for a user.
-- Parameters:
--   p_user_id UUID: The ID of the user for whom to generate recommendations.
--   limit_count INTEGER: The maximum number of recommended songs to return.
-- Returns: SETOF songs or JSONB array of song objects.
-- Logic: May consider user's genre preferences, listening history, liked songs, and songs liked by similar users.
COMMENT ON FUNCTION public.get_recommended_songs(p_user_id uuid, limit_count integer) IS 'Generates personalized song recommendations for a user based on their activity, preferences, and potentially collaborative filtering.';
```

### 5. RPCs de Recherche

#### Search Songs and Artists (`search_songs_and_artists`)
Performs a full-text search across various entities like songs (titles, artists, genres, lyrics), profiles/artists (usernames, display names), and potentially albums. Supabase's built-in text search capabilities (e.g., using `tsvector` and `tsquery`) are likely used here.

```sql
-- FUNCTION: public.search_songs_and_artists(search_query text, p_user_id uuid DEFAULT NULL)
-- Purpose: Allows users to search for music content and creators across the platform.
-- Parameters:
--   search_query TEXT: The search term entered by the user.
--   p_user_id UUID (Optional): The ID of the searching user, which could be used for personalizing search results or applying access control.
-- Returns: JSONB array containing mixed results (e.g., {type: 'song', data: {...}}, {type: 'artist', data: {...}}), ranked by relevance.
COMMENT ON FUNCTION public.search_songs_and_artists(search_query text, p_user_id uuid) IS 'Performs a full-text search across songs, artists/profiles, and potentially albums. Results may be ranked by relevance and potentially personalized if user context is provided.';
```

-- *Note: The RPC definitions for `create_playlist_with_coin_deduction`, `toggle_profile_follow`, `toggle_band_follow`, and `get_playlist_details_for_view` were originally in the "Recent Additions (May 2025)" section and have been consolidated here under their respective functional categories. The full SQL definitions for these, if lengthy, might still be referenced in the "Recent Additions" or a dedicated SQL appendix if preferred for brevity in this overview section.*

## Tagging System
(Definitions are correct)
...

## Entity Relationship Diagram

```mermaid
-- Placeholder for ERD --
-- TODO: Generate and embed an updated ERD that includes:
--   - conversation_participants
--   - song_tags
--   - project_comments
--   - song_comments
--   - project_versions
--   - playlist_followers
--   - song_user_views
--   - song_versions (ensure relationships are clear)
--   - New count columns (dislike_count, etc.)
--   - Verify relationships for all recently detailed tables/views, especially the ones added in this documentation pass.
```
...

## Recent Additions (May 2025)

### Table: `playlist_songs`
This table links songs to playlists and defines their order.
```sql
CREATE TABLE public.playlist_songs (
    -- Based on get_table_schema, assuming (playlist_id, song_id) or (playlist_id, position) might be a composite PK
    -- or a separate UUID PK 'id' was intended but not found by get_table_schema.
    -- The 'id' column was used in EditPlaylistPage to identify playlist_song entries for deletion.
    -- If 'id' does not exist, deletion logic needs to target playlist_id and song_id/position.
    -- For now, documenting based on get_table_schema output:
    playlist_id UUID NOT NULL REFERENCES public.playlists(id) ON DELETE CASCADE,
    song_id UUID REFERENCES public.songs(id) ON DELETE SET NULL, -- Assuming it should be songs(id)
    position INTEGER, -- Used for track_number
    -- created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), -- This column was not found by get_table_schema
    -- user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- This column was not found by get_table_schema but used in AddToPlaylistModal
    PRIMARY KEY (playlist_id, song_id) -- Example, actual PK might differ or be missing
);
-- RLS Policies for playlist_songs...
-- TODO: Clarify actual schema for playlist_songs, especially PK, user_id, and created_at.
```
**Note:** The `playlist_songs` table schema needs verification. `get_table_schema` did not show an `id` PK, `user_id`, or `created_at`, but client code was written assuming an `id` column for `playlist_song_id`. The `song_id` FK was also reported as `my_songs(id)`.

### Table: `creation_costs`
Stores admin-configurable costs in coins for creating different types of resources.
```sql
CREATE TABLE public.creation_costs (
  resource_type TEXT PRIMARY KEY,
  cost INTEGER NOT NULL DEFAULT 0 CHECK (cost >= 0),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

COMMENT ON TABLE public.creation_costs IS 'Stores admin-configurable costs in coins for creating different types of resources.';
COMMENT ON COLUMN public.creation_costs.resource_type IS 'Type of resource (e.g., playlist, album, band, song).';
COMMENT ON COLUMN public.creation_costs.cost IS 'Cost in coins to create this type of resource.';

-- RLS Policies:
-- Admins: ALL
-- Authenticated: SELECT
```

### View: `user_playlist_details`
Aggregates playlist information including counts and creator details.
```sql
CREATE OR REPLACE VIEW public.user_playlist_details AS
SELECT
    p.id,
    p.user_id,
    p.name,
    p.description,
    p.cover_url,
    p.is_public,
    p.created_at,
    p.updated_at,
    p.slug,
    p.view_count,
    p.like_count,
    p.dislike_count,
    p.follower_count,
    p.plays,
    p.banner_url,
    p.genres AS playlist_genres,
    p.moods AS playlist_moods,
    p.instrumentation AS playlist_instrumentation,
    u.username AS creator_username,
    u.display_name AS creator_display_name,
    u.avatar_url AS creator_avatar_url,
    COUNT(ps.song_id) AS songs_count,
    COALESCE(SUM(s.duration), 0) AS total_duration_seconds
FROM
    public.playlists p
LEFT JOIN
    public.profiles u ON p.user_id = u.id
LEFT JOIN
    public.playlist_songs ps ON p.id = ps.playlist_id
LEFT JOIN
    public.songs s ON ps.song_id = s.id
GROUP BY
    p.id, 
    u.id; 
```

### View: `playlist_songs_view`
Provides a denormalized view of songs within playlists, including song details and song creator details.
```sql
CREATE OR REPLACE VIEW public.playlist_songs_view AS
SELECT
    ps.playlist_id,
    ps.song_id,
    ps.position AS track_number, 
    s.id AS actual_song_id,
    s.title AS song_title,
    s.duration AS song_duration,
    s.cover_url AS song_cover_url,
    s.audio_url AS song_audio_url,
    s.user_id AS song_creator_id,
    prof.username AS song_creator_username,
    prof.display_name AS song_creator_display_name,
    prof.avatar_url AS song_creator_avatar_url
FROM
    public.playlist_songs ps
JOIN
    public.songs s ON ps.song_id = s.id 
LEFT JOIN
    public.profiles prof ON s.user_id = prof.id;

COMMENT ON VIEW public.playlist_songs_view IS 'Provides a denormalized view of songs within playlists, including song details and song creator details. Adapted to current playlist_songs schema.';
```

### RPC: `create_playlist_with_coin_deduction` (Updated)
Creates a playlist and deducts coins based on cost defined in `creation_costs`. Admins pay 0.
```sql
CREATE OR REPLACE FUNCTION public.create_playlist_with_coin_deduction(
  p_user_id UUID,
  p_name TEXT,
  p_description TEXT,
  p_is_public BOOLEAN,
  p_slug TEXT,
  p_cover_url TEXT,
  p_banner_url TEXT,
  p_genres TEXT[],
  p_moods TEXT[],
  p_instrumentation TEXT[],
  p_are_comments_public BOOLEAN -- Added May 2025
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_balance INTEGER;
  v_playlist_cost INTEGER;
  v_user_role TEXT;
  v_actual_cost INTEGER;
  new_playlist_id UUID;
  profile_exists BOOLEAN;
BEGIN
  SELECT profiles.user_role, profiles.coins_balance, EXISTS(SELECT 1 FROM public.profiles WHERE id = p_user_id)
  INTO v_user_role, current_balance, profile_exists
  FROM public.profiles WHERE id = p_user_id;

  IF NOT profile_exists THEN
    RETURN jsonb_build_object('status', 'error', 'message', 'User profile not found.');
  END IF;
  IF current_balance IS NULL THEN
    RETURN jsonb_build_object('status', 'error', 'message', 'User coins_balance is null.');
  END IF;

  SELECT cost INTO v_playlist_cost FROM public.creation_costs WHERE resource_type = 'playlist';
  IF NOT FOUND THEN
    v_playlist_cost := 0; 
  END IF;

  IF v_user_role = 'admin' THEN
    v_actual_cost := 0;
  ELSE
    v_actual_cost := v_playlist_cost;
  END IF;

  IF current_balance < v_actual_cost THEN
    RETURN jsonb_build_object('status', 'error', 'message', 'insufficient_coins', 'required', v_actual_cost, 'balance', current_balance);
  END IF;

  IF v_actual_cost > 0 THEN
    UPDATE public.profiles
    SET coins_balance = coins_balance - v_actual_cost
    WHERE id = p_user_id;
  END IF;

  INSERT INTO public.playlists (
    user_id, name, description, is_public, slug, 
    cover_url, banner_url, genres, moods, instrumentation, are_comments_public, -- Added are_comments_public
    created_at, updated_at
  )
  VALUES (
    p_user_id, p_name, p_description, p_is_public, p_slug, 
    p_cover_url, p_banner_url, p_genres, p_moods, p_instrumentation, p_are_comments_public, -- Added p_are_comments_public
    now(), now()
  )
  RETURNING id INTO new_playlist_id;

  IF new_playlist_id IS NULL THEN
     IF v_actual_cost > 0 THEN 
        UPDATE public.profiles SET coins_balance = coins_balance + v_actual_cost WHERE id = p_user_id;
     END IF;
     RETURN jsonb_build_object('status', 'error', 'message', 'Playlist creation failed after coin deduction attempt.');
  END IF;

  RETURN jsonb_build_object('status', 'success', 'playlist_id', new_playlist_id, 'new_balance', current_balance - v_actual_cost, 'cost_applied', v_actual_cost);

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object('status', 'error', 'message', SQLERRM, 'details', SQLSTATE);
END;
$$;
```

### RPC: `toggle_profile_follow` (New - May 2025)
```sql
CREATE OR REPLACE FUNCTION toggle_profile_follow(
    p_profile_id UUID,
    p_user_id UUID
)
RETURNS TABLE (is_following BOOLEAN, new_follower_count INTEGER)
LANGUAGE plpgsql
SECURITY DEFINER 
AS $$
DECLARE
    v_is_following BOOLEAN;
    v_new_follower_count INTEGER;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM public.profile_followers
        WHERE follower_id = p_user_id AND followed_profile_id = p_profile_id
    ) INTO v_is_following;

    IF v_is_following THEN
        DELETE FROM public.profile_followers
        WHERE follower_id = p_user_id AND followed_profile_id = p_profile_id;
        UPDATE public.profiles
        SET follower_count = GREATEST(0, follower_count - 1)
        WHERE id = p_profile_id
        RETURNING follower_count INTO v_new_follower_count;
        v_is_following := FALSE;
    ELSE
        IF p_user_id = p_profile_id THEN
            RAISE EXCEPTION 'User cannot follow themselves.';
        END IF;
        INSERT INTO public.profile_followers (follower_id, followed_profile_id)
        VALUES (p_user_id, p_profile_id);
        UPDATE public.profiles
        SET follower_count = follower_count + 1
        WHERE id = p_profile_id
        RETURNING follower_count INTO v_new_follower_count;
        v_is_following := TRUE;
    END IF;
    RETURN QUERY SELECT v_is_following, v_new_follower_count;
END;
$$;
```

### RPC: `toggle_band_follow` (New - May 2025)
```sql
CREATE OR REPLACE FUNCTION toggle_band_follow(
    p_band_id UUID,
    p_user_id UUID
)
RETURNS TABLE (is_following BOOLEAN, new_follower_count INTEGER)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_is_following BOOLEAN;
    v_new_follower_count INTEGER;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM public.band_followers
        WHERE user_id = p_user_id AND band_id = p_band_id
    ) INTO v_is_following;

    IF v_is_following THEN
        DELETE FROM public.band_followers
        WHERE user_id = p_user_id AND band_id = p_band_id;
        UPDATE public.bands
        SET follower_count = GREATEST(0, follower_count - 1)
        WHERE id = p_band_id
        RETURNING follower_count INTO v_new_follower_count;
        v_is_following := FALSE;
    ELSE
        INSERT INTO public.band_followers (user_id, band_id)
        VALUES (p_user_id, p_band_id);
        UPDATE public.bands
        SET follower_count = follower_count + 1
        WHERE id = p_band_id
        RETURNING follower_count INTO v_new_follower_count;
        v_is_following := TRUE;
    END IF;
    RETURN QUERY SELECT v_is_following, v_new_follower_count;
END;
$$;
```

### RPC: `get_playlist_details_for_view`
### Plays (`plays`)

Enregistre chaque écoute d'un morceau par un utilisateur.

| Colonne        | Type      | Description                                                                 |
|----------------|-----------|-----------------------------------------------------------------------------|
| id             | UUID      | Identifiant unique de l'écoute (clé primaire)                               |
| song_id        | UUID      | Référence au morceau écouté (clé étrangère vers public.songs(id))           |
| user_id        | UUID      | Utilisateur qui a écouté le morceau (clé étrangère vers auth.users(id))      |
| created_at     | TIMESTAMP | Date et heure de l'écoute                                                   |
| duration       | INTEGER   | Durée de l'écoute en secondes                                               |
| completed      | BOOLEAN   | Indique si l'écoute a été complétée                                         |
| source         | TEXT      | Source de l'écoute (playlist, album, recherche, etc.)                       |
| device_type    | TEXT      | Type d'appareil utilisé (mobile, desktop, etc.)                             |

```sql
CREATE TABLE public.plays (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  song_id UUID REFERENCES public.songs(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  duration INTEGER,
  completed BOOLEAN DEFAULT FALSE,
  source TEXT,
  device_type TEXT
);

CREATE INDEX plays_song_id_idx ON public.plays(song_id);
CREATE INDEX plays_user_id_idx ON public.plays(user_id);
CREATE INDEX plays_created_at_idx ON public.plays(created_at);
```

### Audio Analysis (`audio_analysis`)

Stocke les caractéristiques audio des morceaux pour l'analyse et la visualisation.

| Colonne            | Type      | Description                                                     |
|--------------------|-----------|-----------------------------------------------------------------|
| id                 | UUID      | Identifiant unique (clé primaire)                               |
| song_id            | UUID      | Référence au morceau (clé étrangère vers public.songs(id))      |
| energy             | FLOAT     | Niveau d'énergie du morceau (0.0 à 1.0)                         |
| danceability       | FLOAT     | Niveau de dansabilité (0.0 à 1.0)                               |
| valence            | FLOAT     | Positivité émotionnelle (0.0 à 1.0)                             |
| acousticness       | FLOAT     | Caractère acoustique (0.0 à 1.0)                                |
| instrumentalness   | FLOAT     | Caractère instrumental (0.0 à 1.0)                              |
| speechiness        | FLOAT     | Présence vocale (0.0 à 1.0)                                     |
| liveness           | FLOAT     | Caractère live (0.0 à 1.0)                                      |
| created_at         | TIMESTAMP | Date de création de l'analyse                                   |
| updated_at         | TIMESTAMP | Date de mise à jour de l'analyse                                |

```sql
CREATE TABLE public.audio_analysis (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  song_id UUID REFERENCES public.songs(id) ON DELETE CASCADE UNIQUE NOT NULL,
  energy FLOAT CHECK (energy BETWEEN 0 AND 1),
  danceability FLOAT CHECK (danceability BETWEEN 0 AND 1),
  valence FLOAT CHECK (valence BETWEEN 0 AND 1),
  acousticness FLOAT CHECK (acousticness BETWEEN 0 AND 1),
  instrumentalness FLOAT CHECK (instrumentalness BETWEEN 0 AND 1),
  speechiness FLOAT CHECK (speechiness BETWEEN 0 AND 1),
  liveness FLOAT CHECK (liveness BETWEEN 0 AND 1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX audio_analysis_song_id_idx ON public.audio_analysis(song_id);
```

### Audience Demographics (`audience_demographics`)

Stocke les données démographiques agrégées des auditeurs pour chaque artiste.

| Colonne        | Type      | Description                                                                 |
|----------------|-----------|-----------------------------------------------------------------------------|
| id             | UUID      | Identifiant unique (clé primaire)                                           |
| artist_id      | UUID      | Référence à l'artiste (clé étrangère vers auth.users(id))                   |
| age_group      | TEXT      | Groupe d'âge (18-24, 25-34, etc.)                                           |
| gender         | TEXT      | Genre (homme, femme, non-binaire, etc.)                                     |
| country        | TEXT      | Pays de l'auditeur                                                          |
| device_type    | TEXT      | Type d'appareil utilisé                                                     |
| count          | INTEGER   | Nombre d'auditeurs dans ce segment                                          |
| last_updated   | TIMESTAMP | Date de dernière mise à jour                                                |

```sql
CREATE TABLE public.audience_demographics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  age_group TEXT,
  gender TEXT,
  country TEXT,
  device_type TEXT,
  count INTEGER NOT NULL DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(artist_id, age_group, gender, country, device_type)
);

CREATE INDEX audience_demographics_artist_id_idx ON public.audience_demographics(artist_id);
```

Fetches comprehensive details for a single playlist, intended for display on a playlist detail page. Includes permission checks.
```sql
CREATE OR REPLACE FUNCTION public.get_playlist_details_for_view(p_playlist_id UUID, p_requesting_user_id UUID DEFAULT NULL)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_playlist_details JSONB;
  v_playlist_owner_id UUID;
  v_is_public BOOLEAN;
BEGIN
  SELECT user_id, is_public INTO v_playlist_owner_id, v_is_public
  FROM public.playlists
  WHERE id = p_playlist_id;

  IF NOT FOUND THEN
    RETURN NULL; 
  END IF;

  IF NOT v_is_public AND (p_requesting_user_id IS NULL OR v_playlist_owner_id <> p_requesting_user_id) THEN
    RETURN NULL; 
  END IF;

  SELECT jsonb_build_object(
    'id', p.id,
    'name', p.name,
    'description', p.description,
    'is_public', p.is_public,
    'cover_url', p.cover_url,
    'user_id', p.user_id,
    'created_at', p.created_at,
    'updated_at', p.updated_at,
    'like_count', p.like_count,
    'dislike_count', p.dislike_count,
    'follower_count', p.follower_count,
    'plays', p.plays,
    'banner_url', p.banner_url,
    'genres', p.genres,
    'moods', p.moods,
    'instrumentation', p.instrumentation,
    'profiles', creator_profile.prof_json,
    'playlist_songs', playlist_songs_data.songs_agg
  )
  INTO v_playlist_details
  FROM public.playlists p
  LEFT JOIN (
    SELECT 
      pr.id as profile_id, 
      jsonb_build_object(
        'username', pr.username,
        'display_name', pr.display_name,
        'avatar_url', pr.avatar_url
      ) as prof_json 
    FROM public.profiles pr
  ) creator_profile ON p.user_id = creator_profile.profile_id
  LEFT JOIN (
    SELECT 
      psv.playlist_id,
      jsonb_agg(jsonb_build_object(
        'songs', song_details.s_json 
      )) as songs_agg
    FROM public.playlist_songs_view psv
    LEFT JOIN (
        SELECT 
            s_inner.id as song_id, 
            jsonb_build_object(
                'id', s_inner.id, 
                'title', s_inner.title, 
                'duration', s_inner.duration, 
                'cover_url', s_inner.cover_url, 
                'audio_url', s_inner.audio_url, 
                'user_id', s_inner.user_id,
                'profiles', song_creator_prof.prof_json
            ) as s_json
        FROM public.songs s_inner
        LEFT JOIN (
            SELECT pr_song.id as song_creator_id, jsonb_build_object('username', pr_song.username, 'display_name', pr_song.display_name) as prof_json
            FROM public.profiles pr_song
        ) song_creator_prof ON s_inner.user_id = song_creator_prof.song_creator_id
    ) song_details ON psv.song_id = song_details.song_id
    WHERE psv.playlist_id = p_playlist_id
    GROUP BY psv.playlist_id
  ) playlist_songs_data ON p.id = playlist_songs_data.playlist_id
  WHERE p.id = p_playlist_id;

  RETURN v_playlist_details;
END;
$$;
```
