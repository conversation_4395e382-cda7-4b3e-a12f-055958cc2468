'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useUser } from '@/contexts/user-context';
import { useRouter } from 'next/navigation';
import { toast } from '@/hooks/use-toast';
import { useAIComposerConfig } from '@/hooks/useAIComposerConfig';

// Types pour l'état principal
export interface ChordPosition {
  id: string;
  position: number;
  chord: string;
  instrument: string;
  tuning?: string;
  fret?: number;
  fingering?: number[];
  preview?: {
    svg?: string;
    audio?: string;
  };
}

export interface LyricsSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  content: string;
  chords: ChordPosition[];
  aiSuggestions?: string[];
  key?: string;
  tempo?: number;
  duration?: number;
  startTime?: number;
  lyrics?: any[];
}

export interface StyleConfig {
  genres: string[];
  moods: string[];
  instrumentation: string[];
  capo: number;
  tuningFrequency: number;
  bpm: number;
  timeSignature: string;
  key: string;
  mode: string;
}

export interface CurrentSong {
  title: string;
  artist: string;
  key: string;
  tempo: number;
  timeSignature: string;
}

// Hook principal pour l'état AI Composer
export const useAIComposerCore = (songId?: string) => {
  const router = useRouter();
  const supabase = createClientComponentClient();
  const user = useUser();
  
  // Configuration IA
  const { 
    config: aiConfig, 
    saveConfig: saveAiConfig, 
    callAI, 
    isLoading: aiLoading, 
    error: aiError, 
    isConfigured 
  } = useAIComposerConfig();

  // États principaux
  const [activeTab, setActiveTab] = useState('compose');
  const [currentSong, setCurrentSong] = useState<CurrentSong>({
    title: 'Nouvelle Composition',
    artist: '',
    key: 'C',
    tempo: 120,
    timeSignature: '4/4'
  });

  // Configuration style/thème
  const [styleConfig, setStyleConfig] = useState<StyleConfig>({
    genres: [],
    moods: [],
    instrumentation: [],
    capo: 0,
    tuningFrequency: 440,
    bpm: 120,
    timeSignature: '4/4',
    key: 'C',
    mode: 'major'
  });

  // États pour les paroles et structure
  const [lyricsContent, setLyricsContent] = useState('');
  const [songSections, setSongSections] = useState<LyricsSection[]>([
    {
      id: '1',
      type: 'verse',
      title: 'Couplet 1',
      content: '',
      chords: []
    },
    {
      id: '2', 
      type: 'chorus',
      title: 'Refrain',
      content: '',
      chords: []
    }
  ]);

  const [selectedSection, setSelectedSection] = useState<string>('1');
  const [isPlaying, setIsPlaying] = useState(false);
  const [saving, setSaving] = useState(false);

  // Historique IA
  const [aiHistory, setAiHistory] = useState<{ role: string; content: string }[]>([]);
  const [lastAiResult, setLastAiResult] = useState<string>('');
  const [generalPrompt, setGeneralPrompt] = useState<string>(
    'Vous êtes un assistant musical expert. Aidez-moi à composer et améliorer ma musique.'
  );

  // Fonction pour parser les paroles en sections
  const parseLyricsIntoSections = useCallback((lyrics: string): LyricsSection[] => {
    const lines = lyrics.split('\n');
    const sections: LyricsSection[] = [];
    let currentSection: LyricsSection | null = null;
    
    lines.forEach((line, index) => {
      // Détecter les titres de section [Verse], [Chorus], etc.
      const sectionMatch = line.match(/^\[(.+)\]$/);
      if (sectionMatch) {
        if (currentSection) {
          sections.push(currentSection);
        }
        currentSection = {
          id: `section-${Date.now()}-${index}`,
          type: getSectionType(sectionMatch[1]),
          title: sectionMatch[1],
          content: '',
          chords: []
        };
      } else if (currentSection && line.trim()) {
        // Ajouter le contenu à la section courante
        if (currentSection.content) {
          currentSection.content += '\n';
        }
        currentSection.content += line;
      }
    });
    
    if (currentSection) {
      sections.push(currentSection);
    }
    
    // Si aucune section n'a été trouvée, créer une section par défaut
    if (sections.length === 0 && lyrics.trim()) {
      sections.push({
        id: `section-${Date.now()}`,
        type: 'verse',
        title: 'Verse 1',
        content: lyrics,
        chords: []
      });
    }
    
    return sections;
  }, []);

  // Fonction pour déterminer le type de section
  const getSectionType = useCallback((title: string): LyricsSection['type'] => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('chorus') || lowerTitle.includes('refrain')) return 'chorus';
    if (lowerTitle.includes('verse') || lowerTitle.includes('couplet')) return 'verse';
    if (lowerTitle.includes('bridge') || lowerTitle.includes('pont')) return 'bridge';
    if (lowerTitle.includes('intro')) return 'intro';
    if (lowerTitle.includes('outro')) return 'outro';
    return 'verse'; // Par défaut
  }, []);

  // Chargement des données de la chanson
  useEffect(() => {
    const loadSongData = async () => {
      if (songId && supabase && user) {
        try {
          const { data: song, error } = await supabase
            .from('songs')
            .select('*')
            .eq('id', songId)
            .single();
          
          if (error) throw error;
          
          if (song) {
            // Mettre à jour les données de la chanson
            setCurrentSong({
              title: song.title || 'Chanson sans titre',
              artist: song.artist || 'Artiste inconnu',
              key: song.key || 'C',
              tempo: song.bpm || 120,
              timeSignature: song.time_signature || '4/4'
            });
            
            // Mettre à jour le contenu des paroles
            if (song.lyrics) {
              setLyricsContent(song.lyrics);
              
              // Parser les sections depuis les paroles
              const sections = parseLyricsIntoSections(song.lyrics);
              setSongSections(sections);
              if (sections.length > 0) {
                setSelectedSection(sections[0].id);
              }
            }
            
            // Mettre à jour la configuration de style
            setStyleConfig(prev => ({
              ...prev,
              key: song.key || 'C',
              bpm: song.bpm || 120,
              timeSignature: song.time_signature || '4/4',
              genre: song.genre || 'Pop'
            }));
          }
        } catch (error) {
          console.error('Erreur lors du chargement de la chanson:', error);
          toast({
            title: 'Erreur',
            description: 'Erreur lors du chargement de la chanson.',
            variant: 'destructive'
          });
        }
      }
    };
    
    loadSongData();
  }, [songId, supabase, user, parseLyricsIntoSections]);

  // Fonctions utilitaires pour extraire les données
  const extractChordProgressions = useCallback(() => {
    const progressions: string[] = [];
    songSections.forEach(section => {
      section.chords.forEach(chord => {
        if (!progressions.includes(chord.chord)) {
          progressions.push(chord.chord);
        }
      });
    });
    return progressions;
  }, [songSections]);
  
  const extractSongStructure = useCallback(() => {
    return songSections.map(section => ({
      type: section.type,
      title: section.title,
      duration: section.content.split('\n').length * 4, // Estimation
      hasChords: section.chords.length > 0
    }));
  }, [songSections]);

  return {
    // États
    activeTab,
    setActiveTab,
    currentSong,
    setCurrentSong,
    styleConfig,
    setStyleConfig,
    lyricsContent,
    setLyricsContent,
    songSections,
    setSongSections,
    selectedSection,
    setSelectedSection,
    isPlaying,
    setIsPlaying,
    saving,
    setSaving,
    aiHistory,
    setAiHistory,
    lastAiResult,
    setLastAiResult,
    generalPrompt,
    setGeneralPrompt,
    
    // Configuration IA
    aiConfig,
    saveAiConfig,
    callAI,
    aiLoading,
    aiError,
    isConfigured,
    
    // Utilitaires
    parseLyricsIntoSections,
    getSectionType,
    extractChordProgressions,
    extractSongStructure,
    
    // Contexte
    router,
    supabase,
    user,
    songId
  };
};
