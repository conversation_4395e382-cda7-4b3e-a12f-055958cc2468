import { Controller, useFormContext, Control, FieldValues, FieldPath } from 'react-hook-form';
import { Input } from '@/components/ui/input'; // Using a basic input for now
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from '@/components/ui/form';

interface RHFDropzoneProps<TFieldValues extends FieldValues = FieldValues> extends React.InputHTMLAttributes<HTMLInputElement> {
  control?: Control<TFieldValues>; // Made control optional
  name: FieldPath<TFieldValues>; // Changed from string to FieldPath for consistency
  label?: string;
  helperText?: React.ReactNode;
  onFileChange?: (file: File | null) => void;
}

export function RHFDropzone<TFieldValues extends FieldValues = FieldValues>({ name, label, helperText, onFileChange, control: controlProp, ...other }: RHFDropzoneProps<TFieldValues>) {
  const context = useFormContext<TFieldValues>();
  const control = controlProp || context?.control;

  if (!control) {
    console.error('RHFDropzone requires control prop or to be used within a FormProvider.');
    // Return a simplified error message or null
    return <FormItem><FormLabel>{label}</FormLabel><FormMessage>Control not found.</FormMessage></FormItem>;
  }

  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState: { error } }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Input
              {...other}
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0] || null;
                field.onChange(file); // Store the File object or its path/URL
                if (onFileChange) {
                  onFileChange(file);
                }
              }}
              // value is not directly controlled for file inputs in this manner
              // field.value will hold the File object or relevant data
            />
          </FormControl>
          {helperText && !error && <FormDescription>{helperText}</FormDescription>}
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormItem>
      )}
    />
  );
}