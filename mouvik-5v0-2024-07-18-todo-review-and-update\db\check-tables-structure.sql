-- Vérifier la structure de la table follows si elle existe
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'follows';

-- Vérifier la structure de la table likes si elle existe
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'likes';

-- Vérifier la structure de la table views si elle existe
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'views';

-- Vérifier la structure de la table comments si elle existe
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'comments';

-- Vérifier les tables existantes dans le schéma public
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public';
