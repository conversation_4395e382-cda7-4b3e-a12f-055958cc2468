# Suivi des Tâches - MOUVIK

## ✅ Tâches Complétées

### Design et UI
- [x] Création du système de design (couleurs, typographie, composants)
- [x] Implémentation du thème sombre avec accents turquoise
- [x] Création de la page d'accueil (landing page)
- [x] Création du layout du dashboard
- [x] Développement des composants UI réutilisables
- [x] Création du lecteur de musique
- [x] Implémentation de la barre latérale de navigation
- [x] Création de l'en-tête avec recherche et notifications

### Authentification
- [x] Mise en place de l'authentification avec Supabase
- [x] Création des pages de connexion et d'inscription
- [x] Implémentation de la confirmation d'email
- [x] Création du provider d'authentification
- [x] Gestion des callbacks d'authentification

### Base de Données
- [x] Configuration de Supabase
- [x] Création des tables principales (users, profiles, tracks, albums)
- [x] Scripts d'initialisation de la base de données
- [x] Ajout des colonnes pour les abonnements

### Fonctionnalités Principales
- [x] Implémentation du lecteur de musique
- [x] Création des cartes pour les pistes, albums et playlists
- [x] Développement de la section tendances
- [x] Création de la page de profil utilisateur
- [x] Implémentation de la page d'édition de profil
- [x] Création des pages d'albums et de pistes
- [x] Développement du système de playlists
- [x] Implémentation de la section artistes en vedette

### Fonctionnalités Pro
- [x] Création des pages pour utilisateurs premium
- [x] Implémentation de la liste des pistes et albums
- [x] Développement des pages d'édition d'albums et de pistes
- [x] Création de la page de tarification
- [x] Implémentation de la bannière d'abonnement

### Administration
- [x] Création du tableau de bord administrateur
- [x] Implémentation de la gestion des utilisateurs
- [x] Développement de la page de débogage
- [x] Création des routes API d'administration

## 📋 Tâches À Faire

### Liste des Tâches - Nouvelles Fonctionnalités

Ce document centralise les tâches de développement (TODOs) pour les modules "Découvrir & Playlists" et "Communauté", organisées par priorité (P0 = Très Haute, P1 = Haute, P2 = Moyenne, P3 = Basse).

## 1. Module "Découvrir & Playlists"

### Priorité P0 (Fondations)
- [ ] **(P0)** Modéliser/Créer tables Supabase : `music_resources`, `tags`, `resource_tags`, `playlists`, `playlist_resources`.
- [ ] **(P0)** Définir taxonomie `tags` initiale (genres, styles, plateformes principales).

### Priorité P1 (Fonctionnalités Core Import)
- [ ] **(P1)** Développer service d'extraction pour Spotify (API) : récupération métadonnées.
- [ ] **(P1)** UI formulaire d'ajout/import : détection URL, affichage preview, input tags avec auto-complétion.
- [ ] **(P1)** API backend (`POST /api/discover/resources`) : logique d'import complète (validation, création BDD, liaison tags, création activité).
- [ ] **(P1)** API backend (`POST /api/discover/preview`) : logique de preview URL externe.
- [ ] **(P1)** API backend (`GET /api/tags/suggest` ou similaire) : pour l'auto-complétion des tags.

### Priorité P2 (Fonctionnalités Core Affichage & Autres Sources)
- [ ] **(P2)** Feed `Découvrir` basique : affichage liste `music_resources` (composant `ResourceList` & `ResourceCard`).
- [ ] **(P2)** API backend (`GET /api/discover/resources`) : logique de base pour lister les ressources.
- [ ] **(P2)** Développer service d'extraction pour Suno/Udio (Scraping/API si dispo).
- [ ] **(P2)** Intégrer badges de provenance (UI `ResourceCard`).
- [ ] **(P2)** Intégrer player interne (pour `type='ai_composer'`) et embed simple (pour externes comme YouTube) dans `ResourceCard` ou page détail.

### Priorité P3 (Améliorations & Finitions)
- [ ] **(P3)** Filtres avancés & recherche textuelle sur le feed `Découvrir` (UI + logique API).
- [ ] **(P3)** Gestion complète des playlists (UI création/édition, API `GET/POST/PUT/DELETE /api/discover/playlists` et gestion `playlist_resources`).
- [ ] **(P3)** Système de modération/suggestion pour les nouveaux `tags` custom créés par les utilisateurs.
- [ ] **(P3)** Tests unitaires/intégration pour les services d'extraction, le mapping de tags, et les endpoints API critiques.
- [ ] **(P3)** Ajouter d'autres sources d'import (YouTube, SoundCloud...).

## 2. Module "Communauté"

### Priorité P0 (Fondations)
- [ ] **(P0)** Finaliser/Valider modèles BDD Supabase : `activity`, `hashtags`, `activity_hashtags`, `likes`. Vérifier liaisons avec `profiles`, `bands`, `music_resources`, `albums`.

### Priorité P1 (Fonctionnalités Core Mur & Activité)
- [ ] **(P1)** API backend (`POST /api/community/activity`) : création post texte, détection/création hashtags et liens `activity_hashtags`.
- [ ] **(P1)** UI Mur d'activité basique (`ActivityFeed`, `ActivityCard`) : affichage posts (texte, auteur, date).
- [ ] **(P1)** Intégrer la génération automatique d'activité (`type='add_resource'`) lors de l'ajout via le module Découvrir.
- [ ] **(P1)** API backend (`GET /api/community/activity`) : logique de base pour lister les activités (mur général).

### Priorité P2 (Interactions & Hashtags)
- [ ] **(P2)** API backend (`POST /api/community/activity`) : gestion des commentaires (`parent_activity_id`).
- [ ] **(P2)** API backend/Server Actions pour Likes (`POST /likes`, `DELETE /likes` ou actions `likeActivity`, `unlikeActivity`).
- [ ] **(P2)** UI Affichage commentaires sous les posts (`CommentSection`).
- [ ] **(P2)** UI Affichage compteur de likes et bouton Like (`LikeButton`, intégration Server Action/API).
- [ ] **(P2)** Système de hashtags : page dédiée par hashtag (`/community/tags/[label]`), API (`GET /api/community/hashtags/{label}`).
- [ ] **(P2)** API (`GET /api/community/hashtags`) : pour lister/rechercher hashtags.

### Priorité P3 (Améliorations & Finitions)
- [ ] **(P3)** Filtres sur le mur d'activité (par type, utilisateur, groupe, hashtag).
- [ ] **(P3)** Gestion complète des `bands` (UI création/gestion membres, mur dédié au groupe - filtre API).
- [ ] **(P3)** Système de notifications basique (création table `notifications`, triggers/logique API pour mentions, likes, commentaires).
- [ ] **(P3)** Outils de modération simples (signalement UI, API pour marquer/masquer contenu).
- [ ] **(P3)** Ajouter d'autres types d'activité (ex: partage externe, création album/band).
- [ ] **(P3)** Tests unitaires/intégration pour les endpoints API critiques et les Server Actions.

### Fonctionnalités Audio Avancées
- [ ] Amélioration de l'affichage des formes d'onde
- [ ] Implémentation d'effets audio en temps réel
- [ ] Développement d'un égaliseur intégré
- [ ] Création d'un système de mixage multipiste
- [ ] Implémentation de l'exportation en différents formats

### Fonctionnalités IA
- [ ] Amélioration de l'assistant de composition IA
- [ ] Développement de la génération de mélodies par IA
- [ ] Implémentation de la transcription audio automatique
- [ ] Création d'un système de recommandation personnalisé
- [ ] Développement d'un mastering automatique par IA

### Fonctionnalités Sociales
- [ ] Amélioration du système de commentaires
- [ ] Implémentation du système de followers/following
- [ ] Développement des notifications en temps réel
- [ ] Création d'un système de messages privés avancé
- [ ] Implémentation du partage sur réseaux sociaux

### Monétisation
- [ ] Intégration d'un système de paiement (Stripe)
- [ ] Développement de la vente directe de musique
- [ ] Implémentation des abonnements récurrents
- [ ] Création d'un système de pourboires pour les artistes
- [ ] Développement des statistiques de revenus

### Performance et Optimisation
- [ ] Optimisation du chargement des pistes audio
- [ ] Amélioration des performances sur mobile
- [ ] Implémentation du streaming adaptatif
- [ ] Optimisation du stockage et de la compression audio
- [ ] Mise en cache avancée pour les ressources fréquemment utilisées

### Déploiement et Infrastructure
- [ ] Configuration du CI/CD
- [ ] Mise en place de tests automatisés
- [ ] Optimisation pour le SEO
- [ ] Configuration des backups automatiques
- [ ] Mise en place d'un CDN pour les fichiers audio

### Documentation
- [ ] Création d'une documentation technique complète
- [ ] Développement de guides utilisateurs
- [ ] Création de tutoriels vidéo
- [ ] Documentation de l'API pour les développeurs tiers
- [ ] Création d'une FAQ
