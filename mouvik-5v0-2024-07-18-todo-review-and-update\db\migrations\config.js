/**
 * Configuration pour les scripts de migration
 * 
 * Ce fichier contient les paramètres de configuration pour les scripts de migration.
 * Copiez ce fichier en tant que `config.local.js` et ajustez les valeurs selon votre environnement.
 */

module.exports = {
  // Configuration de la base de données
  database: {
    host: process.env.SUPABASE_DB_HOST || 'db.your-supabase-url.supabase.co',
    port: process.env.SUPABASE_DB_PORT || '5432',
    name: process.env.SUPABASE_DB_NAME || 'postgres',
    user: process.env.SUPABASE_DB_USER || 'postgres',
    password: process.env.SUPABASE_DB_PASSWORD || 'your-db-password',
    ssl: process.env.SUPABASE_DB_SSL === 'true' || false,
  },
  
  // Chemins des fichiers
  paths: {
    migrations: __dirname,
    seeds: path.join(__dirname, 'seeds'),
  },
  
  // Options de migration
  migration: {
    tableName: 'migrations',
    directory: __dirname,
  },
  
  // Options de test
  test: {
    // Nombre maximum d'enregistrements à vérifier lors des tests
    maxRecordsToCheck: 1000,
    
    // Activer/désactiver les tests spécifiques
    enableDataIntegrityTest: true,
    enablePerformanceTest: true,
    enableRollbackTest: true,
  },
  
  // Journalisation
  logging: {
    level: 'debug', // 'error', 'warn', 'info', 'debug'
    file: path.join(__dirname, 'migration.log'),
  },
  
  // Paramètres de performance
  performance: {
    // Délai d'attente maximum pour les opérations (en millisecondes)
    timeout: 30000,
    
    // Nombre de tentatives en cas d'échec
    retryAttempts: 3,
  },
  
  // Validation
  validation: {
    // Vérifier que le nombre d'enregistrements correspond avant/après migration
    checkRecordCount: true,
    
    // Vérifier l'intégrité des données échantillonnées
    sampleDataCheck: true,
    sampleSize: 10,
  },
};

// Configuration spécifique à l'environnement
const env = process.env.NODE_ENV || 'development';

// Exporter la configuration en fonction de l'environnement
if (env === 'test') {
  module.exports.database.name = process.env.TEST_DB_NAME || 'mouvik_test';
  module.exports.test.enableRollbackTest = false; // Désactiver le test de rollback en environnement de test
}

if (env === 'production') {
  module.exports.logging.level = 'warn';
  module.exports.performance.timeout = 60000;
}

// Surcharger avec la configuration locale si elle existe
try {
  const localConfig = require('./config.local');
  module.exports = deepMerge(module.exports, localConfig);
} catch (e) {
  // Fichier de configuration locale non trouvé, continuer avec la configuration par défaut
}

// Fonction utilitaire pour fusionner les objets de configuration
function deepMerge(target, source) {
  const output = Object.assign({}, target);
  
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          Object.assign(output, { [key]: source[key] });
        } else {
          output[key] = deepMerge(target[key], source[key]);
        }
      } else {
        Object.assign(output, { [key]: source[key] });
      }
    });
  }
  
  return output;
}

function isObject(item) {
  return (item && typeof item === 'object' && !Array.isArray(item));
}
