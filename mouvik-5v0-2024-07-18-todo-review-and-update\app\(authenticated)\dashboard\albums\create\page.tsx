"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase/client"
import {
  Disc,
  Save,
  X,
  Plus,
  Info,
  Upload,
  Music,
  Calendar,
  Tag,
  Globe,
  Users,
  BarChart4,
  FileText,
  FlipVerticalIcon as DragVertical,
  Search,
  Check,
  Minus,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { ImageUploader } from "@/components/ui/image-uploader"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

const genres = [
  "Pop",
  "Rock",
  "Hip-Hop",
  "R&B",
  "Jazz",
  "Électronique",
  "Classique",
  "Folk",
  "Country",
  "Blues",
  "Reggae",
  "Metal",
  "Punk",
  "Funk",
  "Soul",
  "Ambient",
  "Expérimental",
  "Indie",
  "Dance",
  "Trap",
  "Lo-fi",
  "House",
  "Techno",
  "Autre",
]

const albumTypes = ["Album", "EP", "Single", "Compilation", "Live", "Remix", "Autre"]

export default function CreateAlbumPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("info")
  const [currentDate] = useState(new Date().toISOString().split("T")[0])

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    genre: "",
    albumType: "Album",
    releaseDate: currentDate,
    isExplicit: false,
    status: "draft",
    coverUrl: "",
    label: "",
    upc: "",
    copyright: `© ${new Date().getFullYear()} Tous droits réservés`,
    recordingYear: new Date().getFullYear().toString(),
    language: "fr",
    tags: [] as string[],
    songs: [] as string[],
    isPublic: true,
    notifyFollowers: true,
    addToDiscovery: true,
    collaborators: [] as { id: string; role: string }[],
    notes: "",
  })

  const [currentTag, setCurrentTag] = useState("")
  const [availableSongs, setAvailableSongs] = useState<any[]>([])
  const [selectedSongs, setSelectedSongs] = useState<any[]>([])
  const [availableUsers, setAvailableUsers] = useState<any[]>([])
  const [collaboratorRole, setCollaboratorRole] = useState("")
  const [searchTerm, setSearchTerm] = useState("")

  // Charger les morceaux disponibles
  const loadAvailableSongs = async () => {
    try {
      const supabase = getSupabaseClient()
      const { data: session } = await supabase.auth.getSession()

      if (!session.session) return

      // Correction de la requête pour éviter l'erreur de colonne inexistante
      const { data, error } = await supabase
        .from("songs")
        .select("id, title, cover_url, duration, status, user_id")
        .eq("user_id", session.session.user.id)
        .eq("status", "published")
        .order("title", { ascending: true })

      if (error) throw error

      // Récupérer les informations de profil séparément si nécessaire
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("id, username, avatar_url")
        .eq("id", session.session.user.id)
        .single()

      if (profileError) console.error("Erreur lors du chargement du profil:", profileError)

      // Combiner les données
      const songsWithProfile =
        data?.map((song) => ({
          ...song,
          profiles: profileData || { username: "Utilisateur", avatar_url: null },
        })) || []

      setAvailableSongs(songsWithProfile)
    } catch (error) {
      console.error("Erreur lors du chargement des morceaux:", error)
    }
  }

  // Charger les utilisateurs pour les collaborations
  const loadAvailableUsers = async () => {
    try {
      const supabase = getSupabaseClient()
      const { data: session } = await supabase.auth.getSession()

      if (!session.session) return

      // Rechercher les utilisateurs suivis ou qui suivent l'utilisateur actuel
      const { data, error } = await supabase
        .from("profiles")
        .select("id, username, display_name, avatar_url")
        .neq("id", session.session.user.id)
        .order("username", { ascending: true })
        .limit(20)

      if (error) throw error

      setAvailableUsers(data || [])
    } catch (error) {
      console.error("Erreur lors du chargement des utilisateurs:", error)
    }
  }

  // Charger les données au montage du composant
  useEffect(() => {
    loadAvailableSongs()
    loadAvailableUsers()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }))
  }

  const handleCoverUpload = (url: string) => {
    setFormData((prev) => ({ ...prev, coverUrl: url }))
  }

  const addTag = () => {
    if (currentTag && !formData.tags.includes(currentTag)) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, currentTag],
      }))
      setCurrentTag("")
    }
  }

  const removeTag = (tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((t) => t !== tag),
    }))
  }

  const addSongToAlbum = (songId: string) => {
    const song = availableSongs.find((s) => s.id === songId)
    if (song && !selectedSongs.some((s) => s.id === songId)) {
      setSelectedSongs((prev) => [...prev, song])
      setFormData((prev) => ({
        ...prev,
        songs: [...prev.songs, songId],
      }))
    }
  }

  const removeSongFromAlbum = (songId: string) => {
    setSelectedSongs((prev) => prev.filter((s) => s.id !== songId))
    setFormData((prev) => ({
      ...prev,
      songs: prev.songs.filter((id) => id !== songId),
    }))
  }

  const addCollaborator = (userId: string) => {
    if (!collaboratorRole) {
      toast({
        title: "Rôle requis",
        description: "Veuillez spécifier un rôle pour ce collaborateur",
        variant: "destructive",
      })
      return
    }

    const user = availableUsers.find((u) => u.id === userId)
    if (user && !formData.collaborators.some((c) => c.id === userId)) {
      setFormData((prev) => ({
        ...prev,
        collaborators: [...prev.collaborators, { id: userId, role: collaboratorRole }],
      }))
      setCollaboratorRole("")
    }
  }

  const removeCollaborator = (userId: string) => {
    setFormData((prev) => ({
      ...prev,
      collaborators: prev.collaborators.filter((c) => c.id !== userId),
    }))
  }

  const handleDragEnd = (result: any) => {
    if (!result.destination) return

    const items = Array.from(selectedSongs)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    setSelectedSongs(items)
    setFormData((prev) => ({
      ...prev,
      songs: items.map((item) => item.id),
    }))
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  const handleSave = async (status: "draft" | "published") => {
    try {
      setIsLoading(true)

      if (!formData.title) {
        toast({
          title: "Erreur",
          description: "Le titre est obligatoire",
          variant: "destructive",
        })
        return
      }

      if (status === "published" && !formData.coverUrl) {
        toast({
          title: "Erreur",
          description: "Vous devez ajouter une pochette pour publier l'album",
          variant: "destructive",
        })
        return
      }

      if (status === "published" && formData.songs.length === 0) {
        toast({
          title: "Erreur",
          description: "Vous devez ajouter au moins un morceau à l'album pour le publier",
          variant: "destructive",
        })
        return
      }

      const supabase = getSupabaseClient()

      const {
        data: { session },
      } = await supabase.auth.getSession()

      if (!session) {
        toast({
          title: "Erreur d'authentification",
          description: "Vous devez être connecté pour créer un album",
          variant: "destructive",
        })
        return
      }

      // Créer l'album
      const { data, error } = await supabase
        .from("albums")
        .insert({
          user_id: session.user.id,
          title: formData.title,
          description: formData.description,
          genre: formData.genre,
          album_type: formData.albumType,
          release_date: formData.releaseDate || null,
          cover_url: formData.coverUrl,
          status: status,
          is_explicit: formData.isExplicit,
          label: formData.label,
          upc: formData.upc,
          copyright: formData.copyright,
          recording_year: formData.recordingYear,
          language: formData.language,
          notes: formData.notes,
        })
        .select()

      if (error) {
        throw error
      }

      const albumId = data[0].id

      // Ajouter les morceaux à l'album
      if (formData.songs.length > 0) {
        const albumSongs = formData.songs.map((songId, index) => ({
          album_id: albumId,
          song_id: songId,
          track_number: index + 1,
        }))

        const { error: songsError } = await supabase.from("album_songs").insert(albumSongs)

        if (songsError) {
          console.error("Erreur lors de l'ajout des morceaux à l'album:", songsError)
        }
      }

      // Ajouter les tags
      if (formData.tags.length > 0) {
        for (const tagName of formData.tags) {
          // Vérifier si le tag existe déjà
          const { data: existingTag } = await supabase.from("tags").select("id").eq("name", tagName).single()

          let tagId

          if (existingTag) {
            tagId = existingTag.id
          } else {
            // Créer le tag s'il n'existe pas
            const { data: newTag, error: tagError } = await supabase.from("tags").insert({ name: tagName }).select()

            if (tagError) {
              console.error("Erreur lors de la création du tag:", tagError)
              continue
            }

            tagId = newTag?.[0]?.id
          }

          if (tagId) {
            // Associer le tag à l'album
            await supabase.from("resource_tags").insert({
              tag_id: tagId,
              resource_type: "album",
              resource_id: albumId,
            })
          }
        }
      }

      // Ajouter les collaborateurs
      if (formData.collaborators.length > 0) {
        const collaborations = formData.collaborators.map((collab) => ({
          resource_id: albumId,
          resource_type: "album",
          user_id: collab.id,
          role: collab.role,
        }))

        const { error: collabError } = await supabase.from("collaborations").insert(collaborations)

        if (collabError) {
          console.error("Erreur lors de l'ajout des collaborateurs:", collabError)
        }
      }

      // Ajouter une activité
      await supabase.from("activities").insert({
        user_id: session.user.id,
        activity_type: "album_created",
        resource_type: "album",
        resource_id: albumId,
        content: `a créé un nouvel album: ${formData.title}`,
      })

      toast({
        title: status === "published" ? "Album publié" : "Brouillon enregistré",
        description:
          status === "published" ? "Votre album a été publié avec succès" : "Votre brouillon a été enregistré",
      })

      router.push(status === "published" ? `/dashboard/albums/${albumId}` : "/dashboard/albums")
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.message || "Une erreur s'est produite lors de la création de l'album",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const calculateTotalDuration = () => {
    return selectedSongs.reduce((acc, song) => acc + (song.duration || 0), 0)
  }

  const filteredSongs = availableSongs.filter(
    (song) => !formData.songs.includes(song.id) && song.title.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Créer un nouvel album</h1>
          <p className="text-muted-foreground">Ajoutez les détails de votre album et sélectionnez vos morceaux</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            Annuler
          </Button>
          <Button variant="outline" onClick={() => handleSave("draft")} disabled={isLoading}>
            <Save className="mr-2 h-4 w-4" />
            Enregistrer comme brouillon
          </Button>
          <Button onClick={() => handleSave("published")} disabled={isLoading}>
            <Globe className="mr-2 h-4 w-4" />
            Publier
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="info">
            <Info className="h-4 w-4 mr-2" />
            Informations
          </TabsTrigger>
          <TabsTrigger value="songs">
            <Music className="h-4 w-4 mr-2" />
            Morceaux
          </TabsTrigger>
          <TabsTrigger value="collaborators">
            <Users className="h-4 w-4 mr-2" />
            Collaborateurs
          </TabsTrigger>
          <TabsTrigger value="publish">
            <Globe className="h-4 w-4 mr-2" />
            Publication
          </TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Info className="mr-2 h-5 w-5" />
                    Informations de l'album
                  </CardTitle>
                  <CardDescription>Ajoutez les informations de base de votre album</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Titre de l'album *</Label>
                    <Input
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Entrez le titre de votre album"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Décrivez votre album"
                      rows={4}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="genre">Genre</Label>
                      <Select value={formData.genre} onValueChange={(value) => handleSelectChange("genre", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez un genre" />
                        </SelectTrigger>
                        <SelectContent>
                          {genres.map((genre) => (
                            <SelectItem key={genre} value={genre}>
                              {genre}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="albumType">Type d'album</Label>
                      <Select
                        value={formData.albumType}
                        onValueChange={(value) => handleSelectChange("albumType", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez un type" />
                        </SelectTrigger>
                        <SelectContent>
                          {albumTypes.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="releaseDate">Date de sortie</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="releaseDate"
                          name="releaseDate"
                          type="date"
                          value={formData.releaseDate}
                          onChange={handleInputChange}
                          className="pl-10"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="language">Langue</Label>
                      <Select
                        value={formData.language}
                        onValueChange={(value) => handleSelectChange("language", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez une langue" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fr">Français</SelectItem>
                          <SelectItem value="en">Anglais</SelectItem>
                          <SelectItem value="es">Espagnol</SelectItem>
                          <SelectItem value="de">Allemand</SelectItem>
                          <SelectItem value="it">Italien</SelectItem>
                          <SelectItem value="pt">Portugais</SelectItem>
                          <SelectItem value="ja">Japonais</SelectItem>
                          <SelectItem value="ko">Coréen</SelectItem>
                          <SelectItem value="zh">Chinois</SelectItem>
                          <SelectItem value="instrumental">Instrumental</SelectItem>
                          <SelectItem value="other">Autre</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="isExplicit" className="flex items-center gap-2 cursor-pointer">
                      <span>Contenu explicite</span>
                      <span className="text-xs text-muted-foreground">
                        (contient du langage ou des thèmes pour adultes)
                      </span>
                    </Label>
                    <Switch
                      id="isExplicit"
                      checked={formData.isExplicit}
                      onCheckedChange={(checked) => handleSwitchChange("isExplicit", checked)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Tags</Label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {formData.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <div className="relative flex-1">
                        <Tag className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          value={currentTag}
                          onChange={(e) => setCurrentTag(e.target.value)}
                          placeholder="Ajouter un tag"
                          className="pl-10"
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.preventDefault()
                              addTag()
                            }
                          }}
                        />
                      </div>
                      <Button type="button" variant="outline" onClick={addTag}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Upload className="mr-2 h-5 w-5" />
                    Pochette de l'album
                  </CardTitle>
                  <CardDescription>Téléchargez une image pour votre album</CardDescription>
                </CardHeader>
                <CardContent>
                  <ImageUploader
                    onImageUploaded={handleCoverUpload}
                    existingImageUrl={formData.coverUrl}
                    aspectRatio="square"
                    maxWidth={3000}
                    maxHeight={3000}
                    bucketName="album-covers"
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    Format recommandé : image carrée de haute qualité (minimum 1000x1000 pixels, idéalement 3000x3000)
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Informations supplémentaires
                  </CardTitle>
                  <CardDescription>Ajoutez des détails supplémentaires pour votre album</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="label">Label</Label>
                      <Input
                        id="label"
                        name="label"
                        value={formData.label}
                        onChange={handleInputChange}
                        placeholder="Nom du label (optionnel)"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="upc">Code UPC/EAN</Label>
                      <Input
                        id="upc"
                        name="upc"
                        value={formData.upc}
                        onChange={handleInputChange}
                        placeholder="Code UPC/EAN (optionnel)"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="copyright">Copyright</Label>
                      <Input
                        id="copyright"
                        name="copyright"
                        value={formData.copyright}
                        onChange={handleInputChange}
                        placeholder="© 2023 Tous droits réservés"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="recordingYear">Année d'enregistrement</Label>
                      <Input
                        id="recordingYear"
                        name="recordingYear"
                        value={formData.recordingYear}
                        onChange={handleInputChange}
                        placeholder="2023"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes de production</Label>
                    <Textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      placeholder="Notes internes sur la production (non visibles publiquement)"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Prévisualisation</CardTitle>
                  <CardDescription>Aperçu de votre album</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col items-center">
                  <div className="w-full max-w-[240px] aspect-square rounded-md overflow-hidden bg-muted mb-4">
                    {formData.coverUrl ? (
                      <img
                        src={formData.coverUrl || "/placeholder.svg"}
                        alt="Pochette"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-primary/10">
                        <Disc className="h-16 w-16 text-primary/40" />
                      </div>
                    )}
                  </div>

                  <h3 className="text-xl font-bold">{formData.title || "Titre de l'album"}</h3>
                  <p className="text-muted-foreground">{formData.genre || "Genre"}</p>

                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline">{formData.albumType}</Badge>
                    {formData.isExplicit && (
                      <Badge variant="outline" className="bg-red-500/10">
                        Explicite
                      </Badge>
                    )}
                  </div>

                  <div className="mt-2 text-sm text-muted-foreground">
                    {formData.releaseDate && (
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{format(new Date(formData.releaseDate), "d MMMM yyyy", { locale: fr })}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-wrap gap-1 mt-4 justify-center">
                    {formData.tags.map((tag) => (
                      <Badge key={tag} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Conseils</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Pochette</h4>
                    <p className="text-sm text-muted-foreground">
                      Utilisez une image carrée de haute qualité (minimum 1000x1000 pixels, idéalement 3000x3000) pour
                      une meilleure visibilité sur tous les appareils.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Morceaux</h4>
                    <p className="text-sm text-muted-foreground">
                      Ajoutez au moins un morceau à votre album avant de le publier. Vous pouvez réorganiser l'ordre des
                      morceaux par glisser-déposer.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Tags</h4>
                    <p className="text-sm text-muted-foreground">
                      Ajoutez des tags pertinents pour améliorer la découvrabilité de votre album. Les tags aident les
                      auditeurs à trouver votre musique.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="songs" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Music className="mr-2 h-5 w-5" />
                  Morceaux disponibles
                </CardTitle>
                <CardDescription>Sélectionnez les morceaux à ajouter à votre album</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Rechercher un morceau..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="h-[400px] overflow-y-auto">
                  {filteredSongs.length > 0 ? (
                    <div className="space-y-2">
                      {filteredSongs.map((song) => (
                        <div
                          key={song.id}
                          className="flex items-center justify-between p-2 rounded-md hover:bg-accent/50 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                              {song.cover_url ? (
                                <img
                                  src={song.cover_url || "/placeholder.svg"}
                                  alt={song.title}
                                  className="h-10 w-10 rounded-md object-cover"
                                />
                              ) : (
                                <Music className="h-5 w-5 text-primary" />
                              )}
                            </div>
                            <div>
                              <div className="font-medium">{song.title}</div>
                              <div className="text-xs text-muted-foreground">
                                {song.duration ? formatDuration(song.duration) : ""}
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => addSongToAlbum(song.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full">
                      <p className="text-muted-foreground">
                        {searchTerm ? "Aucun morceau trouvé" : "Aucun morceau disponible"}
                      </p>
                      <Button variant="outline" className="mt-4" asChild>
                        <a href="/dashboard/songs/create">Créer un morceau</a>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Disc className="mr-2 h-5 w-5" />
                  Morceaux de l'album
                </CardTitle>
                <CardDescription>Organisez l'ordre des morceaux dans votre album</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] overflow-y-auto">
                  {selectedSongs.length > 0 ? (
                    <DragDropContext onDragEnd={handleDragEnd}>
                      <Droppable droppableId="tracks">
                        {(provided) => (
                          <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                            {selectedSongs.map((song, index) => (
                              <Draggable key={song.id} draggableId={song.id} index={index}>
                                {(provided) => (
                                  <div
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    className="flex items-center justify-between p-2 rounded-md border bg-card"
                                  >
                                    <div className="flex items-center gap-3">
                                      <div {...provided.dragHandleProps} className="cursor-grab">
                                        <DragVertical className="h-4 w-4 text-muted-foreground" />
                                      </div>
                                      <div className="w-6 text-center font-medium text-muted-foreground">
                                        {index + 1}
                                      </div>
                                      <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                                        {song.cover_url ? (
                                          <img
                                            src={song.cover_url || "/placeholder.svg"}
                                            alt={song.title}
                                            className="h-10 w-10 rounded-md object-cover"
                                          />
                                        ) : (
                                          <Music className="h-5 w-5 text-primary" />
                                        )}
                                      </div>
                                      <div>
                                        <div className="font-medium">{song.title}</div>
                                        <div className="text-xs text-muted-foreground">
                                          {song.duration ? formatDuration(song.duration) : ""}
                                        </div>
                                      </div>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => removeSongFromAlbum(song.id)}
                                      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </div>
                                )}
                              </Draggable>
                            ))}
                            {provided.placeholder}
                          </div>
                        )}
                      </Droppable>
                    </DragDropContext>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full">
                      <p className="text-muted-foreground">Aucun morceau ajouté à l'album</p>
                      <p className="text-xs text-muted-foreground mt-2">
                        Ajoutez des morceaux depuis la liste des morceaux disponibles
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <div className="w-full flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    {selectedSongs.length} morceau{selectedSongs.length !== 1 ? "x" : ""}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Durée totale: {formatDuration(calculateTotalDuration())}
                  </div>
                </div>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="collaborators" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  Ajouter des collaborateurs
                </CardTitle>
                <CardDescription>Ajoutez les personnes qui ont contribué à cet album</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="collaboratorRole">Rôle</Label>
                      <Input
                        id="collaboratorRole"
                        value={collaboratorRole}
                        onChange={(e) => setCollaboratorRole(e.target.value)}
                        placeholder="Ex: Producteur, Mixage, Mastering..."
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Utilisateur</Label>
                      <Select onValueChange={(value) => addCollaborator(value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un utilisateur" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableUsers.map((user) => (
                            <SelectItem key={user.id} value={user.id}>
                              {user.display_name || user.username}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="h-[300px] overflow-y-auto border rounded-md p-4">
                    {formData.collaborators.length > 0 ? (
                      <div className="space-y-2">
                        {formData.collaborators.map((collab) => {
                          const user = availableUsers.find((u) => u.id === collab.id)
                          return (
                            <div key={collab.id} className="flex items-center justify-between p-2 rounded-md border">
                              <div className="flex items-center gap-3">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={user?.avatar_url || ""} alt={user?.username || ""} />
                                  <AvatarFallback>{user?.username?.charAt(0).toUpperCase() || "U"}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{user?.display_name || user?.username}</div>
                                  <div className="text-xs text-muted-foreground">{collab.role}</div>
                                </div>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeCollaborator(collab.id)}
                                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          )
                        })}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-full">
                        <p className="text-muted-foreground">Aucun collaborateur ajouté</p>
                        <p className="text-xs text-muted-foreground mt-2">
                          Ajoutez les personnes qui ont contribué à cet album
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Info className="mr-2 h-5 w-5" />À propos des collaborations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Pourquoi ajouter des collaborateurs ?</h4>
                  <p className="text-sm text-muted-foreground">
                    Ajouter des collaborateurs permet de reconnaître le travail de chacun et d'améliorer la visibilité
                    de votre album.
                  </p>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Exemples de rôles</h4>
                  <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-4">
                    <li>Producteur</li>
                    <li>Mixage</li>
                    <li>Mastering</li>
                    <li>Compositeur</li>
                    <li>Parolier</li>
                    <li>Musicien (précisez l'instrument)</li>
                    <li>Artwork</li>
                    <li>Direction artistique</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Notification</h4>
                  <p className="text-sm text-muted-foreground">
                    Les collaborateurs seront notifiés lorsque l'album sera publié et pourront le partager sur leur
                    profil.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="publish" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="mr-2 h-5 w-5" />
                  Options de visibilité
                </CardTitle>
                <CardDescription>Définissez qui peut voir votre album</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-4 rounded-md border p-4">
                    <Globe className="h-5 w-5" />
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">Public</p>
                      <p className="text-xs text-muted-foreground">Visible par tous les utilisateurs</p>
                    </div>
                    <Switch
                      checked={formData.isPublic}
                      onCheckedChange={(checked) => handleSwitchChange("isPublic", checked)}
                    />
                  </div>

                  <div className="flex items-center space-x-4 rounded-md border p-4">
                    <Users className="h-5 w-5" />
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">Abonnés uniquement</p>
                      <p className="text-xs text-muted-foreground">Visible uniquement par vos abonnés</p>
                    </div>
                    <Switch
                      checked={!formData.isPublic}
                      onCheckedChange={(checked) => handleSwitchChange("isPublic", !checked)}
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Options de promotion</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="notifyFollowers" className="flex flex-col gap-1">
                        <span>Notifier mes abonnés</span>
                        <span className="font-normal text-xs text-muted-foreground">
                          Envoyer une notification à vos abonnés
                        </span>
                      </Label>
                      <Switch
                        id="notifyFollowers"
                        checked={formData.notifyFollowers}
                        onCheckedChange={(checked) => handleSwitchChange("notifyFollowers", checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="addToDiscovery" className="flex flex-col gap-1">
                        <span>Soumettre à "Découvertes"</span>
                        <span className="font-normal text-xs text-muted-foreground">Pour considération éditoriale</span>
                      </Label>
                      <Switch
                        id="addToDiscovery"
                        checked={formData.addToDiscovery}
                        onCheckedChange={(checked) => handleSwitchChange("addToDiscovery", checked)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart4 className="mr-2 h-5 w-5" />
                  Prêt à publier ?
                </CardTitle>
                <CardDescription>Vérifiez que tout est prêt avant de publier</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div
                      className={`h-5 w-5 rounded-full flex items-center justify-center ${formData.title ? "bg-green-500" : "bg-red-500"}`}
                    >
                      {formData.title ? <Check className="h-3 w-3 text-white" /> : <X className="h-3 w-3 text-white" />}
                    </div>
                    <p className="text-sm">Titre de l'album</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <div
                      className={`h-5 w-5 rounded-full flex items-center justify-center ${formData.coverUrl ? "bg-green-500" : "bg-red-500"}`}
                    >
                      {formData.coverUrl ? (
                        <Check className="h-3 w-3 text-white" />
                      ) : (
                        <X className="h-3 w-3 text-white" />
                      )}
                    </div>
                    <p className="text-sm">Pochette d'album</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <div
                      className={`h-5 w-5 rounded-full flex items-center justify-center ${formData.songs.length > 0 ? "bg-green-500" : "bg-red-500"}`}
                    >
                      {formData.songs.length > 0 ? (
                        <Check className="h-3 w-3 text-white" />
                      ) : (
                        <X className="h-3 w-3 text-white" />
                      )}
                    </div>
                    <p className="text-sm">Au moins un morceau ajouté</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <div
                      className={`h-5 w-5 rounded-full flex items-center justify-center ${formData.genre ? "bg-green-500" : "bg-amber-500"}`}
                    >
                      {formData.genre ? (
                        <Check className="h-3 w-3 text-white" />
                      ) : (
                        <Minus className="h-3 w-3 text-white" />
                      )}
                    </div>
                    <p className="text-sm">Genre sélectionné (recommandé)</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <div
                      className={`h-5 w-5 rounded-full flex items-center justify-center ${formData.description ? "bg-green-500" : "bg-amber-500"}`}
                    >
                      {formData.description ? (
                        <Check className="h-3 w-3 text-white" />
                      ) : (
                        <Minus className="h-3 w-3 text-white" />
                      )}
                    </div>
                    <p className="text-sm">Description ajoutée (recommandé)</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <div
                      className={`h-5 w-5 rounded-full flex items-center justify-center ${formData.tags.length > 0 ? "bg-green-500" : "bg-amber-500"}`}
                    >
                      {formData.tags.length > 0 ? (
                        <Check className="h-3 w-3 text-white" />
                      ) : (
                        <Minus className="h-3 w-3 text-white" />
                      )}
                    </div>
                    <p className="text-sm">Tags ajoutés (recommandé)</p>
                  </div>
                </div>

                <Separator />

                <div className="pt-2 space-y-4">
                  <Button
                    className="w-full"
                    onClick={() => handleSave("published")}
                    disabled={!formData.title || !formData.coverUrl || formData.songs.length === 0 || isLoading}
                  >
                    <Globe className="mr-2 h-4 w-4" />
                    Publier l'album
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleSave("draft")}
                    disabled={!formData.title || isLoading}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Enregistrer comme brouillon
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
