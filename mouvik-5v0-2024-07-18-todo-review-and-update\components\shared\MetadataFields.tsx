"use client";

import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { MultiSelect, Option } from "@/components/ui/multi-select"; // Ensure Option is accessible from multi-select or define it here
import { Control } from 'react-hook-form';

// If Option is not exported from multi-select.tsx, define it here:
// export type Option = {
//   value: string;
//   label: string;
// };

export interface MetadataFieldConfig {
  name: string; // Corresponds to react-hook-form field name
  label: string;
  options: Option[];
  placeholder?: string;
}

interface MetadataFieldsProps {
  control: Control<any>; // Or a more specific form values type if available
  configs: MetadataFieldConfig[];
  gridCols?: string; // e.g., 'md:grid-cols-2 lg:grid-cols-3'
  gap?: string;      // e.g., 'gap-6'
}

export function MetadataFields({
  control,
  configs,
  gridCols = "grid-cols-1 md:grid-cols-2 lg:grid-cols-3", // Default grid
  gap = "gap-6" // Default gap
}: MetadataFieldsProps) {
  return (
    <div className={`grid ${gridCols} ${gap}`}>
      {configs.map((config) => (
        <FormField
          key={config.name}
          control={control}
          name={config.name}
          render={({ field }) => (
            <FormItem>
              <FormLabel>{config.label}</FormLabel>
              <FormControl>
                <MultiSelect
                  options={config.options}
                  selected={field.value || []} // Ensure field.value is an array
                  onChange={field.onChange}
                  placeholder={config.placeholder || `Ajouter des ${config.label.toLowerCase()}`}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      ))}
    </div>
  );
}
