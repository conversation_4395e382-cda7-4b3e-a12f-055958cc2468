# Admin Module Documentation

## Overview

The Admin Module provides administrators and moderators with tools to manage users, content, and platform settings. Access to this module is restricted based on the `user_role` in the `profiles` table (e.g., 'admin', 'moderator').

## Core Features

1.  **User Management**:
    *   List all users with search and filtering capabilities.
    *   View user details (profile information, subscription tier, user role, activity logs).
    *   Modify user roles (`user_role`: admin, moderator, user).
    *   Modify user subscription tiers (`subscription_tier`: free, pro, studio).
    *   Manually adjust user `ia_credits` or `coins_balance`.
    *   **Modify individual user quotas** (e.g., `uploads_per_month`, `vault_space_gb`, `vault_max_files`) to override their plan defaults.
    *   Suspend, ban, or delete user accounts.
    *   View user-generated content (songs, albums, bands).

2.  **Content Moderation**:
    *   **Songs, Albums, Bands, Profiles**:
        *   Review content flagged by users or by automated systems.
        *   Approve, reject, or edit content.
        *   Set a `moderation_status` (e.g., 'approved', 'rejected', 'under_review', 'hidden_by_admin') for content items.
        *   Add `moderation_notes` explaining actions taken.
    *   **Comments**:
        *   Review comments flagged by users.
        *   Edit, hide, or delete comments.
        *   Set a `status` for comments (e.g., 'visible', 'hidden_by_moderator').

3.  **Platform Statistics & Analytics**:
    *   Global platform analytics (total users, active users, content counts, growth trends).
    *   Content performance (most popular songs/albums/artists across the platform).
    *   Usage metrics for features like AI assistance, vault storage.
    *   Subscription revenue and tier distribution (if integrated with payment).

4.  **Platform Configuration**:
    *   Manage `plan_limits` table: UI to adjust quotas (including `vault_max_files`) and features for each subscription tier.
    *   Manage global tag lists, genre options, mood options, etc. (if these are centrally managed).
    *   Site-wide announcements or notifications.
    *   Basic site settings (e.g., default content visibility, feature flags).

## Database Considerations

*   **`profiles.user_role`**: Determines access to admin/moderator functionalities.
*   **`profiles.subscription_tier`**: Can be managed by admins.
*   **`profiles` table additions for custom quotas**:
    *   `custom_uploads_per_month SMALLINT NULL`: Overrides plan limit if set.
    *   `custom_vault_space_gb NUMERIC(5,2) NULL`: Overrides plan limit if set.
    *   `custom_vault_max_files SMALLINT NULL`: Overrides plan limit for max files in vault if set.
    *   `custom_ia_credits_month INTEGER NULL`: Overrides plan limit if set.
    *   `custom_coins_month INTEGER NULL`: Overrides plan limit if set.
    *   (Similar overrides for `max_playlists`, `max_friends` if needed).
*   **Content Tables (`songs`, `albums`, `bands`, `profiles`)**:
    *   Need `moderation_status TEXT` (e.g., 'approved', 'pending', 'rejected', 'hidden').
    *   Need `moderation_notes TEXT`.
*   **`comments` Table**:
    *   Needs `status TEXT` (e.g., 'visible', 'hidden_by_moderator', 'flagged').
*   **`audit_logs` Table**:
    *   Crucial for tracking admin/moderator actions (who did what, when, to what resource).
    *   Columns: `timestamp`, `admin_user_id`, `action_type` (e.g., 'user_ban', 'content_delete', 'comment_hide'), `target_resource_type`, `target_resource_id`, `details JSONB`.

## UI Structure (Conceptual)

*   A dedicated section of the application, e.g., `/admin`, accessible only to users with 'admin' or 'moderator' roles.
*   **Dashboard**: Overview of key metrics, pending moderation tasks.
*   **Users Tab**: Table of users with search, filters, and actions (edit, ban, etc.).
*   **Content Tabs (Songs, Albums, Bands, Profiles)**: Tables of content items with moderation actions. Filters for flagged/pending content.
*   **Comments Tab**: Table of comments with moderation actions. Filters for flagged comments.
*   **Platform Stats Tab**: Display global analytics.
*   **Settings Tab**:
    *   Plan Limits Management: UI to edit values in `plan_limits`.
    *   (Other configuration UIs).

## Implementation Notes

*   **Authorization**: All admin actions must be strictly protected by checking `user_role` on the server-side (RLS policies for direct DB access, checks in Edge Functions/API routes).
*   **Security**: Admin actions are powerful; logging and careful permission design are essential.
*   **Development Order**:
    1.  Basic user listing and role/tier modification.
    2.  Content (song/album) listing and basic moderation status changes.
    3.  Comment moderation.
    4.  Global stats display.
    5.  Platform configuration UIs (like `plan_limits` editor).

This module will be built incrementally, starting with the most critical management and moderation tools.
