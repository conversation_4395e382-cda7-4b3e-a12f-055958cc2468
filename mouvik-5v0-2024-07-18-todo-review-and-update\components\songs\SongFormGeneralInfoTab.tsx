import { FieldErrors } from 'react-hook-form';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { RHFTextField, RHFSelect, RHFMultiSelectChip, RHFTextarea, RHFDatePicker } from '@/components/hook-form';
import { SelectItem } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { SongFormValues } from './song-schema'; 
import { LANGUAGE_OPTIONS, VISIBILITY_OPTIONS, STATUS_OPTIONS } from './song-options';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import { AudioWaveformPreview } from './AudioWaveformPreview';
import { UploadCloud, ImagePlus, Trash2, FileAudio, Mic2, <PERSON>useCircle } from 'lucide-react';
import type { LocalFileState } from './hooks/useLocalFileManagement';
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Control } from 'react-hook-form';

interface SongFormGeneralInfoTabProps {
  control: Control<SongFormValues>; // Added control prop
  errors?: FieldErrors<SongFormValues>;
  albumsData?: Array<{ id: string; title: string }>;
  isLoadingAlbums?: boolean;

}

export const SongFormGeneralInfoTab: React.FC<SongFormGeneralInfoTabProps> = ({ 
  control, // Destructure control
  errors,
  albumsData, 
  isLoadingAlbums,
}) => {
  return (
    <Card>
      <CardHeader><CardTitle>Informations Générales</CardTitle></CardHeader>
      <CardContent className="space-y-6">
        
        {/* Section 1: Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <RHFTextField name="title" label="Titre du morceau" />
          <RHFTextField name="artist" label="Nom de l'artiste principal" />
          <RHFTextField name="artist_name" label="Nom d'artiste (pour affichage)" description="Si différent du nom principal."/>
          <RHFMultiSelectChip name="featured_artists" label="Featuring (Artistes invités)" options={[]} placeholder="Ajouter un artiste..." />
          <RHFSelect name="album_id" label="Album (Optionnel)">
            <SelectItem value="__NONE__">Aucun album</SelectItem>
            {albumsData?.map((album) => (
              <SelectItem key={album.id} value={album.id}>{album.title}</SelectItem>
            ))}
          </RHFSelect>
          {isLoadingAlbums && <p className="text-sm text-muted-foreground">Chargement des albums...</p>}
          <RHFTextField name="slug" label="Slug URL" placeholder="Ex: mon-super-morceau" description="Partie de l'URL pour la page publique."/>
          <RHFDatePicker name="release_date" label="Date de sortie" />
          <RHFTextField name="duration_ms" label="Durée (ms)" type="number" placeholder="Ex: 240000 pour 4 minutes"/>
          <RHFSelect name="lyrics_language" label="Langue des paroles">
            {LANGUAGE_OPTIONS.map(lang => <SelectItem key={lang.value} value={lang.value}>{lang.label}</SelectItem>)}
          </RHFSelect>
          <RHFSelect name="language" label="Langue principale du morceau (si différent)">
            {LANGUAGE_OPTIONS.map(lang => <SelectItem key={lang.value} value={lang.value}>{lang.label}</SelectItem>)}
          </RHFSelect>
        </div>
        <RHFTextarea name="description" label="Description / Notes publiques" rows={3} placeholder="Courte description pour la page publique..."/>

        {/* Section 3: Status & Visibility */}
        <h3 className="text-lg font-semibold pt-4 border-t mt-6 mb-2">Statut & Visibilité</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <RHFSelect name="status" label="Statut du morceau">
            {STATUS_OPTIONS.map((opt: { value: string; label: string }) => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
          </RHFSelect>
          <RHFSelect name="visibility" label="Visibilité">
            {VISIBILITY_OPTIONS.map((opt: { value: string; label: string }) => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
          </RHFSelect>
          <FormField
            control={control}
            name="is_public"
            render={({ field }) => (
              <FormItem className={cn("flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm col-span-1 md:col-span-2 transition-all duration-300 ease-in-out", field.value ? "shadow-[0_0_10px_1px_rgba(74,222,128,0.7)] border-green-500 bg-green-500/10" : "shadow-[0_0_10px_1px_rgba(248,113,113,0.6)] border-red-500 bg-red-500/10")}>
                <div className="space-y-0.5">
                  <FormLabel>Public (visible par tous)</FormLabel>
                  <FormDescription>
                    Rendre ce morceau visible publiquement sur le site.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="is_favorite"
            render={({ field }) => (
              <FormItem className={cn("flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm transition-all duration-300 ease-in-out", field.value ? "shadow-[0_0_10px_1px_rgba(74,222,128,0.7)] border-green-500 bg-green-500/10" : "shadow-[0_0_10px_1px_rgba(248,113,113,0.6)] border-red-500 bg-red-500/10")}>
                <div className="space-y-0.5">
                  <FormLabel>Marquer comme favori</FormLabel>
                  <FormDescription>
                    Mettre ce morceau en avant dans vos favoris.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="is_archived"
            render={({ field }) => (
              <FormItem className={cn("flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm transition-all duration-300 ease-in-out", field.value ? "shadow-[0_0_10px_1px_rgba(74,222,128,0.7)] border-green-500 bg-green-500/10" : "shadow-[0_0_10px_1px_rgba(248,113,113,0.6)] border-red-500 bg-red-500/10")}>
                <div className="space-y-0.5">
                  <FormLabel>Archiver ce morceau</FormLabel>
                  <FormDescription>
                    Masquer ce morceau des listes principales.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="is_incomplete"
            render={({ field }) => (
              <FormItem className={cn("flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm transition-all duration-300 ease-in-out", field.value ? "shadow-[0_0_10px_1px_rgba(74,222,128,0.7)] border-green-500 bg-green-500/10" : "shadow-[0_0_10px_1px_rgba(248,113,113,0.6)] border-red-500 bg-red-500/10")}>
                <div className="space-y-0.5">
                  <FormLabel>Marquer comme incomplet / Brouillon</FormLabel>
                  <FormDescription>
                    Indiquer que ce morceau est encore à l'état d'ébauche.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="is_cover"
            render={({ field }) => (
              <FormItem className={cn("flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm transition-all duration-300 ease-in-out", field.value ? "shadow-[0_0_10px_1px_rgba(74,222,128,0.7)] border-green-500 bg-green-500/10" : "shadow-[0_0_10px_1px_rgba(248,113,113,0.6)] border-red-500 bg-red-500/10")}>
                <div className="space-y-0.5">
                  <FormLabel>Ceci est une reprise (cover)</FormLabel>
                  <FormDescription>
                    Indiquer si ce morceau est une reprise d'un autre artiste.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="is_instrumental"
            render={({ field }) => (
              <FormItem className={cn("flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm transition-all duration-300 ease-in-out", field.value ? "shadow-[0_0_10px_1px_rgba(74,222,128,0.7)] border-green-500 bg-green-500/10" : "shadow-[0_0_10px_1px_rgba(248,113,113,0.6)] border-red-500 bg-red-500/10")}>
                <div className="space-y-0.5">
                  <FormLabel>Morceau instrumental</FormLabel>
                  <FormDescription>
                    Indiquer si ce morceau ne contient pas de paroles.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="is_explicit"
            render={({ field }) => (
              <FormItem className={cn("flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm transition-all duration-300 ease-in-out", field.value ? "shadow-[0_0_10px_1px_rgba(74,222,128,0.7)] border-green-500 bg-green-500/10" : "shadow-[0_0_10px_1px_rgba(248,113,113,0.6)] border-red-500 bg-red-500/10")}>
                <div className="space-y-0.5">
                  <FormLabel>Contenu explicite</FormLabel>
                  <FormDescription>
                    Indiquer si ce morceau contient du contenu explicite.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

      </CardContent>
    </Card>
  );
};
