// components/hook-form/rhf-select.tsx
import React from 'react';
import { Control, FieldValues, FieldPath, useFormContext } from 'react-hook-form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormField, FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { cn } from '@/lib/utils'; // Ajout de l'importation pour cn

interface RHFSelectProps<TFieldValues extends FieldValues = FieldValues> {
  control?: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
  label: string;
  options?: Array<{ value: string; label: string }>;
  placeholder?: string;
  description?: string;
  children?: React.ReactNode; // To allow passing SelectItem as children
  className?: string;
  selectTriggerClassName?: string;
}

export const RHFSelect = <TFieldValues extends FieldValues = FieldValues>({

  control: controlProp,
  name,
  label,
  options,
  placeholder,
  description,
  children,
  className,
  selectTriggerClassName,
}: RHFSelectProps<TFieldValues>) => {
  const context = useFormContext<TFieldValues>();
  const control = controlProp || context?.control;

  const { control: contextControl } = useFormContext<TFieldValues>() || {};
  const finalControl = controlProp || contextControl;

  if (!finalControl) {
    console.error('RHFSelect requires control prop or to be used within a FormProvider.');
    return <FormItem className={className}><FormLabel>{label}</FormLabel><FormMessage>Control not found or FormProvider is missing.</FormMessage></FormItem>;
  }

  return (
    <FormField
      control={finalControl}
      name={name}
      render={({ field, fieldState: { error } }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value as string | undefined} value={field.value as string | undefined}>
            <FormControl>
                <SelectTrigger
                  className={cn(
                    'w-full h-11 px-4 py-3 text-base',
                    'focus:border-primary focus:ring-1 focus:ring-primary-focus',
                    error && 'border-destructive focus:ring-destructive text-destructive placeholder:text-destructive/70'
                  )}
                >
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
              {children} {/* Render children if provided (e.g., for static SelectItems) */}
            </SelectContent>
          </Select>
          {description && <FormDescription>{description}</FormDescription>}
          {error && <p className="text-sm text-destructive mt-1.5">{error.message}</p>}
        </FormItem>
      )}
    />
  );
};