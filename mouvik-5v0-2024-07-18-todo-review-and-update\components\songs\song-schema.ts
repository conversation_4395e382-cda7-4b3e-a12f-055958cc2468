import { z } from 'zod';

// Define musical keys and time signatures for validation
const musicalKeys = [
  'C', 'C#', 'Db', 'D', 'D#', 'Eb', 'E', 'F', 'F#', 'Gb', 'G', 'G#', 'Ab', 'A', 'A#', 'Bb', 'B',
  'Cm', 'C#m', 'Dbm', 'Dm', 'D#m', 'Ebm', 'Em', 'Fm', 'F#m', 'Gbm', 'Gm', 'G#m', 'Abm', 'Am', 'A#m', 'Bbm', 'Bm',
  'Other'
] as const;

const timeSignatures = ['2/4', '3/4', '4/4', '5/4', '6/8', '7/8', '9/8', '12/8', 'Other'] as const;

// Define VisibilityType enum matching types/song.ts
export const visibilityTypeSchema = z.enum(['public', 'private', 'unlisted']);

export const songSchema = z.object({
  subgenre: z.array(z.string()).optional(), // Added subgenre
  is_archived: z.boolean().default(false).optional(), // Added is_archived
  featured_artists: z.array(z.string()).optional(), // Added featured_artists as an array of strings
  id: z.string().uuid().optional(),
  title: z.string().min(1, { message: "Le titre est requis." }),
  artist: z.string().min(1, { message: "Le nom de l'artiste est requis." }),
  artist_name: z.string().nullable().default(null).optional(),
  duration_ms: z.number().int().min(0).nullable().optional(), // Changed from duration to duration_ms
  bpm: z.number().nullable().optional(),
  musical_key: z.preprocess(
    (arg) => (typeof arg === 'string' && arg.trim() === "" ? null : arg),
    z.union([z.enum(musicalKeys), z.null()])
  ).optional().default(null),
  time_signature: z.preprocess(
    (arg) => (typeof arg === 'string' && arg.trim() === "" ? null : arg),
    z.union([z.enum(timeSignatures), z.null()])
  ).optional().default(null), 
  genre: z.string().nullable().default(null).optional(), // Changed from genres:string[] to genre:string
  moods: z.array(z.string().trim().min(1)).default([]),
  lyrics: z.string().nullable().default(null).optional(),
  composer_name: z.string().nullable().default(null).optional(),
  writers: z.array(z.string().trim().min(1)).default([]),
  producers: z.array(z.string().trim().min(1)).default([]),
  bloc_note: z.string().nullable().default(null).optional(),
  right_column_notepad: z.string().nullable().default(null).optional(),
  status: z.string().nullable().default(null).optional(), // Consider z.enum([...]) later
  progress_data: z.any().optional(), // Or z.string() if JSON stringified
  slug: z.string().trim().nullable().default(null).optional(),
  attribution_type: z.enum(['user', 'band']).nullable().optional(),
  tuning_frequency: z.number().positive().nullable().optional(),
  description: z.string().nullable().default(null).optional(), // From backup, distinct from 'notes'
  lyrics_language: z.string().trim().nullable().default(null).optional(),
  custom_css: z.string().nullable().default(null).optional(),
  chords: z.string().nullable().default(null).optional(),
  structure: z.string().nullable().default(null).optional(),
  notes: z.string().nullable().default(null).optional(),
  is_public: z.boolean().default(false),
  is_favorite: z.boolean().default(false),
  is_incomplete: z.boolean().default(true),
  is_cover: z.boolean().default(false),
  is_instrumental: z.boolean().default(false),
  is_explicit: z.boolean().default(false),
  release_date: z.date().nullable().optional(),
  audio_url: z.string().url({ message: "URL audio invalide." }).nullable().optional(),
  cover_art_url: z.string().url({ message: "URL de la pochette invalide." }).nullable().optional(),
  cover_art_file_name: z.string().nullable().optional(), // Added
  audio_file_name: z.string().nullable().optional(), // Added
  creator_user_id: z.string().uuid({ message: "ID créateur invalide." }),
  allow_comments: z.boolean().default(true).optional(),
  allow_downloads: z.boolean().default(false).optional(), 
  band_id: z.string().uuid().nullable().optional(),
  tags: z.array(z.string().trim().min(1)).default([]),
  themes: z.array(z.string().trim().min(1)).default([]),
  instruments: z.array(z.string().trim().min(1)).default([]),
  contributors: z.array(z.object({
    id: z.string().uuid(), 
    name: z.string().min(1, { message: "Nom du contributeur requis."}),
    role: z.string().min(1, { message: "Rôle du contributeur requis."}),
  })).default([]),
  plays: z.number().int().min(0).default(0),
  visibility: visibilityTypeSchema.default('private').optional(), // Added visibility
  created_at: z.string().datetime({ message: "Date de création invalide." }).optional(),
  updated_at: z.string().datetime({ message: "Date de mise à jour invalide." }).optional(),
  iswc_code: z.string().nullable().default(null).optional(), // Added iswc_code
  editor_data: z.any().optional(), // Added editor_data, matching Record<string, unknown>
  
  // Fields added to align with Supabase and SongFormValues
  isrc_code: z.string().nullable().default(null).optional(),
  upc_code: z.string().nullable().default(null).optional(),
  album_id: z.string().uuid({ message: "ID d'album invalide." }).nullable().optional(),
  copyright_notice: z.string().nullable().default(null).optional(),
  publisher_name: z.string().nullable().default(null).optional(),
  record_label: z.string().nullable().default(null).optional(),
  licensing_info: z.string().nullable().default(null).optional(),
  language: z.string().nullable().default(null).optional(), // General song language
  parental_advisory: z.string().nullable().default(null).optional(),
  recording_date: z.string().datetime({ message: "Date d'enregistrement invalide." }).nullable().optional(),
  recording_status: z.string().nullable().optional(), // TODO: Consider z.enum with PROGRESS_STATUS_OPTIONS
  mastering_status: z.string().nullable().optional(), // TODO: Consider z.enum with PROGRESS_STATUS_OPTIONS
  mixing_engineer: z.string().nullable().default(null).optional(),
  mastering_engineer: z.string().nullable().default(null).optional(),
  mixing_status: z.string().nullable().optional(), // TODO: Consider z.enum with PROGRESS_STATUS_OPTIONS
  ai_collaboration_level: z.number().int().min(0).max(100).nullable().optional(),
  artwork_credits: z.string().nullable().default(null).optional(),
  tablature: z.string().nullable().default(null).optional(),
  performance_notes: z.string().nullable().default(null).optional(),
  song_versions: z.array(z.any()).optional(), // Placeholder, define specific type later
  audio_file_versions: z.array(z.any()).optional(), // Placeholder, define specific type later
  external_links: z.array(z.object({ type: z.string(), url: z.string().url({ message: "URL externe invalide." }) })).optional(),
  completion_percentage: z.number().int().min(0).max(100).nullable().optional(),
  creation_process_type: z.string().nullable().optional(), // TODO: Consider z.enum with CREATION_PROCESS_OPTIONS
  custom_fields: z.record(z.any()).optional(), // For user-defined fields
  lyrics_sync_data: z.any().nullable().optional(), // For LRC or other sync formats
  chords_diagrams: z.record(z.unknown()).optional(), // JSONB for chord diagrams
  privacy_settings: z.object({
    allow_embedding: z.boolean(),
    allow_download: z.boolean()
  }).optional(),
  collaborators: z.array(z.any()).optional(), // Array of collaborators, specific structure TBD
  split_sheet: z.any().nullable().optional(),
  last_played_at: z.string().datetime({ message: "Date de dernière lecture invalide." }).nullable().optional(),
  play_count: z.number().int().min(0).optional().default(0),
  rating_average: z.number().min(0).max(5).optional().default(0), // Assuming a 0-5 rating scale
  rating_count: z.number().int().min(0).optional().default(0),
  comments_count: z.number().int().min(0).optional().default(0),
  shares_count: z.number().int().min(0).optional().default(0),
  downloads_count: z.number().int().min(0).optional().default(0),
  stream_sources: z.array(z.any()).optional(), // Placeholder for specific stream source object structure
  purchase_links: z.array(z.any()).optional(), // Placeholder for specific purchase link object structure
  related_songs: z.array(z.string().uuid({ message: "ID de morceau lié invalide." })).optional(), // Assuming array of song UUIDs
  song_story: z.string().nullable().default(null).optional(),
  production_notes: z.string().nullable().default(null).optional(),
  gear_used: z.array(z.string()).optional(), // Array of strings for gear used
  sample_credits: z.array(z.any()).optional(), // Placeholder for specific sample credit object structure
  remix_info: z.string().nullable().default(null).optional(),
  version_of_song_id: z.string().uuid({ message: "ID de la version originale du morceau invalide." }).nullable().optional(),
  ai_config: z.custom<AiConfig>().optional(), // Added AiConfig
  arrangement_status: z.string().nullable().optional(), // TODO: Consider z.enum with PROGRESS_STATUS_OPTIONS
  ai_history: z.array(z.custom<AiHistoryItem>()).optional() // Added AiHistory
  // TODO: Review if any other fields from backup's songFormSchema need to be added or if any existing fields conflict
});

// Type TypeScript déduit du schéma Zod
export type SongFormValues = z.infer<typeof songSchema>;

// AI Related Types
export interface AiConfig {
  provider: string; // e.g., 'openai', 'anthropic'
  model: string;    // e.g., 'gpt-3.5-turbo', 'claude-2'
  temperature: number; // 0.0 - 1.0
  max_tokens?: number; // Optional: Add other relevant API params like max_tokens, etc.
}

export interface AiHistoryItem {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string; // Optional timestamp
}