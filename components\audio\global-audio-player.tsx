"use client"

import { useState, useRef, useEffect } from "react"
import { 
  <PERSON>, Pause, Ski<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PlusCircle, ListMusic, Minimize2, Maximize2, VolumeX, Volume2, Eye, Heart, ChevronDown, CornerRightDown, Expand as ExpandIcon, Layers as CycleModeIcon, ThumbsDown
} from "lucide-react" // Added ThumbsDown
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
// import { useAudio } from "@/contexts/audio-context" // Supprimé
import { useAudioPlayerStore } from "@/lib/stores/audioPlayerStore" // Ajouté
import { cn } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { LikeButton } from "@/components/social/like-button"
import { DislikeButton } from "@/components/social/dislike-button"; // Added DislikeButton import
import { AddToPlaylistButton } from "@/components/playlists/add-to-playlist-button"; 
import { createBrowserClient } from "@/lib/supabase/client"; 
import type { FunctionsError } from "@supabase/supabase-js"; 
import { QueueModal } from "./queue-modal"; // Added QueueModal import
import { WaveformPlayer } from "./waveform-player"; // Added WaveformPlayer import

const TEMP_RECORDING_PREVIEW_ID = 'temp-recording-preview'; // ID used by SongForm for previews

type DisplayMode = 'full' | 'compact' | 'micro'; 

export function GlobalAudioPlayer() {
  const { 
    currentSong, 
    isPlaying, 
    // pauseSong, // Sera remplacé par togglePlayPause du store
    // resumeSong, // Sera remplacé par togglePlayPause du store
    // nextSong, // Logique de file d'attente à revoir si elle existe dans le store
    // previousSong, // Logique de file d'attente à revoir
    togglePlayPause, // Du store
    seek,            // Du store
    currentTime: globalCurrentTime, // Du store
    duration: globalDuration,       // Du store
    coverUrl: globalCoverUrl,       // Du store
    audioElement,    // Du store
    // playSong, // Pour démarrer une nouvelle chanson, si nécessaire depuis le player global (ex: file d'attente)
  } = useAudioPlayerStore();

  // const [currentTime, setCurrentTime] = useState(0) // Remplacé par globalCurrentTime
  // const [duration, setDuration] = useState(0)   // Remplacé par globalDuration
  const [volume, setVolume] = useState(0.8)
  const [isMuted, setIsMuted] = useState(false)
  const [displayMode, setDisplayMode] = useState<DisplayMode>('compact');
  const [userId, setUserId] = useState<string | undefined>(undefined);
  // const audioRef = useRef<HTMLAudioElement>(null); // Remplacé par audioElement du store
  // const animationRef = useRef<number>(0); // La mise à jour du temps est gérée dans le store

  // States for like/dislike of current song in player
  const [currentPlayerSongIsLiked, setCurrentPlayerSongIsLiked] = useState(false);
  const [currentPlayerSongLikeCount, setCurrentPlayerSongLikeCount] = useState(0);
  const [currentPlayerSongIsDisliked, setCurrentPlayerSongIsDisliked] = useState(false);
  const [currentPlayerSongDislikeCount, setCurrentPlayerSongDislikeCount] = useState(0);
  const [isLoadingLikeDislikeStatus, setIsLoadingLikeDislikeStatus] = useState(false);
  const [isQueueModalOpen, setIsQueueModalOpen] = useState(false); // Added state for queue modal

  useEffect(() => {
    let isMounted = true;
    import("@/lib/supabase/client").then(({ createBrowserClient }) => {
      const supabase = createBrowserClient();
      supabase.auth.getUser().then(({ data }) => {
        if (isMounted && data.user) setUserId(data.user.id);
      });
    });
    return () => { isMounted = false };
  }, []);

  useEffect(() => {
    if (displayMode === 'micro') {
      document.body.classList.add('micro-player-active');
    } else {
      document.body.classList.remove('micro-player-active');
    }
    return () => {
      document.body.classList.remove('micro-player-active');
    };
  }, [displayMode]);

  // Gestion du volume (peut rester locale au player global mais agir sur l'audioElement du store)
  useEffect(() => { 
    if (audioElement) audioElement.volume = isMuted ? 0 : volume;
  }, [volume, isMuted, audioElement]);

  const [lastPlayedSongIdForCounter, setLastPlayedSongIdForCounter] = useState<string | null>(null);

  // L'initialisation et la gestion des événements de l'audioElement (play, pause, timeupdate, ended)
  // sont maintenant centralisées dans audioPlayerStore.ts.
  // Les useEffect qui géraient audioRef.current.play/pause/load/addEventListener peuvent être supprimés ou simplifiés.

  // Exemple de suppression d'un useEffect lié à l'ancien audioRef
  /*
  useEffect(() => {
    const audio = audioElement; // audio devient audioElement du store
    if (audio) {
      // ... logique de play/pause ...
    }
  }, [isPlaying, currentSong, togglePlayPause]); 
  */

  useEffect(() => {
    if (currentSong) {
      setCurrentPlayerSongLikeCount(currentSong.like_count || 0);
      setCurrentPlayerSongDislikeCount(currentSong.dislike_count || 0);
      // Note: currentPlayerSongIsLiked and currentPlayerSongIsDisliked are handled by another useEffect based on userId
    }
  }, [currentSong]);

  // Fetch like/dislike status when currentSong or userId changes
  useEffect(() => {
    if (currentSong && currentSong.id !== TEMP_RECORDING_PREVIEW_ID && userId) {
      const supabase = createBrowserClient();
      setIsLoadingLikeDislikeStatus(true);
      Promise.all([
        supabase.from('likes').select('id').eq('resource_id', currentSong.id).eq('resource_type', 'song').eq('user_id', userId).maybeSingle(),
        supabase.from('dislikes').select('id').eq('resource_id', currentSong.id).eq('resource_type', 'song').eq('user_id', userId).maybeSingle(),
      ]).then(([userLikeData, userDislikeData]) => {
        setCurrentPlayerSongIsLiked(!!userLikeData.data);
        setCurrentPlayerSongIsDisliked(!!userDislikeData.data);
      }).catch(error => {
        console.error("Error fetching like/dislike status for player song:", error);
      }).finally(() => {
        setIsLoadingLikeDislikeStatus(false);
      });
    } else {
      // Reset if no song or no user
      setCurrentPlayerSongIsLiked(false);
      setCurrentPlayerSongLikeCount(0);
      setCurrentPlayerSongIsDisliked(false);
      setCurrentPlayerSongDislikeCount(0);
    }
  
  }, [currentSong, userId]);

  // Plus besoin de handleLoadedMetadata ici, c'est dans le store
  // Plus besoin de updateProgress, c'est dans le store

  // const mainTogglePlayPause = () => { if (isPlaying) pauseSong(); else resumeSong(); } // Remplacé par togglePlayPause du store. Renommé pour éviter conflit de nom.
  // const updateProgress = () => { if (audioElement) { /* setCurrentTime est géré par le store */ animationRef.current = requestAnimationFrame(updateProgress); } } // La mise à jour du temps est gérée dans le store via ontimeupdate
  const handleTimeChange = (value: number[]) => { 
    seek(value[0]); // Utilise la fonction seek du store
  }
  const handleVolumeChange = (value: number[]) => { 
    const newVolume = value[0]; 
    setVolume(newVolume); 
    setIsMuted(newVolume === 0); 
    // L'effet sur audioElement.volume est géré par le useEffect dédié
  }
  const toggleMute = () => { 
    const newMutedState = !isMuted; 
    setIsMuted(newMutedState); 
    if (!newMutedState && volume === 0) setVolume(0.8); // Si on unmute et que le volume était à 0, le remonter
    // L'effet sur audioElement.volume est géré par le useEffect dédié
  }
  const formatTime = (time: number) => { if (isNaN(time) || time === Infinity) return "0:00"; const m = Math.floor(time / 60); const s = Math.floor(time % 60); return `${m}:${s.toString().padStart(2, "0")}`; }
  // const handleEnded = () => nextSong(); // La logique de 'onended' est dans le store

  const cycleDisplayMode = () => {
    if (displayMode === 'full') setDisplayMode('compact');
    else if (displayMode === 'compact') setDisplayMode('micro');
    else if (displayMode === 'micro') setDisplayMode('full');
  };

  const handlePlayerLikeToggle = (newIsLiked: boolean, newLikeCount: number, newDislikeCount?: number) => {
    setCurrentPlayerSongIsLiked(newIsLiked);
    setCurrentPlayerSongLikeCount(newLikeCount);
    if (newDislikeCount !== undefined) {
      setCurrentPlayerSongIsDisliked(false);
      setCurrentPlayerSongDislikeCount(newDislikeCount);
    }
  };

  const handlePlayerDislikeToggle = (newIsDisliked: boolean, newDislikeCount: number, newLikeCount?: number) => {
    setCurrentPlayerSongIsDisliked(newIsDisliked);
    setCurrentPlayerSongDislikeCount(newDislikeCount);
    if (newLikeCount !== undefined) {
      setCurrentPlayerSongIsLiked(false);
      setCurrentPlayerSongLikeCount(newLikeCount);
    }
  };

  const getMainModeButton = () => {
    switch (displayMode) {
      case 'full':
        return { icon: <Minimize2 className="h-4 w-4" />, label: "Passer en mode compact" };
      case 'compact':
        return { icon: <CornerRightDown className="h-4 w-4" />, label: "Passer en mode micro" }; // Icon for Compact -> Micro
      case 'micro':
        return { icon: <ExpandIcon className="h-4 w-4" />, label: "Agrandir le lecteur (mode complet)" };
      default: // Should not happen
        return { icon: <CycleModeIcon className="h-4 w-4" />, label: "Changer de mode" };
    }
  };
  const mainModeButton = getMainModeButton();

  

  if (!currentSong) return null;
  
  const commonPlayerBarClasses = "fixed bottom-0 left-0 right-0 px-4 py-2 flex flex-col gap-0 transition-all duration-300 border-t border-white/10 z-[1000] shadow-2xl bg-zinc-900/80 backdrop-blur-lg";
  const sidebarMarginClass = typeof window !== 'undefined' && document.body.classList.contains('sidebar-collapsed') ? 'ml-[var(--sidebar-width-icon)]' : 'ml-[var(--sidebar-width)]';

  return (
    <div
      className={cn(
        displayMode === 'micro' 
          ? "fixed bottom-4 right-4 p-2 rounded-lg shadow-2xl bg-zinc-800/90 backdrop-blur-md border border-white/20 w-auto min-w-[280px]" // Added min-w for micro
          : `${commonPlayerBarClasses} ${sidebarMarginClass}`,
        
        displayMode === 'full' ? "h-26" :
        displayMode === 'compact' ? "h-18" :
      // displayMode === 'minimal' ? "h-14" : // Minimal mode height removed
        displayMode === 'micro' ? "h-auto py-1.5 px-2" : "" // Adjusted padding for micro
      )}
    >
      {/* <audio ref={audioRef} src={currentSong.audio_url || ""} onEnded={handleEnded} onTimeUpdate={() => audioRef.current && setCurrentTime(audioRef.current.currentTime)} onLoadedMetadata={() => audioRef.current && setDuration(audioRef.current.duration)} /> */}
      {/* L'élément audio est maintenant géré globalement par audioPlayerStore */}

      {displayMode === 'micro' && (
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2 overflow-hidden">
            <Link href={`/songs/${currentSong.slug || currentSong.id}`} className="flex-shrink-0">
              <div className="relative h-10 w-10 rounded-sm overflow-hidden">
                <Image src={globalCoverUrl || "/images/covers/default-cover.webp"} alt={currentSong.title || "No title"} fill className="object-cover" />
              </div>
            </Link>
            <div className="flex-grow overflow-hidden max-w-[120px] h-[24px]">
              <WaveformPlayer 
                key={`waveform-${currentSong.id}-micro`}
                song={currentSong} 
                height={24} 
                waveColor="#38bdf8" 
                progressColor="#0ea5e9" 
              />
            </div>
          </div>
          <div className="flex items-center gap-0.5 flex-shrink-0">
            <Button size="icon" variant="ghost" /* onClick={previousSong} // TODO: Implement queue logic */ className="text-white hover:bg-white/20 w-7 h-7 disabled:opacity-50" disabled><SkipBack className="h-3 w-3" /></Button>
            <Button size="icon" variant="ghost" onClick={togglePlayPause} className="text-white hover:bg-white/20 w-8 h-8">{isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}</Button>
            <Button size="icon" variant="ghost" /* onClick={nextSong} // TODO: Implement queue logic */ className="text-white hover:bg-white/20 w-7 h-7 disabled:opacity-50" disabled><SkipForward className="h-3 w-3" /></Button>
            <Button size="icon" variant="ghost" onClick={cycleDisplayMode} aria-label={mainModeButton.label} className="text-white hover:bg-white/20 w-7 h-7">{mainModeButton.icon}</Button>
          </div>
        </div>
      )}

      {(displayMode === 'compact' || displayMode === 'full') && (
        <>
          {/* Main 3-column grid */}
          <div className="grid grid-cols-[auto_1fr_auto] items-center w-full gap-x-2 sm:gap-x-3 px-2 pt-1 pb-1 flex-grow min-h-0">
            {/* Column 1: Cover Art & Stats */}
            <div className="flex flex-col items-center flex-shrink-0">
              <Link href={`/songs/${currentSong.slug || currentSong.id}`} className="block">
                <div className={cn(
                  "relative overflow-hidden rounded shadow-md",
                  displayMode === 'full' ? "h-12 w-12 sm:h-14 sm:w-14" : "h-10 w-10 sm:h-12 sm:w-12"
                )}>
                  <Image src={globalCoverUrl || "/images/covers/default-cover.webp"} alt={currentSong.title || "No title"} fill className="object-cover" />
                </div>
              </Link>
              {displayMode === 'full' && (
                <div className="flex items-center gap-1 md:gap-2 mt-1"> {/* Adjusted class from 'order-3 md:order-none' to 'mt-1' to match typical structure if this was a copy-paste error, or verify original intent. Assuming 'mt-1' is more standard for spacing. */}
                  {userId && currentSong && currentSong.id !== TEMP_RECORDING_PREVIEW_ID && (
                    <>
                      <LikeButton 
                        resourceId={currentSong.id} 
                        resourceType="song"
                        initialLikes={currentPlayerSongLikeCount}
                        initialIsLiked={currentPlayerSongIsLiked}
                        userId={userId} 
                        onLikeToggle={handlePlayerLikeToggle}
                        size="icon"
                        variant="ghost"
                        className="text-white/70 hover:text-white p-1 h-auto w-auto"
                        disabled={isLoadingLikeDislikeStatus}
                      />
                      <DislikeButton
                        resourceId={currentSong.id}
                        resourceType="song"
                        initialDislikes={currentPlayerSongDislikeCount}
                        initialIsDisliked={currentPlayerSongIsDisliked}
                        userId={userId}
                        onDislikeToggle={handlePlayerDislikeToggle}
                        size="icon"
                        variant="ghost"
                        className="text-white/70 hover:text-white p-1 h-auto w-auto"
                        disabled={isLoadingLikeDislikeStatus}
                      />
                      <AddToPlaylistButton songId={currentSong.id} buttonClassName="text-white/70 hover:text-white p-1 h-auto w-auto" buttonVariant="ghost" buttonSize="icon" />
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Column 2: Song Info, Playback Controls & Timeline */}
            <div className="flex flex-col justify-center min-w-0 h-full flex-grow">
              <div className="min-w-0">
                <Link href={`/songs/${currentSong.slug || currentSong.id}`} className="text-sm sm:text-base font-semibold hover:underline truncate block leading-tight text-white">{currentSong.title}</Link>
                <Link href={`/artists/${currentSong.profiles?.id || currentSong.creator_user_id}`} className="text-[10px] sm:text-xs text-muted-foreground hover:underline truncate block text-zinc-400">{(currentSong.profiles?.display_name || currentSong.artist_name || "Unknown Artist")}</Link>
              </div>

              <div className="flex items-center gap-x-1 sm:gap-x-1.5 w-full mt-0.5">
                <Button size="icon" variant="ghost" aria-label="Précédent" className="text-primary hover:bg-primary/10 disabled:opacity-50 w-6 h-6 sm:w-7 sm:h-7 flex-shrink-0" disabled><SkipBack className="h-3 w-3 sm:h-3.5 sm:w-3.5" /></Button>
                <Button size="icon" variant="default" onClick={togglePlayPause} aria-label={isPlaying ? "Pause" : "Lecture"} className="bg-primary text-white shadow-md hover:bg-primary/90 w-7 h-7 sm:w-8 sm:h-8 flex-shrink-0">{isPlaying ? <Pause className="h-3.5 w-3.5 sm:h-4 sm:w-4" /> : <Play className="h-3.5 w-3.5 sm:h-4 sm:w-4" />}</Button>
                <Button size="icon" variant="ghost" aria-label="Suivant" className="text-primary hover:bg-primary/10 disabled:opacity-50 w-6 h-6 sm:w-7 sm:h-7 flex-shrink-0" disabled><SkipForward className="h-3 w-3 sm:h-3.5 sm:w-3.5" /></Button>
                <WaveformPlayer 
                  key={`waveform-${currentSong.id}-${displayMode}`}
                  song={currentSong} 
                  height={displayMode === 'full' ? 48 : 32} 
                  waveColor="#38bdf8" 
                  progressColor="#0ea5e9" 
                />
              </div>

              {displayMode === 'full' && (
                <div className="flex items-center gap-1 md:gap-2 mt-1">
                  {userId && currentSong && currentSong.id !== TEMP_RECORDING_PREVIEW_ID && (
                    <>
                      <LikeButton 
                        resourceId={currentSong.id} 
                        resourceType="song"
                        initialLikes={currentPlayerSongLikeCount}
                        initialIsLiked={currentPlayerSongIsLiked}
                        userId={userId} 
                        onLikeToggle={handlePlayerLikeToggle}
                        size="icon"
                        variant="ghost"
                        className="text-white/70 hover:text-white p-1 h-auto w-auto"
                        disabled={isLoadingLikeDislikeStatus}
                      />
                      <DislikeButton
                        resourceId={currentSong.id}
                        resourceType="song"
                        initialDislikes={currentPlayerSongDislikeCount}
                        initialIsDisliked={currentPlayerSongIsDisliked}
                        userId={userId}
                        onDislikeToggle={handlePlayerDislikeToggle}
                        size="icon"
                        variant="ghost"
                        className="text-white/70 hover:text-white p-1 h-auto w-auto"
                        disabled={isLoadingLikeDislikeStatus}
                      />
                      <AddToPlaylistButton songId={currentSong.id} buttonVariant="ghost" buttonClassName="text-white/70 hover:text-white p-1 h-auto w-auto" />
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Column 3: Action Buttons & Volume */}
            <div className={cn(
              "flex items-center justify-end gap-x-0.5 sm:gap-x-1",
              displayMode === 'full' ? "flex-col justify-start h-full gap-y-0" : "flex-row"
            )}>
              <Button size="icon" variant="ghost" aria-label="Voir la queue" className="text-primary hover:bg-primary/10 w-6 h-6 sm:w-7 sm:h-7" onClick={() => setIsQueueModalOpen(true)}><ListMusic className="h-3 w-3 sm:h-3.5 sm:w-3.5" /></Button>
              <Button size="icon" variant="ghost" onClick={cycleDisplayMode} aria-label={mainModeButton.label} className="text-primary hover:bg-primary/10 w-6 h-6 sm:w-7 sm:h-7">{mainModeButton.icon}</Button>
              
              <div className={cn(
                "flex items-center",
                displayMode === 'full' ? "flex-col mt-auto gap-y-0.5" : "flex-row gap-x-0.5 sm:gap-x-1"
              )}>
                <Button size="icon" variant="ghost" onClick={toggleMute} aria-label={isMuted ? "Activer le son" : "Couper le son"} className="text-primary hover:bg-primary/10 w-6 h-6 sm:w-7 sm:h-7">{isMuted ? <VolumeX className="h-3 w-3 sm:h-3.5 sm:w-3.5" /> : <Volume2 className="h-3 w-3 sm:h-3.5 sm:w-3.5" />}</Button>
                {displayMode === 'full' && <Slider value={[Math.round(volume * 100)]} min={0} max={100} step={1} onValueChange={([v]) => handleVolumeChange([v/100])} orientation="vertical" className="h-8 w-1.5 player-slider-v cursor-pointer my-0.5" />}
                {displayMode === 'compact' && <Slider value={[Math.round(volume * 100)]} min={0} max={100} step={1} onValueChange={([v]) => handleVolumeChange([v/100])} className="w-8 sm:w-10 player-slider cursor-pointer" />}
              </div>
            </div>
          </div> {/* End of main 3-column grid */}

          {/* Old ProgressBar removed, timeline is now integrated in Col2 */}
        </>
      )}
      {isQueueModalOpen && <QueueModal isOpen={isQueueModalOpen} onClose={() => setIsQueueModalOpen(false)} />}
    </div>
  );
}
