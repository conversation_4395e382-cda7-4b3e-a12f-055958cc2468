import { createStore } from 'zustand/vanilla';
import { useStore } from 'zustand/react';

export type ResourceType = 'song' | 'album' | 'playlist';

// Describes the interaction state for any given resource
export interface ResourceInteractionStatus {
  isLiked: boolean;
  likeCount: number;
  isDisliked: boolean; // Primarily for songs; defaults to false for others
  dislikeCount: number; // Primarily for songs; defaults to 0 for others
  isFollowed: boolean; // For albums/playlists; defaults to false for songs
  followerCount: number; // For albums/playlists; defaults to 0 for songs
}

// --- API Result Types ---
export type ToggleLikeResult = {
  newLikeCount: number;
  newIsLiked: boolean;
  // Optional: For songs, if liking also affects dislike status
  newDislikeCount?: number; 
  newIsDisliked?: boolean;
};

export type ToggleDislikeResult = { // Primarily for songs
  newDislikeCount: number;
  newIsDisliked: boolean;
  // Optional: For songs, if disliking also affects like status
  newLikeCount?: number;
  newIsLiked?: boolean;
};

export type ToggleFollowResult = { // For albums/playlists
  newFollowerCount: number;
  newIsFollowed: boolean;
};

// --- Store State and Actions ---
export interface ResourceInteractionStoreState {
  resourceStates: { [resourceKey: string]: ResourceInteractionStatus };
  
  setResourceStatus: (
    resourceType: ResourceType, 
    resourceId: string, 
    initialStatus: Partial<ResourceInteractionStatus> // Allow partial updates for initialization
  ) => void;
  
  toggleLike: (
    resourceType: ResourceType, 
    resourceId: string, 
    apiCall: () => Promise<ToggleLikeResult>
  ) => Promise<ToggleLikeResult>;
  
  toggleDislike: ( // Primarily for songs
    resourceType: ResourceType, 
    resourceId: string, 
    apiCall: () => Promise<ToggleDislikeResult>
  ) => Promise<ToggleDislikeResult>;

  toggleFollow: ( // For albums/playlists
    resourceType: ResourceType,
    resourceId: string,
    apiCall: () => Promise<ToggleFollowResult>
  ) => Promise<ToggleFollowResult>;
}

export const DEFAULT_RESOURCE_STATE: ResourceInteractionStatus = {
  isLiked: false,
  likeCount: 0,
  isDisliked: false,
  dislikeCount: 0,
  isFollowed: false,
  followerCount: 0,
};

export const getResourceKey = (resourceType: ResourceType, resourceId: string): string => {
  return `${resourceType}_${resourceId}`;
};

const storeApi = createStore<ResourceInteractionStoreState>((set, get) => ({
  resourceStates: {},

  setResourceStatus: (resourceType, resourceId, initialStatus) => {
    const key = getResourceKey(resourceType, resourceId);
    set((state) => ({
      resourceStates: {
        ...state.resourceStates,
        [key]: { 
          ...(state.resourceStates[key] || DEFAULT_RESOURCE_STATE), 
          ...initialStatus 
        },
      },
    }));
  },

  toggleLike: async (resourceType, resourceId, apiCall) => {
    const key = getResourceKey(resourceType, resourceId);
    const currentResourceState = get().resourceStates[key] || DEFAULT_RESOURCE_STATE;
    const oldResourceState = { ...currentResourceState };

    // Optimistic update
    const optimisticState: ResourceInteractionStatus = {
      ...oldResourceState,
      isLiked: !oldResourceState.isLiked,
      likeCount: !oldResourceState.isLiked ? oldResourceState.likeCount + 1 : Math.max(0, oldResourceState.likeCount - 1),
    };

    if (resourceType === 'song' && !oldResourceState.isLiked && oldResourceState.isDisliked) {
      // If liking a song that was disliked, remove dislike
      optimisticState.isDisliked = false;
      optimisticState.dislikeCount = Math.max(0, oldResourceState.dislikeCount - 1);
    }

    set((state) => ({
      resourceStates: { ...state.resourceStates, [key]: optimisticState },
    }));

    try {
      const result = await apiCall();
      // Sync with server response
      set((state) => ({
        resourceStates: {
          ...state.resourceStates,
          [key]: {
            ...optimisticState, // Start from optimistic to keep follow status etc.
            isLiked: result.newIsLiked,
            likeCount: result.newLikeCount,
            // Update dislike status only if provided by API (for songs)
            ...(result.newIsDisliked !== undefined && { isDisliked: result.newIsDisliked }),
            ...(result.newDislikeCount !== undefined && { dislikeCount: result.newDislikeCount }),
          },
        },
      }));
      return result;
    } catch (error) {
      console.error(`Failed to toggle like for ${resourceType} ${resourceId}:`, error);
      set((state) => ({ // Revert on error
        resourceStates: { ...state.resourceStates, [key]: oldResourceState },
      }));
      throw error;
    }
  },

  toggleDislike: async (resourceType, resourceId, apiCall) => {
    if (resourceType !== 'song') {
      console.warn(`toggleDislike called for non-song resource: ${resourceType}`);
      // Or throw an error, or return a mock error result
      throw new Error('Dislike is only applicable to songs.');
    }
    const key = getResourceKey(resourceType, resourceId);
    const currentResourceState = get().resourceStates[key] || DEFAULT_RESOURCE_STATE;
    const oldResourceState = { ...currentResourceState };

    // Optimistic update
    const optimisticState: ResourceInteractionStatus = {
      ...oldResourceState,
      isDisliked: !oldResourceState.isDisliked,
      dislikeCount: !oldResourceState.isDisliked ? oldResourceState.dislikeCount + 1 : Math.max(0, oldResourceState.dislikeCount - 1),
    };

    if (!oldResourceState.isDisliked && oldResourceState.isLiked) {
      // If disliking a song that was liked, remove like
      optimisticState.isLiked = false;
      optimisticState.likeCount = Math.max(0, oldResourceState.likeCount - 1);
    }

    set((state) => ({
      resourceStates: { ...state.resourceStates, [key]: optimisticState },
    }));

    try {
      const result = await apiCall();
      set((state) => ({
        resourceStates: {
          ...state.resourceStates,
          [key]: {
            ...optimisticState,
            isDisliked: result.newIsDisliked,
            dislikeCount: result.newDislikeCount,
            ...(result.newIsLiked !== undefined && { isLiked: result.newIsLiked }),
            ...(result.newLikeCount !== undefined && { likeCount: result.newLikeCount }),
          },
        },
      }));
      return result;
    } catch (error) {
      console.error(`Failed to toggle dislike for ${resourceType} ${resourceId}:`, error);
      set((state) => ({ // Revert on error
        resourceStates: { ...state.resourceStates, [key]: oldResourceState },
      }));
      throw error;
    }
  },

  toggleFollow: async (resourceType, resourceId, apiCall) => {
    if (resourceType === 'song') {
      console.warn(`toggleFollow called for song resource, which is not standard.`);
      throw new Error('Follow is not typically applicable to songs.');
    }
    const key = getResourceKey(resourceType, resourceId);
    const currentResourceState = get().resourceStates[key] || DEFAULT_RESOURCE_STATE;
    const oldResourceState = { ...currentResourceState };

    // Optimistic update
    const optimisticState: ResourceInteractionStatus = {
      ...oldResourceState,
      isFollowed: !oldResourceState.isFollowed,
      followerCount: !oldResourceState.isFollowed ? oldResourceState.followerCount + 1 : Math.max(0, oldResourceState.followerCount - 1),
    };

    set((state) => ({
      resourceStates: { ...state.resourceStates, [key]: optimisticState },
    }));

    try {
      const result = await apiCall();
      set((state) => ({
        resourceStates: {
          ...state.resourceStates,
          [key]: {
            ...optimisticState,
            isFollowed: result.newIsFollowed,
            followerCount: result.newFollowerCount,
          },
        },
      }));
      return result;
    } catch (error) {
      console.error(`Failed to toggle follow for ${resourceType} ${resourceId}:`, error);
      set((state) => ({ // Revert on error
        resourceStates: { ...state.resourceStates, [key]: oldResourceState },
      }));
      throw error;
    }
  },
}));

// The storeApi is now internal. Use useResourceInteractionStore for reactive access
// or getResourceInteractionStoreState / setResourceInteractionStoreState for direct access.

// Custom hook for using the store reactively
export const useResourceInteractionStore = <StateSlice,>(
  selector: (state: ResourceInteractionStoreState) => StateSlice,
  equalityFn?: (a: StateSlice, b: StateSlice) => boolean // equalityFn can be undefined
): StateSlice => {
  // Always pass selector and equalityFn.
  // If equalityFn is undefined, useStore should use its default behavior (reference equality).
  // This aims to fix lint error e4c10694-cc46-4fd1-867d-136dc3fbc921
  return useStore(storeApi, selector, equalityFn);
};

// Direct access to getState, setState, and subscribe if needed outside React components
export const getResourceInteractionStoreState = storeApi.getState;
export const setResourceInteractionStoreState = storeApi.setState;
export const subscribeToResourceInteractionStore = storeApi.subscribe;
