"use client";

import Link from 'next/link';
import Image from 'next/image';
import type { Playlist } from '@/types';
import { ListMusic, PlayCircle } from 'lucide-react';

interface PlaylistCardCompactProps {
  playlist: Playlist;
}

export function PlaylistCardCompact({ playlist }: PlaylistCardCompactProps) {
  const viewPageUrl = playlist.is_public && playlist.slug 
    ? `/playlist/${playlist.slug}` 
    : `/playlists/${playlist.id}`;

  return (
    <Link href={viewPageUrl} className="block group rounded-md overflow-hidden shadow hover:shadow-lg transition-shadow duration-200 bg-card">
      <div className="relative aspect-square">
        {playlist.cover_url ? (
          <Image
            src={playlist.cover_url}
            alt={playlist.name}
            width={150} 
            height={150}
            className="object-cover w-full h-full"
          />
        ) : (
          <div className="w-full h-full bg-muted flex items-center justify-center">
            <ListMusic className="w-10 h-10 text-muted-foreground" />
          </div>
        )}
        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
          <PlayCircle className="w-10 h-10 text-white" />
        </div>
      </div>
      <div className="p-2">
        <p className="text-xs font-medium truncate group-hover:underline" title={playlist.name}>
          {playlist.name}
        </p>
        <p className="text-xs text-muted-foreground">
          {playlist.songs_count ?? 0} morceau{playlist.songs_count === 1 || playlist.songs_count === 0 ? '' : 's'}
        </p>
      </div>
    </Link>
  );
}
