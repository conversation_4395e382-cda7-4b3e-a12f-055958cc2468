"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AiQuickActions } from "@/components/ia/ai-quick-actions";
import { AiConfigMenu } from "@/components/ia/ai-config-menu";
import { Music2, Cog } from 'lucide-react';
import { AiConfig, AiHistoryItem } from './SongForm'; // Assuming types are co-located or will be moved

interface AiAssistantPanelProps {
  // Visibility and Config Menu
  isAiPanelCollapsed: boolean; // This prop might be handled by the parent to show/hide this whole panel
  setIsAiPanelCollapsed: (collapsed: boolean) => void;
  showAiConfigMenu: boolean;
  setShowAiConfigMenu: (show: boolean) => void;

  // AI State
  aiLoading: boolean;
  aiLastResult?: string;
  aiError?: string;
  aiHistory: AiHistoryItem[]; // For AiQuickActions if it needs it directly
  
  // AI Configuration
  aiConfig: AiConfig;
  setAiConfig: (newPartialConfig: Partial<AiConfig>) => void; // Renamed from handleAiConfigChange for clarity
  generalPrompt: string;
  onEditGeneralPrompt: (newPrompt: string) => void;

  // AI Action Handlers
  // onGenerate, onCorrect, onTranslate, onFormatLayout, onRhymeSuggestions, onAnalyzeTone were moved/removed.
  // Only handlers remaining in SongForm are kept:
  onGeneralSuggestions: () => Promise<void>;
  onMelodySuggestion: () => Promise<void>;
  onRecordingAdvice: () => Promise<void>;
  onInstrumentationSuggestion: () => Promise<void>;
  onCreativeFx: () => Promise<void>;
  onArrangementAdvice: () => Promise<void>;
  // isTextSelected is removed as selection is handled in LyricsEditorWithAI now
}

export function AiAssistantPanel({
  setIsAiPanelCollapsed,
  showAiConfigMenu,
  setShowAiConfigMenu,
  aiLoading,
  aiLastResult,
  aiError,
  aiHistory,
  aiConfig,
  setAiConfig,
  generalPrompt,
  onEditGeneralPrompt,
  // onGenerate, // Moved
  // onCorrect, // Moved
  // onTranslate, // Removed from props
  // onFormatLayout, // Removed from props
  onGeneralSuggestions,
  // onRhymeSuggestions, // Removed from props
  // onAnalyzeTone, // Removed from props
  onMelodySuggestion,
  onRecordingAdvice,
  onInstrumentationSuggestion,
  onCreativeFx,
  onArrangementAdvice,
  // isTextSelected removed from destructuring
  // isTextSelected removed from destructuring
}: AiAssistantPanelProps) {
  return (
    <div className="lg:col-span-1 space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="space-y-1">
            <CardTitle className="text-lg">
              <Music2 className="mr-2 h-5 w-5 inline-block" />
              Assistance IA
            </CardTitle>
            <CardDescription className="text-xs">
              Suggestions, corrections, formatage.
            </CardDescription>
          </div>
          <div className="flex items-center gap-1">
            <Button type="button" variant="ghost" size="icon" className="h-7 w-7" onClick={() => setShowAiConfigMenu(true)} title="Configurer l'IA">
              <Cog className="h-4 w-4" />
            </Button>
            <Button type="button" variant="ghost" size="icon" className="h-7 w-7" onClick={() => setIsAiPanelCollapsed(true)} title="Réduire le panneau IA">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <line x1="9" y1="3" x2="9" y2="21"/>
              </svg>
            </Button>
          </div>
        </CardHeader>
        <div className="p-0"> {/* AiQuickActions was in a CardContent, now directly in a div with p-0 */}
          <AiQuickActions 
            // onGenerate, onCorrect, onTranslate are no longer passed as they are not props of AiQuickActions
            loading={aiLoading} 
            lastResult={aiLastResult}
            error={aiError}
            iaHistory={aiHistory} // Changed from iaHistory
            aiConfig={aiConfig}
            setAiConfig={setAiConfig} 
            // Pass only the relevant handlers down
            generalPrompt={generalPrompt} 
            onEditGeneralPrompt={onEditGeneralPrompt}
            onGeneralSuggestions={onGeneralSuggestions} 
            onMelodySuggestion={onMelodySuggestion}
            onRecordingAdvice={onRecordingAdvice}
            onInstrumentationSuggestion={onInstrumentationSuggestion}
            onCreativeFx={onCreativeFx}
            onArrangementAdvice={onArrangementAdvice}
            // Removed handlers that are no longer props of AiAssistantPanel:
            // onGenerate, onCorrect, onTranslate, onFormatLayout, onRhymeSuggestions, onAnalyzeTone
            // Removed prop: isTextSelected
          />
          <AiConfigMenu
            isPopoverOpen={showAiConfigMenu}
            setIsPopoverOpen={setShowAiConfigMenu}
          />
        </div>
      </Card>
    </div>
  );
}
