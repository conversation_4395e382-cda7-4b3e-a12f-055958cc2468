'use client';

import React, { useState, useRef, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import {
  Type, Music, Eye, EyeOff, Wand2, Plus, Copy, Trash2,
  FileText, Guitar, Layers, BarChart3, Target, Clock,
  Mic, Volume2, Hash, SkipForward, RotateCcw, Play, Square
} from 'lucide-react';

interface LyricsEditorMegaProps {
  content: string;
  onContentChange: (content: string) => void;
  selectedSection: string;
  sections: any[];
  setSections: (sections: any[]) => void;
  setSelectedSection: (sectionId: string) => void;
  onAIGenerate: (prompt: string, type: string) => Promise<void>;
  styleConfig: any;
  generalPrompt: string;
  projectMetrics: any;
}

type ViewMode = 'text' | 'chords' | 'analysis';

export const LyricsEditorMega: React.FC<LyricsEditorMegaProps> = ({
  content,
  onContentChange,
  selectedSection,
  sections,
  setSections,
  setSelectedSection,
  onAIGenerate,
  styleConfig,
  generalPrompt,
  projectMetrics
}) => {
  
  const [viewMode, setViewMode] = useState<ViewMode>('text');
  const [showSections, setShowSections] = useState(true);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const currentSection = sections.find(s => s.id === selectedSection);

  // Actions IA rapides pour les paroles
  const quickAIActions = [
    {
      id: 'continue',
      label: 'Continuer',
      icon: SkipForward,
      prompt: 'Continue ces paroles en gardant le même style et la même énergie',
      color: 'bg-blue-500'
    },
    {
      id: 'improve',
      label: 'Améliorer',
      icon: Wand2,
      prompt: 'Améliore ces paroles : meilleur flow, rimes plus riches, images plus fortes',
      color: 'bg-green-500'
    },
    {
      id: 'rhyme',
      label: 'Rimes',
      icon: Hash,
      prompt: 'Suggère des mots qui riment et enrichissent le sens de ces paroles',
      color: 'bg-purple-500'
    },
    {
      id: 'rewrite',
      label: 'Réécrire',
      icon: RotateCcw,
      prompt: 'Réécris ces paroles avec un angle différent mais le même message',
      color: 'bg-orange-500'
    }
  ];

  // Types de sections disponibles
  const sectionTypes = [
    { id: 'verse', label: 'Couplet', icon: FileText, color: 'bg-blue-500' },
    { id: 'chorus', label: 'Refrain', icon: Music, color: 'bg-green-500' },
    { id: 'bridge', label: 'Pont', icon: Layers, color: 'bg-purple-500' },
    { id: 'intro', label: 'Intro', icon: Play, color: 'bg-orange-500' },
    { id: 'outro', label: 'Outro', icon: Square, color: 'bg-red-500' },
    { id: 'pre-chorus', label: 'Pré-refrain', icon: Target, color: 'bg-yellow-500' }
  ];

  // Gestionnaire pour ajouter une section
  const handleAddSection = useCallback((type: string = 'verse') => {
    const newSection = {
      id: `section-${Date.now()}`,
      type,
      title: `${sectionTypes.find(t => t.id === type)?.label || 'Section'} ${sections.length + 1}`,
      content: '',
      chords: [],
      duration: type === 'chorus' ? 32 : 16,
      startTime: sections.reduce((total, s) => total + (s.duration || 16), 0)
    };
    
    setSections([...sections, newSection]);
    setSelectedSection(newSection.id);
  }, [sections, setSections, setSelectedSection]);

  // Gestionnaire pour dupliquer une section
  const handleDuplicateSection = useCallback(() => {
    if (!currentSection) return;
    
    const newSection = {
      ...currentSection,
      id: `section-${Date.now()}`,
      title: `${currentSection.title} (Copie)`,
      startTime: sections.reduce((total, s) => total + (s.duration || 16), 0)
    };
    
    setSections([...sections, newSection]);
    setSelectedSection(newSection.id);
  }, [currentSection, sections, setSections, setSelectedSection]);

  // Gestionnaire pour supprimer une section
  const handleDeleteSection = useCallback(() => {
    if (!currentSection || sections.length <= 1) return;
    
    const filteredSections = sections.filter(s => s.id !== currentSection.id);
    setSections(filteredSections);
    
    if (filteredSections.length > 0) {
      setSelectedSection(filteredSections[0].id);
    }
  }, [currentSection, sections, setSections, setSelectedSection]);

  // Gestionnaire pour changer le type de section
  const handleSectionTypeChange = useCallback((newType: string) => {
    if (!currentSection) return;
    
    const updatedSections = sections.map(section =>
      section.id === currentSection.id
        ? { ...section, type: newType, title: `${sectionTypes.find(t => t.id === newType)?.label || 'Section'} ${sections.indexOf(section) + 1}` }
        : section
    );
    
    setSections(updatedSections);
  }, [currentSection, sections, setSections]);

  // Gestionnaire pour les actions IA
  const handleQuickAI = useCallback(async (action: any) => {
    if (!content.trim()) {
      // Si pas de contenu, générer pour la section
      const sectionPrompt = `Écris des paroles pour un ${currentSection?.type || 'couplet'} qui s'intègre parfaitement dans cette chanson`;
      await onAIGenerate(sectionPrompt, 'lyrics');
    } else {
      await onAIGenerate(action.prompt, 'lyrics');
    }
  }, [content, currentSection, onAIGenerate]);

  // Modes de visualisation
  const viewModes = [
    { id: 'text' as ViewMode, label: 'Texte', icon: Type, description: 'Éditeur de texte pur' },
    { id: 'chords' as ViewMode, label: 'Accords', icon: Guitar, description: 'Avec accords intégrés' },
    { id: 'analysis' as ViewMode, label: 'Analyse', icon: BarChart3, description: 'Métriques et suggestions' }
  ];

  return (
    <div className="h-full flex flex-col bg-slate-900/30">
      {/* Barre d'outils */}
      <div className="border-b border-slate-700 bg-slate-800/50 p-3">
        <div className="flex items-center justify-between">
          {/* Section actuelle et type */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="capitalize">
                {currentSection?.type || 'Section'}
              </Badge>
              <span className="text-white font-medium">{currentSection?.title || 'Nouvelle section'}</span>
            </div>
            
            {/* Changer type de section */}
            <div className="flex items-center gap-1">
              {sectionTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <Button
                    key={type.id}
                    variant={currentSection?.type === type.id ? "default" : "ghost"}
                    size="sm"
                    onClick={() => handleSectionTypeChange(type.id)}
                    className={`gap-1 ${currentSection?.type === type.id ? type.color : ''}`}
                    title={type.label}
                  >
                    <Icon className="h-3 w-3" />
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Modes de vue */}
          <div className="flex items-center gap-1 bg-slate-700/50 rounded-lg p-1">
            {viewModes.map((mode) => {
              const Icon = mode.icon;
              return (
                <Button
                  key={mode.id}
                  variant={viewMode === mode.id ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode(mode.id)}
                  className="gap-1"
                  title={mode.description}
                >
                  <Icon className="h-4 w-4" />
                  {mode.label}
                </Button>
              );
            })}
          </div>

          {/* Actions sections */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => handleAddSection()} className="gap-1">
              <Plus className="h-4 w-4" />
              Ajouter
            </Button>
            {currentSection && (
              <>
                <Button variant="outline" size="sm" onClick={handleDuplicateSection} className="gap-1">
                  <Copy className="h-4 w-4" />
                  Dupliquer
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleDeleteSection}
                  className="gap-1 text-red-400 hover:text-red-300"
                  disabled={sections.length <= 1}
                >
                  <Trash2 className="h-4 w-4" />
                  Supprimer
                </Button>
              </>
            )}
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowSections(!showSections)}
              className="gap-1"
            >
              {showSections ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              Sections
            </Button>
          </div>
        </div>
      </div>

      {/* Navigation des sections */}
      {showSections && (
        <div className="border-b border-slate-700 bg-slate-800/30 p-3">
          <ScrollArea className="w-full">
            <div className="flex items-center gap-2">
              {sections.map((section, index) => {
                const sectionType = sectionTypes.find(t => t.id === section.type);
                const Icon = sectionType?.icon || FileText;
                
                return (
                  <Button
                    key={section.id}
                    variant={selectedSection === section.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedSection(section.id)}
                    className={`gap-2 min-w-fit ${
                      selectedSection === section.id 
                        ? sectionType?.color || 'bg-blue-600'
                        : 'bg-slate-700 hover:bg-slate-600'
                    }`}
                  >
                    <Icon className="h-3 w-3" />
                    <span className="text-xs opacity-70">#{index + 1}</span>
                    <span>{section.title}</span>
                  </Button>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* Contenu principal */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'text' && (
          <div className="h-full flex flex-col">
            {/* Zone d'édition */}
            <div className="flex-1 p-4">
              <Textarea
                ref={textareaRef}
                value={content}
                onChange={(e) => onContentChange(e.target.value)}
                placeholder={`Écrivez les paroles pour ${currentSection?.title || 'cette section'}...\n\nLa vision générale guide l'IA :\n${generalPrompt.substring(0, 100)}...`}
                className="w-full h-full resize-none bg-slate-800/50 border-slate-600 text-white placeholder-slate-400 font-mono text-base leading-relaxed"
                style={{ minHeight: '400px' }}
              />
            </div>

            {/* Actions IA rapides */}
            <div className="border-t border-slate-700 bg-slate-800/30 p-3">
              <div className="flex items-center gap-2">
                <span className="text-sm text-slate-400 mr-2">Actions IA:</span>
                {quickAIActions.map((action) => {
                  const Icon = action.icon;
                  return (
                    <Button
                      key={action.id}
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickAI(action)}
                      className={`gap-1 ${action.color} text-white border-slate-600 hover:border-slate-500`}
                    >
                      <Icon className="h-4 w-4" />
                      {action.label}
                    </Button>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {viewMode === 'chords' && (
          <div className="h-full p-6 flex items-center justify-center">
            <div className="text-center">
              <Guitar className="h-16 w-16 text-slate-400 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-white mb-2">Mode Accords</h3>
              <p className="text-slate-400">Intégration Enhanced Chord System en cours</p>
            </div>
          </div>
        )}

        {viewMode === 'analysis' && (
          <div className="h-full p-4">
            <ScrollArea className="h-full">
              <div className="space-y-4">
                {/* Métriques de la section */}
                <Card className="bg-slate-700/50 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-sm text-white">Métriques Section Actuelle</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-2 bg-slate-600/50 rounded">
                        <div className="text-lg font-bold text-white">{content.trim().split(/\s+/).filter(Boolean).length}</div>
                        <div className="text-xs text-slate-400">Mots</div>
                      </div>
                      <div className="text-center p-2 bg-slate-600/50 rounded">
                        <div className="text-lg font-bold text-white">{content.split('\n').length}</div>
                        <div className="text-xs text-slate-400">Lignes</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Progression globale */}
                <Card className="bg-slate-700/50 border-slate-600">
                  <CardHeader>
                    <CardTitle className="text-sm text-white">Progression Globale</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-slate-400">Complétude</span>
                        <span className="text-white">{projectMetrics.completion}%</span>
                      </div>
                      <Progress value={projectMetrics.completion} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-slate-400">Structure</span>
                        <span className="text-white">{projectMetrics.structure}%</span>
                      </div>
                      <Progress value={projectMetrics.structure} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-slate-400">Harmonie</span>
                        <span className="text-white">{projectMetrics.harmony}%</span>
                      </div>
                      <Progress value={projectMetrics.harmony} className="h-2" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </div>
        )}
      </div>

      {/* Barre d'état */}
      <div className="border-t border-slate-700 bg-slate-800/50 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-slate-400">
          <div className="flex items-center gap-4">
            <span>Mode: {viewModes.find(m => m.id === viewMode)?.label}</span>
            <span>Section: {currentSection?.type || 'N/A'}</span>
            <span>Durée: {currentSection?.duration || 0}s</span>
          </div>
          <div className="flex items-center gap-2">
            <span>{content.trim().split(/\s+/).filter(Boolean).length} mots</span>
            <span>•</span>
            <span>{content.split('\n').length} lignes</span>
          </div>
        </div>
      </div>
    </div>
  );
};
