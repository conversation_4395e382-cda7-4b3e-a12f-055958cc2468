// c:\_DEV_projects\TOOL\mouvik-5v0\components\songs\AudioWaveformPreview.tsx
import React, { useEffect, useRef, useState } from 'react';
import WaveSurfer from 'wavesurfer.js';
import { Play, Pause } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AudioWaveformPreviewProps {
  audioUrl?: string | null;
  audioFileName?: string | null;
  waveColor?: string;
  progressColor?: string;
  cursorColor?: string;
  height?: number;
}

export const AudioWaveformPreview: React.FC<AudioWaveformPreviewProps> = ({
  audioUrl,
  audioFileName,
  waveColor = '#A9A9A9', // DarkGray
  progressColor = '#87CEEB', // SkyBlue
  cursorColor = '#333333', // Darker Gray
  height = 80,
}) => {
  const waveformRef = useRef<HTMLDivElement>(null);
  const wavesurfer = useRef<WaveSurfer | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!audioUrl || audioUrl.trim() === '') {
      wavesurfer.current?.destroy();
      wavesurfer.current = null;
      setIsLoading(false);
      setError(null);
      setIsPlaying(false);
      return;
    }

    if (!waveformRef.current) return;

    setIsLoading(true);
    setError(null);

    // Destroy any previous instance *before* creating a new one if audioUrl changes.
    if (wavesurfer.current) {
      wavesurfer.current.destroy();
      wavesurfer.current = null; // Ensure ref is cleared before reassigning
    }

    const newWsInstance = WaveSurfer.create({
      container: waveformRef.current,
      waveColor,
      progressColor,
      cursorColor,
      height,
      barWidth: 2,
      barGap: 1,
      interact: true,
    });

    wavesurfer.current = newWsInstance;

    newWsInstance.on('ready', () => {
      if (wavesurfer.current === newWsInstance) {
        setIsLoading(false);
        setError(null); // Clear any previous error on successful load
        console.log('[AudioWaveformPreview] Wavesurfer ready. URL:', audioUrl);
      }
    });

    // Load logic: always fetch URL then load as blob for http/https, or directly for blob URLs if already fetched
    const loadAudio = async () => {
      try {
        let audioBlob: Blob;
        if (audioUrl.startsWith('blob:')) {
          console.log('[AudioWaveformPreview] audioUrl is a blob. Fetching and using loadBlob(). URL:', audioUrl);
          const response = await fetch(audioUrl);
          if (!response.ok) {
            throw new Error(`Failed to fetch blob: ${response.status} ${response.statusText} (URL: ${audioUrl})`);
          }
          audioBlob = await response.blob();
        } else if (audioUrl.startsWith('http:') || audioUrl.startsWith('https:')) {
          console.log('[AudioWaveformPreview] audioUrl is HTTP/HTTPS. Fetching and converting to blob. URL:', audioUrl);
          const response = await fetch(audioUrl); // Standard fetch for remote URLs
          if (!response.ok) {
            // Attempt to get more detailed error from response body if available
            let errorBody = '';
            try {
              errorBody = await response.text();
            } catch (e) { /* ignore if can't read body */ }
            throw new Error(`Failed to fetch audio: ${response.status} ${response.statusText}. URL: ${audioUrl}. Body: ${errorBody}`);
          }
          audioBlob = await response.blob();
        } else {
          // Potentially a local file path or data URI - WaveSurfer might handle these if supported
          // For now, we assume http, https, or blob. If other types are expected, this needs extension.
          console.warn('[AudioWaveformPreview] Unhandled URL type, attempting direct load:', audioUrl);
          newWsInstance.load(audioUrl);
          return; // Exit early as load is asynchronous and handled by WaveSurfer events
        }

        if (wavesurfer.current === newWsInstance) { // Check if instance is still current
          console.log('[AudioWaveformPreview] Blob data ready. Type:', audioBlob.type, 'Size:', audioBlob.size, '. Calling loadBlob().');
          newWsInstance.loadBlob(audioBlob);
        } else {
          console.log('[AudioWaveformPreview] Wavesurfer instance changed before blob could be loaded. Aborting loadBlob for URL:', audioUrl);
        }
      } catch (fetchError: any) {
        if (wavesurfer.current === newWsInstance) { // Check if instance is still current
          console.error('[AudioWaveformPreview] Error fetching or processing audio for URL:', audioUrl, 'Error:', fetchError);
          setError(`Erreur au chargement du fichier: ${fetchError.message}`);
          setIsLoading(false);
        }
      }
    };

    loadAudio();

    newWsInstance.on('play', () => {
      if (wavesurfer.current === newWsInstance) setIsPlaying(true);
    });

    newWsInstance.on('pause', () => {
      if (wavesurfer.current === newWsInstance) setIsPlaying(false);
    });

    newWsInstance.on('finish', () => {
      if (wavesurfer.current === newWsInstance) setIsPlaying(false);
    });

    newWsInstance.on('error', (err) => {
      if (wavesurfer.current === newWsInstance) {
        const errorMessage = typeof err === 'string' ? err : (err as Error).message;
        console.error('[AudioWaveformPreview] Wavesurfer .on("error") event. URL:', audioUrl, 'Error:', errorMessage);
        setError(`Erreur Wavesurfer: ${errorMessage}`);
        setIsLoading(false);
      }
    });

    return () => {
      newWsInstance.destroy();
      if (wavesurfer.current === newWsInstance) {
        wavesurfer.current = null;
      }
    };
  }, [audioUrl, waveColor, progressColor, cursorColor, height]);

  const handlePlayPause = () => {
    if (wavesurfer.current) {
      wavesurfer.current.playPause();
    }
  };

  if (!audioUrl || audioUrl.trim() === '') {
    return <p className="text-sm text-muted-foreground">Aucun aperçu audio disponible.</p>;
  }

  return (
    <div className="w-full space-y-2">
      {audioFileName && <p className="text-sm font-medium truncate">{audioFileName}</p>}
      <div ref={waveformRef} style={{ width: '100%', minHeight: `${height}px` }} className="bg-muted/30 rounded-md" />
      {isLoading && <p className="text-xs text-muted-foreground">Chargement de la forme d'onde...</p>}
      {error && <p className="text-xs text-destructive">{error}</p>}
      {!isLoading && !error && (
        <Button onClick={handlePlayPause} variant="outline" size="sm" className="mt-2">
          {isPlaying ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
          {isPlaying ? 'Pause' : 'Lecture'}
        </Button>
      )}
    </div>
  );
};
