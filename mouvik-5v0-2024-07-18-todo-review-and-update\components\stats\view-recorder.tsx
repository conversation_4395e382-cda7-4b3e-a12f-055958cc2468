"use client";

import { useEffect } from 'react';
import { getSupabaseClient } from '@/lib/supabase/client';

export interface ViewRecorderProps { // Exporting the interface
  resourceId: string;
  resourceType: 'song' | 'album' | 'profile' | 'band' | 'playlist'; // Added 'playlist'
}

export function ViewRecorder({ resourceId, resourceType }: ViewRecorderProps) {
  useEffect(() => {
    const recordView = async () => {
      const supabase = getSupabaseClient();
      try {
        // No need to await here, fire and forget
        supabase.functions.invoke('record-view', {
          body: { resource_id: resourceId, resource_type: resourceType },
        }).then(({ data, error }) => {
          if (error) {
            console.warn(`Failed to record view for ${resourceType} ${resourceId}:`, error.message);
          } else {
            // console.log(`View recording status for ${resourceType} ${resourceId}:`, data.message);
          }
        });
      } catch (e) {
        console.warn(`Error invoking record-view function:`, e);
      }
    };

    if (resourceId && resourceType) {
      recordView();
    }
  }, [resourceId, resourceType]);

  return null; // This component does not render anything
}
