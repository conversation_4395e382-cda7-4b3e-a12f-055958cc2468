// c:\_DEV_projects\TOOL\mouvik-5v0\components\ui\file-input.tsx
import React, { ChangeEvent, useRef } from 'react';
import { Button } from '@/components/ui/button'; // Assuming Button is in this path
import { Input } from '@/components/ui/input'; // Assuming Input is in this path
import { X } from 'lucide-react';

interface FileInputProps {
  id: string;
  label: string;
  onFileSelect: (file: File | null) => void;
  currentFile?: File | null;
  onClear?: () => void;
  accept?: string;
  disabled?: boolean;
  buttonText?: string;
  clearButtonText?: string;
  className?: string;
}

export const FileInput: React.FC<FileInputProps> = ({
  id,
  label,
  onFileSelect,
  currentFile,
  onClear,
  accept,
  disabled,
  buttonText = "Choisir un fichier",
  clearButtonText = "Retirer",
  className,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    onFileSelect(file || null);
  };

  const handleButtonClick = () => {
    inputRef.current?.click();
  };

  const handleClearClick = () => {
    if (inputRef.current) {
      inputRef.current.value = ''; // Reset the input field
    }
    onFileSelect(null); // Notify parent that file is cleared
    if (onClear) {
      onClear();
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label htmlFor={id} className="block text-sm font-medium text-gray-700">
        {label}
      </label>
      <div className="flex items-center space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={handleButtonClick}
          disabled={disabled}
        >
          {buttonText}
        </Button>
        {currentFile && (
          <div className="flex items-center space-x-2 p-2 border rounded-md bg-gray-50 flex-grow">
            <span className="text-sm text-gray-600 truncate flex-grow" title={currentFile.name}>
              {currentFile.name}
            </span>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClearClick}
              disabled={disabled}
              aria-label={clearButtonText}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
         {!currentFile && (
           <span className="text-sm text-muted-foreground italic">Aucun fichier sélectionné</span>
         )}
      </div>
      <Input
        type="file"
        id={id}
        ref={inputRef}
        onChange={handleFileChange}
        accept={accept}
        disabled={disabled}
        className="hidden" // Hidden, as we use a custom button
      />
    </div>
  );
};
