"use client"; // Or remove if it can be a server component

import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Frown } from 'lucide-react';

export default function PlaylistNotFound() {
  return (
    <div className="container mx-auto flex flex-col items-center justify-center py-12 text-center">
      <Frown className="h-16 w-16 text-muted-foreground mb-4" />
      <h1 className="text-3xl font-bold mb-2">Playlist Non Trouvée</h1>
      <p className="text-muted-foreground mb-6">
        Désolé, la playlist que vous cherchez n'existe pas ou n'est plus accessible.
      </p>
      <Button asChild>
        <Link href="/playlists">Retour à Mes Playlists</Link>
      </Button>
    </div>
  );
}
