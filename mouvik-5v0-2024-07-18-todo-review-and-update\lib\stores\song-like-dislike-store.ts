import { createStore } from 'zustand/vanilla';
import type { StateCreator } from 'zustand';

export type LikeDislikeApiResult = {
  newLikeCount: number;
  newIsLiked: boolean;
  newIsDisliked: boolean;
  newDislikeCount: number;
};

export interface SongSpecificState {
  isLiked: boolean;
  isDisliked: boolean;
  likeCount: number;
  dislikeCount: number;
}

export interface SongLikeDislikeState {
  songStates: { [songId: string]: SongSpecificState };
  // Action to initialize or update the store with a specific song's full like/dislike status
  setSongStatus: (songId: string, initialStatus: SongSpecificState) => void;
  // Actions to reflect user interaction on a specific song in the store
  toggleLike: (songId: string, apiCall: () => Promise<LikeDislikeApiResult>) => Promise<LikeDislikeApiResult>;
  toggleDislike: (songId: string, apiCall: () => Promise<LikeDislikeApiResult>) => Promise<LikeDislikeApiResult>;
}

export const songLikeDislikeStoreApi = createStore<SongLikeDislikeState>((set, get) => ({
    songStates: {},

    setSongStatus: (songId: string, initialStatus: SongSpecificState) => set((state) => ({
      songStates: {
        ...state.songStates,
        [songId]: initialStatus,
      },
    })),

    toggleLike: async (songId: string, apiCall: () => Promise<LikeDislikeApiResult>): Promise<LikeDislikeApiResult> => {
      const currentSongState = get().songStates[songId] || { isLiked: false, isDisliked: false, likeCount: 0, dislikeCount: 0 };
      const oldSongState = { ...currentSongState };

      // Optimistic update for the specific song
      set((state) => ({
        songStates: {
          ...state.songStates,
          [songId]: {
            ...oldSongState,
            isLiked: !oldSongState.isLiked,
            likeCount: !oldSongState.isLiked ? oldSongState.likeCount + 1 : Math.max(0, oldSongState.likeCount - 1),
            isDisliked: !oldSongState.isLiked && oldSongState.isDisliked ? false : oldSongState.isDisliked, // if liking a disliked song, remove dislike
            dislikeCount: !oldSongState.isLiked && oldSongState.isDisliked ? Math.max(0, oldSongState.dislikeCount - 1) : oldSongState.dislikeCount,
          },
        },
      }));

      try {
        const result = await apiCall();
        // Sync with server response for the specific song
        set((state) => ({
          songStates: {
            ...state.songStates,
            [songId]: {
              isLiked: result.newIsLiked,
              isDisliked: result.newIsDisliked,
              likeCount: result.newLikeCount,
              dislikeCount: result.newDislikeCount,
            },
          },
        }));
        return result;
      } catch (error) {
        console.error(`Failed to toggle like for song ${songId}:`, error);
        // Revert to old state for the specific song on error
        set((state) => ({
          songStates: {
            ...state.songStates,
            [songId]: oldSongState,
          },
        }));
        throw error;
      }
    },

    toggleDislike: async (songId: string, apiCall: () => Promise<LikeDislikeApiResult>): Promise<LikeDislikeApiResult> => {
      const currentSongState = get().songStates[songId] || { isLiked: false, isDisliked: false, likeCount: 0, dislikeCount: 0 };
      const oldSongState = { ...currentSongState };

      // Optimistic update for the specific song
      set((state) => ({
        songStates: {
          ...state.songStates,
          [songId]: {
            ...oldSongState,
            isDisliked: !oldSongState.isDisliked,
            dislikeCount: !oldSongState.isDisliked ? oldSongState.dislikeCount + 1 : Math.max(0, oldSongState.dislikeCount - 1),
            isLiked: !oldSongState.isDisliked && oldSongState.isLiked ? false : oldSongState.isLiked, // if disliking a liked song, remove like
            likeCount: !oldSongState.isDisliked && oldSongState.isLiked ? Math.max(0, oldSongState.likeCount - 1) : oldSongState.likeCount,
          },
        },
      }));

      try {
        const result = await apiCall();
        // Update store with actual data from API for the specific song
        set((state) => ({
          songStates: {
            ...state.songStates,
            [songId]: {
              isLiked: result.newIsLiked,
              isDisliked: result.newIsDisliked,
              likeCount: result.newLikeCount,
              dislikeCount: result.newDislikeCount,
            },
          },
        }));
        return result;
      } catch (error) {
        console.error(`Failed to toggle dislike for song ${songId}:`, error);
        // Revert to old state for the specific song on error
        set((state) => ({
          songStates: {
            ...state.songStates,
            [songId]: oldSongState,
          },
        }));
        throw error;
      }
    },
  }));


