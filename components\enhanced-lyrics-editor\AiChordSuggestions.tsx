/**
 * 🎼 AI CHORD SUGGESTIONS - Suggestions IA pour Accords
 * 
 * Intégration avec AiQuickActions pour suggestions harmoniques
 * Analyse contextuelle du texte et suggestions intelligentes
 * 
 * @version 1.0.0
 * @date 2025-06-11
 */

'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { 
  <PERSON>rkles, Brain, Wand2, RefreshCw, CheckCircle2, 
  XCircle, Volume2, Plus, ArrowRight, Lightbulb,
  Music, Target, TrendingUp, Shuffle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ChordDiagramViewer } from '@/components/chord-system/components/ChordDiagramViewer';
import { useChordSystem } from '@/components/chord-system/providers/ChordSystemProvider';
import type { UnifiedChordPosition } from '@/components/chord-system/types/chord-system';
import type { ChordPlacement } from './EnhancedLyricsEditor';

// ============================================================================
// TYPES POUR LES SUGGESTIONS IA
// ============================================================================

export interface ChordSuggestion {
  chord: UnifiedChordPosition;
  confidence: number; // 0-100%
  reason: string;
  musicTheory: string;
  position?: number; // Position suggérée dans le texte
  category: 'progression' | 'substitution' | 'transition' | 'ending';
}

export interface SuggestionContext {
  lyrics: string;
  existingChords: ChordPlacement[];
  currentPosition: number;
  songMetadata?: {
    genre?: string;
    key?: string;
    tempo?: number;
    mood?: string;
  };
}

export interface AiChordSuggestionsProps {
  /** Contexte pour les suggestions */
  context: SuggestionContext;
  /** Callback pour demander des suggestions */
  onRequestSuggestions: (context: SuggestionContext) => Promise<ChordSuggestion[]>;
  /** Callback pour sélectionner un accord */
  onChordSelect: (chord: UnifiedChordPosition, position?: number) => void;
  /** Callback pour jouer un accord */
  onChordPlay?: (chord: UnifiedChordPosition) => void;
  /** Configuration IA */
  aiConfig?: {
    provider: string;
    model: string;
    temperature: number;
  };
  /** État de chargement */
  isLoading?: boolean;
  /** Classe CSS personnalisée */
  className?: string;
}

interface SuggestionCardProps {
  suggestion: ChordSuggestion;
  onSelect: () => void;
  onPlay?: () => void;
  isSelected?: boolean;
}

// ============================================================================
// COMPOSANTS INTERNES
// ============================================================================

/**
 * Carte de suggestion d'accord
 */
const SuggestionCard: React.FC<SuggestionCardProps> = ({
  suggestion,
  onSelect,
  onPlay,
  isSelected = false
}) => {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'progression': return <TrendingUp className="w-4 h-4" />;
      case 'substitution': return <Shuffle className="w-4 h-4" />;
      case 'transition': return <ArrowRight className="w-4 h-4" />;
      case 'ending': return <Target className="w-4 h-4" />;
      default: return <Music className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'progression': return 'text-blue-600 bg-blue-50';
      case 'substitution': return 'text-purple-600 bg-purple-50';
      case 'transition': return 'text-green-600 bg-green-50';
      case 'ending': return 'text-orange-600 bg-orange-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className={`
      relative p-4 border rounded-lg cursor-pointer transition-all
      ${isSelected 
        ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' 
        : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
      }
    `}>
      {/* En-tête */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <h4 className="font-semibold text-gray-900 mr-2">
            {suggestion.chord.chord}
          </h4>
          <div className={`
            flex items-center px-2 py-1 rounded-full text-xs font-medium
            ${getCategoryColor(suggestion.category)}
          `}>
            {getCategoryIcon(suggestion.category)}
            <span className="ml-1 capitalize">{suggestion.category}</span>
          </div>
        </div>

        {/* Score de confiance */}
        <div className="flex items-center">
          <div className={`
            px-2 py-1 rounded-full text-xs font-bold
            ${suggestion.confidence >= 80 
              ? 'bg-green-100 text-green-800' 
              : suggestion.confidence >= 60
              ? 'bg-yellow-100 text-yellow-800'
              : 'bg-red-100 text-red-800'
            }
          `}>
            {suggestion.confidence}%
          </div>
        </div>
      </div>

      {/* Diagramme */}
      <div className="w-full h-20 mb-3 flex justify-center">
        <ChordDiagramViewer
          chord={suggestion.chord}
          size="small"
          interactive={false}
          showLabels={false}
          showDetails={false}
        />
      </div>

      {/* Raison */}
      <div className="mb-3">
        <p className="text-sm text-gray-700 mb-1">
          <strong>Pourquoi :</strong> {suggestion.reason}
        </p>
        <p className="text-xs text-gray-600">
          <strong>Théorie :</strong> {suggestion.musicTheory}
        </p>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onPlay?.();
          }}
          className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
        >
          <Volume2 className="w-4 h-4 mr-1" />
          Écouter
        </button>

        <button
          onClick={onSelect}
          className="flex items-center px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4 mr-1" />
          Ajouter
        </button>
      </div>
    </div>
  );
};

/**
 * Panneau de configuration des suggestions
 */
const SuggestionSettings: React.FC<{
  onRefresh: () => void;
  isLoading: boolean;
  suggestionCount: number;
}> = ({ onRefresh, isLoading, suggestionCount }) => {
  return (
    <div className="flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200">
      <div className="flex items-center">
        <Sparkles className="w-5 h-5 text-purple-600 mr-2" />
        <h3 className="font-semibold text-gray-900">Suggestions IA</h3>
        {suggestionCount > 0 && (
          <span className="ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
            {suggestionCount} suggestion{suggestionCount !== 1 ? 's' : ''}
          </span>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <button
          onClick={onRefresh}
          disabled={isLoading}
          className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
          Actualiser
        </button>
      </div>
    </div>
  );
};

// ============================================================================
// COMPOSANT PRINCIPAL
// ============================================================================

export const AiChordSuggestions: React.FC<AiChordSuggestionsProps> = ({
  context,
  onRequestSuggestions,
  onChordSelect,
  onChordPlay,
  aiConfig,
  isLoading = false,
  className = ''
}) => {
  const { actions } = useChordSystem();
  
  // État local
  const [suggestions, setSuggestions] = useState<ChordSuggestion[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState<ChordSuggestion | null>(null);
  const [isRequesting, setIsRequesting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleRequestSuggestions = useCallback(async () => {
    setIsRequesting(true);
    setError(null);
    
    try {
      const newSuggestions = await onRequestSuggestions(context);
      setSuggestions(newSuggestions);
      setSelectedSuggestion(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors de la génération des suggestions');
      console.error('Erreur suggestions IA:', err);
    } finally {
      setIsRequesting(false);
    }
  }, [context, onRequestSuggestions]);

  const handleChordSelect = useCallback((suggestion: ChordSuggestion) => {
    onChordSelect(suggestion.chord, suggestion.position);
    setSelectedSuggestion(suggestion);
  }, [onChordSelect]);

  const handleChordPlay = useCallback(async (chord: UnifiedChordPosition) => {
    try {
      if (onChordPlay) {
        onChordPlay(chord);
      } else {
        await actions.playChord(chord, { mode: 'chord' });
      }
    } catch (error) {
      console.error('Erreur lecture accord:', error);
    }
  }, [onChordPlay, actions]);

  // Demander des suggestions automatiquement au changement de contexte
  useEffect(() => {
    if (context.lyrics && context.lyrics.length > 10) {
      handleRequestSuggestions();
    }
  }, [context.lyrics, context.existingChords.length]); // Éviter les appels trop fréquents

  // ============================================================================
  // RENDU
  // ============================================================================

  return (
    <div className={`ai-chord-suggestions ${className}`}>
      {/* En-tête */}
      <SuggestionSettings
        onRefresh={handleRequestSuggestions}
        isLoading={isRequesting || isLoading}
        suggestionCount={suggestions.length}
      />

      {/* Contenu */}
      <div className="p-4">
        {/* État de chargement */}
        {(isRequesting || isLoading) && (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center">
              <Brain className="w-6 h-6 text-purple-600 animate-pulse mr-3" />
              <div>
                <p className="text-gray-900 font-medium">Analyse en cours...</p>
                <p className="text-gray-600 text-sm">L'IA analyse vos paroles pour suggérer des accords</p>
              </div>
            </div>
          </div>
        )}

        {/* Erreur */}
        {error && (
          <div className="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
            <XCircle className="w-5 h-5 text-red-600 mr-3" />
            <div>
              <p className="text-red-900 font-medium">Erreur de suggestion</p>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Suggestions */}
        {!isRequesting && !isLoading && suggestions.length === 0 && !error && (
          <div className="text-center py-8">
            <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune suggestion</h3>
            <p className="text-gray-600 mb-4">
              Écrivez quelques paroles pour obtenir des suggestions d'accords
            </p>
            <button
              onClick={handleRequestSuggestions}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              Générer des suggestions
            </button>
          </div>
        )}

        {suggestions.length > 0 && (
          <div className="space-y-4">
            {/* Informations contextuelles */}
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center mb-2">
                <Brain className="w-4 h-4 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-900">Contexte analysé</span>
              </div>
              <div className="text-sm text-blue-800">
                <p><strong>Paroles :</strong> {context.lyrics.slice(0, 100)}...</p>
                {context.songMetadata?.genre && (
                  <p><strong>Genre :</strong> {context.songMetadata.genre}</p>
                )}
                {context.songMetadata?.key && (
                  <p><strong>Tonalité :</strong> {context.songMetadata.key}</p>
                )}
                <p><strong>Accords existants :</strong> {context.existingChords.length}</p>
              </div>
            </div>

            {/* Grille de suggestions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {suggestions.map((suggestion, index) => (
                <SuggestionCard
                  key={`${suggestion.chord.id}-${index}`}
                  suggestion={suggestion}
                  onSelect={() => handleChordSelect(suggestion)}
                  onPlay={() => handleChordPlay(suggestion.chord)}
                  isSelected={selectedSuggestion?.chord.id === suggestion.chord.id}
                />
              ))}
            </div>

            {/* Actions globales */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                {suggestions.length} suggestion{suggestions.length !== 1 ? 's' : ''} générée{suggestions.length !== 1 ? 's' : ''}
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => {
                    // Ajouter tous les accords avec haute confiance
                    suggestions
                      .filter(s => s.confidence >= 80)
                      .forEach(s => handleChordSelect(s));
                  }}
                  className="px-3 py-1 text-sm text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors"
                >
                  <CheckCircle2 className="w-4 h-4 mr-1 inline" />
                  Ajouter les meilleurs
                </button>
                
                <button
                  onClick={handleRequestSuggestions}
                  className="px-3 py-1 text-sm text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg transition-colors"
                >
                  <Wand2 className="w-4 h-4 mr-1 inline" />
                  Nouvelles suggestions
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
