// components/songs/SongVault.tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle,
} from '@/components/ui/card';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, DialogClose
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { PlusCircle, History, Trash2, PlayCircle, Edit3, Save, CheckSquare } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { type LocalSongVersion } from './hooks/useSongVersioning'; // Assuming this path is correct
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

// Define and export the handle type for SongVault
export interface SongVaultHandle {
  refreshVersions: () => void; // Example method, adjust as needed
  // Add other methods exposed by the handle
}

interface SongVaultProps {
  songId: string;
  activeVersionId: string | null; // Renamed from currentVersionId for clarity
  versions: LocalSongVersion[];
  onLoadVersion: (versionId: string) => Promise<void>;
  onDeleteVersion: (versionId: string) => Promise<void>;
  onSaveNewVersion: (name: string, notes: string, isMajor: boolean) => Promise<void>; 
  isLoadingSaveVersion: boolean;
  isLoadingDeleteVersion: boolean; // To disable delete button while deleting
  onUpdateVersion: (versionId: string, newName: string, newNotes: string) => Promise<void>;
  isLoadingUpdateVersion: boolean;
  // userId?: string; // If needed for permissions within vault UI
}

const SongVault = React.forwardRef<SongVaultHandle, SongVaultProps>(({ 
  songId, 
  activeVersionId, 
  versions, 
  onLoadVersion, 
  onDeleteVersion, 
  onSaveNewVersion,
  isLoadingSaveVersion,
  isLoadingDeleteVersion,
  onUpdateVersion,
  isLoadingUpdateVersion,
}, ref) => {
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const [newVersionName, setNewVersionName] = useState('');
  const [newVersionNotes, setNewVersionNotes] = useState('');
  const [markAsMajor, setMarkAsMajor] = useState(false);

  // State for editing an existing version
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [versionToEdit, setVersionToEdit] = useState<LocalSongVersion | null>(null);
  const [editedVersionName, setEditedVersionName] = useState('');
  const [editedVersionNotes, setEditedVersionNotes] = useState('');


  const handleSave = async () => {
    await onSaveNewVersion(newVersionName, newVersionNotes, markAsMajor);
    if (!isLoadingSaveVersion) { // Only close if save wasn't blocked by loading state (e.g. error)
      setIsSaveModalOpen(false);
      setNewVersionName('');
      setNewVersionNotes('');
      setMarkAsMajor(false);
    }
  };

  const handleOpenEditModal = (version: LocalSongVersion) => {
    setVersionToEdit(version);
    setEditedVersionName(version.version_name || `Version ${version.version_number}`);
    setEditedVersionNotes(version.notes || '');
    setIsEditModalOpen(true);
  };

  const handleConfirmEdit = async () => {
    if (!versionToEdit) return;
    await onUpdateVersion(versionToEdit.version_id, editedVersionName, editedVersionNotes);
    // Only close modal if not in loading state (i.e., error didn't prevent submission)
    if (!isLoadingUpdateVersion) {
      setIsEditModalOpen(false);
      setVersionToEdit(null);
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span><History className="inline-block mr-2 h-5 w-5" />Historique des Versions</span>
          <Dialog open={isSaveModalOpen} onOpenChange={setIsSaveModalOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <PlusCircle className="mr-2 h-4 w-4" /> Sauvegarder Version
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Sauvegarder une Nouvelle Version</DialogTitle>
                <DialogDescription>
                  Nommez cette version et ajoutez des notes si nécessaire.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="version-name" className="text-right">
                    Nom
                  </Label>
                  <Input 
                    id="version-name" 
                    value={newVersionName} 
                    onChange={(e) => setNewVersionName(e.target.value)} 
                    className="col-span-3" 
                    placeholder={`Version ${versions.length + 1}`}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="version-notes" className="text-right">
                    Notes
                  </Label>
                  <Textarea 
                    id="version-notes" 
                    value={newVersionNotes} 
                    onChange={(e) => setNewVersionNotes(e.target.value)} 
                    className="col-span-3" 
                    placeholder="Décrivez les changements..."
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="mark-as-major" className="text-right">
                    Majeure?
                  </Label>
                  <div className="col-span-3 flex items-center">
                    <Checkbox 
                      id="mark-as-major" 
                      checked={markAsMajor} 
                      onCheckedChange={(checked) => setMarkAsMajor(checked === true)} 
                    />
                    <span className="ml-2 text-sm text-muted-foreground">Marquer comme version majeure</span>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsSaveModalOpen(false)}>Annuler</Button>
                <Button onClick={handleSave} disabled={isLoadingSaveVersion}>
                  {isLoadingSaveVersion ? 'Sauvegarde...' : 'Sauvegarder'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Edit Version Dialog */}
          <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Modifier la Version</DialogTitle>
                <DialogDescription>
                  Mettez à jour le nom et les notes de cette version.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-version-name" className="text-right">
                    Nom
                  </Label>
                  <Input 
                    id="edit-version-name" 
                    value={editedVersionName} 
                    onChange={(e) => setEditedVersionName(e.target.value)} 
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-version-notes" className="text-right">
                    Notes
                  </Label>
                  <Textarea 
                    id="edit-version-notes" 
                    value={editedVersionNotes} 
                    onChange={(e) => setEditedVersionNotes(e.target.value)} 
                    className="col-span-3"
                    placeholder="Décrivez les changements..."
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => { setIsEditModalOpen(false); setVersionToEdit(null); }}>Annuler</Button>
                <Button onClick={handleConfirmEdit} disabled={isLoadingUpdateVersion}>
                  {isLoadingUpdateVersion ? 'Sauvegarde...' : 'Sauvegarder Modifications'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-grow overflow-hidden">
        {versions.length === 0 ? (
          <p className="text-sm text-muted-foreground text-center py-10">Aucune version sauvegardée pour ce morceau.</p>
        ) : (
          <ScrollArea className="h-full pr-4">
            <div className="space-y-4">
              {versions.map((version) => (
                <Card key={version.version_id} className={`relative ${activeVersionId === version.version_id ? 'border-primary' : ''}`}>
                  {activeVersionId === version.version_id && (
                    <Badge variant="secondary" className="absolute -top-2 -right-2">Actuelle</Badge>
                  )}
                  <CardHeader className="pb-2 pt-4">
                    <CardTitle className="text-md flex justify-between items-center">
                      <span>{version.version_name || `Version ${version.version_number}`}</span>
                      <span className="text-xs font-normal text-muted-foreground">
                        {format(new Date(version.created_at), 'dd MMM yyyy, HH:mm', { locale: fr })}
                      </span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-sm pb-3">
                    {version.notes && <p className="text-muted-foreground mb-2 line-clamp-2">{version.notes}</p>}
                    <div className="flex gap-2 mt-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => onLoadVersion(version.version_id)}
                        disabled={activeVersionId === version.version_id}
                      >
                        <PlayCircle className="mr-1 h-3 w-3" /> Charger
                      </Button>
                      <Button 
                        variant="destructive" 
                        size="sm" 
                        onClick={() => onDeleteVersion(version.version_id)} 
                        disabled={isLoadingDeleteVersion}
                      >
                        <Trash2 className="mr-1 h-3 w-3" /> Supprimer
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleOpenEditModal(version)}
                        className="ml-2"
                      >
                        <Edit3 className="mr-1 h-3 w-3" /> Modifier
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
});

SongVault.displayName = 'SongVault';

export default React.memo(SongVault);
