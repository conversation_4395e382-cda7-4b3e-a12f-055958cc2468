'use client';

import React, { useState, useCallback, useRef, forwardRef, useImperativeHandle } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, Guitar, Eye, EyeOff, Music, Layers, Type, Wand2
} from 'lucide-react';

// Import de notre Enhanced Lyrics Editor (temporairement commenté pour éviter les dépendances circulaires)
// import { LyricsChordWorkflow, type ChordPlacement } from '@/components/enhanced-lyrics-editor';
// import { ChordSystemProvider } from '@/components/chord-system/providers/ChordSystemProvider';

// Types temporaires
interface ChordPlacement {
  id: string;
  chord: string;
  textPosition: number;
  lineNumber: number;
  wordIndex: number;
  timestamp: string;
  metadata?: any;
}

// Import des outils IA existants (à extraire dans AILyricsToolbar)
import { AILyricsToolbar } from './AILyricsToolbar';
import { LyricsAnalysisPanel } from './LyricsAnalysisPanel';

interface LyricsSection {
  id: string;
  type: 'verse' | 'chorus' | 'bridge' | 'intro' | 'outro' | 'pre-chorus' | 'coda';
  title: string;
  content: string;
  chords: Array<{
    position: number;
    chord: string;
    instrument: string;
  }>;
  aiSuggestions?: string[];
}

interface LyricsEditorUnifiedProps {
  content: string;
  onContentChange: (content: string) => void;
  selectedSection: string;
  sections: LyricsSection[];
  onAIGenerate: (prompt: string, type: 'lyrics' | 'chords' | 'structure') => Promise<void>;
  generalPrompt?: string;
  onEditGeneralPrompt?: (newPrompt: string) => void;
}

type ViewMode = 'text' | 'chords' | 'hybrid' | 'enhanced';

export const LyricsEditorUnified = forwardRef<any, LyricsEditorUnifiedProps>((
  { content, onContentChange, selectedSection, sections, onAIGenerate },
  ref
) => {
  // États pour les modes de visualisation
  const [viewMode, setViewMode] = useState<ViewMode>('enhanced');
  const [showAIPanel, setShowAIPanel] = useState(true);
  const [showAnalysisPanel, setShowAnalysisPanel] = useState(false);
  const [chordPlacements, setChordPlacements] = useState<ChordPlacement[]>([]);
  
  // Références
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const enhancedEditorRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    focus: () => {
      if (viewMode === 'enhanced' && enhancedEditorRef.current) {
        enhancedEditorRef.current.focus();
      } else if (textareaRef.current) {
        textareaRef.current.focus();
      }
    },
    insertText: (text: string) => {
      if (viewMode === 'enhanced' && enhancedEditorRef.current) {
        enhancedEditorRef.current.insertText(text);
      } else if (textareaRef.current) {
        const start = textareaRef.current.selectionStart;
        const end = textareaRef.current.selectionEnd;
        const newContent = content.substring(0, start) + text + content.substring(end);
        onContentChange(newContent);
        
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.selectionStart = start + text.length;
            textareaRef.current.selectionEnd = start + text.length;
            textareaRef.current.focus();
          }
        }, 0);
      }
    }
  }));

  // Gestionnaire pour les changements du Enhanced Lyrics Editor
  const handleEnhancedLyricsChange = useCallback((
    newLyrics: string, 
    newChordPlacements: ChordPlacement[]
  ) => {
    onContentChange(newLyrics);
    setChordPlacements(newChordPlacements);
  }, [onContentChange]);

  // Gestionnaire pour les changements de mode
  const handleViewModeChange = useCallback((mode: ViewMode) => {
    setViewMode(mode);
  }, []);

  const currentSection = sections.find(s => s.id === selectedSection);

  // Configuration des modes de visualisation
  const viewModes = [
    {
      id: 'text' as ViewMode,
      label: 'Texte',
      icon: Type,
      description: 'Texte brut sans accords'
    },
    {
      id: 'chords' as ViewMode,
      label: 'Accords',
      icon: Music,
      description: 'Accords uniquement'
    },
    {
      id: 'hybrid' as ViewMode,
      label: 'Hybride',
      icon: Layers,
      description: 'Texte avec accords intégrés'
    },
    {
      id: 'enhanced' as ViewMode,
      label: 'Enhanced',
      icon: Wand2,
      description: 'Éditeur avancé avec overlay'
    }
  ];

  return (
    // <ChordSystemProvider> temporairement désactivé
    <div className="h-full flex flex-col bg-background">
      <div className="h-full flex flex-col">
        {/* En-tête avec contrôles de mode */}
        <div className="border-b bg-card p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="capitalize">
                {currentSection?.type || 'Section'}
              </Badge>
              <h2 className="font-medium">{currentSection?.title || 'Section'}</h2>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Sélecteur de mode de visualisation */}
              <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
                {viewModes.map((mode) => {
                  const Icon = mode.icon;
                  return (
                    <Button
                      key={mode.id}
                      variant={viewMode === mode.id ? "default" : "ghost"}
                      size="sm"
                      onClick={() => handleViewModeChange(mode.id)}
                      className="gap-1"
                      title={mode.description}
                    >
                      <Icon className="h-4 w-4" />
                      {mode.label}
                    </Button>
                  );
                })}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAnalysisPanel(!showAnalysisPanel)}
                className="gap-1"
              >
                <FileText className="h-4 w-4" />
                Analyse
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAIPanel(!showAIPanel)}
                className="gap-1"
              >
                {showAIPanel ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showAIPanel ? 'Masquer IA' : 'Afficher IA'}
              </Button>
            </div>
          </div>
        </div>

        {/* Contenu principal */}
        <div className="flex-1 flex overflow-hidden">
          {/* Éditeur principal */}
          <div className="flex-1 flex flex-col">
            {viewMode === 'enhanced' ? (
              // Mode Enhanced temporairement remplacé par textarea
              <div className="flex-1 p-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <p className="text-blue-800 text-sm">
                    🚧 Mode Enhanced temporairement désactivé - En cours d'intégration
                  </p>
                </div>
                <textarea
                  ref={textareaRef}
                  value={content}
                  onChange={(e) => onContentChange(e.target.value)}
                  placeholder={`Mode Enhanced - Écrivez les paroles pour ${currentSection?.title || 'cette section'}...\n\nUtilisez [Accord] pour insérer des accords\nEx: [C] Hello [G] world [Am] how are [F] you`}
                  className="w-full h-full resize-none font-mono text-sm leading-relaxed border rounded-lg p-4"
                  style={{ minHeight: '400px' }}
                />
              </div>
            ) : (
              // Modes traditionnels (texte, accords, hybride)
              <div className="flex-1 p-4">
                <textarea
                  ref={textareaRef}
                  value={content}
                  onChange={(e) => onContentChange(e.target.value)}
                  placeholder={`Écrivez les paroles pour ${currentSection?.title || 'cette section'}...\n\nMode ${viewMode} sélectionné`}
                  className="w-full h-full resize-none font-mono text-sm leading-relaxed border rounded-lg p-4"
                  style={{ minHeight: '400px' }}
                />
              </div>
            )}
          </div>

          {/* Panneau IA (conditionnel) */}
          {showAIPanel && (
            <div className="w-80 border-l bg-card/50">
              <AILyricsToolbar
                content={content}
                onContentChange={onContentChange}
                selectedSection={selectedSection}
                sections={sections}
                onAIGenerate={onAIGenerate}
                viewMode={viewMode}
              />
            </div>
          )}

          {/* Panneau d'analyse (conditionnel) */}
          {showAnalysisPanel && (
            <div className="w-80 border-l bg-card/50">
              <LyricsAnalysisPanel
                content={content}
                chordPlacements={chordPlacements}
                currentSection={currentSection}
              />
            </div>
          )}
        </div>

        {/* Barre d'état */}
        <div className="border-t bg-card p-3">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>Lignes: {content.split('\n').length}</span>
              <span>Mots: {content.trim().split(/\s+/).filter(Boolean).length}</span>
              <span>Caractères: {content.length}</span>
              {chordPlacements.length > 0 && (
                <span>Accords: {chordPlacements.length}</span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                Mode: {viewModes.find(m => m.id === viewMode)?.label}
              </Badge>
              {currentSection && (
                <Badge variant="outline" className="text-xs capitalize">
                  {currentSection.type}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
    // </ChordSystemProvider> temporairement désactivé
  );
});

LyricsEditorUnified.displayName = 'LyricsEditorUnified';
