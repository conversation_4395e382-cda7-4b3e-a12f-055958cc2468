-- Table pour les likes (morceaux, albums)
CREATE TABLE IF NOT EXISTS likes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('song', 'album', 'playlist')),
  resource_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, resource_type, resource_id)
);

-- Table pour les follows (artistes, groupes)
CREATE TABLE IF NOT EXISTS follows (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  following_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  following_type VARCHAR(20) NOT NULL CHECK (following_type IN ('user', 'band')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(follower_id, following_id, following_type)
);

-- Table pour les vues (morceaux, albums, profils)
CREATE TABLE IF NOT EXISTS views (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('song', 'album', 'profile', 'band')),
  resource_id UUID NOT NULL,
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les commentaires publics
CREATE TABLE IF NOT EXISTS comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  resource_type VARCHAR(20) NOT NULL CHECK (resource_type IN ('song', 'album', 'band')),
  resource_id UUID NOT NULL,
  content TEXT NOT NULL,
  parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les commentaires privés de groupe
CREATE TABLE IF NOT EXISTS band_comments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  band_id UUID NOT NULL REFERENCES bands(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  parent_id UUID REFERENCES band_comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les conversations
CREATE TABLE IF NOT EXISTS conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table pour les participants aux conversations
CREATE TABLE IF NOT EXISTS conversation_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(conversation_id, user_id)
);

-- Table pour les messages
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  read_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fonction pour mettre à jour le timestamp updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers pour mettre à jour updated_at
CREATE TRIGGER update_comments_updated_at
BEFORE UPDATE ON comments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_band_comments_updated_at
BEFORE UPDATE ON band_comments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at
BEFORE UPDATE ON conversations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at
BEFORE UPDATE ON messages
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Fonction pour incrémenter le compteur de lectures d'un morceau
CREATE OR REPLACE FUNCTION increment_song_plays(song_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE songs SET plays = COALESCE(plays, 0) + 1 WHERE id = song_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir les statistiques d'un artiste
CREATE OR REPLACE FUNCTION get_artist_stats(artist_id UUID)
RETURNS TABLE (
  total_plays BIGINT,
  total_followers BIGINT,
  total_albums BIGINT,
  total_songs BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(SUM(s.plays), 0)::BIGINT AS total_plays,
    (SELECT COUNT(*) FROM follows WHERE following_id = artist_id AND following_type = 'user')::BIGINT AS total_followers,
    (SELECT COUNT(*) FROM albums WHERE user_id = artist_id)::BIGINT AS total_albums,
    (SELECT COUNT(*) FROM songs WHERE user_id = artist_id)::BIGINT AS total_songs;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir les statistiques d'un groupe
CREATE OR REPLACE FUNCTION get_band_stats(band_id UUID)
RETURNS TABLE (
  total_plays BIGINT,
  total_followers BIGINT,
  total_albums BIGINT,
  total_songs BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(SUM(s.plays), 0)::BIGINT AS total_plays,
    (SELECT COUNT(*) FROM follows WHERE following_id = band_id AND following_type = 'band')::BIGINT AS total_followers,
    (SELECT COUNT(*) FROM albums WHERE band_id = band_id)::BIGINT AS total_albums,
    (SELECT COUNT(*) FROM songs WHERE band_id = band_id)::BIGINT AS total_songs;
END;
$$ LANGUAGE plpgsql;
