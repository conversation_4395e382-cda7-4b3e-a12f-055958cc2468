"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"
import { Loader2, Users, TrendingUp, Award } from "lucide-react"

interface ArtistComparisonProps {
  userId: string
  // timeRange?: '7d' | '30d' | '90d' | '1y'; // Optionnel, si la RPC le prend en compte
}

interface ArtistComparisonData {
  rank: number
  growth_percentile: number
  engagement_percentile: number
  metrics: {
    metric: string
    value: number    // Valeur de l'utilisateur actuel
    average: number  // Moyenne des artistes similaires
    percentile: number // Percentile de l'utilisateur actuel
  }[]
}

export function ArtistComparison({ userId }: ArtistComparisonProps) {
  const [comparisonData, setComparisonData] = useState<ArtistComparisonData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchComparisonData = async () => {
      if (!userId) {
        setIsLoading(false);
        return;
      }
      try {
        setIsLoading(true)
        setError(null)
        const supabase = createBrowserClient()
        
        // Appel réel à la fonction RPC Supabase
        // La RPC 'get_artist_comparison_data' doit être créée.
        // Elle prendra p_user_id et potentiellement p_time_range.
        // Elle devra calculer le rang, les percentiles et les métriques comparatives.
        // C'est une RPC complexe à implémenter côté backend.
        const { data, error: rpcError } = await supabase.rpc('get_artist_comparison_data', {
          p_user_id: userId
          // p_time_range: timeRange // Si la RPC est conçue pour utiliser timeRange
        })
        
        if (rpcError) {
          console.error("Erreur RPC get_artist_comparison_data:", rpcError)
          setError(rpcError.message)
          setComparisonData(null)
        } else if (data) {
          setComparisonData(data as ArtistComparisonData)
        } else {
          setComparisonData(null)
        }
      } catch (err) {
        console.error("Erreur lors de la récupération des données de comparaison:", err)
        setError(err instanceof Error ? err.message : "Une erreur est survenue")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchComparisonData()
  }, [userId]) // Ajouter timeRange si la RPC l'utilise
  
  if (isLoading) { /* ... Loader ... */ 
    return (
      <Card>
        <CardHeader><CardTitle>Comparaison artistes</CardTitle><CardDescription>Chargement...</CardDescription></CardHeader>
        <CardContent className="flex justify-center items-center h-[300px]"><Loader2 className="h-8 w-8 animate-spin text-primary" /></CardContent>
      </Card>
    );
  }
  
  if (error) { /* ... Error ... */ 
    return (
      <Card>
        <CardHeader><CardTitle>Comparaison artistes</CardTitle><CardDescription>Erreur: {error}</CardDescription></CardHeader>
        <CardContent>
          <p className="text-destructive">Impossible de charger les données de comparaison.</p>
          <p className="text-destructive mt-2">Veuillez vérifier que la fonction RPC 'get_artist_comparison_data' existe et fonctionne.</p>
        </CardContent>
      </Card>
    );
  }
  
  if (!comparisonData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Comparaison avec artistes similaires</CardTitle>
          <CardDescription>Données non disponibles</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Les données de comparaison ne sont pas encore disponibles.</p>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="h-4 w-4 mr-2" />
          Comparaison avec artistes similaires
        </CardTitle>
        <CardDescription>Comment vous vous positionnez par rapport aux artistes de votre genre</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Votre rang</p>
                  <h3 className="text-2xl font-bold">#{comparisonData.rank}</h3>
                  <p className="text-xs text-muted-foreground">Parmi les artistes de votre genre</p>
                </div>
                <div className="p-2 rounded-full bg-indigo-50 dark:bg-indigo-950">
                  <Award className="h-5 w-5 text-indigo-500" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Croissance</p>
                  <h3 className="text-2xl font-bold">{comparisonData.growth_percentile}%</h3>
                  <p className="text-xs text-muted-foreground">Meilleur que % des artistes</p>
                </div>
                <div className="p-2 rounded-full bg-green-50 dark:bg-green-950">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Engagement</p>
                  <h3 className="text-2xl font-bold">{comparisonData.engagement_percentile}%</h3>
                  <p className="text-xs text-muted-foreground">Meilleur que % des artistes</p>
                </div>
                <div className="p-2 rounded-full bg-cyan-50 dark:bg-cyan-950">
                  <Users className="h-5 w-5 text-cyan-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={comparisonData.metrics}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              layout="vertical"
            >
              <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
              <XAxis type="number" />
              <YAxis dataKey="metric" type="category" width={150} tick={{ fontSize: 10 }} />
              <Tooltip 
                formatter={(value, name) => [
                  value, 
                  name === 'value' ? 'Votre valeur' : 'Moyenne du genre'
                ]}
              />
              <Legend />
              <Bar dataKey="value" name="Votre valeur" fill="#4ECDC4" radius={[0, 4, 4, 0]} />
              <Bar dataKey="average" name="Moyenne du genre" fill="#C44D58" radius={[0, 4, 4, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        <div className="mt-6 space-y-4">
          <h4 className="text-sm font-medium">Votre positionnement par métrique</h4>
          <div className="space-y-4">
            {comparisonData.metrics.map((metric) => (
              <div key={metric.metric} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-xs sm:text-sm">{metric.metric}</span>
                  <span className="text-xs text-muted-foreground">
                    {metric.value} vs {metric.average} (moyenne)
                  </span>
                </div>
                <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-cyan-500" 
                    style={{ width: `${metric.percentile}%` }} 
                  />
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>0%</span>
                  <span>Percentile: {metric.percentile}%</span>
                  <span>100%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
