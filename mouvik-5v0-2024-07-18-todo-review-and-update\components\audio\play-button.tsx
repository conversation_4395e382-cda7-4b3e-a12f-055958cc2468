"use client"

import { Play, Pause } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useAudio } from "@/contexts/audio-context"
import { usePlaySong } from "@/hooks/use-play-song"
import type { Song } from "@/types"
import { cn } from "@/lib/utils"

// Importons l'action pour incrémenter le compteur de lecture
import { incrementPlayCount } from "@/lib/actions/stats"

interface PlayButtonProps {
  song: Song & { artist?: string }
  size?: "sm" | "md" | "lg"
  variant?: "default" | "outline" | "ghost"
  className?: string
}

export function PlayButton({ song, size = "md", variant = "outline", className }: PlayButtonProps) {
  const { currentSong, isPlaying } = useAudio()
  const { play } = usePlaySong()

  const isCurrentSong = currentSong?.id === song.id

  // Dans la fonction handlePlay, ajoutons l'incrémentation du compteur
  const handlePlay = () => {
    if (song) {
      play(song)
      // Incrémenter le compteur de lecture
      incrementPlayCount(song.id).catch(console.error)
    }
  }

  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10",
    lg: "h-12 w-12",
  }

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  }

  return (
    <Button variant={variant} size="icon" onClick={handlePlay} className={cn(sizeClasses[size], className)}>
      {isCurrentSong && isPlaying ? <Pause className={iconSizes[size]} /> : <Play className={iconSizes[size]} />}
      <span className="sr-only">{isCurrentSong && isPlaying ? "Pause" : "Play"}</span>
    </Button>
  )
}
