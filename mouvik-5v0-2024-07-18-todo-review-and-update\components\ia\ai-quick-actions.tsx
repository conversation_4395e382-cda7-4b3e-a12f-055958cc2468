"use client";

import React, { useState, useEffect } from "react"; // Import useEffect
import { But<PERSON> } from "../ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Input } from "../ui/input";
import { Loader2, Brain, Wand2, RefreshCcw, Languages, CheckCircle2, XCircle } from "lucide-react";
import { countTokens, truncateToTokens } from './tokenizer';

interface AiQuickActionsProps {
  // Removed props related to lyrics manipulation:
  // onGenerate: (prompt?: string) => void;
  // onCorrect: () => void;
  // onTranslate: (lang: string) => void;
  // onFormatLayout?: () => void; 
  // onRhymeSuggestions?: () => void; 
  // onAnalyzeTone?: () => void; 
  // isTextSelected?: boolean; 

  // Props related to general suggestions/advice remain:
  onGeneralSuggestions?: () => void; 
  onMelodySuggestion?: () => void;
  onRecordingAdvice?: () => void;
  onInstrumentationSuggestion?: () => void;
  onCreativeFx?: () => void;
  onArrangementAdvice?: () => void;
  
  // Props related to AI config and state remain:
  aiConfig: {
    provider: string;
    model: string;
    temperature: number;
  };
  setAiConfig: (cfg: Partial<AiQuickActionsProps["aiConfig"]>) => void;
  loading?: boolean;
  lastResult?: string;
  error?: string;
  iaHistory?: { role: string; content: string }[];
  maxOutputTokens?: number;
  setMaxOutputTokens?: (n: number) => void;
  onLightenPrompt?: () => void;
  // isTextSelected is removed
}

// Removed languageOptions constant

// Remove PromptEditDialog import as it's no longer used here
// import { PromptEditDialog } from "./PromptEditDialog"; 
import { Lightbulb, Palette, Mic2, Sparkles, SlidersHorizontal, Music3 } from 'lucide-react';

export function AiQuickActions({
  // onGenerate, // Removed
  // onCorrect, // Removed
  // onTranslate, // Removed
  aiConfig,
  setAiConfig,
  loading,
  lastResult,
  error,
  iaHistory = [],
  maxOutputTokens = 200,
  setMaxOutputTokens = () => {},
  onLightenPrompt = () => {},
  generalPrompt = "", // Keep general prompt editing
  onEditGeneralPrompt = () => {}, // Keep general prompt editing
  // onFormatLayout, // Removed
  onGeneralSuggestions, // Keep
  // onRhymeSuggestions, // Removed
  // onAnalyzeTone, // Removed
  onMelodySuggestion, // Keep
  onRecordingAdvice, // Keep
  onInstrumentationSuggestion, // Keep
  onCreativeFx, // Keep
  onArrangementAdvice, // Keep
  // isTextSelected, // Removed
}: AiQuickActionsProps & {
  generalPrompt: string; // Keep these if needed for displaying/editing general prompt
  onEditGeneralPrompt: (newPrompt: string) => void; // Keep these if needed for displaying/editing general prompt
}) {
  // const [prompt, setPrompt] = useState(""); // Removed
  // const [selectedLang, setSelectedLang] = useState<string>("en"); // Removed

  // Removed useEffect for setting language based on model

  // Calcul du nombre de tokens prompt actuel (user history only)
  const currentPrompt = iaHistory.filter(h => h.role === 'user').map(h => h.content).join('\n');
  const promptTokenCount = countTokens(currentPrompt);

  // Removed dynamic labels

  return (
    <div className="flex flex-col gap-4 w-[360px] bg-white dark:bg-zinc-900 rounded-2xl shadow-2xl border border-zinc-200 dark:border-zinc-700 p-5">
      {/* Header - Remains the same */}
      <div className="flex items-center gap-3 mb-1 pb-2 border-b border-zinc-200 dark:border-zinc-700 justify-between">
        <div className="flex items-center gap-3">
          <Brain className="w-6 h-6 text-primary" />
          <span className="font-bold text-lg tracking-tight">Assistance IA</span>
        </div>
      </div>
      {/* Removed Prompt Input, Generate, Correct, Translate blocks */}

      {/* Actions Block - Only General Advice/Suggestions */}
      <div className="flex flex-col gap-2 border-t border-zinc-200 dark:border-zinc-700 pt-3 mt-2">
         {/* Removed Format Layout Button */}
         {/* Removed Rhyme Suggestions Button */}
         {/* Removed Analyze Tone Button */}

         {/* --- Songwriting & Production Suggestions --- */}
         {onGeneralSuggestions && (
           <Button size="sm" variant="outline" className="w-full text-xs h-8 justify-start" onClick={onGeneralSuggestions} disabled={loading} title="Obtenir des suggestions générales sur la structure, les thèmes, etc.">
             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 flex-shrink-0"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/><polyline points="15 3 21 3 21 9"/><line x1="10" y1="14" x2="21" y2="3"/></svg>
             Suggestions Générales
           </Button>
         )}
           {onMelodySuggestion && (
            <Button size="sm" variant="outline" className="w-full text-xs h-8 justify-start" onClick={onMelodySuggestion} disabled={loading} title="Suggérer des idées de mélodie basées sur les paroles/contexte">
              <Music3 className="mr-2 h-4 w-4 flex-shrink-0" />
              Idées Mélodie
            </Button>
          )}
           {onInstrumentationSuggestion && (
            <Button size="sm" variant="outline" className="w-full text-xs h-8 justify-start" onClick={onInstrumentationSuggestion} disabled={loading} title="Suggérer des instruments additionnels">
              <SlidersHorizontal className="mr-2 h-4 w-4 flex-shrink-0" />
              Suggestion Instruments
            </Button>
          )}
           {onArrangementAdvice && (
            <Button size="sm" variant="outline" className="w-full text-xs h-8 justify-start" onClick={onArrangementAdvice} disabled={loading} title="Conseils sur la structure et l'arrangement du morceau">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 flex-shrink-0"><path d="M12 3V12"/><path d="M18.36 6.64L12 12"/><path d="M21 12H12"/><path d="M18.36 17.36L12 12"/><path d="M12 21V12"/><path d="M5.64 17.36L12 12"/><path d="M3 12H12"/><path d="M5.64 6.64L12 12"/></svg>
              Conseil Arrangement
            </Button>
          )}
          {/* --- Production Specific --- */}
           {onCreativeFx && (
            <Button size="sm" variant="outline" className="w-full text-xs h-8 justify-start" onClick={onCreativeFx} disabled={loading} title="Suggérer des effets créatifs (reverb, delay, etc.)">
              <Sparkles className="mr-2 h-4 w-4 flex-shrink-0" />
              Idées Effets (FX)
            </Button>
          )}
          {onRecordingAdvice && (
            <Button size="sm" variant="outline" className="w-full text-xs h-8 justify-start" onClick={onRecordingAdvice} disabled={loading} title="Conseils techniques pour l'enregistrement">
              <Mic2 className="mr-2 h-4 w-4 flex-shrink-0" />
              Conseil Enregistrement
            </Button>
          )}
      </div>


      {/* Bloc Infos Prompt & Réglages */}
      <div className="flex flex-col gap-2 bg-zinc-50 dark:bg-zinc-800 rounded-xl px-3 py-2 mt-2 border-t border-zinc-200 dark:border-zinc-700 pt-3">
        <div className="flex items-center justify-between">
          <span className="text-xs text-zinc-500 dark:text-zinc-400">Prompt tokens : <b>{promptTokenCount}</b></span>
          {onLightenPrompt && (
            <Button size="sm" variant="ghost" onClick={onLightenPrompt} title="Alléger le prompt (tronquer)" className="text-xs px-2 py-1 h-6 text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200">
              Alléger
            </Button>
          )}
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-zinc-500 dark:text-zinc-400">Max tokens sortie :</span>
          <input
            type="number"
            min={32}
            max={4096} // Increased max
            step={32} // Larger step
            value={maxOutputTokens}
            onChange={e => setMaxOutputTokens(Number(e.target.value))}
            className="w-16 border rounded px-1 text-xs focus:ring-1 focus:ring-primary/50 bg-white dark:bg-zinc-700 h-6"
            title="Nombre max de tokens générés par l'IA"
          />
          {/* <span className="text-xs text-zinc-500 dark:text-zinc-400">tokens</span> */}
        </div>
         <div className="flex items-center gap-2">
          <span className="text-xs text-zinc-500 dark:text-zinc-400">Température :</span>
          <input
            type="number"
            min={0}
            max={2}
            step={0.1}
            value={aiConfig.temperature}
            onChange={e => setAiConfig({ temperature: parseFloat(e.target.value) })}
            className="w-16 border rounded px-1 text-xs focus:ring-1 focus:ring-primary/50 bg-white dark:bg-zinc-700 h-6"
            title="Contrôle la créativité (0=déterministe, 2=très créatif)"
          />
        </div>
      </div>
      {/* Bloc Provider & Modèle - REMOVED - Handled by AiConfigMenu Popover */}
      {/* 
      <div className="flex flex-col gap-2 bg-zinc-50 dark:bg-zinc-800 rounded-xl px-3 py-2">
        ... Provider/Model selection ...
      </div> 
      */}

      {/* Bloc Feedback IA */}
      <div className="mt-3 space-y-2">
        {loading && (
          <div className="flex items-center gap-2 text-primary text-xs">
            <Loader2 className="animate-spin w-4 h-4" />
            <span>Traitement IA…</span>
          </div>
        )}
        {lastResult && !loading && (
          <div className="bg-green-50 dark:bg-green-900/50 rounded p-2 text-xs flex items-start gap-2 border border-green-200 dark:border-green-700/50 text-green-800 dark:text-green-200">
            <CheckCircle2 className="w-4 h-4 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
            <span className="break-words">{lastResult}</span>
          </div>
        )}
        {error && !loading && (
          <div className="bg-red-50 dark:bg-red-900/50 rounded p-2 text-xs flex items-start gap-2 border border-red-200 dark:border-red-700/50 text-red-800 dark:text-red-200">
            <XCircle className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
            <span className="break-words">{error}</span>
          </div>
        )}
      </div>
    </div>
  );
}
